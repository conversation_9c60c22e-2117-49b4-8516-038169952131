#!/usr/bin/env node

/**
 * Setup Test User Script
 * Creates a test user for swap execution
 */

require('dotenv').config();
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

const prisma = new PrismaClient();

const TEST_USER = {
  email: '<EMAIL>',
  walletAddress: process.env.TRADING_WALLET_ADDRESS,
  password: 'testpassword123',
  role: 'user'
};

async function setupTestUser() {
  console.log('👤 Setting up test user for swap execution...');
  console.log(`📧 Email: ${TEST_USER.email}`);
  console.log(`🏦 Wallet: ${TEST_USER.walletAddress}`);
  console.log('');

  try {
    // Check if user already exists
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { email: TEST_USER.email },
          { walletAddress: TEST_USER.walletAddress }
        ]
      }
    });

    let user;
    if (existingUser) {
      console.log('👤 Test user already exists, updating...');
      user = existingUser;
    } else {
      console.log('👤 Creating new test user...');
      
      // Hash password
      const hashedPassword = await bcrypt.hash(TEST_USER.password, 12);
      
      // Create user
      user = await prisma.user.create({
        data: {
          email: TEST_USER.email,
          walletAddress: TEST_USER.walletAddress,
          passwordHash: hashedPassword,
          role: TEST_USER.role,
          isEmailVerified: true,
          settings: {
            create: {
              currency: 'USD',
              riskLevel: 'MEDIUM',
              alertsEnabled: true,
              theme: 'dark'
            }
          }
        }
      });
      
      console.log('✅ Test user created successfully');
    }

    // Generate JWT tokens
    const accessToken = jwt.sign(
      { 
        id: user.id, 
        email: user.email, 
        role: user.role,
        walletAddress: user.walletAddress
      },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );

    console.log('');
    console.log('🔑 Authentication Details:');
    console.log(`   User ID: ${user.id}`);
    console.log(`   Access Token: ${accessToken}`);
    console.log('');
    console.log('✅ Test user setup complete!');
    
    return {
      user,
      accessToken
    };

  } catch (error) {
    console.error('❌ Error setting up test user:', error.message);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run setup
setupTestUser()
  .then(result => {
    console.log('Test user setup completed successfully');
    
    // Write auth token to file for use by swap script
    const fs = require('fs');
    fs.writeFileSync('/Users/<USER>/dev/github/agmentcode/auth_token.txt', result.accessToken);
    console.log('🔐 Auth token saved to auth_token.txt');
  })
  .catch(error => {
    console.error('Setup failed:', error);
    process.exit(1);
  });