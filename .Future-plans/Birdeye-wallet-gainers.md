# requirements.md
___________________
# Requirements Document

## Introduction

This feature involves creating a web component that integrates with the Birdeye API to display the top 10 wallet gainers on the Solana blockchain. The component will provide users with real-time insights into the most profitable wallets over selectable time periods, featuring a clean professional interface that integrates seamlessly with the existing MemeTrader Pro application.

## Requirements

### Requirement 1

**User Story:** As a trader using MemeTrader Pro, I want to view the top 10 wallet gainers on Solana, so that I can identify successful trading patterns and potentially profitable wallets to monitor.

#### Acceptance Criteria

1. WHEN the component loads THEN the system SHALL display the top 10 wallet gainers for the default "1 Week" time period
2. WHEN the API request is successful THEN the system SHALL display wallet addresses, PnL values, percentage gains, and other relevant data from the response
3. WHEN the component is displayed THEN the system SHALL show data in a well-formatted list or table structure
4. WHEN the component renders THEN the system SHALL use responsive design that adapts to both desktop and mobile viewports

### Requirement 2

**User Story:** As a user, I want to select different time periods for viewing wallet gainers, so that I can analyze performance over various timeframes.

#### Acceptance Criteria

1. WHEN the component loads THEN the system SHALL provide a time period selector with options "Today", "Yesterday", and "1 Week"
2. WHEN a user selects a time period THEN the system SHALL update the API request with the corresponding type parameter ("today", "yesterday", "1W")
3. WHEN the time period changes THEN the system SHALL fetch new data and update the display accordingly
4. WHEN no selection is made THEN the system SHALL default to "1 Week" time period

### Requirement 3

**User Story:** As a user, I want the component to handle loading and error states gracefully, so that I have a smooth experience even when the API is slow or unavailable.

#### Acceptance Criteria

1. WHEN an API request is initiated THEN the system SHALL display a loading state indicator
2. WHEN the API returns an error (400, 401, 403, 429, 500) THEN the system SHALL display an appropriate error message
3. WHEN an API request fails THEN the system SHALL provide a way for users to retry the request
4. WHEN the component is loading THEN the system SHALL prevent multiple simultaneous API requests

### Requirement 4

**User Story:** As a user, I want to refresh the wallet gainers data, so that I can see the most current information without reloading the entire page.

#### Acceptance Criteria

1. WHEN the component is displayed THEN the system SHALL provide a refresh functionality
2. WHEN the refresh is triggered THEN the system SHALL fetch updated data using the current time period selection
3. WHEN refreshing THEN the system SHALL show appropriate loading indicators
4. WHEN refresh completes THEN the system SHALL update the displayed data with the latest information

### Requirement 5

**User Story:** As a developer integrating this component, I want it to follow MemeTrader Pro's design patterns and styling conventions, so that it maintains visual consistency with the existing trading interface.

#### Acceptance Criteria

1. WHEN the component is rendered THEN the system SHALL use styling that matches the professional trading platform aesthetic
2. WHEN integrated THEN the system SHALL follow established design patterns used in current trading interface components
3. WHEN displayed THEN the system SHALL maintain visual consistency with the existing MemeTrader Pro application
4. WHEN implemented THEN the system SHALL use proper TypeScript types for all data structures and API responses

### Requirement 6

**User Story:** As a system integrating with the Birdeye API, I want to make properly formatted requests with correct parameters, so that I receive accurate wallet gainer data.

#### Acceptance Criteria

1. WHEN making API requests THEN the system SHALL use the endpoint `https://public-api.birdeye.so/trader/gainers-losers`
2. WHEN sending requests THEN the system SHALL include headers `x-chain: solana` and `accept: application/json`
3. WHEN querying data THEN the system SHALL use fixed parameters: `sort_by: "PnL"`, `sort_type: "desc"`, `offset: 0`, `limit: 10`
4. WHEN the time period is selected THEN the system SHALL map UI selections to API parameters: "Today" → "today", "Yesterday" → "yesterday", "1 Week" → "1W"


# spec.md
# Design Document

## Overview

The Birdeye Wallet Gainers component is a React TypeScript component that displays the top 10 performing wallets on the Solana blockchain. It integrates with the Birdeye API to fetch real-time data and presents it in a professional, responsive interface that matches MemeTrader Pro's design aesthetic.

The component follows a modular architecture with clear separation of concerns: API service layer, state management, UI presentation, and error handling. It implements modern React patterns including hooks for state management, custom hooks for API integration, and proper TypeScript typing throughout.

## Architecture

### Component Structure
```
BirdeyeWalletGainers/
├── BirdeyeWalletGainers.tsx          # Main component
├── BirdeyeWalletGainers.module.css   # Component-specific styles
├── hooks/
│   └── useBirdeyeAPI.ts              # Custom hook for API integration
├── types/
│   └── birdeye.types.ts              # TypeScript type definitions
├── services/
│   └── birdeyeAPI.ts                 # API service layer
└── components/
    ├── TimePeriodSelector.tsx        # Time period selection component
    ├── WalletGainersTable.tsx        # Data display table component
    ├── LoadingSpinner.tsx            # Loading state component
    └── ErrorMessage.tsx              # Error display component
```

### Data Flow
1. User selects time period → State update triggers API call
2. API service makes request to Birdeye → Returns typed response
3. Custom hook manages loading/error states → Updates component state
4. Component renders data through child components → UI updates

## Components and Interfaces

### Main Component: BirdeyeWalletGainers
**Props Interface:**
```typescript
interface BirdeyeWalletGainersProps {
  className?: string;
  refreshInterval?: number; // Optional auto-refresh in milliseconds
  onWalletClick?: (walletAddress: string) => void; // Optional callback
}
```

**State Management:**
- `selectedTimePeriod`: Current time period selection
- `walletData`: Array of wallet gainer data
- `loading`: Boolean loading state
- `error`: Error message string or null
- `lastRefresh`: Timestamp of last data fetch

### TimePeriodSelector Component
**Props Interface:**
```typescript
interface TimePeriodSelectorProps {
  selectedPeriod: TimePeriod;
  onPeriodChange: (period: TimePeriod) => void;
  disabled?: boolean;
}

type TimePeriod = 'today' | 'yesterday' | '1W';
```

### WalletGainersTable Component
**Props Interface:**
```typescript
interface WalletGainersTableProps {
  data: WalletGainer[];
  loading: boolean;
  onWalletClick?: (walletAddress: string) => void;
}
```

### Custom Hook: useBirdeyeAPI
**Return Interface:**
```typescript
interface UseBirdeyeAPIReturn {
  data: WalletGainer[] | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  lastFetch: Date | null;
}
```

## Data Models

### API Response Types
```typescript
interface BirdeyeAPIResponse {
  success: boolean;
  data: {
    items: WalletGainer[];
    total: number;
  };
}

interface WalletGainer {
  wallet: string;
  pnl: number;
  pnl_percentage: number;
  total_buy: number;
  total_sell: number;
  total_volume: number;
  win_rate: number;
  total_trades: number;
  last_trade_time: number;
}
```

### UI Display Types
```typescript
interface DisplayWalletGainer {
  walletAddress: string;
  shortAddress: string; // Truncated for display
  pnl: string; // Formatted currency
  pnlPercentage: string; // Formatted percentage
  totalVolume: string; // Formatted currency
  winRate: string; // Formatted percentage
  totalTrades: number;
  lastTradeTime: string; // Formatted date
}
```

## Error Handling

### API Error Categories
1. **Network Errors**: Connection failures, timeouts
2. **HTTP Errors**: 400 (Bad Request), 401 (Unauthorized), 403 (Forbidden), 429 (Rate Limited), 500 (Server Error)
3. **Data Errors**: Invalid response format, missing required fields

### Error Handling Strategy
- **Retry Logic**: Automatic retry for network errors and 5xx errors (max 3 attempts)
- **Rate Limiting**: Exponential backoff for 429 errors
- **User Feedback**: Clear error messages with actionable guidance
- **Fallback States**: Graceful degradation when data is unavailable

### Error Message Mapping
```typescript
const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Unable to connect to Birdeye API. Please check your connection.',
  RATE_LIMITED: 'Too many requests. Please wait a moment before refreshing.',
  UNAUTHORIZED: 'API access denied. Please check your configuration.',
  SERVER_ERROR: 'Birdeye API is temporarily unavailable. Please try again later.',
  INVALID_DATA: 'Received invalid data from API. Please refresh to try again.'
};
```

## Testing Strategy

### Unit Tests
- **API Service**: Mock HTTP requests, test error handling, validate request parameters
- **Custom Hook**: Test state transitions, loading states, error conditions
- **Components**: Test rendering, user interactions, prop handling
- **Utilities**: Test data formatting, address truncation, date formatting

### Integration Tests
- **API Integration**: Test actual API calls with various parameters
- **Component Integration**: Test full user workflows (select period → fetch data → display)
- **Error Scenarios**: Test network failures, API errors, invalid responses

### Test Coverage Requirements
- Minimum 80% code coverage
- All error paths tested
- All user interactions tested
- API integration scenarios covered

### Testing Tools
- **Jest**: Unit testing framework
- **React Testing Library**: Component testing
- **MSW (Mock Service Worker)**: API mocking for tests
- **TypeScript**: Compile-time type checking

## Performance Considerations

### Optimization Strategies
- **Memoization**: Use React.memo for child components, useMemo for expensive calculations
- **Debouncing**: Prevent rapid API calls during time period changes
- **Caching**: Cache API responses for short periods to reduce redundant requests
- **Lazy Loading**: Code splitting for non-critical components

### API Rate Limiting
- Implement request throttling to respect Birdeye API limits
- Use exponential backoff for rate-limited requests
- Cache responses to minimize API calls

### Responsive Design
- Mobile-first CSS approach
- Flexible table layout that adapts to screen size
- Touch-friendly interactive elements
- Optimized for both desktop and mobile viewports


# checklist.md

