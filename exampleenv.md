









=============================================================================
# SOLANA MAINNET WALLET CONFIGURATION
# =============================================================================
# Your Solana wallet private key (base58 encoded) - KEEP THIS SECRET!
# ⚠️ FOR MAINNET DEVELOPMENT - USE DEDICATED WALLET WITH LIMITED FUNDS!
ENCRYPTED_PRIVATE_KEY=YOUR_ENCRYPTED_MAINNET_PRIVATE_KEY_HERE
WALLET_PASSWORD=your_secure_mainnet_wallet_password
TRADING_WALLET_ADDRESS=YOUR_MAINNET_WALLET_ADDRESS_HERE
SOLANA_RPC_URL=https://mainnet.helius-rpc.com/?api-key=YOUR_HELIUS_API_KEY
SOLANA_NETWORK=mainnet-beta

# Fee account for collecting referral fees (mainnet)
SOLANA_FEE_ACCOUNT=YOUR_MAINNET_FEE_COLLECTION_WALLET_HERE

# =============================================================================
# HELIUS API CONFIGURATION (MAINNET)
# =============================================================================
# Your Helius API key from https://helius.dev
HELIUS_API_KEY=YOUR_HELIUS_API_KEY_HERE

# Helius RPC URL (mainnet)
HELIUS_RPC_URL=https://mainnet.helius-rpc.com/?api-key=YOUR_HELIUS_API_KEY_HERE

# Helius WebSocket URL (for real-time updates)
HELIUS_WS_URL=wss://atlas-mainnet.helius-rpc.com/?api-key=YOUR_HELIUS_API_KEY_HERE

# Helius Webhook URL (your app's webhook endpoint)
HELIUS_WEBHOOK_URL=https://your-domain.com/api/webhooks/helius

# =============================================================================
# JUPITER API CONFIGURATION
# =============================================================================
# Jupiter v6 API endpoints (mainnet) - No API key required
JUPITER_API_URL=https://quote-api.jup.ag/v6
JUPITER_V6_ENDPOINT=https://quote-api.jup.ag/v6
JUP_ENDPOINT=https://quote-api.jup.ag/v6

# Jupiter Swap API (mainnet)
JUPITER_SWAP_ENDPOINT=https://quote-api.jup.ag/v6/swap

# Jupiter Price API (mainnet)
JUPITER_PRICE_API=https://price.jup.ag/v4/price

# Jupiter Token List API (mainnet)
JUPITER_TOKEN_LIST=https://token.jup.ag/all


# Telegram API Configuration (Replace with your credentials)
TELEGRAM_API_ID=YOUR_TELEGRAM_API_ID_HERE
TELEGRAM_API_HASH=YOUR_TELEGRAM_API_HASH_HERE
TELEGRAM_SESSION_STRING=YOUR_TELEGRAM_SESSION_STRING_HERE

# Alert Configuration
TELEGRAM_ALERTS_ENABLED=true
TELEGRAM_ALERTS_CHAT_ID=YOUR_TELEGRAM_CHAT_ID_HERE


# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
# Email settings for alerts
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="YOUR_APP_PASSWORD"
ALERT_EMAIL="<EMAIL>"

# =============================================================================
# ADDITIONAL MAINNET CONFIGURATION
# =============================================================================
# Database (use separate mainnet database)
DATABASE_URL="postgresql://user:password@localhost:5432/memetrader_mainnet"
REDIS_URL="redis://localhost:6379"

# JWT Secrets (generate strong secrets for mainnet)
JWT_SECRET="your-super-secret-jwt-key-at-least-32-characters-long-mainnet"
JWT_EXPIRES_IN="7d"
JWT_REFRESH_SECRET="your-super-secret-refresh-key-at-least-32-characters-long-mainnet"
JWT_REFRESH_EXPIRES_IN="30d"

# Trading Configuration (Conservative mainnet values)
MAX_SLIPPAGE_BPS="100"  # 1% max slippage
DEFAULT_PRIORITY_FEE="10000"  # 0.00001 SOL
RATE_LIMIT_MAX_REQUESTS="100"  # Conservative rate limiting
LOG_LEVEL="info"  # Production logging level

# Security
BCRYPT_ROUNDS="12"  # Higher security for mainnet
CORS_ORIGIN="http://localhost:3000"
NODE_ENV="development"
PORT="3001"
