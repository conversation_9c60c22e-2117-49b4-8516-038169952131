const path = require('path');

/** @type {import('next').NextConfig} */
const nextConfig = {
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  reactStrictMode: false, // Temporarily disabled to help debug infinite loops
  webpack: (config, { isServer }) => {
    // Add alias for shared workspace module
    config.resolve.alias = {
      ...config.resolve.alias,
      'shared': path.resolve(__dirname, '..', 'shared'),
    };
    
    // Configure module resolution for workspace dependencies
    config.resolve.modules = [
      ...(config.resolve.modules || []),
      path.resolve(__dirname, 'node_modules'),
      'node_modules',
    ];

    return config;
  },
};

module.exports = nextConfig;
