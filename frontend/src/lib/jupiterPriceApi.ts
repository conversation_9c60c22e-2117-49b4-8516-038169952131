/**
 * Jupiter Price API integration with multiple fallback endpoints
 */

interface JupiterPriceData {
  [address: string]: {
    id: string
    mintSymbol: string
    vsToken: string
    vsTokenSymbol: string
    price: number
  }
}

interface PriceApiResponse {
  data: JupiterPriceData
  timeTaken?: number
}

class JupiterPriceApi {
  private static instance: JupiterPriceApi
  private cache: Map<string, { data: JupiterPriceData; timestamp: number }> = new Map()
  private readonly cacheTimeout = 30000 // 30 seconds

  private readonly endpoints = [
    'https://api.jup.ag/price/v2',
    'https://quote-api.jup.ag/v6/price',
    'https://api.jup.ag/price/v1'
  ]

  public static getInstance(): JupiterPriceApi {
    if (!JupiterPriceApi.instance) {
      JupiterPriceApi.instance = new JupiterPriceApi()
    }
    return JupiterPriceApi.instance
  }

  /**
   * Get prices for multiple token addresses
   */
  public async getPrices(addresses: string[]): Promise<JupiterPriceData> {
    if (addresses.length === 0) {
      return {}
    }

    const cacheKey = addresses.sort().join(',')
    const cached = this.cache.get(cacheKey)
    
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      console.log('🎯 Using cached Jupiter prices')
      return cached.data
    }

    // Try each endpoint in order
    for (const endpoint of this.endpoints) {
      try {
        const url = `${endpoint}?ids=${addresses.join(',')}`
        console.log(`🔍 Trying Jupiter price API: ${endpoint}`)
        
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        })

        if (response.ok) {
          const data: PriceApiResponse = await response.json()
          console.log(`✅ Jupiter price API success: ${endpoint}`)
          
          // Cache the result
          this.cache.set(cacheKey, {
            data: data.data || {},
            timestamp: Date.now()
          })
          
          return data.data || {}
        } else {
          console.warn(`⚠️ Jupiter API ${endpoint} failed:`, response.status, response.statusText)
        }
      } catch (error) {
        console.warn(`⚠️ Jupiter API ${endpoint} error:`, error instanceof Error ? error.message : error)
      }
    }

    // If all endpoints fail, return empty data
    console.error('❌ All Jupiter price API endpoints failed')
    return {}
  }

  /**
   * Get price for a single token
   */
  public async getPrice(address: string): Promise<number | null> {
    const prices = await this.getPrices([address])
    const priceData = prices[address]
    return priceData?.price || null
  }

  /**
   * Get SOL price in USD
   */
  public async getSOLPrice(): Promise<number> {
    const solAddress = 'So11111111111111111111111111111111111111112'
    const price = await this.getPrice(solAddress)
    return price || 150 // Fallback to ~$150 if API fails
  }

  /**
   * Calculate USD value for token amount
   */
  public async calculateUSDValue(tokenAddress: string, amount: number): Promise<number> {
    const price = await this.getPrice(tokenAddress)
    if (!price) return 0
    return amount * price
  }

  /**
   * Clear cache (useful for testing or forced refresh)
   */
  public clearCache(): void {
    this.cache.clear()
  }
}

// Export singleton instance
export const jupiterPriceApi = JupiterPriceApi.getInstance()

// Export types
export type { JupiterPriceData, PriceApiResponse }