/**
 * Helius Priority Fee Estimation for Frontend
 * 
 * This module provides intelligent priority fee estimation using Helius APIs
 * for optimal transaction landing rates on Solana.
 */

export interface PriorityFeeEstimate {
  priorityFeeEstimate: number
  priorityFeeLevels?: {
    min: number
    low: number
    medium: number
    high: number
    veryHigh: number
    unsafeMax: number
  }
  networkMetrics?: {
    avgPriorityFee: number
    networkCongestion: 'low' | 'medium' | 'high' | 'extreme'
    recommendedLevel: 'low' | 'medium' | 'high' | 'veryHigh'
    confidence: number
  }
}

/**
 * Get priority fee estimate using Helius API
 */
export async function getPriorityFeeEstimate(params: {
  accountKeys: string[]
  priorityLevel?: 'Low' | 'Medium' | 'High' | 'VeryHigh'
  rpcUrl: string
}): Promise<PriorityFeeEstimate> {
  try {
    const response = await fetch(params.rpcUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: `priority-fee-${Date.now()}`,
        method: 'getPriorityFeeEstimate',
        params: [
          {
            accountKeys: params.accountKeys,
            options: {
              priorityLevel: params.priorityLevel || 'High',
              includeAllPriorityFeeLevels: true,
              recommended: true
            }
          }
        ]
      })
    })

    if (!response.ok) {
      throw new Error(`Priority fee request failed: ${response.status}`)
    }

    const data = await response.json()

    if (data.error) {
      throw new Error(`Priority fee API error: ${data.error.message}`)
    }

    // Analyze network conditions
    const avgPriorityFee = data.result.priorityFeeEstimate
    let networkCongestion: 'low' | 'medium' | 'high' | 'extreme' = 'medium'
    
    if (avgPriorityFee < 1000) {
      networkCongestion = 'low'
    } else if (avgPriorityFee < 10000) {
      networkCongestion = 'medium'
    } else if (avgPriorityFee < 100000) {
      networkCongestion = 'high'
    } else {
      networkCongestion = 'extreme'
    }

    const recommendedLevel = networkCongestion === 'extreme' ? 'veryHigh' :
                           networkCongestion === 'high' ? 'high' :
                           networkCongestion === 'low' ? 'low' : 'medium'

    return {
      priorityFeeEstimate: data.result.priorityFeeEstimate,
      priorityFeeLevels: data.result.priorityFeeLevels,
      networkMetrics: {
        avgPriorityFee,
        networkCongestion,
        recommendedLevel,
        confidence: 0.8
      }
    }

  } catch (error) {
    console.error('Failed to get priority fee estimate:', error)
    
    // Return fallback values
    return {
      priorityFeeEstimate: 5000,
      networkMetrics: {
        avgPriorityFee: 5000,
        networkCongestion: 'medium',
        recommendedLevel: 'medium',
        confidence: 0.5
      }
    }
  }
}

/**
 * Get smart priority fee based on trading context
 */
export async function getSmartPriorityFee(params: {
  accountKeys: string[]
  urgency: 'low' | 'medium' | 'high' | 'critical'
  maxFeeWillingness: number
  rpcUrl: string
}): Promise<{
  recommendedFee: number
  priorityLevel: 'Low' | 'Medium' | 'High' | 'VeryHigh'
  reasoning: string[]
  confidence: number
}> {
  try {
    // Get all priority levels
    const estimate = await getPriorityFeeEstimate({
      accountKeys: params.accountKeys,
      rpcUrl: params.rpcUrl
    })

    // Determine priority level based on urgency and network conditions
    let priorityLevel: 'Low' | 'Medium' | 'High' | 'VeryHigh' = 'Medium'
    let reasoning: string[] = []

    // Base level from urgency
    switch (params.urgency) {
      case 'critical':
        priorityLevel = 'VeryHigh'
        reasoning.push('Critical urgency requires highest priority')
        break
      case 'high':
        priorityLevel = 'High'
        reasoning.push('High urgency trading scenario')
        break
      case 'medium':
        priorityLevel = 'Medium'
        reasoning.push('Standard trading priority')
        break
      case 'low':
        priorityLevel = 'Low'
        reasoning.push('Low urgency allows for lower fees')
        break
    }

    // Adjust for network congestion
    if (estimate.networkMetrics?.networkCongestion === 'extreme') {
      priorityLevel = 'VeryHigh'
      reasoning.push('Extreme network congestion detected')
    } else if (estimate.networkMetrics?.networkCongestion === 'high') {
      if (priorityLevel === 'Low') priorityLevel = 'Medium'
      if (priorityLevel === 'Medium') priorityLevel = 'High'
      reasoning.push('High network congestion')
    }

    // Get recommended fee
    let recommendedFee = estimate.priorityFeeLevels?.[priorityLevel.toLowerCase() as keyof typeof estimate.priorityFeeLevels] || estimate.priorityFeeEstimate

    // Check against max willingness
    if (recommendedFee > params.maxFeeWillingness) {
      const levels = estimate.priorityFeeLevels
      if (levels) {
        if (params.maxFeeWillingness >= levels.high) {
          priorityLevel = 'High'
          recommendedFee = levels.high
          reasoning.push('Adjusted to High level due to fee constraints')
        } else if (params.maxFeeWillingness >= levels.medium) {
          priorityLevel = 'Medium'
          recommendedFee = levels.medium
          reasoning.push('Adjusted to Medium level due to fee constraints')
        } else if (params.maxFeeWillingness >= levels.low) {
          priorityLevel = 'Low'
          recommendedFee = levels.low
          reasoning.push('Adjusted to Low level due to fee constraints')
        }
      }
    }

    return {
      recommendedFee,
      priorityLevel,
      reasoning,
      confidence: estimate.networkMetrics?.confidence || 0.7
    }

  } catch (error) {
    console.error('Failed to get smart priority fee:', error)
    
    // Return safe defaults
    return {
      recommendedFee: 10000,
      priorityLevel: 'High',
      reasoning: ['Using default high priority due to estimation failure'],
      confidence: 0.5
    }
  }
}

/**
 * Get network congestion level
 */
export async function getNetworkCongestion(rpcUrl: string): Promise<{
  level: 'low' | 'medium' | 'high' | 'extreme'
  avgPriorityFee: number
  confidence: number
}> {
  try {
    const testAccountKeys = ['So11111111111111111111111111111111111111112'] // SOL mint
    const estimate = await getPriorityFeeEstimate({
      accountKeys: testAccountKeys,
      rpcUrl
    })

    return {
      level: estimate.networkMetrics?.networkCongestion || 'medium',
      avgPriorityFee: estimate.networkMetrics?.avgPriorityFee || 5000,
      confidence: estimate.networkMetrics?.confidence || 0.7
    }

  } catch (error) {
    console.error('Failed to get network congestion:', error)
    
    return {
      level: 'medium',
      avgPriorityFee: 5000,
      confidence: 0.5
    }
  }
}