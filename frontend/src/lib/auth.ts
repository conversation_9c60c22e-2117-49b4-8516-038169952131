/**
 * Authentication utilities for MemeTrader Pro
 */

const AUTH_TOKEN_KEY = 'auth_token'
const AUTH_USER_KEY = 'auth_user'

export interface AuthUser {
  id: string
  email: string
  role: string
  walletAddress: string
}

export interface AuthResponse {
  success: boolean
  data?: {
    token: string
    user: AuthUser
  }
  error?: string
}

/**
 * Get stored auth token from localStorage
 */
export function getStoredAuthToken(): string | null {
  if (typeof window === 'undefined') return null
  return localStorage.getItem(AUTH_TOKEN_KEY)
}

/**
 * Store auth token in localStorage
 */
export function storeAuthToken(token: string, user: AuthUser): void {
  if (typeof window === 'undefined') return
  localStorage.setItem(AUTH_TOKEN_KEY, token)
  localStorage.setItem(AUTH_USER_KEY, JSON.stringify(user))
}

/**
 * Clear stored auth data
 */
export function clearAuthData(): void {
  if (typeof window === 'undefined') return
  localStorage.removeItem(AUTH_TOKEN_KEY)
  localStorage.removeItem(AUTH_USER_KEY)
}

/**
 * Get stored user data
 */
export function getStoredUser(): AuthUser | null {
  if (typeof window === 'undefined') return null
  const userData = localStorage.getItem(AUTH_USER_KEY)
  if (!userData) return null
  
  try {
    return JSON.parse(userData)
  } catch {
    return null
  }
}

/**
 * Generate and store a new auth token
 */
export async function generateAuthToken(walletAddress?: string): Promise<string | null> {
  try {
    const response = await fetch('/api/auth/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ walletAddress })
    })

    if (!response.ok) {
      throw new Error('Failed to generate auth token')
    }

    const data: AuthResponse = await response.json()
    
    if (data.success && data.data) {
      storeAuthToken(data.data.token, data.data.user)
      return data.data.token
    }
    
    throw new Error(data.error || 'Failed to generate token')
  } catch (error) {
    console.error('Error generating auth token:', error)
    return null
  }
}

/**
 * Get a valid auth token (generate new one if needed)
 */
export async function getValidAuthToken(): Promise<string | null> {
  let token = getStoredAuthToken()
  
  if (!token) {
    // Generate new token
    token = await generateAuthToken()
  }
  
  return token
}

/**
 * Check if user is authenticated
 */
export function isAuthenticated(): boolean {
  return getStoredAuthToken() !== null
}