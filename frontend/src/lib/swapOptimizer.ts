/**
 * Swap optimization for major tokens (SOL, USDT, USDC)
 * Provides intelligent routing and parameter optimization
 */

interface Token {
  address: string
  symbol: string
  name: string
  decimals: number
  verified?: boolean
}

interface SwapConfig {
  slippageToleranceBps: number
  priorityFeeLamports: number
  mevProtectionLevel: 'Standard' | 'Enhanced' | 'Maximum'
  useDirectRoute: boolean
  maxAccounts: number
  dynamicSlippage: boolean
  restrictIntermediateTokens: boolean
}

interface SwapRoute {
  tokenIn: Token
  tokenOut: Token
  config: SwapConfig
  reasoning: string
}

export const MAJOR_TOKENS = {
  SOL: {
    address: 'So11111111111111111111111111111111111111112',
    symbol: 'SOL',
    name: '<PERSON><PERSON>',
    decimals: 9,
    verified: true
  },
  USDC: {
    address: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
    symbol: 'USDC',
    name: 'USD Coin',
    decimals: 6,
    verified: true
  },
  USDT: {
    address: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
    symbol: 'USDT',
    name: 'Tether USD',
    decimals: 6,
    verified: true
  }
} as const

// Optimized configurations for different swap types
const SWAP_CONFIGS = {
  // Any token to SOL - Most liquid pairs, minimal slippage needed
  TO_SOL: {
    slippageToleranceBps: 50, // 0.5%
    priorityFeeLamports: 5000, // Lower fees since SOL is highly liquid
    mevProtectionLevel: 'Standard' as const,
    useDirectRoute: true,
    maxAccounts: 32,
    dynamicSlippage: true,
    restrictIntermediateTokens: true
  },
  
  // SOL to any token - Usually good liquidity
  FROM_SOL: {
    slippageToleranceBps: 100, // 1.0%
    priorityFeeLamports: 8000, // Slightly higher for token purchases
    mevProtectionLevel: 'Enhanced' as const,
    useDirectRoute: true,
    maxAccounts: 48,
    dynamicSlippage: true,
    restrictIntermediateTokens: false
  },
  
  // Stablecoin swaps (USDT <-> USDC)
  STABLECOIN: {
    slippageToleranceBps: 25, // 0.25% - Very tight for stables
    priorityFeeLamports: 3000, // Lower fees for stable swaps
    mevProtectionLevel: 'Standard' as const,
    useDirectRoute: true,
    maxAccounts: 16,
    dynamicSlippage: false, // Fixed slippage for stables
    restrictIntermediateTokens: true
  },
  
  // Token to stablecoin
  TO_STABLECOIN: {
    slippageToleranceBps: 75, // 0.75%
    priorityFeeLamports: 6000,
    mevProtectionLevel: 'Enhanced' as const,
    useDirectRoute: false, // Allow routing through SOL if needed
    maxAccounts: 64,
    dynamicSlippage: true,
    restrictIntermediateTokens: false
  },
  
  // Stablecoin to token
  FROM_STABLECOIN: {
    slippageToleranceBps: 100, // 1.0%
    priorityFeeLamports: 8000,
    mevProtectionLevel: 'Enhanced' as const,
    useDirectRoute: false,
    maxAccounts: 64,
    dynamicSlippage: true,
    restrictIntermediateTokens: false
  },
  
  // Token to token (neither is SOL or stablecoin)
  TOKEN_TO_TOKEN: {
    slippageToleranceBps: 150, // 1.5% - Higher slippage for complex routes
    priorityFeeLamports: 12000,
    mevProtectionLevel: 'Maximum' as const,
    useDirectRoute: false, // Likely needs routing
    maxAccounts: 64,
    dynamicSlippage: true,
    restrictIntermediateTokens: false
  }
} as const

export class SwapOptimizer {
  /**
   * Get optimized swap configuration based on token pair
   */
  static getOptimizedConfig(tokenInAddress: string, tokenOutAddress: string): SwapRoute {
    const tokenIn = this.getTokenInfo(tokenInAddress)
    const tokenOut = this.getTokenInfo(tokenOutAddress)
    
    // Determine swap type and get appropriate config
    const swapType = this.determineSwapType(tokenIn, tokenOut)
    const config = SWAP_CONFIGS[swapType]
    const reasoning = this.getSwapReasoning(swapType, tokenIn, tokenOut)
    
    return {
      tokenIn,
      tokenOut,
      config,
      reasoning
    }
  }
  
  /**
   * Determine the type of swap based on token pair
   */
  private static determineSwapType(tokenIn: Token, tokenOut: Token): keyof typeof SWAP_CONFIGS {
    const isSOL = (token: Token) => token.address === MAJOR_TOKENS.SOL.address
    const isStablecoin = (token: Token) => 
      token.address === MAJOR_TOKENS.USDC.address || 
      token.address === MAJOR_TOKENS.USDT.address
    
    // SOL-related swaps
    if (isSOL(tokenOut)) return 'TO_SOL'
    if (isSOL(tokenIn)) return 'FROM_SOL'
    
    // Stablecoin swaps
    if (isStablecoin(tokenIn) && isStablecoin(tokenOut)) return 'STABLECOIN'
    if (isStablecoin(tokenOut)) return 'TO_STABLECOIN'
    if (isStablecoin(tokenIn)) return 'FROM_STABLECOIN'
    
    // Default to token-to-token
    return 'TOKEN_TO_TOKEN'
  }
  
  /**
   * Get token information
   */
  private static getTokenInfo(address: string): Token {
    // Check if it's a major token
    for (const token of Object.values(MAJOR_TOKENS)) {
      if (token.address === address) {
        return token
      }
    }
    
    // Return minimal info for unknown tokens
    return {
      address,
      symbol: 'UNKNOWN',
      name: 'Unknown Token',
      decimals: 9,
      verified: false
    }
  }
  
  /**
   * Get reasoning for swap configuration
   */
  private static getSwapReasoning(swapType: keyof typeof SWAP_CONFIGS, tokenIn: Token, tokenOut: Token): string {
    const reasoningMap = {
      TO_SOL: `Swapping ${tokenIn.symbol} to SOL - Using optimized settings for high liquidity SOL pairs with minimal slippage`,
      FROM_SOL: `Swapping SOL to ${tokenOut.symbol} - Enhanced MEV protection for token purchases from SOL`,
      STABLECOIN: `Stablecoin swap ${tokenIn.symbol} ↔ ${tokenOut.symbol} - Tight slippage for stable-to-stable trades`,
      TO_STABLECOIN: `Converting ${tokenIn.symbol} to ${tokenOut.symbol} - Optimized routing for stablecoin exits`,
      FROM_STABLECOIN: `Buying ${tokenOut.symbol} with ${tokenIn.symbol} - Enhanced protection for stablecoin entries`,
      TOKEN_TO_TOKEN: `Token-to-token swap ${tokenIn.symbol} → ${tokenOut.symbol} - Maximum protection for complex routing`
    }
    
    return reasoningMap[swapType]
  }
  
  /**
   * Check if a swap requires special handling
   */
  static isOptimizedPair(tokenInAddress: string, tokenOutAddress: string): boolean {
    const majorAddresses = Object.values(MAJOR_TOKENS).map(t => t.address)
    return majorAddresses.includes(tokenInAddress) || majorAddresses.includes(tokenOutAddress)
  }
  
  /**
   * Get recommended trade size limits based on token pair
   */
  static getTradeSizeLimits(tokenInAddress: string, tokenOutAddress: string): {
    minTradeUSD: number
    maxTradeUSD: number
    optimalTradeUSD: number
  } {
    const config = this.getOptimizedConfig(tokenInAddress, tokenOutAddress)
    
    // Base limits on swap type
    const limits = {
      TO_SOL: { minTradeUSD: 1, maxTradeUSD: 100000, optimalTradeUSD: 1000 },
      FROM_SOL: { minTradeUSD: 5, maxTradeUSD: 50000, optimalTradeUSD: 500 },
      STABLECOIN: { minTradeUSD: 1, maxTradeUSD: 500000, optimalTradeUSD: 5000 },
      TO_STABLECOIN: { minTradeUSD: 5, maxTradeUSD: 25000, optimalTradeUSD: 1000 },
      FROM_STABLECOIN: { minTradeUSD: 5, maxTradeUSD: 25000, optimalTradeUSD: 1000 },
      TOKEN_TO_TOKEN: { minTradeUSD: 10, maxTradeUSD: 5000, optimalTradeUSD: 250 }
    }
    
    const swapType = this.determineSwapType(config.tokenIn, config.tokenOut)
    return limits[swapType]
  }
  
  /**
   * Validate swap parameters and suggest improvements
   */
  static validateSwap(
    tokenInAddress: string, 
    tokenOutAddress: string, 
    amountIn: number,
    currentSlippage: number
  ): {
    isValid: boolean
    warnings: string[]
    suggestions: string[]
    optimizations: string[]
  } {
    const config = this.getOptimizedConfig(tokenInAddress, tokenOutAddress)
    const limits = this.getTradeSizeLimits(tokenInAddress, tokenOutAddress)
    
    const warnings: string[] = []
    const suggestions: string[] = []
    const optimizations: string[] = []
    
    // Check trade size
    const estimatedUSD = amountIn * 200 // Rough estimate assuming SOL price
    if (estimatedUSD < limits.minTradeUSD) {
      warnings.push(`Trade size too small. Minimum recommended: $${limits.minTradeUSD}`)
    }
    if (estimatedUSD > limits.maxTradeUSD) {
      warnings.push(`Large trade detected. Consider splitting into smaller trades.`)
    }
    
    // Check slippage settings
    const optimalSlippage = config.config.slippageToleranceBps / 100
    if (currentSlippage < optimalSlippage * 0.5) {
      suggestions.push(`Consider increasing slippage to ${optimalSlippage}% for better execution`)
    }
    if (currentSlippage > optimalSlippage * 2) {
      suggestions.push(`High slippage detected. You may get a better price with ${optimalSlippage}%`)
    }
    
    // Optimization suggestions
    if (config.config.useDirectRoute) {
      optimizations.push('Direct routing enabled for optimal execution')
    }
    if (config.config.mevProtectionLevel === 'Maximum') {
      optimizations.push('Maximum MEV protection active for this token pair')
    }
    
    return {
      isValid: warnings.length === 0,
      warnings,
      suggestions,
      optimizations
    }
  }
}