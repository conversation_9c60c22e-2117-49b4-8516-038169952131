import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatCurrency(value: number, currency = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 6,
  }).format(value)
}

export function formatNumber(value: number, decimals = 2): string {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value)
}

export function formatPercentage(value: number, decimals = 2): string {
  return `${value >= 0 ? '+' : ''}${formatNumber(value, decimals)}%`
}

export function formatAddress(address: string, chars = 4): string {
  return `${address.slice(0, chars)}...${address.slice(-chars)}`
}

export function formatTimeAgo(date: Date): string {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) {
    return `${diffInSeconds}s ago`
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60)
  if (diffInMinutes < 60) {
    return `${diffInMinutes}m ago`
  }

  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) {
    return `${diffInHours}h ago`
  }

  const diffInDays = Math.floor(diffInHours / 24)
  return `${diffInDays}d ago`
}

export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    
    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// Solana address validation utilities
export function validateSolanaAddress(address: string): boolean {
  if (!address || typeof address !== 'string') {
    return false
  }
  
  // Remove whitespace
  const cleanAddress = address.trim()
  
  // Solana addresses are base58 encoded and typically 32-44 characters
  const base58Regex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/
  
  return base58Regex.test(cleanAddress)
}

export function sanitizeTokenAddress(address: string): string {
  return address.trim().replace(/\s+/g, '')
}

// Token validation result types
export interface TokenValidationResult {
  isValid: boolean
  error?: string
  sanitizedAddress?: string
}

export function validateAndSanitizeTokenAddress(address: string): TokenValidationResult {
  if (!address) {
    return { isValid: false, error: 'Address is required' }
  }
  
  const sanitizedAddress = sanitizeTokenAddress(address)
  
  if (sanitizedAddress.length === 0) {
    return { isValid: false, error: 'Address cannot be empty' }
  }
  
  if (!validateSolanaAddress(sanitizedAddress)) {
    return { 
      isValid: false, 
      error: 'Invalid Solana address format. Must be 32-44 characters, base58 encoded.' 
    }
  }
  
  return { isValid: true, sanitizedAddress }
}

// Recent tokens localStorage utilities
const RECENT_TOKENS_STORAGE_KEY = 'memetrader-recent-tokens'
const MAX_RECENT_TOKENS = 8

export interface RecentToken {
  address: string
  symbol: string
  name: string
  logoURI?: string
  decimals: number
  verified?: boolean
  addedAt: number // timestamp
}

export function loadRecentTokensFromStorage(): RecentToken[] {
  if (typeof window === 'undefined') {
    return [] // SSR safety
  }
  
  try {
    const stored = localStorage.getItem(RECENT_TOKENS_STORAGE_KEY)
    if (!stored) {
      return []
    }
    
    const parsed = JSON.parse(stored)
    if (!Array.isArray(parsed)) {
      return []
    }
    
    // Validate each token and filter out invalid ones
    return parsed.filter((token: any) => {
      return token && 
             typeof token.address === 'string' && 
             typeof token.symbol === 'string' && 
             typeof token.name === 'string' &&
             typeof token.decimals === 'number' &&
             typeof token.addedAt === 'number'
    }).slice(0, MAX_RECENT_TOKENS) // Ensure we don't exceed max
    
  } catch (error) {
    console.warn('Failed to load recent tokens from localStorage:', error)
    return []
  }
}

export function saveRecentTokensToStorage(tokens: RecentToken[]): void {
  if (typeof window === 'undefined') {
    return // SSR safety
  }
  
  try {
    const tokensToSave = tokens.slice(0, MAX_RECENT_TOKENS) // Ensure max limit
    localStorage.setItem(RECENT_TOKENS_STORAGE_KEY, JSON.stringify(tokensToSave))
  } catch (error) {
    console.warn('Failed to save recent tokens to localStorage:', error)
  }
}

export function addToRecentTokens(newToken: Omit<RecentToken, 'addedAt'>, existingTokens: RecentToken[]): RecentToken[] {
  const tokenWithTimestamp: RecentToken = {
    ...newToken,
    addedAt: Date.now()
  }
  
  // Remove existing token with same address if it exists
  const filteredTokens = existingTokens.filter(token => token.address !== newToken.address)
  
  // Add new token to the beginning
  const updatedTokens = [tokenWithTimestamp, ...filteredTokens]
  
  // Limit to max recent tokens
  return updatedTokens.slice(0, MAX_RECENT_TOKENS)
}

// Custom popular tokens localStorage utilities
const CUSTOM_POPULAR_TOKENS_STORAGE_KEY = 'memetrader-custom-popular-tokens'
const MAX_CUSTOM_POPULAR_TOKENS = 12

export interface CustomPopularToken {
  address: string
  symbol: string
  name: string
  logoURI?: string
  decimals: number
  verified?: boolean
  isCustom: boolean
  addedAt: number
}

export function loadCustomPopularTokensFromStorage(): CustomPopularToken[] {
  if (typeof window === 'undefined') {
    return [] // SSR safety
  }
  
  try {
    const stored = localStorage.getItem(CUSTOM_POPULAR_TOKENS_STORAGE_KEY)
    if (!stored) {
      return []
    }
    
    const parsed = JSON.parse(stored)
    if (!Array.isArray(parsed)) {
      return []
    }
    
    // Validate each token and filter out invalid ones
    return parsed.filter((token: any) => {
      return token && 
             typeof token.address === 'string' && 
             typeof token.symbol === 'string' && 
             typeof token.name === 'string' &&
             typeof token.decimals === 'number' &&
             typeof token.addedAt === 'number' &&
             typeof token.isCustom === 'boolean'
    }).slice(0, MAX_CUSTOM_POPULAR_TOKENS) // Ensure we don't exceed max
    
  } catch (error) {
    console.warn('Failed to load custom popular tokens from localStorage:', error)
    return []
  }
}

export function saveCustomPopularTokensToStorage(tokens: CustomPopularToken[]): void {
  if (typeof window === 'undefined') {
    return // SSR safety
  }
  
  try {
    const tokensToSave = tokens.slice(0, MAX_CUSTOM_POPULAR_TOKENS) // Ensure max limit
    localStorage.setItem(CUSTOM_POPULAR_TOKENS_STORAGE_KEY, JSON.stringify(tokensToSave))
  } catch (error) {
    console.warn('Failed to save custom popular tokens to localStorage:', error)
  }
}

export function addToCustomPopularTokens(
  newToken: Omit<CustomPopularToken, 'isCustom' | 'addedAt'>, 
  existingTokens: CustomPopularToken[]
): CustomPopularToken[] {
  // Check if token already exists
  const existingToken = existingTokens.find(token => token.address === newToken.address)
  if (existingToken) {
    return existingTokens // Don't add duplicates
  }
  
  const tokenWithMetadata: CustomPopularToken = {
    ...newToken,
    isCustom: true,
    addedAt: Date.now()
  }
  
  // Add new token to the beginning
  const updatedTokens = [tokenWithMetadata, ...existingTokens]
  
  // Limit to max custom popular tokens
  return updatedTokens.slice(0, MAX_CUSTOM_POPULAR_TOKENS)
}

export function removeFromCustomPopularTokens(
  tokenAddress: string, 
  existingTokens: CustomPopularToken[]
): CustomPopularToken[] {
  return existingTokens.filter(token => token.address !== tokenAddress)
}

export function isTokenInCustomPopular(tokenAddress: string, customTokens: CustomPopularToken[]): boolean {
  return customTokens.some(token => token.address === tokenAddress)
}

// Custom token names localStorage utilities
const CUSTOM_TOKEN_NAMES_STORAGE_KEY = 'memetrader-custom-token-names'

export interface CustomTokenName {
  address: string
  customName: string
  originalSymbol: string
  addedAt: number
  updatedAt: number
}

export function loadCustomTokenNamesFromStorage(): Record<string, CustomTokenName> {
  if (typeof window === 'undefined') {
    return {} // SSR safety
  }
  
  try {
    const stored = localStorage.getItem(CUSTOM_TOKEN_NAMES_STORAGE_KEY)
    if (!stored) {
      return {}
    }
    
    const parsed = JSON.parse(stored)
    if (typeof parsed !== 'object' || parsed === null) {
      return {}
    }
    
    // Validate each entry
    const validated: Record<string, CustomTokenName> = {}
    for (const [address, tokenName] of Object.entries(parsed)) {
      if (
        tokenName && 
        typeof tokenName === 'object' &&
        typeof (tokenName as any).address === 'string' && 
        typeof (tokenName as any).customName === 'string' && 
        typeof (tokenName as any).originalSymbol === 'string' &&
        typeof (tokenName as any).addedAt === 'number' &&
        typeof (tokenName as any).updatedAt === 'number'
      ) {
        validated[address] = tokenName as CustomTokenName
      }
    }
    
    return validated
  } catch (error) {
    console.warn('Failed to load custom token names from localStorage:', error)
    return {}
  }
}

export function saveCustomTokenNamesToStorage(names: Record<string, CustomTokenName>): void {
  if (typeof window === 'undefined') {
    return // SSR safety
  }
  
  try {
    localStorage.setItem(CUSTOM_TOKEN_NAMES_STORAGE_KEY, JSON.stringify(names))
    // Dispatch custom event to notify other components
    window.dispatchEvent(new CustomEvent('customTokenNamesChanged'))
  } catch (error) {
    console.warn('Failed to save custom token names to localStorage:', error)
  }
}

export function setCustomTokenName(
  address: string, 
  customName: string, 
  originalSymbol: string,
  existingNames: Record<string, CustomTokenName>
): Record<string, CustomTokenName> {
  const now = Date.now()
  const existing = existingNames[address]
  
  return {
    ...existingNames,
    [address]: {
      address,
      customName: customName.trim(),
      originalSymbol,
      addedAt: existing?.addedAt || now,
      updatedAt: now
    }
  }
}

export function removeCustomTokenName(
  address: string,
  existingNames: Record<string, CustomTokenName>
): Record<string, CustomTokenName> {
  const { [address]: removed, ...rest } = existingNames
  return rest
}

export function getCustomTokenName(
  address: string,
  customNames: Record<string, CustomTokenName>
): CustomTokenName | undefined {
  return customNames[address]
}

// User token verification localStorage utilities
const USER_VERIFIED_TOKENS_STORAGE_KEY = 'memetrader-user-verified-tokens'

export interface UserVerifiedToken {
  address: string
  verifiedAt: number
  userNotes?: string
}

export function loadUserVerifiedTokensFromStorage(): Record<string, UserVerifiedToken> {
  if (typeof window === 'undefined') {
    return {} // SSR safety
  }
  
  try {
    const stored = localStorage.getItem(USER_VERIFIED_TOKENS_STORAGE_KEY)
    if (!stored) {
      return {}
    }
    
    const parsed = JSON.parse(stored)
    if (typeof parsed !== 'object' || parsed === null) {
      return {}
    }
    
    // Validate each entry
    const validated: Record<string, UserVerifiedToken> = {}
    for (const [address, verification] of Object.entries(parsed)) {
      if (
        verification && 
        typeof verification === 'object' &&
        typeof (verification as any).address === 'string' && 
        typeof (verification as any).verifiedAt === 'number'
      ) {
        validated[address] = verification as UserVerifiedToken
      }
    }
    
    return validated
  } catch (error) {
    console.warn('Failed to load user verified tokens from localStorage:', error)
    return {}
  }
}

export function saveUserVerifiedTokensToStorage(verifications: Record<string, UserVerifiedToken>): void {
  if (typeof window === 'undefined') {
    return // SSR safety
  }
  
  try {
    localStorage.setItem(USER_VERIFIED_TOKENS_STORAGE_KEY, JSON.stringify(verifications))
    // Dispatch custom event to notify other components
    window.dispatchEvent(new CustomEvent('userVerifiedTokensChanged'))
  } catch (error) {
    console.warn('Failed to save user verified tokens to localStorage:', error)
  }
}

export function addUserVerifiedToken(
  address: string, 
  userNotes: string = '',
  existingVerifications: Record<string, UserVerifiedToken>
): Record<string, UserVerifiedToken> {
  return {
    ...existingVerifications,
    [address]: {
      address,
      verifiedAt: Date.now(),
      userNotes: userNotes.trim() || undefined
    }
  }
}

export function removeUserVerifiedToken(
  address: string,
  existingVerifications: Record<string, UserVerifiedToken>
): Record<string, UserVerifiedToken> {
  const { [address]: removed, ...rest } = existingVerifications
  return rest
}

export function isTokenUserVerified(
  address: string,
  userVerifications: Record<string, UserVerifiedToken>
): boolean {
  return address in userVerifications
}

export function getUserTokenVerification(
  address: string,
  userVerifications: Record<string, UserVerifiedToken>
): UserVerifiedToken | undefined {
  return userVerifications[address]
}
