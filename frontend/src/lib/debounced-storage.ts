'use client'

import { debounce } from '@/lib/utils'

// Create debounced localStorage save functions
export const debouncedLocalStorage = {
  // Debounced save with 300ms delay
  set: debounce((key: string, value: string) => {
    try {
      localStorage.setItem(key, value)
    } catch (error) {
      console.warn('Failed to save to localStorage:', error)
    }
  }, 300),

  // Immediate get (no debouncing needed for reads)
  get: (key: string): string | null => {
    try {
      return localStorage.getItem(key)
    } catch (error) {
      console.warn('Failed to read from localStorage:', error)
      return null
    }
  },

  // Debounced remove with 300ms delay
  remove: debounce((key: string) => {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.warn('Failed to remove from localStorage:', error)
    }
  }, 300)
}

// Utility function to create debounced save functions for specific storage keys
export function createDebouncedSaver(key: string, delay: number = 300) {
  return debounce((data: any) => {
    try {
      localStorage.setItem(key, JSON.stringify(data))
    } catch (error) {
      console.warn(`Failed to save ${key} to localStorage:`, error)
    }
  }, delay)
}