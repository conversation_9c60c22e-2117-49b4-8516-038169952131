export interface Token {
  symbol: string
  name: string
  address: string
}

export interface Position {
  id: string
  token: Token
  amount: number
  entryPrice: number
  currentPrice: number
  entryValue: number
  currentValue: number
  pnlPercentage: number
  pnlDollar: number
  trailingStop: number
  exitMilestones: number[]
  completedExits: number
  timeOpened: Date
  moonBag: {
    percentage: number
    targetGain: number
    currentProgress: number
  }
  status: 'taking_profits' | 'approaching_target' | 'moon_bag' | 'trailing'
  progressToNextExit: {
    current: number
    next: number
  }
}


export interface ExposureData {
  currentExposure: number
  maximumLimit: number
  capitalUsage: number
  activePositions: number
  availableCapital: number
  positionsUnderLimit: number
}

export type PositionType = 'open' | 'all'
export type SortField = 'pnl' | 'value' | 'symbol' | 'entry' | 'progress'
export type SortDirection = 'asc' | 'desc'
export type StatusFilter = 'all' | 'taking_profits' | 'approaching_target' | 'moon_bag' | 'trailing'
export type PnlFilter = 'all' | 'profitable' | 'breakeven' | 'losing'
