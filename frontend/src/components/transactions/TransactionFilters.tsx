'use client'

import { useState, useCallback, useMemo } from 'react'
import { Search, Calendar, Filter, X, RefreshCw } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { TransactionFilters as ITransactionFilters, PresetType, TransactionType } from 'shared/src'

interface TransactionFiltersProps {
  filters?: ITransactionFilters
  onFiltersChange?: (filters: ITransactionFilters) => void
  onReset?: () => void
}

const defaultFilters: ITransactionFilters = {
  dateRange: { start: null, end: null },
  strategies: [],
  transactionTypes: [],
  tokenSearch: '',
  pnlRange: { min: null, max: null },
  profitableOnly: false,
  lossesOnly: false
}

const strategyOptions: { value: PresetType; label: string; color: string }[] = [
  { value: 'DEFAULT', label: 'Default', color: 'bg-gray-500/20 text-gray-400' },
  { value: 'VOL', label: 'Volatile', color: 'bg-purple-500/20 text-purple-400' },
  { value: 'DEAD', label: 'Dead', color: 'bg-red-500/20 text-red-400' },
  { value: 'NUN', label: 'New/Unknown', color: 'bg-yellow-500/20 text-yellow-400' },
  { value: 'P5', label: 'Premium', color: 'bg-green-500/20 text-green-400' }
]

const transactionTypeOptions: { value: TransactionType; label: string; color: string }[] = [
  { value: 'BUY', label: 'Buy', color: 'bg-blue-500/20 text-blue-400' },
  { value: 'SELL', label: 'Sell', color: 'bg-red-500/20 text-red-400' },
  { value: 'PARTIAL_SELL', label: 'Partial Sell', color: 'bg-orange-500/20 text-orange-400' },
  { value: 'STOP_LOSS', label: 'Stop Loss', color: 'bg-red-600/20 text-red-500' },
  { value: 'PROFIT_TARGET', label: 'Profit Target', color: 'bg-green-600/20 text-green-500' }
]

const quickFilterOptions = [
  { id: 'today', label: 'Today', action: 'dateRange' },
  { id: 'week', label: 'This Week', action: 'dateRange' },
  { id: 'month', label: 'This Month', action: 'dateRange' },
  { id: 'profitable', label: 'Profitable Only', action: 'profitableOnly' },
  { id: 'losses', label: 'Losses Only', action: 'lossesOnly' }
]

export function TransactionFilters({ 
  filters = defaultFilters,
  onFiltersChange,
  onReset
}: TransactionFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [localFilters, setLocalFilters] = useState<ITransactionFilters>(filters)

  const updateFilters = useCallback((updates: Partial<ITransactionFilters>) => {
    const newFilters = { ...localFilters, ...updates }
    setLocalFilters(newFilters)
    onFiltersChange?.(newFilters)
  }, [localFilters, onFiltersChange])

  const activeFilterCount = useMemo(() => {
    let count = 0
    if (localFilters.dateRange.start || localFilters.dateRange.end) count++
    if (localFilters.strategies.length > 0) count++
    if (localFilters.transactionTypes.length > 0) count++
    if (localFilters.tokenSearch.trim()) count++
    if (localFilters.pnlRange.min !== null || localFilters.pnlRange.max !== null) count++
    if (localFilters.profitableOnly) count++
    if (localFilters.lossesOnly) count++
    return count
  }, [localFilters])

  const handleQuickFilter = (filterId: string) => {
    const now = new Date()
    switch (filterId) {
      case 'today':
        const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate())
        updateFilters({
          dateRange: { start: startOfDay, end: now }
        })
        break
      case 'week':
        const startOfWeek = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay())
        updateFilters({
          dateRange: { start: startOfWeek, end: now }
        })
        break
      case 'month':
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
        updateFilters({
          dateRange: { start: startOfMonth, end: now }
        })
        break
      case 'profitable':
        updateFilters({
          profitableOnly: !localFilters.profitableOnly,
          lossesOnly: false
        })
        break
      case 'losses':
        updateFilters({
          lossesOnly: !localFilters.lossesOnly,
          profitableOnly: false
        })
        break
    }
  }

  const handleStrategyToggle = (strategy: PresetType) => {
    const newStrategies = localFilters.strategies.includes(strategy)
      ? localFilters.strategies.filter(s => s !== strategy)
      : [...localFilters.strategies, strategy]
    updateFilters({ strategies: newStrategies })
  }

  const handleTransactionTypeToggle = (type: TransactionType) => {
    const newTypes = localFilters.transactionTypes.includes(type)
      ? localFilters.transactionTypes.filter(t => t !== type)
      : [...localFilters.transactionTypes, type]
    updateFilters({ transactionTypes: newTypes })
  }

  const handleReset = () => {
    setLocalFilters(defaultFilters)
    onFiltersChange?.(defaultFilters)
    onReset?.()
  }

  const formatDate = (date: Date | null) => {
    return date ? date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: date.getFullYear() !== new Date().getFullYear() ? 'numeric' : undefined
    }) : ''
  }

  return (
    <div className="space-y-4">
      {/* Quick Filters and Search Row */}
      <div className="flex flex-wrap items-center gap-3">
        {/* Search Input */}
        <div className="relative flex-1 min-w-64">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <input
            type="text"
            placeholder="Search by token symbol..."
            value={localFilters.tokenSearch}
            onChange={(e) => updateFilters({ tokenSearch: e.target.value })}
            className="w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-foreground placeholder-muted-foreground focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Quick Filter Buttons */}
        <div className="flex items-center gap-2">
          {quickFilterOptions.map((option) => {
            const isActive = 
              (option.id === 'profitable' && localFilters.profitableOnly) ||
              (option.id === 'losses' && localFilters.lossesOnly) ||
              (option.action === 'dateRange' && (localFilters.dateRange.start || localFilters.dateRange.end))

            return (
              <button
                key={option.id}
                onClick={() => handleQuickFilter(option.id)}
                className={`px-3 py-1.5 text-sm rounded-lg border transition-colors ${
                  isActive
                    ? 'bg-blue-500/20 text-blue-400 border-blue-500/30'
                    : 'bg-gray-800 text-muted-foreground border-gray-600 hover:bg-gray-700'
                }`}
              >
                {option.label}
              </button>
            )
          })}
        </div>

        {/* Advanced Filters Toggle */}
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className={`flex items-center gap-2 px-3 py-1.5 text-sm rounded-lg border transition-colors ${
            isExpanded || activeFilterCount > 0
              ? 'bg-blue-500/20 text-blue-400 border-blue-500/30'
              : 'bg-gray-800 text-muted-foreground border-gray-600 hover:bg-gray-700'
          }`}
        >
          <Filter className="w-4 h-4" />
          Advanced
          {activeFilterCount > 0 && (
            <Badge className="bg-blue-500 text-white text-xs px-1.5 py-0.5">
              {activeFilterCount}
            </Badge>
          )}
        </button>

        {/* Reset Button */}
        {activeFilterCount > 0 && (
          <button
            onClick={handleReset}
            className="flex items-center gap-2 px-3 py-1.5 text-sm text-red-400 hover:text-red-300 border border-red-500/30 rounded-lg hover:bg-red-500/10 transition-colors"
          >
            <RefreshCw className="w-4 h-4" />
            Reset
          </button>
        )}
      </div>

      {/* Active Filters Display */}
      {activeFilterCount > 0 && (
        <div className="flex flex-wrap items-center gap-2">
          <span className="text-sm text-muted-foreground">Active filters:</span>
          
          {(localFilters.dateRange.start || localFilters.dateRange.end) && (
            <Badge className="bg-gray-700 text-gray-300 flex items-center gap-1">
              <Calendar className="w-3 h-3" />
              {formatDate(localFilters.dateRange.start)} - {formatDate(localFilters.dateRange.end)}
              <button 
                onClick={() => updateFilters({ dateRange: { start: null, end: null } })}
                className="ml-1 hover:text-red-400"
              >
                <X className="w-3 h-3" />
              </button>
            </Badge>
          )}

          {localFilters.strategies.map(strategy => {
            const option = strategyOptions.find(o => o.value === strategy)
            return (
              <Badge key={strategy} className={`${option?.color} flex items-center gap-1`}>
                {option?.label}
                <button 
                  onClick={() => handleStrategyToggle(strategy)}
                  className="ml-1 hover:text-red-400"
                >
                  <X className="w-3 h-3" />
                </button>
              </Badge>
            )
          })}

          {localFilters.transactionTypes.map(type => {
            const option = transactionTypeOptions.find(o => o.value === type)
            return (
              <Badge key={type} className={`${option?.color} flex items-center gap-1`}>
                {option?.label}
                <button 
                  onClick={() => handleTransactionTypeToggle(type)}
                  className="ml-1 hover:text-red-400"
                >
                  <X className="w-3 h-3" />
                </button>
              </Badge>
            )
          })}

          {localFilters.profitableOnly && (
            <Badge className="bg-green-500/20 text-green-400 flex items-center gap-1">
              Profitable Only
              <button 
                onClick={() => updateFilters({ profitableOnly: false })}
                className="ml-1 hover:text-red-400"
              >
                <X className="w-3 h-3" />
              </button>
            </Badge>
          )}

          {localFilters.lossesOnly && (
            <Badge className="bg-red-500/20 text-red-400 flex items-center gap-1">
              Losses Only
              <button 
                onClick={() => updateFilters({ lossesOnly: false })}
                className="ml-1 hover:text-red-400"
              >
                <X className="w-3 h-3" />
              </button>
            </Badge>
          )}
        </div>
      )}

      {/* Advanced Filters Panel */}
      {isExpanded && (
        <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-4 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Date Range */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">Date Range</label>
              <div className="grid grid-cols-2 gap-2">
                <input
                  type="date"
                  value={localFilters.dateRange.start?.toISOString().split('T')[0] || ''}
                  onChange={(e) => updateFilters({
                    dateRange: {
                      ...localFilters.dateRange,
                      start: e.target.value ? new Date(e.target.value) : null
                    }
                  })}
                  className="px-3 py-2 bg-gray-800 border border-gray-600 rounded text-sm text-foreground focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <input
                  type="date"
                  value={localFilters.dateRange.end?.toISOString().split('T')[0] || ''}
                  onChange={(e) => updateFilters({
                    dateRange: {
                      ...localFilters.dateRange,
                      end: e.target.value ? new Date(e.target.value) : null
                    }
                  })}
                  className="px-3 py-2 bg-gray-800 border border-gray-600 rounded text-sm text-foreground focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* P&L Range */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">P&L Range (SOL)</label>
              <div className="grid grid-cols-2 gap-2">
                <input
                  type="number"
                  step="0.001"
                  placeholder="Min"
                  value={localFilters.pnlRange.min || ''}
                  onChange={(e) => updateFilters({
                    pnlRange: {
                      ...localFilters.pnlRange,
                      min: e.target.value ? parseFloat(e.target.value) : null
                    }
                  })}
                  className="px-3 py-2 bg-gray-800 border border-gray-600 rounded text-sm text-foreground focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <input
                  type="number"
                  step="0.001"
                  placeholder="Max"
                  value={localFilters.pnlRange.max || ''}
                  onChange={(e) => updateFilters({
                    pnlRange: {
                      ...localFilters.pnlRange,
                      max: e.target.value ? parseFloat(e.target.value) : null
                    }
                  })}
                  className="px-3 py-2 bg-gray-800 border border-gray-600 rounded text-sm text-foreground focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Strategy Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">Trading Strategies</label>
            <div className="flex flex-wrap gap-2">
              {strategyOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => handleStrategyToggle(option.value)}
                  className={`px-3 py-1.5 text-sm rounded-lg border transition-colors ${
                    localFilters.strategies.includes(option.value)
                      ? option.color + ' border-current'
                      : 'bg-gray-800 text-muted-foreground border-gray-600 hover:bg-gray-700'
                  }`}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>

          {/* Transaction Type Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">Transaction Types</label>
            <div className="flex flex-wrap gap-2">
              {transactionTypeOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => handleTransactionTypeToggle(option.value)}
                  className={`px-3 py-1.5 text-sm rounded-lg border transition-colors ${
                    localFilters.transactionTypes.includes(option.value)
                      ? option.color + ' border-current'
                      : 'bg-gray-800 text-muted-foreground border-gray-600 hover:bg-gray-700'
                  }`}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}