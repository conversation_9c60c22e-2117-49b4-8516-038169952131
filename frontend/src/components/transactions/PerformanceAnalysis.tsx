'use client'

import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from '@/components/ui/Card'
import { Badge } from '@/components/ui/badge'
import { ChevronDown, ChevronRight, TrendingUp, TrendingDown, Target, Zap, DollarSign, Activity } from 'lucide-react'
import { StrategyAnalytics, PresetType } from 'shared/src/types/trading'

interface PerformanceAnalysisProps {
  strategyAnalytics?: StrategyAnalytics[]
  isLoading?: boolean
}

// Mock strategy analytics data
const mockStrategyAnalytics: StrategyAnalytics[] = [
  {
    strategyType: 'VOL',
    totalTrades: 42,
    winRate: 85.7,
    averagePnl: 29.45,
    totalPnl: 1237.89,
    bestTrade: 245.67,
    worstTrade: -87.34,
    averageHoldTime: 3.2,
    totalVolume: 18.5
  },
  {
    strategyType: 'P5',
    totalTrades: 38,
    winRate: 82.1,
    averagePnl: 31.78,
    totalPnl: 1127.64,
    bestTrade: 198.45,
    worstTrade: -92.18,
    averageHoldTime: 5.8,
    totalVolume: 22.3
  },
  {
    strategyType: 'DEFAULT',
    totalTrades: 54,
    winRate: 76.9,
    averagePnl: 18.23,
    totalPnl: 984.42,
    bestTrade: 156.89,
    worstTrade: -78.23,
    averageHoldTime: 4.1,
    totalVolume: 15.7
  },
  {
    strategyType: 'NUN',
    totalTrades: 28,
    winRate: 71.4,
    averagePnl: 22.67,
    totalPnl: 634.76,
    bestTrade: 234.56,
    worstTrade: -125.34,
    averageHoldTime: 6.5,
    totalVolume: 12.8
  },
  {
    strategyType: 'DEAD',
    totalTrades: 19,
    winRate: 63.2,
    averagePnl: 15.89,
    totalPnl: 301.91,
    bestTrade: 98.76,
    worstTrade: -67.45,
    averageHoldTime: 2.9,
    totalVolume: 8.2
  }
]

const getStrategyColor = (strategy: PresetType) => {
  switch (strategy) {
    case 'DEFAULT': return { bg: 'bg-gray-500/20', text: 'text-gray-400', border: 'border-gray-500/30' }
    case 'VOL': return { bg: 'bg-purple-500/20', text: 'text-purple-400', border: 'border-purple-500/30' }
    case 'DEAD': return { bg: 'bg-red-500/20', text: 'text-red-400', border: 'border-red-500/30' }
    case 'NUN': return { bg: 'bg-yellow-500/20', text: 'text-yellow-400', border: 'border-yellow-500/30' }
    case 'P5': return { bg: 'bg-green-500/20', text: 'text-green-400', border: 'border-green-500/30' }
  }
}

const getStrategyName = (strategy: PresetType) => {
  switch (strategy) {
    case 'DEFAULT': return 'Default Strategy'
    case 'VOL': return 'Volatile Markets'
    case 'DEAD': return 'Low Volume'
    case 'NUN': return 'New/Unknown Tokens'
    case 'P5': return 'Premium Tokens'
  }
}

const getPerformanceRecommendation = (analytics: StrategyAnalytics): string => {
  if (analytics.winRate > 80 && analytics.averagePnl > 25) {
    return 'Excellent performance - consider increasing position sizes'
  } else if (analytics.winRate > 75 && analytics.averagePnl > 20) {
    return 'Strong performance - maintain current approach'
  } else if (analytics.winRate > 65 && analytics.averagePnl > 15) {
    return 'Good performance - monitor for optimization opportunities'
  } else if (analytics.winRate < 60 || analytics.averagePnl < 10) {
    return 'Review strategy parameters - consider adjustments'
  } else {
    return 'Average performance - analyze recent trades for patterns'
  }
}

export function PerformanceAnalysis({ 
  strategyAnalytics = mockStrategyAnalytics,
  isLoading = false 
}: PerformanceAnalysisProps) {
  const [expandedRows, setExpandedRows] = useState<Set<PresetType>>(new Set())

  const toggleExpanded = (strategy: PresetType) => {
    setExpandedRows(prev => {
      const newSet = new Set(prev)
      if (newSet.has(strategy)) {
        newSet.delete(strategy)
      } else {
        newSet.add(strategy)
      }
      return newSet
    })
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Strategy Performance Analysis</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  // Sort strategies by total P&L descending
  const sortedAnalytics = [...strategyAnalytics].sort((a, b) => b.totalPnl - a.totalPnl)

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Target className="w-5 h-5" />
          Strategy Performance Analysis
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Header Row */}
          <div className="grid grid-cols-8 gap-4 px-4 py-2 bg-gray-800/30 rounded-lg text-sm font-medium text-muted-foreground">
            <div>Strategy</div>
            <div className="text-right">Trades</div>
            <div className="text-right">Win Rate</div>
            <div className="text-right">Avg P&L</div>
            <div className="text-right">Total P&L</div>
            <div className="text-right">Best Trade</div>
            <div className="text-right">Worst Trade</div>
            <div className="text-center">Details</div>
          </div>

          {/* Strategy Rows */}
          {sortedAnalytics.map((analytics, index) => {
            const colors = getStrategyColor(analytics.strategyType)
            const isExpanded = expandedRows.has(analytics.strategyType)
            const recommendation = getPerformanceRecommendation(analytics)

            return (
              <div key={analytics.strategyType} className="space-y-2">
                {/* Main Row */}
                <div className="grid grid-cols-8 gap-4 px-4 py-3 bg-gray-800/20 hover:bg-gray-800/40 rounded-lg transition-colors">
                  {/* Strategy Name */}
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-8 rounded ${colors.bg}`} />
                    <div>
                      <Badge className={`${colors.bg} ${colors.text} text-xs`}>
                        {analytics.strategyType}
                      </Badge>
                      <div className="text-xs text-muted-foreground mt-1">
                        {getStrategyName(analytics.strategyType)}
                      </div>
                    </div>
                  </div>

                  {/* Total Trades */}
                  <div className="text-right">
                    <div className="font-medium">{analytics.totalTrades}</div>
                    <div className="text-xs text-muted-foreground">trades</div>
                  </div>

                  {/* Win Rate */}
                  <div className="text-right">
                    <div className={`font-medium ${analytics.winRate >= 75 ? 'text-green-400' : analytics.winRate >= 65 ? 'text-yellow-400' : 'text-red-400'}`}>
                      {analytics.winRate.toFixed(1)}%
                    </div>
                    <div className="flex items-center justify-end text-xs">
                      {analytics.winRate >= 75 ? (
                        <TrendingUp className="w-3 h-3 text-green-400" />
                      ) : analytics.winRate >= 65 ? (
                        <Activity className="w-3 h-3 text-yellow-400" />
                      ) : (
                        <TrendingDown className="w-3 h-3 text-red-400" />
                      )}
                    </div>
                  </div>

                  {/* Average P&L */}
                  <div className="text-right">
                    <div className={`font-medium ${analytics.averagePnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                      {analytics.averagePnl >= 0 ? '+' : ''}{analytics.averagePnl.toFixed(2)} SOL
                    </div>
                    <div className="text-xs text-muted-foreground">per trade</div>
                  </div>

                  {/* Total P&L */}
                  <div className="text-right">
                    <div className={`font-medium text-lg ${analytics.totalPnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                      {analytics.totalPnl >= 0 ? '+' : ''}{analytics.totalPnl.toFixed(2)}
                    </div>
                    <div className="text-xs text-muted-foreground">SOL</div>
                  </div>

                  {/* Best Trade */}
                  <div className="text-right">
                    <div className="font-medium text-green-400">
                      +{analytics.bestTrade.toFixed(2)}
                    </div>
                    <div className="text-xs text-muted-foreground">SOL</div>
                  </div>

                  {/* Worst Trade */}
                  <div className="text-right">
                    <div className="font-medium text-red-400">
                      {analytics.worstTrade.toFixed(2)}
                    </div>
                    <div className="text-xs text-muted-foreground">SOL</div>
                  </div>

                  {/* Expand Button */}
                  <div className="flex justify-center">
                    <button
                      onClick={() => toggleExpanded(analytics.strategyType)}
                      className="p-1 hover:bg-gray-700 rounded transition-colors"
                    >
                      {isExpanded ? (
                        <ChevronDown className="w-4 h-4 text-muted-foreground" />
                      ) : (
                        <ChevronRight className="w-4 h-4 text-muted-foreground" />
                      )}
                    </button>
                  </div>
                </div>

                {/* Expanded Details */}
                {isExpanded && (
                  <div className="ml-8 p-4 bg-gray-800/30 rounded-lg border-l-4" style={{ borderColor: colors.text.replace('text-', '') }}>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Additional Metrics */}
                      <div className="space-y-4">
                        <h4 className="font-medium text-foreground">Additional Metrics</h4>
                        
                        <div className="grid grid-cols-2 gap-4">
                          <div className="flex items-center gap-3">
                            <Activity className="w-4 h-4 text-blue-400" />
                            <div>
                              <div className="text-sm text-muted-foreground">Avg Hold Time</div>
                              <div className="font-medium">{analytics.averageHoldTime.toFixed(1)}h</div>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-3">
                            <Zap className="w-4 h-4 text-purple-400" />
                            <div>
                              <div className="text-sm text-muted-foreground">Total Volume</div>
                              <div className="font-medium">{analytics.totalVolume.toFixed(1)} SOL</div>
                            </div>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div className="flex items-center gap-3">
                            <DollarSign className="w-4 h-4 text-green-400" />
                            <div>
                              <div className="text-sm text-muted-foreground">Profit Factor</div>
                              <div className="font-medium">
                                {(Math.abs(analytics.totalPnl) / Math.abs(analytics.worstTrade * (analytics.totalTrades - Math.round(analytics.totalTrades * analytics.winRate / 100)))).toFixed(2)}
                              </div>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-3">
                            <Target className="w-4 h-4 text-orange-400" />
                            <div>
                              <div className="text-sm text-muted-foreground">Risk/Reward</div>
                              <div className="font-medium">
                                {(analytics.bestTrade / Math.abs(analytics.worstTrade)).toFixed(2)}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Recommendations */}
                      <div className="space-y-4">
                        <h4 className="font-medium text-foreground">Optimization Recommendations</h4>
                        
                        <div className="p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                          <div className="text-sm text-blue-400 mb-2">
                            Strategy Assessment
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {recommendation}
                          </div>
                        </div>

                        {/* Performance Indicators */}
                        <div className="space-y-2">
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-muted-foreground">Consistency</span>
                            <div className="flex items-center gap-1">
                              <div className={`w-16 bg-gray-700 rounded-full h-2`}>
                                <div 
                                  className={`h-2 rounded-full ${analytics.winRate >= 75 ? 'bg-green-400' : analytics.winRate >= 65 ? 'bg-yellow-400' : 'bg-red-400'}`}
                                  style={{ width: `${Math.min(100, analytics.winRate)}%` }}
                                />
                              </div>
                              <span className="text-xs text-muted-foreground ml-2">
                                {analytics.winRate >= 75 ? 'High' : analytics.winRate >= 65 ? 'Medium' : 'Low'}
                              </span>
                            </div>
                          </div>
                          
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-muted-foreground">Profitability</span>
                            <div className="flex items-center gap-1">
                              <div className={`w-16 bg-gray-700 rounded-full h-2`}>
                                <div 
                                  className={`h-2 rounded-full ${analytics.averagePnl >= 25 ? 'bg-green-400' : analytics.averagePnl >= 15 ? 'bg-yellow-400' : 'bg-red-400'}`}
                                  style={{ width: `${Math.min(100, (analytics.averagePnl / 50) * 100)}%` }}
                                />
                              </div>
                              <span className="text-xs text-muted-foreground ml-2">
                                {analytics.averagePnl >= 25 ? 'High' : analytics.averagePnl >= 15 ? 'Medium' : 'Low'}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )
          })}

          {/* Summary */}
          <div className="mt-6 p-4 bg-gradient-to-r from-blue-500/10 to-green-500/10 border border-blue-500/20 rounded-lg">
            <h4 className="font-medium text-foreground mb-2">Overall Performance Summary</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-lg font-bold text-green-400">
                  {sortedAnalytics.reduce((sum, s) => sum + s.totalTrades, 0)}
                </div>
                <div className="text-xs text-muted-foreground">Total Trades</div>
              </div>
              <div>
                <div className="text-lg font-bold text-blue-400">
                  {(sortedAnalytics.reduce((sum, s) => sum + (s.winRate * s.totalTrades), 0) / sortedAnalytics.reduce((sum, s) => sum + s.totalTrades, 0)).toFixed(1)}%
                </div>
                <div className="text-xs text-muted-foreground">Overall Win Rate</div>
              </div>
              <div>
                <div className="text-lg font-bold text-green-400">
                  +{sortedAnalytics.reduce((sum, s) => sum + s.totalPnl, 0).toFixed(2)}
                </div>
                <div className="text-xs text-muted-foreground">Total P&L (SOL)</div>
              </div>
              <div>
                <div className="text-lg font-bold text-purple-400">
                  {sortedAnalytics.reduce((sum, s) => sum + s.totalVolume, 0).toFixed(1)}
                </div>
                <div className="text-xs text-muted-foreground">Total Volume (SOL)</div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}