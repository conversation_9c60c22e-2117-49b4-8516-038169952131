'use client'

import { useState, useMemo, useCallback } from 'react'
import { FixedSizeList as List } from 'react-window'
import { Card, CardContent } from '@/components/ui/Card'
import { Badge } from '@/components/ui/badge'
import { Copy, ExternalLink, ChevronLeft, ChevronRight } from 'lucide-react'
import { Transaction, TransactionType, PresetType } from 'shared/src'

interface TransactionListProps {
  transactions?: Transaction[]
  isLoading?: boolean
  onRowSelect?: (transactionIds: string[]) => void
}

// Mock transaction data for development
const mockTransactions: Transaction[] = Array.from({ length: 250 }, (_, i) => ({
  id: `tx-${i + 1}`,
  userId: 'user-1',
  positionId: i % 10 === 0 ? undefined : `pos-${Math.floor(i / 3) + 1}`,
  hash: `${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`,
  type: (['BUY', 'SELL', 'PARTIAL_SELL'] as TransactionType[])[Math.floor(Math.random() * 3)],
  tokenIn: i % 2 === 0 ? 'SOL' : ['PEPE', 'BONK', 'DOGE', 'SHIB'][Math.floor(Math.random() * 4)],
  tokenOut: i % 2 === 0 ? ['PEPE', 'BONK', 'DOGE', 'SHIB'][Math.floor(Math.random() * 4)] : 'SOL',
  amountIn: Math.random() * 1000 + 50,
  amountOut: Math.random() * 50000 + 1000,
  price: Math.random() * 0.001 + 0.00001,
  fees: {
    jupiterFee: Math.random() * 0.01 + 0.001,
    priorityFee: Math.random() * 0.05 + 0.01,
    networkFee: 0.000005,
    total: Math.random() * 0.06 + 0.011005
  },
  strategyId: Math.random() > 0.3 ? `strategy-${Math.floor(Math.random() * 5) + 1}` : undefined,
  presetUsed: (['DEFAULT', 'VOL', 'DEAD', 'NUN', 'P5'] as PresetType[])[Math.floor(Math.random() * 5)],
  mevProtected: Math.random() > 0.5,
  timestamp: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000) // Last 30 days
}))

interface TransactionRowProps {
  index: number
  style: React.CSSProperties
  data: {
    transactions: Transaction[]
    selectedIds: Set<string>
    onToggleSelect: (id: string) => void
  }
}

const TransactionRow = ({ index, style, data }: TransactionRowProps) => {
  const transaction = data.transactions[index]
  const isSelected = data.selectedIds.has(transaction.id)
  
  const copyToClipboard = useCallback((text: string) => {
    navigator.clipboard.writeText(text)
    // TODO: Add toast notification
  }, [])

  const truncateHash = (hash: string) => {
    return `${hash.slice(0, 8)}...${hash.slice(-8)}`
  }

  const formatAmount = (amount: number) => {
    if (amount >= 1000000) {
      return `${(amount / 1000000).toFixed(2)}M`
    } else if (amount >= 1000) {
      return `${(amount / 1000).toFixed(2)}K`
    }
    return amount.toFixed(2)
  }

  const calculatePnL = (transaction: Transaction) => {
    // Mock P&L calculation - in real app this would be more complex
    const isBuy = transaction.type === 'BUY'
    const baseAmount = isBuy ? transaction.amountIn : transaction.amountOut
    const mockPnL = (Math.random() - 0.5) * baseAmount * 0.3
    return mockPnL
  }

  const pnl = calculatePnL(transaction)
  const pnlUsd = pnl * 200 // Mock SOL price

  const getTypeColor = (type: TransactionType) => {
    switch (type) {
      case 'BUY': return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
      case 'SELL': return 'bg-red-500/20 text-red-400 border-red-500/30'
      case 'PARTIAL_SELL': return 'bg-orange-500/20 text-orange-400 border-orange-500/30'
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getStrategyColor = (strategy: PresetType) => {
    switch (strategy) {
      case 'DEFAULT': return 'bg-gray-500/20 text-gray-400'
      case 'VOL': return 'bg-purple-500/20 text-purple-400'
      case 'DEAD': return 'bg-red-500/20 text-red-400'
      case 'NUN': return 'bg-yellow-500/20 text-yellow-400'
      case 'P5': return 'bg-green-500/20 text-green-400'
    }
  }

  return (
    <div style={style} className="px-6">
      <div 
        className={`flex items-center justify-between p-4 border-b border-gray-800 hover:bg-gray-800/30 transition-colors cursor-pointer ${
          isSelected ? 'bg-blue-500/10 border-blue-500/30' : ''
        }`}
        onClick={() => data.onToggleSelect(transaction.id)}
      >
        <div className="flex items-center gap-4 flex-1">
          {/* Checkbox */}
          <input
            type="checkbox"
            checked={isSelected}
            onChange={() => data.onToggleSelect(transaction.id)}
            className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2"
          />
          
          {/* Date */}
          <div className="w-24 text-sm text-muted-foreground">
            {transaction.timestamp.toLocaleDateString('en-US', { 
              month: 'short', 
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}
          </div>
          
          {/* Token Pair */}
          <div className="w-32 text-sm font-medium">
            {transaction.tokenIn}/{transaction.tokenOut}
          </div>
          
          {/* Type */}
          <Badge className={`w-20 justify-center ${getTypeColor(transaction.type)}`}>
            {transaction.type}
          </Badge>
          
          {/* Amount */}
          <div className="w-24 text-sm text-right">
            {formatAmount(transaction.amountIn)}
          </div>
          
          {/* Price */}
          <div className="w-24 text-sm text-right">
            ${transaction.price.toFixed(6)}
          </div>
          
          {/* Fees */}
          <div className="w-20 text-sm text-right text-muted-foreground">
            {transaction.fees.total.toFixed(4)} SOL
          </div>
          
          {/* Strategy */}
          <Badge className={`w-16 justify-center text-xs ${getStrategyColor(transaction.presetUsed)}`}>
            {transaction.presetUsed}
          </Badge>
          
          {/* P&L */}
          <div className="w-24 text-sm text-right">
            <div className={pnl >= 0 ? 'text-green-400' : 'text-red-400'}>
              {pnl >= 0 ? '+' : ''}{pnl.toFixed(3)} SOL
            </div>
            <div className={`text-xs ${pnl >= 0 ? 'text-green-400/70' : 'text-red-400/70'}`}>
              ${pnlUsd.toFixed(2)}
            </div>
          </div>
          
          {/* Status */}
          <div className="w-16">
            {transaction.mevProtected && (
              <Badge className="bg-green-500/20 text-green-400 text-xs">MEV</Badge>
            )}
          </div>
        </div>
        
        {/* Hash with actions */}
        <div className="flex items-center gap-2 ml-4">
          <code className="text-xs text-muted-foreground bg-gray-800 px-2 py-1 rounded">
            {truncateHash(transaction.hash)}
          </code>
          <button
            onClick={(e) => {
              e.stopPropagation()
              copyToClipboard(transaction.hash)
            }}
            className="p-1 hover:bg-gray-700 rounded transition-colors"
            title="Copy hash"
          >
            <Copy className="w-3 h-3 text-muted-foreground" />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation()
              window.open(`https://solscan.io/tx/${transaction.hash}`, '_blank')
            }}
            className="p-1 hover:bg-gray-700 rounded transition-colors"
            title="View on Solscan"
          >
            <ExternalLink className="w-3 h-3 text-muted-foreground" />
          </button>
        </div>
      </div>
    </div>
  )
}

export function TransactionList({ 
  transactions = mockTransactions,
  isLoading = false,
  onRowSelect 
}: TransactionListProps) {
  const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set())
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 50

  const paginatedTransactions = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage
    return transactions.slice(startIndex, startIndex + itemsPerPage)
  }, [transactions, currentPage, itemsPerPage])

  const totalPages = Math.ceil(transactions.length / itemsPerPage)

  const handleToggleSelect = useCallback((id: string) => {
    setSelectedIds(prev => {
      const newSet = new Set(prev)
      if (newSet.has(id)) {
        newSet.delete(id)
      } else {
        newSet.add(id)
      }
      onRowSelect?.(Array.from(newSet))
      return newSet
    })
  }, [onRowSelect])

  const handleSelectAll = useCallback(() => {
    if (selectedIds.size === paginatedTransactions.length) {
      setSelectedIds(new Set())
      onRowSelect?.([])
    } else {
      const allIds = new Set(paginatedTransactions.map(t => t.id))
      setSelectedIds(allIds)
      onRowSelect?.(Array.from(allIds))
    }
  }, [selectedIds.size, paginatedTransactions, onRowSelect])

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          {Array.from({ length: 10 }).map((_, i) => (
            <div key={i} className="h-16 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    )
  }

  if (transactions.length === 0) {
    return (
      <div className="p-12 text-center">
        <div className="text-muted-foreground text-lg mb-2">No transactions found</div>
        <div className="text-sm text-muted-foreground">
          Try adjusting your filters or check back later for new transactions.
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between px-6 py-4 bg-gray-800/30 border-b border-gray-800">
        <div className="flex items-center gap-4">
          <input
            type="checkbox"
            checked={selectedIds.size === paginatedTransactions.length && paginatedTransactions.length > 0}
            onChange={handleSelectAll}
            className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2"
          />
          <span className="text-sm text-muted-foreground">
            {selectedIds.size > 0 ? `${selectedIds.size} selected` : `${transactions.length} transactions`}
          </span>
        </div>
        
        <div className="flex items-center gap-6 text-sm text-muted-foreground">
          <div className="w-24">Date</div>
          <div className="w-32">Token Pair</div>
          <div className="w-20">Type</div>
          <div className="w-24 text-right">Amount</div>
          <div className="w-24 text-right">Price</div>
          <div className="w-20 text-right">Fees</div>
          <div className="w-16">Strategy</div>
          <div className="w-24 text-right">P&L</div>
          <div className="w-16">Status</div>
          <div className="w-24">Hash</div>
        </div>
      </div>

      {/* Virtual List */}
      <div className="h-96">
        <List
          height={384}
          itemCount={paginatedTransactions.length}
          itemSize={80}
          itemData={{
            transactions: paginatedTransactions,
            selectedIds,
            onToggleSelect: handleToggleSelect
          }}
        >
          {TransactionRow}
        </List>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between px-6 py-4 border-t border-gray-800">
        <div className="text-sm text-muted-foreground">
          Showing {(currentPage - 1) * itemsPerPage + 1} to {Math.min(currentPage * itemsPerPage, transactions.length)} of {transactions.length} transactions
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
            className="flex items-center gap-1 px-3 py-1 text-sm border border-gray-600 rounded hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <ChevronLeft className="w-4 h-4" />
            Previous
          </button>
          
          <span className="text-sm text-muted-foreground px-4">
            Page {currentPage} of {totalPages}
          </span>
          
          <button
            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
            disabled={currentPage === totalPages}
            className="flex items-center gap-1 px-3 py-1 text-sm border border-gray-600 rounded hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Next
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  )
}