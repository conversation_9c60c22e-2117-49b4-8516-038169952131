'use client'

import { useMemo } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { LineChart, Line, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts'
import { TrendingUp, TrendingDown, Activity, DollarSign, Zap, Target } from 'lucide-react'
import { PerformanceMetrics, DailyPnlData, StrategyPerformanceData, WinLossDistribution } from 'shared/src'

interface AnalyticsDashboardProps {
  metrics?: PerformanceMetrics
  dailyPnlData?: DailyPnlData[]
  strategyPerformanceData?: StrategyPerformanceData[]
  winLossData?: WinLossDistribution[]
  isLoading?: boolean
}

// Mock data for development
const mockMetrics: PerformanceMetrics = {
  totalTrades: 156,
  successfulTrades: 118,
  winRate: 75.6,
  totalPnlSol: 12.45,
  totalPnlUsd: 2485.60,
  totalVolume: 45.2,
  profitFactor: 2.34,
  sharpeRatio: 1.85,
  maxDrawdown: -8.2,
  averageHoldTime: 4.7,
  averageDailyReturn: 3.2,
  standardDeviation: 12.8
}

const mockDailyPnlData: DailyPnlData[] = [
  { date: '2025-07-24', value: 156.23, cumulativePnl: 1856.23, trades: 8 },
  { date: '2025-07-25', value: 289.45, cumulativePnl: 2145.68, trades: 12 },
  { date: '2025-07-26', value: -45.67, cumulativePnl: 2100.01, trades: 6 },
  { date: '2025-07-27', value: 378.90, cumulativePnl: 2478.91, trades: 15 },
  { date: '2025-07-28', value: 198.76, cumulativePnl: 2677.67, trades: 9 },
  { date: '2025-07-29', value: 425.12, cumulativePnl: 3102.79, trades: 18 },
  { date: '2025-07-30', value: 247.83, cumulativePnl: 3350.62, trades: 11 }
]

const mockStrategyData: StrategyPerformanceData[] = [
  { strategyId: '1', strategyName: 'Conservative', pnl: 890.23, trades: 45, winRate: 78.2 },
  { strategyId: '2', strategyName: 'Aggressive TP', pnl: 9234.56, trades: 38, winRate: 82.1 },
  { strategyId: '3', strategyName: 'Trailing Only', pnl: -4567.89, trades: 22, winRate: 68.5 },
  { strategyId: '4', strategyName: 'Moon Bag', pnl: -7345.67, trades: 18, winRate: 72.2 },
  { strategyId: '5', strategyName: 'Quick Scalp', pnl: 17456.78, trades: 33, winRate: 85.3 },
  { strategyId: '6', strategyName: 'Aggressive', pnl: 11789.45, trades: 28, winRate: 75.0 }
]

const mockWinLossData: WinLossDistribution[] = [
  { name: 'Wins', value: 118, percentage: 75.6, color: '#22c55e' },
  { name: 'Losses', value: 38, percentage: 24.4, color: '#ef4444' }
]

export function AnalyticsDashboard({ 
  metrics,
  dailyPnlData = mockDailyPnlData,
  strategyPerformanceData = mockStrategyData,
  winLossData = mockWinLossData,
  isLoading = false 
}: AnalyticsDashboardProps) {
  // Use mock data if metrics is null or loading
  const displayMetrics = metrics || mockMetrics

  // Helper function to determine bar color based on P&L value
  const getBarColor = (pnl: number): string => {
    return pnl >= 0 ? '#22c55e' : '#ef4444' // Green for positive, red for negative
  }
  const MetricCard = ({ 
    title, 
    value, 
    change, 
    icon: Icon, 
    trend, 
    suffix = '' 
  }: {
    title: string
    value: string | number
    change?: number
    icon: React.ElementType
    trend?: 'up' | 'down'
    suffix?: string
  }) => (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-muted-foreground mb-1">{title}</p>
            <p className="text-2xl font-bold text-foreground">
              {typeof value === 'number' ? value.toLocaleString() : value}{suffix}
            </p>
            {change !== undefined && (
              <div className={`flex items-center gap-1 mt-2 text-sm ${trend === 'up' ? 'text-green-400' : trend === 'down' ? 'text-red-400' : 'text-muted-foreground'}`}>
                {trend === 'up' ? <TrendingUp className="w-3 h-3" /> : trend === 'down' ? <TrendingDown className="w-3 h-3" /> : null}
                {change > 0 ? '+' : ''}{change}%
              </div>
            )}
          </div>
          <Icon className="w-8 h-8 text-muted-foreground" />
        </div>
      </CardContent>
    </Card>
  )

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4 mb-8">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        <MetricCard
          title="Total Trades"
          value={displayMetrics.totalTrades}
          icon={Activity}
        />
        
        <MetricCard
          title="Success Rate"
          value={displayMetrics.winRate.toFixed(1)}
          suffix="%"
          change={5.3}
          trend="up"
          icon={Target}
        />
        
        <MetricCard
          title="Total P&L (SOL)"
          value={displayMetrics.totalPnlSol.toFixed(2)}
          suffix=" SOL"
          change={2.1}
          trend="up"
          icon={Zap}
        />
        
        <MetricCard
          title="Total P&L (USD)"
          value={`$${displayMetrics.totalPnlUsd.toFixed(2)}`}
          change={2.1}
          trend="up"
          icon={DollarSign}
        />
        
        <MetricCard
          title="Total Volume"
          value={`${displayMetrics.totalVolume.toFixed(1)} SOL`}
          icon={Activity}
        />
        
        <MetricCard
          title="Profit Factor"
          value={displayMetrics.profitFactor.toFixed(2)}
          change={0.15}
          trend="up"
          icon={TrendingUp}
        />
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        {/* P&L Over Time Chart */}
        <Card>
          <CardHeader>
            <CardTitle>P&L Over Time</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={dailyPnlData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                  <XAxis 
                    dataKey="date" 
                    stroke="#9CA3AF"
                    fontSize={12}
                    tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                  />
                  <YAxis stroke="#9CA3AF" fontSize={12} />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: '#1F2937', 
                      border: '1px solid #374151',
                      borderRadius: '8px'
                    }}
                    formatter={(value: number, name: string) => [
                      `$${value.toFixed(2)}`, 
                      name === 'value' ? 'Daily P&L' : name === 'cumulativePnl' ? 'Cumulative P&L' : name
                    ]}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="value" 
                    stroke="#22c55e" 
                    strokeWidth={2}
                    dot={{ fill: '#22c55e', strokeWidth: 2, r: 4 }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="cumulativePnl" 
                    stroke="#06b6d4" 
                    strokeWidth={2}
                    strokeDasharray="5 5"
                    dot={{ fill: '#06b6d4', strokeWidth: 2, r: 4 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Strategy Performance Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Performance by Strategy</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={strategyPerformanceData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                  <XAxis dataKey="strategyName" stroke="#9CA3AF" fontSize={12} />
                  <YAxis stroke="#9CA3AF" fontSize={12} />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: '#1F2937', 
                      border: '1px solid #374151',
                      borderRadius: '8px'
                    }}
                    formatter={(value: number, name: string) => [
                      name === 'pnl' ? `$${value.toFixed(2)}` : 
                      name === 'winRate' ? `${value.toFixed(1)}%` : 
                      value,
                      name === 'pnl' ? 'P&L' :
                      name === 'winRate' ? 'Win Rate' :
                      name === 'trades' ? 'Trades' : name
                    ]}
                  />
                  <Bar dataKey="pnl" radius={[4, 4, 0, 0]}>
                    {strategyPerformanceData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={getBarColor(entry.pnl)} />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Advanced Metrics and Win/Loss Distribution */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        {/* Advanced Metrics */}
        <Card>
          <CardHeader>
            <CardTitle>Advanced Analytics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-4">
                <div>
                  <div className="text-sm text-muted-foreground">Sharpe Ratio</div>
                  <div className="text-2xl font-bold text-green-400">{displayMetrics.sharpeRatio.toFixed(2)}</div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">Max Drawdown</div>
                  <div className="text-2xl font-bold text-red-400">{displayMetrics.maxDrawdown.toFixed(1)}%</div>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <div className="text-sm text-muted-foreground">Avg Hold Time</div>
                  <div className="text-2xl font-bold text-foreground">{displayMetrics.averageHoldTime.toFixed(1)}h</div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">Daily Return</div>
                  <div className="text-2xl font-bold text-green-400">{displayMetrics.averageDailyReturn.toFixed(1)}%</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Win/Loss Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Win/Loss Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={winLossData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={100}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {winLossData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: '#1F2937', 
                      border: '1px solid #374151',
                      borderRadius: '8px'
                    }}
                    formatter={(value: number, name: string, props: any) => [
                      `${value} trades (${props.payload.percentage}%)`,
                      props.payload.name
                    ]}
                  />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}