'use client'

import { useState, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Download } from 'lucide-react'
import { AnalyticsDashboard } from './AnalyticsDashboard'
import { TransactionList } from './TransactionList'
import { TransactionFilters } from './TransactionFilters'
import { PerformanceAnalysis } from './PerformanceAnalysis'
import { useTransactionStore } from '@/stores/transactionStore'
import { useAnalyticsStore } from '@/stores/analyticsStore'
import { 
  Transaction, 
  PerformanceMetrics, 
  StrategyAnalytics, 
  DailyPnlData, 
  StrategyPerformanceData, 
  WinLossDistribution,
  TransactionFilters as ITransactionFilters
} from 'shared/src'

interface TransactionIntelligenceCenterProps {
  transactions?: Transaction[]
  performanceMetrics?: PerformanceMetrics
  strategyAnalytics?: StrategyAnalytics[]
  dailyPnlData?: DailyPnlData[]
  strategyPerformanceData?: StrategyPerformanceData[]
  winLossData?: WinLossDistribution[]
  isLoading?: boolean
}

export function TransactionIntelligenceCenter({
  transactions: propTransactions,
  performanceMetrics: propPerformanceMetrics,
  strategyAnalytics: propStrategyAnalytics,
  dailyPnlData: propDailyPnlData,
  strategyPerformanceData: propStrategyPerformanceData,
  winLossData: propWinLossData,
  isLoading: propIsLoading
}: TransactionIntelligenceCenterProps = {}) {
  const [isExporting, setIsExporting] = useState(false)

  // Use store data if props are not provided
  const { 
    filteredTransactions, 
    filters, 
    setFilters, 
    clearFilters, 
    exportTransactions,
    setSelectedTransactions,
    selectedTransactionIds
  } = useTransactionStore()

  const { 
    performanceMetrics, 
    strategyAnalytics, 
    dailyPnlData, 
    strategyPerformanceData, 
    winLossData,
    isCalculating
  } = useAnalyticsStore()

  // Use props or store data
  const transactions = propTransactions || filteredTransactions
  const metrics = propPerformanceMetrics || performanceMetrics
  const analytics = propStrategyAnalytics || strategyAnalytics
  const dailyData = propDailyPnlData || dailyPnlData
  const strategyData = propStrategyPerformanceData || strategyPerformanceData
  const winLossDistribution = propWinLossData || winLossData
  const isLoading = propIsLoading || isCalculating

  const handleExport = useCallback(async () => {
    setIsExporting(true)
    try {
      await exportTransactions('csv')
    } catch (error) {
      console.error('Export failed:', error)
    } finally {
      setIsExporting(false)
    }
  }, [exportTransactions])

  const handleFiltersChange = useCallback((newFilters: Partial<ITransactionFilters>) => {
    setFilters(newFilters)
  }, [setFilters])

  const handleFiltersReset = useCallback(() => {
    clearFilters()
  }, [clearFilters])

  const handleRowSelect = useCallback((selectedIds: string[]) => {
    setSelectedTransactions(selectedIds)
  }, [setSelectedTransactions])

  return (
    <div className="space-y-8 p-6">
      {/* Header */}
      <div className="section-container">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold text-foreground mb-2">Transaction Intelligence Center</h1>
            <p className="text-lg text-muted-foreground">
              Comprehensive analysis and insights for all trading activities
            </p>
          </div>
        
          <button
            onClick={handleExport}
            disabled={isExporting}
            className="flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg transition-colors"
          >
            <Download className="w-4 h-4" />
            {isExporting ? 'Exporting...' : 'Export CSV'}
          </button>
        </div>
      </div>

      {/* Analytics Dashboard */}
      <AnalyticsDashboard 
        metrics={metrics}
        dailyPnlData={dailyData}
        strategyPerformanceData={strategyData}
        winLossData={winLossDistribution}
        isLoading={isLoading}
      />

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filter Transactions</CardTitle>
        </CardHeader>
        <CardContent>
          <TransactionFilters 
            filters={filters}
            onFiltersChange={handleFiltersChange}
            onReset={handleFiltersReset}
          />
        </CardContent>
      </Card>

      {/* Performance Analysis */}
      <PerformanceAnalysis 
        strategyAnalytics={analytics}
        isLoading={isLoading}
      />

      {/* Transaction List */}
      <Card>
        <CardHeader>
          <CardTitle>Transaction History</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <TransactionList 
            transactions={transactions}
            isLoading={isLoading}
            onRowSelect={handleRowSelect}
          />
        </CardContent>
      </Card>
    </div>
  )
}