'use client'

import { useEffect, useState } from 'react'
import { getValidAuthToken, isAuthenticated } from '@/lib/auth'

interface AuthProviderProps {
  children: React.ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [isInitialized, setIsInitialized] = useState(false)

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Check if already authenticated
        if (isAuthenticated()) {
          console.log('✅ User already authenticated')
          setIsInitialized(true)
          return
        }

        // Generate auth token
        console.log('🔑 Generating auth token...')
        const token = await getValidAuthToken()
        
        if (token) {
          console.log('✅ Auth token generated successfully')
        } else {
          console.warn('⚠️ Failed to generate auth token')
        }
      } catch (error) {
        console.error('❌ Auth initialization error:', error)
      } finally {
        setIsInitialized(true)
      }
    }

    initializeAuth()
  }, [])

  // Don't render children until auth is initialized
  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-black">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto mb-4"></div>
          <p className="text-gray-400">Initializing...</p>
        </div>
      </div>
    )
  }

  return <>{children}</>
}