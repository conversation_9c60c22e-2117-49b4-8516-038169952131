'use client'

import { useWallet } from '@solana/wallet-adapter-react'
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui'
import { Card } from '@/components/ui/Card'

export function WalletConnectionStatus() {
  const { connected, publicKey } = useWallet()

  return (
    <Card className="p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${connected ? 'bg-profit' : 'bg-muted'}`} />
            <span className="font-medium">
              {connected ? 'Wallet Connected' : 'Wallet Disconnected'}
            </span>
          </div>
          {connected && publicKey && (
            <div className="text-sm text-muted-foreground">
              {publicKey.toString().slice(0, 8)}...{publicKey.toString().slice(-8)}
            </div>
          )}
        </div>
        <WalletMultiButton className="!bg-primary !text-primary-foreground hover:!bg-primary/90" />
      </div>
    </Card>
  )
}
