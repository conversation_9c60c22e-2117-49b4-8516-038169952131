'use client'

import { useSettingsStore } from '@/stores/settingsStore'
import { RiskLevel } from 'shared/src/types/enums'
import { TakeProfitConfig, DiversificationRule } from 'shared/src/types/settings'
import { 
  TrendingUp, 
  ShieldCheck, 
  Target, 
  Percent,
  Activity,
  PieChart,
  Plus,
  Trash2,
  Alert<PERSON><PERSON>gle,
  Rocket,
  Lock,
  LockOpen
} from 'lucide-react'
import { cn } from '@/lib/utils'

export function PortfolioSettingsPanel() {
  const { settings, updateSettings, isSettingLocked, toggleSettingLock } = useSettingsStore()
  const portfolioSettings = settings.portfolio
  
  const LockButton = ({ category, settingKey }: { category: string; settingKey: string }) => {
    const locked = isSettingLocked(category, settingKey)
    const IconComponent = locked ? Lock : LockOpen
    
    return (
      <button
        onClick={() => toggleSettingLock(category, settingKey)}
        className={cn(
          "p-1 rounded transition-colors",
          locked 
            ? "text-red-500 hover:bg-red-500/10" 
            : "text-muted-foreground hover:bg-muted"
        )}
        title={locked ? "Unlock setting" : "Lock setting"}
      >
        <IconComponent className="w-4 h-4" />
      </button>
    )
  }
  
  const handleTakeProfitUpdate = (updates: Partial<TakeProfitConfig>) => {
    updateSettings('portfolio', {
      takeProfitDefaults: {
        ...portfolioSettings.takeProfitDefaults,
        ...updates
      }
    })
  }
  
  const handleAddTarget = () => {
    const { targets, exitPercentages } = portfolioSettings.takeProfitDefaults
    if (targets.length < 10) {
      handleTakeProfitUpdate({
        targets: [...targets, targets[targets.length - 1] + 50],
        exitPercentages: [...exitPercentages, 10]
      })
    }
  }
  
  const handleRemoveTarget = (index: number) => {
    const { targets, exitPercentages } = portfolioSettings.takeProfitDefaults
    if (targets.length > 1) {
      handleTakeProfitUpdate({
        targets: targets.filter((_, i) => i !== index),
        exitPercentages: exitPercentages.filter((_, i) => i !== index)
      })
    }
  }
  
  const handleDiversificationUpdate = (index: number, updates: Partial<DiversificationRule>) => {
    const newRules = [...portfolioSettings.diversificationRules]
    newRules[index] = { ...newRules[index], ...updates }
    updateSettings('portfolio', { diversificationRules: newRules })
  }
  
  const totalExitPercentage = portfolioSettings.takeProfitDefaults.exitPercentages.reduce((sum, pct) => sum + pct, 0) + 
    portfolioSettings.takeProfitDefaults.moonBagPercentage
  
  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-xl font-semibold text-foreground flex items-center gap-2">
          <TrendingUp className="w-6 h-6 text-blue-500" />
          Portfolio Settings
        </h3>
        <p className="text-sm text-muted-foreground mt-1">
          Configure risk management and portfolio allocation strategies
        </p>
      </div>
      
      {/* Risk Tolerance */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <label className="block text-sm font-medium text-foreground">
              Risk Tolerance Level
            </label>
            <div className="flex items-center gap-2">
              <ShieldCheck className="w-5 h-5 text-blue-500" />
              <LockButton category="portfolio" settingKey="riskTolerance" />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
            {Object.values(RiskLevel).map((level) => (
              <button
                key={level}
                onClick={() => updateSettings('portfolio', { riskTolerance: level })}
                disabled={isSettingLocked('portfolio', 'riskTolerance')}
                className={cn(
                  "px-4 py-3 rounded-lg border transition-all text-sm font-medium",
                  isSettingLocked('portfolio', 'riskTolerance')
                    ? "cursor-not-allowed opacity-50"
                    : portfolioSettings.riskTolerance === level
                      ? "bg-primary/10 border-primary text-primary"
                      : "bg-card-interactive border-border text-muted-foreground hover:text-foreground hover:border-primary/50"
                )}
              >
                <div className="capitalize">{level.toLowerCase()}</div>
                <div className="text-xs mt-1 font-normal">
                  {level === RiskLevel.LOW && "Conservative approach"}
                  {level === RiskLevel.MODERATE && "Balanced strategy"}
                  {level === RiskLevel.HIGH && "Aggressive growth"}
                  {level === RiskLevel.VERY_HIGH && "Maximum returns"}
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>
      
      {/* Position Sizing */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
            <Target className="w-4 h-4 text-green-500" />
            Position Sizing & Limits
          </h4>
          
          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm text-foreground">
                  Default Position Size (% of portfolio)
                </label>
                <LockButton category="portfolio" settingKey="defaultPositionSize" />
              </div>
              <div className="flex items-center gap-4">
                <input
                  type="range"
                  min="1"
                  max="100"
                  value={portfolioSettings.defaultPositionSize}
                  onChange={(e) => updateSettings('portfolio', { 
                    defaultPositionSize: parseInt(e.target.value) 
                  })}
                  disabled={isSettingLocked('portfolio', 'defaultPositionSize')}
                  className={cn(
                    "flex-1 h-2 bg-card-interactive rounded-lg appearance-none",
                    isSettingLocked('portfolio', 'defaultPositionSize') 
                      ? "cursor-not-allowed opacity-50" 
                      : "cursor-pointer"
                  )}
                />
                <div className="flex items-center gap-1 min-w-[80px]">
                  <input
                    type="number"
                    min="1"
                    max="100"
                    value={portfolioSettings.defaultPositionSize}
                    onChange={(e) => updateSettings('portfolio', { 
                      defaultPositionSize: Math.min(100, Math.max(1, parseInt(e.target.value) || 1))
                    })}
                    disabled={isSettingLocked('portfolio', 'defaultPositionSize')}
                    className={cn(
                      "w-16 px-2 py-1 bg-card-interactive border border-border rounded text-sm text-right",
                      isSettingLocked('portfolio', 'defaultPositionSize') && "opacity-50 cursor-not-allowed"
                    )}
                  />
                  <Percent className="w-4 h-4 text-muted-foreground" />
                </div>
              </div>
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm text-foreground">
                  Maximum Portfolio Exposure (%)
                </label>
                <LockButton category="portfolio" settingKey="maxExposure" />
              </div>
              <div className="flex items-center gap-4">
                <input
                  type="range"
                  min="10"
                  max="100"
                  value={portfolioSettings.maxExposure}
                  onChange={(e) => updateSettings('portfolio', { 
                    maxExposure: parseInt(e.target.value) 
                  })}
                  disabled={isSettingLocked('portfolio', 'maxExposure')}
                  className={cn(
                    "flex-1 h-2 bg-card-interactive rounded-lg appearance-none",
                    isSettingLocked('portfolio', 'maxExposure') 
                      ? "cursor-not-allowed opacity-50" 
                      : "cursor-pointer"
                  )}
                />
                <div className="flex items-center gap-1 min-w-[80px]">
                  <input
                    type="number"
                    min="10"
                    max="100"
                    value={portfolioSettings.maxExposure}
                    onChange={(e) => updateSettings('portfolio', { 
                      maxExposure: Math.min(100, Math.max(10, parseInt(e.target.value) || 10))
                    })}
                    disabled={isSettingLocked('portfolio', 'maxExposure')}
                    className={cn(
                      "w-16 px-2 py-1 bg-card-interactive border border-border rounded text-sm text-right",
                      isSettingLocked('portfolio', 'maxExposure') && "opacity-50 cursor-not-allowed"
                    )}
                  />
                  <Percent className="w-4 h-4 text-muted-foreground" />
                </div>
              </div>
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm text-foreground">
                  Default Stop Loss (%)
                </label>
                <LockButton category="portfolio" settingKey="stopLossDefault" />
              </div>
              <div className="flex items-center gap-4">
                <input
                  type="range"
                  min="1"
                  max="50"
                  value={portfolioSettings.stopLossDefault}
                  onChange={(e) => updateSettings('portfolio', { 
                    stopLossDefault: parseInt(e.target.value) 
                  })}
                  disabled={isSettingLocked('portfolio', 'stopLossDefault')}
                  className={cn(
                    "flex-1 h-2 bg-card-interactive rounded-lg appearance-none",
                    isSettingLocked('portfolio', 'stopLossDefault') 
                      ? "cursor-not-allowed opacity-50" 
                      : "cursor-pointer"
                  )}
                />
                <div className="flex items-center gap-1 min-w-[80px]">
                  <input
                    type="number"
                    min="1"
                    max="50"
                    value={portfolioSettings.stopLossDefault}
                    onChange={(e) => updateSettings('portfolio', { 
                      stopLossDefault: Math.min(50, Math.max(1, parseInt(e.target.value) || 1))
                    })}
                    disabled={isSettingLocked('portfolio', 'stopLossDefault')}
                    className={cn(
                      "w-16 px-2 py-1 bg-card-interactive border border-border rounded text-sm text-right",
                      isSettingLocked('portfolio', 'stopLossDefault') && "opacity-50 cursor-not-allowed"
                    )}
                  />
                  <Percent className="w-4 h-4 text-muted-foreground" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Take Profit Configuration */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
              <Rocket className="w-4 h-4 text-purple-500" />
              Take Profit Defaults
            </h4>
            <button
              onClick={handleAddTarget}
              disabled={portfolioSettings.takeProfitDefaults.targets.length >= 10}
              className={cn(
                "flex items-center gap-1 px-3 py-1 rounded-md text-sm transition-colors",
                portfolioSettings.takeProfitDefaults.targets.length >= 10
                  ? "bg-muted text-muted-foreground cursor-not-allowed"
                  : "bg-primary/10 text-primary hover:bg-primary/20"
              )}
            >
              <Plus className="w-4 h-4" />
              Add Target
            </button>
          </div>
          
          <div className="space-y-3">
            {portfolioSettings.takeProfitDefaults.targets.map((target, index) => (
              <div key={index} className="flex items-center gap-3">
                <div className="flex items-center gap-1 flex-1">
                  <span className="text-sm text-muted-foreground min-w-[60px]">
                    Target {index + 1}:
                  </span>
                  <input
                    type="number"
                    min="1"
                    max="10000"
                    value={target}
                    onChange={(e) => {
                      const newTargets = [...portfolioSettings.takeProfitDefaults.targets]
                      newTargets[index] = Math.max(1, parseInt(e.target.value) || 1)
                      handleTakeProfitUpdate({ targets: newTargets })
                    }}
                    className="w-20 px-2 py-1.5 bg-card-interactive border border-border rounded text-sm text-right"
                  />
                  <Percent className="w-4 h-4 text-muted-foreground" />
                </div>
                
                <div className="flex items-center gap-1">
                  <span className="text-sm text-muted-foreground">Exit:</span>
                  <input
                    type="number"
                    min="1"
                    max="100"
                    value={portfolioSettings.takeProfitDefaults.exitPercentages[index]}
                    onChange={(e) => {
                      const newExits = [...portfolioSettings.takeProfitDefaults.exitPercentages]
                      newExits[index] = Math.min(100, Math.max(1, parseInt(e.target.value) || 1))
                      handleTakeProfitUpdate({ exitPercentages: newExits })
                    }}
                    className="w-16 px-2 py-1.5 bg-card-interactive border border-border rounded text-sm text-right"
                  />
                  <Percent className="w-4 h-4 text-muted-foreground" />
                </div>
                
                <button
                  onClick={() => handleRemoveTarget(index)}
                  disabled={portfolioSettings.takeProfitDefaults.targets.length <= 1}
                  className={cn(
                    "p-1.5 rounded transition-colors",
                    portfolioSettings.takeProfitDefaults.targets.length <= 1
                      ? "text-muted-foreground cursor-not-allowed"
                      : "text-red-500 hover:bg-red-500/10"
                  )}
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            ))}
            
            <div className="flex items-center gap-3 pt-3 border-t border-border">
              <div className="flex items-center gap-1 flex-1">
                <span className="text-sm text-muted-foreground min-w-[60px]">
                  Moon Bag:
                </span>
                <input
                  type="number"
                  min="0"
                  max="50"
                  value={portfolioSettings.takeProfitDefaults.moonBagPercentage}
                  onChange={(e) => handleTakeProfitUpdate({ 
                    moonBagPercentage: Math.min(50, Math.max(0, parseInt(e.target.value) || 0))
                  })}
                  className="w-16 px-2 py-1.5 bg-card-interactive border border-border rounded text-sm text-right"
                />
                <Percent className="w-4 h-4 text-muted-foreground" />
              </div>
              
              {totalExitPercentage > 100 && (
                <p className="flex items-center gap-1 text-xs text-red-500">
                  <AlertTriangle className="w-3 h-3" />
                  Total exceeds 100%
                </p>
              )}
            </div>
          </div>
          
          <p className="text-xs text-muted-foreground">
            Configure default take profit targets and exit percentages for new positions
          </p>
        </div>
      </div>
      
      {/* Diversification Rules */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
            <PieChart className="w-4 h-4 text-orange-500" />
            Diversification Rules
          </h4>
          
          <div className="space-y-3">
            {portfolioSettings.diversificationRules.map((rule, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-card-interactive rounded-lg">
                <div className="flex items-center gap-3">
                  <button
                    onClick={() => handleDiversificationUpdate(index, { 
                      enabled: !rule.enabled 
                    })}
                    className={cn(
                      "relative inline-flex h-5 w-9 items-center rounded-full transition-colors",
                      rule.enabled ? "bg-primary" : "bg-muted"
                    )}
                  >
                    <span
                      className={cn(
                        "inline-block h-3 w-3 transform rounded-full bg-white transition-transform",
                        rule.enabled ? "translate-x-5" : "translate-x-1"
                      )}
                    />
                  </button>
                  <span className="text-sm font-medium text-foreground capitalize">
                    {rule.type} Limit
                  </span>
                </div>
                
                <div className="flex items-center gap-1">
                  <input
                    type="number"
                    min="1"
                    max="100"
                    value={rule.maxAllocation}
                    onChange={(e) => handleDiversificationUpdate(index, {
                      maxAllocation: Math.min(100, Math.max(1, parseInt(e.target.value) || 1))
                    })}
                    disabled={!rule.enabled}
                    className={cn(
                      "w-16 px-2 py-1 bg-card-elevated border border-border rounded text-sm text-right",
                      !rule.enabled && "opacity-50 cursor-not-allowed"
                    )}
                  />
                  <Percent className="w-4 h-4 text-muted-foreground" />
                </div>
              </div>
            ))}
          </div>
          
          <p className="text-xs text-muted-foreground">
            Set maximum allocation limits to maintain portfolio diversification
          </p>
        </div>
      </div>
      
      {/* Advanced Settings */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
            <Activity className="w-4 h-4 text-cyan-500" />
            Advanced Portfolio Settings
          </h4>
          
          <div className="space-y-3">
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm text-foreground">
                  Correlation Limit (0-1)
                </label>
                <LockButton category="portfolio" settingKey="correlationLimit" />
              </div>
              <input
                type="number"
                min="0"
                max="1"
                step="0.1"
                value={portfolioSettings.correlationLimit}
                onChange={(e) => updateSettings('portfolio', { 
                  correlationLimit: Math.min(1, Math.max(0, parseFloat(e.target.value) || 0))
                })}
                disabled={isSettingLocked('portfolio', 'correlationLimit')}
                className={cn(
                  "w-full px-3 py-2 bg-card-interactive border border-border rounded-md text-sm",
                  isSettingLocked('portfolio', 'correlationLimit') && "opacity-50 cursor-not-allowed"
                )}
              />
              <p className="text-xs text-muted-foreground mt-1">
                Maximum allowed correlation between positions
              </p>
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm text-foreground">
                  Rebalance Threshold (%)
                </label>
                <LockButton category="portfolio" settingKey="rebalanceThreshold" />
              </div>
              <input
                type="number"
                min="1"
                max="50"
                value={portfolioSettings.rebalanceThreshold}
                onChange={(e) => updateSettings('portfolio', { 
                  rebalanceThreshold: Math.min(50, Math.max(1, parseInt(e.target.value) || 1))
                })}
                disabled={isSettingLocked('portfolio', 'rebalanceThreshold')}
                className={cn(
                  "w-full px-3 py-2 bg-card-interactive border border-border rounded-md text-sm",
                  isSettingLocked('portfolio', 'rebalanceThreshold') && "opacity-50 cursor-not-allowed"
                )}
              />
              <p className="text-xs text-muted-foreground mt-1">
                Trigger rebalancing when positions deviate by this percentage
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}