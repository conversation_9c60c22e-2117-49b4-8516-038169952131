'use client'

import { useState } from 'react'
import { useSettingsStore } from '@/stores/settingsStore'
import { 
  Shield, 
  Lock, 
  LockOpen,
  AlertTriangle, 
  Clock,
  FileCheck,
  Ban,
  Plus,
  X,
  Key,
  DollarSign,
  Activity,
  StopCircle
} from 'lucide-react'
import { cn } from '@/lib/utils'

export function SecuritySettingsPanel() {
  const { settings, updateSettings, isSettingLocked, toggleSettingLock } = useSettingsStore()
  const securitySettings = settings.security
  
  const LockButton = ({ category, settingKey }: { category: string; settingKey: string }) => {
    const locked = isSettingLocked(category, settingKey)
    const IconComponent = locked ? Lock : LockOpen
    
    return (
      <button
        onClick={() => toggleSettingLock(category, settingKey)}
        className={cn(
          "p-1 rounded transition-colors",
          locked 
            ? "text-red-500 hover:bg-red-500/10" 
            : "text-muted-foreground hover:bg-muted"
        )}
        title={locked ? "Unlock setting" : "Lock setting"}
      >
        <IconComponent className="w-4 h-4" />
      </button>
    )
  }
  
  const [newWhitelistAddress, setNewWhitelistAddress] = useState('')
  const [newBlacklistAddress, setNewBlacklistAddress] = useState('')
  
  const handleAddWhitelist = () => {
    if (newWhitelistAddress && !securitySettings.whitelistedContracts.includes(newWhitelistAddress)) {
      updateSettings('security', {
        whitelistedContracts: [...securitySettings.whitelistedContracts, newWhitelistAddress]
      })
      setNewWhitelistAddress('')
    }
  }
  
  const handleRemoveWhitelist = (address: string) => {
    updateSettings('security', {
      whitelistedContracts: securitySettings.whitelistedContracts.filter(a => a !== address)
    })
  }
  
  const handleAddBlacklist = () => {
    if (newBlacklistAddress && !securitySettings.blacklistedContracts.includes(newBlacklistAddress)) {
      updateSettings('security', {
        blacklistedContracts: [...securitySettings.blacklistedContracts, newBlacklistAddress]
      })
      setNewBlacklistAddress('')
    }
  }
  
  const handleRemoveBlacklist = (address: string) => {
    updateSettings('security', {
      blacklistedContracts: securitySettings.blacklistedContracts.filter(a => a !== address)
    })
  }
  
  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-xl font-semibold text-foreground flex items-center gap-2">
          <Shield className="w-6 h-6 text-red-500" />
          Security Settings
        </h3>
        <p className="text-sm text-muted-foreground mt-1">
          Configure security features and transaction safeguards
        </p>
      </div>
      
      {/* Emergency Controls */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
            <StopCircle className="w-4 h-4 text-red-500" />
            Emergency Controls
          </h4>
          
          <div className="flex items-center justify-between p-4 bg-red-500/10 border border-red-500/30 rounded-lg">
            <div>
              <span className="text-sm font-medium text-foreground">Emergency Stop</span>
              <p className="text-xs text-muted-foreground mt-1">
                Instantly halt all trading activities
              </p>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={() => updateSettings('security', { 
                  emergencyStopEnabled: !securitySettings.emergencyStopEnabled 
                })}
                disabled={isSettingLocked('security', 'emergencyStopEnabled')}
                className={cn(
                  "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
                  isSettingLocked('security', 'emergencyStopEnabled')
                    ? "opacity-50 cursor-not-allowed"
                    : securitySettings.emergencyStopEnabled ? "bg-red-500" : "bg-muted"
                )}
              >
                <span
                  className={cn(
                    "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                    securitySettings.emergencyStopEnabled ? "translate-x-6" : "translate-x-1"
                  )}
                />
              </button>
              <LockButton category="security" settingKey="emergencyStopEnabled" />
            </div>
          </div>
          
          {securitySettings.emergencyStopEnabled && (
            <p className="flex items-center gap-1 text-xs text-red-500">
              <AlertTriangle className="w-3 h-3" />
              Emergency stop is active - all trading is disabled
            </p>
          )}
        </div>
      </div>
      
      {/* Transaction Security */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
            <Lock className="w-4 h-4 text-yellow-500" />
            Transaction Security
          </h4>
          
          <div className="space-y-3">
            <label className="flex items-center justify-between">
              <span className="text-sm text-foreground">Auto-approve transactions</span>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => updateSettings('security', { 
                    autoApproveTransactions: !securitySettings.autoApproveTransactions 
                  })}
                  disabled={isSettingLocked('security', 'autoApproveTransactions')}
                  className={cn(
                    "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
                    isSettingLocked('security', 'autoApproveTransactions')
                      ? "opacity-50 cursor-not-allowed"
                      : securitySettings.autoApproveTransactions ? "bg-primary" : "bg-muted"
                  )}
                >
                  <span
                    className={cn(
                      "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                      securitySettings.autoApproveTransactions ? "translate-x-6" : "translate-x-1"
                    )}
                  />
                </button>
                <LockButton category="security" settingKey="autoApproveTransactions" />
              </div>
            </label>
            
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm text-foreground">
                  Require confirmation above (SOL)
                </label>
                <LockButton category="security" settingKey="requireConfirmationAbove" />
              </div>
              <div className="flex items-center gap-2">
                <input
                  type="number"
                  min="0"
                  max="1000"
                  step="0.1"
                  value={securitySettings.requireConfirmationAbove}
                  onChange={(e) => updateSettings('security', { 
                    requireConfirmationAbove: Math.max(0, parseFloat(e.target.value) || 0)
                  })}
                  disabled={isSettingLocked('security', 'requireConfirmationAbove')}
                  className={cn(
                    "flex-1 px-3 py-2 bg-card-interactive border border-border rounded-md text-sm",
                    isSettingLocked('security', 'requireConfirmationAbove') && "opacity-50 cursor-not-allowed"
                  )}
                />
                <DollarSign className="w-5 h-5 text-muted-foreground" />
              </div>
            </div>
            
            <label className="flex items-center justify-between">
              <div>
                <span className="text-sm text-foreground">Two-factor authentication</span>
                <p className="text-xs text-muted-foreground mt-1">
                  Require 2FA for sensitive operations
                </p>
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => updateSettings('security', { 
                    twoFactorEnabled: !securitySettings.twoFactorEnabled 
                  })}
                  disabled={isSettingLocked('security', 'twoFactorEnabled')}
                  className={cn(
                    "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
                    isSettingLocked('security', 'twoFactorEnabled')
                      ? "opacity-50 cursor-not-allowed"
                      : securitySettings.twoFactorEnabled ? "bg-primary" : "bg-muted"
                  )}
                >
                  <span
                    className={cn(
                      "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                      securitySettings.twoFactorEnabled ? "translate-x-6" : "translate-x-1"
                    )}
                  />
                </button>
                <LockButton category="security" settingKey="twoFactorEnabled" />
              </div>
            </label>
          </div>
        </div>
      </div>
      
      {/* Session & Timeout Settings */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
            <Clock className="w-4 h-4 text-blue-500" />
            Session & Timeout Settings
          </h4>
          
          <div className="space-y-3">
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm text-foreground">
                  Session timeout (minutes)
                </label>
                <LockButton category="security" settingKey="sessionTimeout" />
              </div>
              <input
                type="number"
                min="5"
                max="1440"
                value={securitySettings.sessionTimeout}
                onChange={(e) => updateSettings('security', { 
                  sessionTimeout: Math.min(1440, Math.max(5, parseInt(e.target.value) || 5))
                })}
                disabled={isSettingLocked('security', 'sessionTimeout')}
                className={cn(
                  "w-full px-3 py-2 bg-card-interactive border border-border rounded-md text-sm",
                  isSettingLocked('security', 'sessionTimeout') && "opacity-50 cursor-not-allowed"
                )}
              />
              <p className="text-xs text-muted-foreground mt-1">
                Automatically log out after {securitySettings.sessionTimeout} minutes of inactivity
              </p>
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm text-foreground">
                  Wallet connection timeout (seconds)
                </label>
                <LockButton category="security" settingKey="walletConnectionTimeout" />
              </div>
              <input
                type="number"
                min="10"
                max="300"
                value={securitySettings.walletConnectionTimeout}
                onChange={(e) => updateSettings('security', { 
                  walletConnectionTimeout: Math.min(300, Math.max(10, parseInt(e.target.value) || 10))
                })}
                disabled={isSettingLocked('security', 'walletConnectionTimeout')}
                className={cn(
                  "w-full px-3 py-2 bg-card-interactive border border-border rounded-md text-sm",
                  isSettingLocked('security', 'walletConnectionTimeout') && "opacity-50 cursor-not-allowed"
                )}
              />
            </div>
          </div>
        </div>
      </div>
      
      {/* Daily Limits */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
            <Activity className="w-4 h-4 text-orange-500" />
            Daily Trading Limits
          </h4>
          
          <div className="space-y-3">
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm text-foreground">
                  Maximum daily transactions
                </label>
                <LockButton category="security" settingKey="maxDailyTransactions" />
              </div>
              <input
                type="number"
                min="1"
                max="1000"
                value={securitySettings.maxDailyTransactions}
                onChange={(e) => updateSettings('security', { 
                  maxDailyTransactions: Math.min(1000, Math.max(1, parseInt(e.target.value) || 1))
                })}
                disabled={isSettingLocked('security', 'maxDailyTransactions')}
                className={cn(
                  "w-full px-3 py-2 bg-card-interactive border border-border rounded-md text-sm",
                  isSettingLocked('security', 'maxDailyTransactions') && "opacity-50 cursor-not-allowed"
                )}
              />
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm text-foreground">
                  Maximum daily volume (SOL)
                </label>
                <LockButton category="security" settingKey="maxDailyVolume" />
              </div>
              <input
                type="number"
                min="1"
                max="100000"
                value={securitySettings.maxDailyVolume}
                onChange={(e) => updateSettings('security', { 
                  maxDailyVolume: Math.min(100000, Math.max(1, parseInt(e.target.value) || 1))
                })}
                disabled={isSettingLocked('security', 'maxDailyVolume')}
                className={cn(
                  "w-full px-3 py-2 bg-card-interactive border border-border rounded-md text-sm",
                  isSettingLocked('security', 'maxDailyVolume') && "opacity-50 cursor-not-allowed"
                )}
              />
            </div>
          </div>
        </div>
      </div>
      
      {/* Contract Whitelist */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
            <FileCheck className="w-4 h-4 text-green-500" />
            Whitelisted Contracts
          </h4>
          
          <div className="flex gap-2">
            <input
              type="text"
              placeholder="Contract address..."
              value={newWhitelistAddress}
              onChange={(e) => setNewWhitelistAddress(e.target.value)}
              className="flex-1 px-3 py-2 bg-card-interactive border border-border rounded-md text-sm"
            />
            <button
              onClick={handleAddWhitelist}
              disabled={!newWhitelistAddress || securitySettings.whitelistedContracts.length >= 100}
              className={cn(
                "px-3 py-2 rounded-md text-sm transition-colors",
                !newWhitelistAddress || securitySettings.whitelistedContracts.length >= 100
                  ? "bg-muted text-muted-foreground cursor-not-allowed"
                  : "bg-primary/10 text-primary hover:bg-primary/20"
              )}
            >
              <Plus className="w-4 h-4" />
            </button>
          </div>
          
          {securitySettings.whitelistedContracts.length > 0 && (
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {securitySettings.whitelistedContracts.map((address) => (
                <div key={address} className="flex items-center justify-between p-2 bg-card-interactive rounded text-sm">
                  <span className="text-muted-foreground font-mono truncate">{address}</span>
                  <button
                    onClick={() => handleRemoveWhitelist(address)}
                    className="p-1 text-red-500 hover:bg-red-500/10 rounded"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
      
      {/* Contract Blacklist */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
            <Ban className="w-4 h-4 text-red-500" />
            Blacklisted Contracts
          </h4>
          
          <div className="flex gap-2">
            <input
              type="text"
              placeholder="Contract address..."
              value={newBlacklistAddress}
              onChange={(e) => setNewBlacklistAddress(e.target.value)}
              className="flex-1 px-3 py-2 bg-card-interactive border border-border rounded-md text-sm"
            />
            <button
              onClick={handleAddBlacklist}
              disabled={!newBlacklistAddress || securitySettings.blacklistedContracts.length >= 100}
              className={cn(
                "px-3 py-2 rounded-md text-sm transition-colors",
                !newBlacklistAddress || securitySettings.blacklistedContracts.length >= 100
                  ? "bg-muted text-muted-foreground cursor-not-allowed"
                  : "bg-primary/10 text-primary hover:bg-primary/20"
              )}
            >
              <Plus className="w-4 h-4" />
            </button>
          </div>
          
          {securitySettings.blacklistedContracts.length > 0 && (
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {securitySettings.blacklistedContracts.map((address) => (
                <div key={address} className="flex items-center justify-between p-2 bg-card-interactive rounded text-sm">
                  <span className="text-muted-foreground font-mono truncate">{address}</span>
                  <button
                    onClick={() => handleRemoveBlacklist(address)}
                    className="p-1 text-red-500 hover:bg-red-500/10 rounded"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}