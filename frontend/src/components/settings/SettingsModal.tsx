'use client'

import { useState, useEffect } from 'react'
import { useSettingsStore } from '@/stores/settingsStore'
import { 
  X, 
  Settings, 
  Target, 
  Bell, 
  TrendingUp, 
  Shield, 
  Palette, 
  Zap,
  Save,
  RotateCcw,
  AlertTriangle,
  Download,
  Upload,
  Check
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { TradingSettingsPanel } from './TradingSettingsPanel'
import { PortfolioSettingsPanel } from './PortfolioSettingsPanel'
import { SecuritySettingsPanel } from './SecuritySettingsPanel'
import { UISettingsPanel } from './UISettingsPanel'
import { PerformanceSettingsPanel } from './PerformanceSettingsPanel'
import { AlertConfig } from '../alerts/AlertConfig'

interface SettingsModalProps {
  isOpen: boolean
  onClose: () => void
}

interface SettingsCategory {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  color: string
  component: React.ComponentType
}

const settingsCategories: SettingsCategory[] = [
  {
    id: 'trading',
    label: 'Trading',
    icon: Target,
    color: 'text-green-500',
    component: TradingSettingsPanel
  },
  {
    id: 'alerts',
    label: 'Alerts',
    icon: Bell,
    color: 'text-yellow-500',
    component: AlertConfig
  },
  {
    id: 'portfolio',
    label: 'Portfolio',
    icon: TrendingUp,
    color: 'text-blue-500',
    component: PortfolioSettingsPanel
  },
  {
    id: 'security',
    label: 'Security',
    icon: Shield,
    color: 'text-red-500',
    component: SecuritySettingsPanel
  },
  {
    id: 'ui',
    label: 'UI/UX',
    icon: Palette,
    color: 'text-purple-500',
    component: UISettingsPanel
  },
  {
    id: 'performance',
    label: 'Performance',
    icon: Zap,
    color: 'text-orange-500',
    component: PerformanceSettingsPanel
  }
]

export function SettingsModal({ isOpen, onClose }: SettingsModalProps) {
  const [activeCategory, setActiveCategory] = useState('trading')
  const [showSaveConfirmation, setShowSaveConfirmation] = useState(false)
  const [showUnsavedWarning, setShowUnsavedWarning] = useState(false)
  const [isMounted, setIsMounted] = useState(false)
  
  const { 
    settings, 
    hasUnsavedChanges, 
    validationErrors,
    error,
    saveSettings, 
    resetAllSettings,
    exportSettings,
    importSettings,
    validateSettings 
  } = useSettingsStore()
  
  useEffect(() => {
    setIsMounted(true)
  }, [])
  
  useEffect(() => {
    if (showSaveConfirmation) {
      const timer = setTimeout(() => setShowSaveConfirmation(false), 2000)
      return () => clearTimeout(timer)
    }
  }, [showSaveConfirmation])
  
  const handleClose = () => {
    if (hasUnsavedChanges) {
      setShowUnsavedWarning(true)
    } else {
      onClose()
    }
  }
  
  const handleForceClose = () => {
    setShowUnsavedWarning(false)
    onClose()
  }
  
  const handleSave = async () => {
    const errors = validateSettings()
    if (errors.length > 0) {
      return
    }
    
    try {
      await saveSettings()
      setShowSaveConfirmation(true)
    } catch (error) {
      console.error('Failed to save settings:', error)
    }
  }
  
  const handleReset = () => {
    if (confirm('Are you sure you want to reset all settings to defaults?')) {
      resetAllSettings()
    }
  }
  
  const handleExport = () => {
    const settingsJson = exportSettings()
    const blob = new Blob([settingsJson], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `memetrader-settings-${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)
  }
  
  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return
    
    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target?.result as string
      const success = importSettings(content)
      if (!success) {
        alert('Failed to import settings. Please check the file format.')
      }
    }
    reader.readAsText(file)
  }
  
  const ActiveComponent = settingsCategories.find(cat => cat.id === activeCategory)?.component
  
  if (!isOpen || !isMounted) return null
  
  return (
    <>
      {/* Modal Backdrop */}
      <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50" onClick={handleClose} />
      
      {/* Modal Content */}
      <div className="fixed inset-4 md:inset-[5%] bg-section-primary border border-elevated rounded-xl shadow-2xl z-50 flex flex-col overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-elevated bg-card-elevated/50">
          <div className="flex items-center gap-3">
            <Settings className="w-6 h-6 text-primary" />
            <h2 className="text-2xl font-bold text-foreground">Settings</h2>
            {hasUnsavedChanges && (
              <span className="text-sm text-yellow-500 font-medium px-2 py-1 bg-yellow-500/10 rounded">
                Unsaved Changes
              </span>
            )}
          </div>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-card-interactive rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-muted-foreground" />
          </button>
        </div>
        
        {/* Validation Errors */}
        {validationErrors.length > 0 && (
          <div className="mx-6 mt-4 p-4 bg-red-500/10 border border-red-500/30 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <AlertTriangle className="w-5 h-5 text-red-500" />
              <span className="font-medium text-red-500">Settings Validation Errors</span>
            </div>
            <ul className="text-sm text-red-500 space-y-1">
              {validationErrors.map((error, index) => (
                <li key={index}>• {error}</li>
              ))}
            </ul>
          </div>
        )}
        
        {/* Main Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Sidebar */}
          <div className="w-64 bg-card-elevated/30 border-r border-elevated p-4 overflow-y-auto">
            <nav className="space-y-2">
              {settingsCategories.map((category) => {
                const Icon = category.icon
                return (
                  <button
                    key={category.id}
                    onClick={() => setActiveCategory(category.id)}
                    className={cn(
                      "w-full flex items-center gap-3 px-4 py-3 rounded-lg transition-all text-left",
                      activeCategory === category.id
                        ? "bg-primary/10 text-primary border border-primary/30"
                        : "hover:bg-card-interactive text-muted-foreground hover:text-foreground"
                    )}
                  >
                    <Icon className={cn("w-5 h-5", category.color)} />
                    <span className="font-medium">{category.label}</span>
                  </button>
                )
              })}
            </nav>
            
            {/* Import/Export */}
            <div className="mt-8 pt-8 border-t border-elevated space-y-2">
              <button
                onClick={handleExport}
                className="w-full flex items-center gap-2 px-4 py-2 text-sm text-muted-foreground hover:text-foreground hover:bg-card-interactive rounded-lg transition-colors"
              >
                <Download className="w-4 h-4" />
                Export Settings
              </button>
              <label className="w-full flex items-center gap-2 px-4 py-2 text-sm text-muted-foreground hover:text-foreground hover:bg-card-interactive rounded-lg transition-colors cursor-pointer">
                <Upload className="w-4 h-4" />
                Import Settings
                <input
                  type="file"
                  accept=".json"
                  onChange={handleImport}
                  className="hidden"
                />
              </label>
            </div>
          </div>
          
          {/* Settings Panel */}
          <div className="flex-1 overflow-y-auto p-6">
            {ActiveComponent && <ActiveComponent />}
          </div>
        </div>
        
        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-elevated bg-card-elevated/50">
          <button
            onClick={handleReset}
            className="px-4 py-2 text-muted-foreground hover:text-foreground hover:bg-card-interactive border border-border rounded-lg transition-colors flex items-center gap-2"
          >
            <RotateCcw className="w-4 h-4" />
            Reset All
          </button>
          
          <div className="flex items-center gap-3">
            <button
              onClick={handleClose}
              className="px-4 py-2 text-muted-foreground hover:text-foreground hover:bg-card-interactive border border-border rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              disabled={validationErrors.length > 0}
              className={cn(
                "px-6 py-2 rounded-lg font-medium transition-all flex items-center gap-2",
                showSaveConfirmation
                  ? "bg-green-500 text-white"
                  : validationErrors.length > 0
                  ? "bg-muted text-muted-foreground cursor-not-allowed"
                  : "bg-primary text-primary-foreground hover:bg-primary/90"
              )}
            >
              {showSaveConfirmation ? (
                <>
                  <Check className="w-4 h-4" />
                  Saved
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  Save Changes
                </>
              )}
            </button>
          </div>
        </div>
      </div>
      
      {/* Unsaved Changes Warning */}
      {showUnsavedWarning && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-[60] flex items-center justify-center">
          <div className="bg-section-primary border border-elevated rounded-xl p-6 max-w-md">
            <h3 className="text-lg font-semibold text-foreground mb-2">Unsaved Changes</h3>
            <p className="text-muted-foreground mb-6">
              You have unsaved changes. Are you sure you want to close without saving?
            </p>
            <div className="flex justify-end gap-3">
              <button
                onClick={() => setShowUnsavedWarning(false)}
                className="px-4 py-2 text-muted-foreground hover:text-foreground hover:bg-card-interactive border border-border rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleForceClose}
                className="px-4 py-2 bg-red-500 text-white hover:bg-red-600 rounded-lg transition-colors"
              >
                Close Without Saving
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}