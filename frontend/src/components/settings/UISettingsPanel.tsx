'use client'

import { useSettingsStore } from '@/stores/settingsStore'
import { 
  Palette, 
  Sun, 
  Moon, 
  Monitor,
  Globe,
  Calendar,
  Hash,
  Layout,
  Sparkles,
  LineChart,
  Keyboard,
  Lock,
  LockOpen
} from 'lucide-react'
import { cn } from '@/lib/utils'

const timezones = [
  { value: 'America/New_York', label: 'Eastern Time (ET)' },
  { value: 'America/Chicago', label: 'Central Time (CT)' },
  { value: 'America/Denver', label: 'Mountain Time (MT)' },
  { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
  { value: 'Europe/London', label: 'London (GMT)' },
  { value: 'Europe/Paris', label: 'Paris (CET)' },
  { value: 'Asia/Tokyo', label: 'Tokyo (JST)' },
  { value: 'Asia/Hong_Kong', label: 'Hong Kong (HKT)' },
  { value: 'Australia/Sydney', label: 'Sydney (AEDT)' }
]

const languages = [
  { value: 'en', label: 'English' },
  { value: 'es', label: 'Spanish' },
  { value: 'fr', label: 'French' },
  { value: 'de', label: 'German' },
  { value: 'ja', label: 'Japanese' },
  { value: 'zh', label: 'Chinese' },
  { value: 'ko', label: 'Korean' }
]

const dateFormats = [
  { value: 'MM/DD/YYYY', label: '12/31/2024' },
  { value: 'DD/MM/YYYY', label: '31/12/2024' },
  { value: 'YYYY-MM-DD', label: '2024-12-31' },
  { value: 'DD.MM.YYYY', label: '31.12.2024' }
]

const chartIntervals = [
  { value: '1m', label: '1 Minute' },
  { value: '5m', label: '5 Minutes' },
  { value: '15m', label: '15 Minutes' },
  { value: '1h', label: '1 Hour' },
  { value: '4h', label: '4 Hours' },
  { value: '1d', label: '1 Day' }
]

export function UISettingsPanel() {
  const { settings, updateSettings, isSettingLocked, toggleSettingLock } = useSettingsStore()
  const uiSettings = settings.ui
  
  const LockButton = ({ category, settingKey }: { category: string; settingKey: string }) => {
    const locked = isSettingLocked(category, settingKey)
    const IconComponent = locked ? Lock : LockOpen
    
    return (
      <button
        onClick={() => toggleSettingLock(category, settingKey)}
        className={cn(
          "p-1 rounded transition-colors",
          locked 
            ? "text-red-500 hover:bg-red-500/10" 
            : "text-muted-foreground hover:bg-muted"
        )}
        title={locked ? "Unlock setting" : "Lock setting"}
      >
        <IconComponent className="w-4 h-4" />
      </button>
    )
  }
  
  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-xl font-semibold text-foreground flex items-center gap-2">
          <Palette className="w-6 h-6 text-purple-500" />
          UI/UX Settings
        </h3>
        <p className="text-sm text-muted-foreground mt-1">
          Customize the appearance and behavior of the interface
        </p>
      </div>
      
      {/* Theme Settings */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
              <Sun className="w-4 h-4 text-yellow-500" />
              Theme
            </h4>
            <LockButton category="ui" settingKey="theme" />
          </div>
          
          <div className="grid grid-cols-3 gap-3">
            <button
              onClick={() => updateSettings('ui', { theme: 'light' })}
              disabled={isSettingLocked('ui', 'theme')}
              className={cn(
                "flex items-center justify-center gap-2 px-4 py-3 rounded-lg border transition-all",
                isSettingLocked('ui', 'theme')
                  ? "cursor-not-allowed opacity-50"
                  : uiSettings.theme === 'light'
                    ? "bg-primary/10 border-primary text-primary"
                    : "bg-card-interactive border-border text-muted-foreground hover:text-foreground hover:border-primary/50"
              )}
            >
              <Sun className="w-5 h-5" />
              <span className="text-sm font-medium">Light</span>
            </button>
            
            <button
              onClick={() => updateSettings('ui', { theme: 'dark' })}
              disabled={isSettingLocked('ui', 'theme')}
              className={cn(
                "flex items-center justify-center gap-2 px-4 py-3 rounded-lg border transition-all",
                isSettingLocked('ui', 'theme')
                  ? "cursor-not-allowed opacity-50"
                  : uiSettings.theme === 'dark'
                    ? "bg-primary/10 border-primary text-primary"
                    : "bg-card-interactive border-border text-muted-foreground hover:text-foreground hover:border-primary/50"
              )}
            >
              <Moon className="w-5 h-5" />
              <span className="text-sm font-medium">Dark</span>
            </button>
            
            <button
              onClick={() => updateSettings('ui', { theme: 'auto' })}
              disabled={isSettingLocked('ui', 'theme')}
              className={cn(
                "flex items-center justify-center gap-2 px-4 py-3 rounded-lg border transition-all",
                isSettingLocked('ui', 'theme')
                  ? "cursor-not-allowed opacity-50"
                  : uiSettings.theme === 'auto'
                    ? "bg-primary/10 border-primary text-primary"
                    : "bg-card-interactive border-border text-muted-foreground hover:text-foreground hover:border-primary/50"
              )}
            >
              <Monitor className="w-5 h-5" />
              <span className="text-sm font-medium">Auto</span>
            </button>
          </div>
        </div>
      </div>
      
      {/* Localization */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
            <Globe className="w-4 h-4 text-blue-500" />
            Localization
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm text-foreground">
                  Language
                </label>
                <LockButton category="ui" settingKey="language" />
              </div>
              <select
                value={uiSettings.language}
                onChange={(e) => updateSettings('ui', { language: e.target.value })}
                disabled={isSettingLocked('ui', 'language')}
                className={cn(
                  "w-full px-3 py-2 bg-card-interactive border border-border rounded-md text-sm",
                  isSettingLocked('ui', 'language') && "opacity-50 cursor-not-allowed"
                )}
              >
                {languages.map(lang => (
                  <option key={lang.value} value={lang.value}>
                    {lang.label}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm text-foreground">
                  Timezone
                </label>
                <LockButton category="ui" settingKey="timezone" />
              </div>
              <select
                value={uiSettings.timezone}
                onChange={(e) => updateSettings('ui', { timezone: e.target.value })}
                disabled={isSettingLocked('ui', 'timezone')}
                className={cn(
                  "w-full px-3 py-2 bg-card-interactive border border-border rounded-md text-sm",
                  isSettingLocked('ui', 'timezone') && "opacity-50 cursor-not-allowed"
                )}
              >
                {timezones.map(tz => (
                  <option key={tz.value} value={tz.value}>
                    {tz.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </div>
      
      {/* Number & Date Formatting */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
            <Calendar className="w-4 h-4 text-green-500" />
            Formatting
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm text-foreground">
                  Date Format
                </label>
                <LockButton category="ui" settingKey="dateFormat" />
              </div>
              <select
                value={uiSettings.dateFormat}
                onChange={(e) => updateSettings('ui', { dateFormat: e.target.value })}
                disabled={isSettingLocked('ui', 'dateFormat')}
                className={cn(
                  "w-full px-3 py-2 bg-card-interactive border border-border rounded-md text-sm",
                  isSettingLocked('ui', 'dateFormat') && "opacity-50 cursor-not-allowed"
                )}
              >
                {dateFormats.map(format => (
                  <option key={format.value} value={format.value}>
                    {format.label}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm text-foreground">
                  Number Format
                </label>
                <LockButton category="ui" settingKey="numberFormat" />
              </div>
              <div className="grid grid-cols-3 gap-2">
                <button
                  onClick={() => updateSettings('ui', { numberFormat: 'comma' })}
                  disabled={isSettingLocked('ui', 'numberFormat')}
                  className={cn(
                    "px-3 py-2 rounded-md text-sm transition-colors",
                    isSettingLocked('ui', 'numberFormat')
                      ? "cursor-not-allowed opacity-50"
                      : uiSettings.numberFormat === 'comma'
                        ? "bg-primary/10 border border-primary text-primary"
                        : "bg-card-interactive border border-border text-muted-foreground hover:text-foreground"
                  )}
                >
                  1,234.56
                </button>
                <button
                  onClick={() => updateSettings('ui', { numberFormat: 'period' })}
                  disabled={isSettingLocked('ui', 'numberFormat')}
                  className={cn(
                    "px-3 py-2 rounded-md text-sm transition-colors",
                    isSettingLocked('ui', 'numberFormat')
                      ? "cursor-not-allowed opacity-50"
                      : uiSettings.numberFormat === 'period'
                        ? "bg-primary/10 border border-primary text-primary"
                        : "bg-card-interactive border border-border text-muted-foreground hover:text-foreground"
                  )}
                >
                  1.234,56
                </button>
                <button
                  onClick={() => updateSettings('ui', { numberFormat: 'space' })}
                  disabled={isSettingLocked('ui', 'numberFormat')}
                  className={cn(
                    "px-3 py-2 rounded-md text-sm transition-colors",
                    isSettingLocked('ui', 'numberFormat')
                      ? "cursor-not-allowed opacity-50"
                      : uiSettings.numberFormat === 'space'
                        ? "bg-primary/10 border border-primary text-primary"
                        : "bg-card-interactive border border-border text-muted-foreground hover:text-foreground"
                  )}
                >
                  1 234.56
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Dashboard Layout */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
            <Layout className="w-4 h-4 text-orange-500" />
            Dashboard Layout
          </h4>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {['default', 'compact', 'advanced', 'custom'].map((layout) => (
              <button
                key={layout}
                onClick={() => updateSettings('ui', { 
                  dashboardLayout: { 
                    ...uiSettings.dashboardLayout, 
                    type: layout as any 
                  } 
                })}
                className={cn(
                  "px-4 py-3 rounded-lg border transition-all text-sm capitalize",
                  uiSettings.dashboardLayout.type === layout
                    ? "bg-primary/10 border-primary text-primary"
                    : "bg-card-interactive border-border text-muted-foreground hover:text-foreground hover:border-primary/50"
                )}
              >
                {layout}
              </button>
            ))}
          </div>
          
          <div>
            <label className="block text-sm text-foreground mb-2">
              Dashboard Columns
            </label>
            <input
              type="range"
              min="1"
              max="12"
              value={uiSettings.dashboardLayout.columns}
              onChange={(e) => updateSettings('ui', {
                dashboardLayout: {
                  ...uiSettings.dashboardLayout,
                  columns: parseInt(e.target.value)
                }
              })}
              className="w-full h-2 bg-card-interactive rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-muted-foreground mt-1">
              <span>1 Column</span>
              <span>{uiSettings.dashboardLayout.columns} Columns</span>
              <span>12 Columns</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Chart Settings */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
            <LineChart className="w-4 h-4 text-cyan-500" />
            Chart Settings
          </h4>
          
          <div className="space-y-3">
            <div>
              <label className="block text-sm text-foreground mb-2">
                Default Chart Interval
              </label>
              <select
                value={uiSettings.chartSettings.defaultInterval}
                onChange={(e) => updateSettings('ui', {
                  chartSettings: {
                    ...uiSettings.chartSettings,
                    defaultInterval: e.target.value as any
                  }
                })}
                className="w-full px-3 py-2 bg-card-interactive border border-border rounded-md text-sm"
              >
                {chartIntervals.map(interval => (
                  <option key={interval.value} value={interval.value}>
                    {interval.label}
                  </option>
                ))}
              </select>
            </div>
            
            <label className="flex items-center justify-between">
              <span className="text-sm text-foreground">Show volume bars</span>
              <button
                onClick={() => updateSettings('ui', {
                  chartSettings: {
                    ...uiSettings.chartSettings,
                    showVolume: !uiSettings.chartSettings.showVolume
                  }
                })}
                className={cn(
                  "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
                  uiSettings.chartSettings.showVolume ? "bg-primary" : "bg-muted"
                )}
              >
                <span
                  className={cn(
                    "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                    uiSettings.chartSettings.showVolume ? "translate-x-6" : "translate-x-1"
                  )}
                />
              </button>
            </label>
            
            <div>
              <label className="block text-sm text-foreground mb-2">
                Color Scheme
              </label>
              <div className="grid grid-cols-3 gap-2">
                {['default', 'tradingview', 'custom'].map((scheme) => (
                  <button
                    key={scheme}
                    onClick={() => updateSettings('ui', {
                      chartSettings: {
                        ...uiSettings.chartSettings,
                        colorScheme: scheme as any
                      }
                    })}
                    className={cn(
                      "px-3 py-2 rounded-md text-sm capitalize transition-colors",
                      uiSettings.chartSettings.colorScheme === scheme
                        ? "bg-primary/10 border border-primary text-primary"
                        : "bg-card-interactive border border-border text-muted-foreground hover:text-foreground"
                    )}
                  >
                    {scheme}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* General UI Preferences */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
            <Sparkles className="w-4 h-4 text-yellow-500" />
            General Preferences
          </h4>
          
          <div className="space-y-3">
            <label className="flex items-center justify-between">
              <span className="text-sm text-foreground">Animations</span>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => updateSettings('ui', { 
                    animations: !uiSettings.animations 
                  })}
                  disabled={isSettingLocked('ui', 'animations')}
                  className={cn(
                    "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
                    isSettingLocked('ui', 'animations')
                      ? "opacity-50 cursor-not-allowed"
                      : uiSettings.animations ? "bg-primary" : "bg-muted"
                  )}
                >
                  <span
                    className={cn(
                      "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                      uiSettings.animations ? "translate-x-6" : "translate-x-1"
                    )}
                  />
                </button>
                <LockButton category="ui" settingKey="animations" />
              </div>
            </label>
            
            <label className="flex items-center justify-between">
              <span className="text-sm text-foreground">Compact Mode</span>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => updateSettings('ui', { 
                    compactMode: !uiSettings.compactMode 
                  })}
                  disabled={isSettingLocked('ui', 'compactMode')}
                  className={cn(
                    "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
                    isSettingLocked('ui', 'compactMode')
                      ? "opacity-50 cursor-not-allowed"
                      : uiSettings.compactMode ? "bg-primary" : "bg-muted"
                  )}
                >
                  <span
                    className={cn(
                      "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                      uiSettings.compactMode ? "translate-x-6" : "translate-x-1"
                    )}
                  />
                </button>
                <LockButton category="ui" settingKey="compactMode" />
              </div>
            </label>
            
            <label className="flex items-center justify-between">
              <div>
                <span className="text-sm text-foreground">Keyboard Shortcuts</span>
                <p className="text-xs text-muted-foreground mt-1">
                  Enable keyboard navigation and shortcuts
                </p>
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => updateSettings('ui', { 
                    keyboardShortcuts: !uiSettings.keyboardShortcuts 
                  })}
                  disabled={isSettingLocked('ui', 'keyboardShortcuts')}
                  className={cn(
                    "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
                    isSettingLocked('ui', 'keyboardShortcuts')
                      ? "opacity-50 cursor-not-allowed"
                      : uiSettings.keyboardShortcuts ? "bg-primary" : "bg-muted"
                  )}
                >
                  <span
                    className={cn(
                      "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                      uiSettings.keyboardShortcuts ? "translate-x-6" : "translate-x-1"
                    )}
                  />
                </button>
                <LockButton category="ui" settingKey="keyboardShortcuts" />
              </div>
            </label>
          </div>
        </div>
      </div>
    </div>
  )
}