'use client'

import { useSettingsStore } from '@/stores/settingsStore'
import { MEVProtectionLevel } from 'shared/src/types/enums'
import { QuickAmountConfig } from 'shared/src/types/settings'
import { 
  Target, 
  Shield, 
  DollarSign, 
  Zap, 
  Plus, 
  Trash2,
  AlertCircle,
  Percent,
  Coins,
  Lock,
  LockOpen
} from 'lucide-react'
import { cn } from '@/lib/utils'

export function TradingSettingsPanel() {
  const { settings, updateSettings, isSettingLocked, toggleSettingLock } = useSettingsStore()
  const tradingSettings = settings.trading
  
  const LockButton = ({ category, settingKey }: { category: string; settingKey: string }) => {
    const locked = isSettingLocked(category, settingKey)
    const IconComponent = locked ? Lock : LockOpen
    
    return (
      <button
        onClick={() => toggleSettingLock(category, settingKey)}
        className={cn(
          "p-1 rounded transition-colors",
          locked 
            ? "text-red-500 hover:bg-red-500/10" 
            : "text-muted-foreground hover:bg-muted"
        )}
        title={locked ? "Unlock setting" : "Lock setting"}
      >
        <IconComponent className="w-4 h-4" />
      </button>
    )
  }
  
  const handleQuickAmountUpdate = (index: number, updates: Partial<QuickAmountConfig>) => {
    const newQuickAmounts = [...tradingSettings.quickAmounts]
    newQuickAmounts[index] = { ...newQuickAmounts[index], ...updates }
    updateSettings('trading', { quickAmounts: newQuickAmounts })
  }
  
  const handleAddQuickAmount = () => {
    if (tradingSettings.quickAmounts.length < 12) {
      updateSettings('trading', {
        quickAmounts: [...tradingSettings.quickAmounts, {
          label: 'NEW',
          type: 'percentage',
          value: 25,
          isDefault: false
        }]
      })
    }
  }
  
  const handleRemoveQuickAmount = (index: number) => {
    if (tradingSettings.quickAmounts.length > 1) {
      const newQuickAmounts = tradingSettings.quickAmounts.filter((_, i) => i !== index)
      updateSettings('trading', { quickAmounts: newQuickAmounts })
    }
  }
  
  
  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-xl font-semibold text-foreground flex items-center gap-2">
          <Target className="w-6 h-6 text-green-500" />
          Trading Settings
        </h3>
        <p className="text-sm text-muted-foreground mt-1">
          Configure your default trading parameters and behaviors
        </p>
      </div>
      
      {/* Slippage Tolerance */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="space-y-4">
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium text-foreground">
                Default Slippage Tolerance
              </label>
              <LockButton category="trading" settingKey="defaultSlippage" />
            </div>
            <div className="flex items-center gap-4">
              <input
                type="range"
                min="0.1"
                max="50"
                step="0.1"
                value={tradingSettings.defaultSlippage}
                onChange={(e) => updateSettings('trading', { 
                  defaultSlippage: parseFloat(e.target.value) 
                })}
                disabled={isSettingLocked('trading', 'defaultSlippage')}
                className={cn(
                  "flex-1 h-2 bg-card-interactive rounded-lg appearance-none",
                  isSettingLocked('trading', 'defaultSlippage') 
                    ? "cursor-not-allowed opacity-50" 
                    : "cursor-pointer"
                )}
              />
              <div className="flex items-center gap-1 min-w-[80px]">
                <input
                  type="number"
                  min="0.1"
                  max="50"
                  step="0.1"
                  value={tradingSettings.defaultSlippage}
                  onChange={(e) => updateSettings('trading', { 
                    defaultSlippage: Math.min(50, Math.max(0.1, parseFloat(e.target.value) || 0.1))
                  })}
                  disabled={isSettingLocked('trading', 'defaultSlippage')}
                  className={cn(
                    "w-16 px-2 py-1 bg-card-interactive border border-border rounded text-sm text-right",
                    isSettingLocked('trading', 'defaultSlippage') && "opacity-50 cursor-not-allowed"
                  )}
                />
                <Percent className="w-4 h-4 text-muted-foreground" />
              </div>
            </div>
            <div className="flex justify-between text-xs text-muted-foreground mt-1">
              <span>Low (0.1%)</span>
              <span>Medium (3%)</span>
              <span>High (50%)</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Priority Fee */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="space-y-4">
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium text-foreground">
                Priority Fee (Lamports)
              </label>
              <LockButton category="trading" settingKey="priorityFee" />
            </div>
            <div className="flex items-center gap-4">
              <input
                type="number"
                min="0"
                max="100000"
                step="100"
                value={tradingSettings.priorityFee}
                onChange={(e) => updateSettings('trading', { 
                  priorityFee: Math.min(100000, Math.max(0, parseInt(e.target.value) || 0))
                })}
                disabled={isSettingLocked('trading', 'priorityFee')}
                className={cn(
                  "flex-1 px-3 py-2 bg-card-interactive border border-border rounded-md text-sm",
                  isSettingLocked('trading', 'priorityFee') && "opacity-50 cursor-not-allowed"
                )}
              />
              <Coins className="w-5 h-5 text-muted-foreground" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              Higher priority fees increase transaction success rate during network congestion
            </p>
          </div>
        </div>
      </div>
      
      {/* MEV Protection */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <label className="block text-sm font-medium text-foreground">
              MEV Protection Level
            </label>
            <div className="flex items-center gap-2">
              <Shield className="w-5 h-5 text-yellow-500" />
              <LockButton category="trading" settingKey="mevProtectionLevel" />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            {Object.values(MEVProtectionLevel).map((level) => (
              <button
                key={level}
                onClick={() => updateSettings('trading', { mevProtectionLevel: level })}
                disabled={isSettingLocked('trading', 'mevProtectionLevel')}
                className={cn(
                  "px-4 py-3 rounded-lg border transition-all text-sm font-medium",
                  isSettingLocked('trading', 'mevProtectionLevel')
                    ? "cursor-not-allowed opacity-50"
                    : tradingSettings.mevProtectionLevel === level
                      ? "bg-primary/10 border-primary text-primary"
                      : "bg-card-interactive border-border text-muted-foreground hover:text-foreground hover:border-primary/50"
                )}
              >
                <div className="capitalize">{level.toLowerCase()}</div>
                <div className="text-xs mt-1 font-normal">
                  {level === MEVProtectionLevel.DISABLED && "No protection"}
                  {level === MEVProtectionLevel.STANDARD && "Basic frontrun protection"}
                  {level === MEVProtectionLevel.HIGH && "Maximum protection"}
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>
      
      {/* Quick Amount Buttons */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <label className="block text-sm font-medium text-foreground">
              Quick Amount Buttons
            </label>
            <button
              onClick={handleAddQuickAmount}
              disabled={tradingSettings.quickAmounts.length >= 12}
              className={cn(
                "flex items-center gap-1 px-3 py-1 rounded-md text-sm transition-colors",
                tradingSettings.quickAmounts.length >= 12
                  ? "bg-muted text-muted-foreground cursor-not-allowed"
                  : "bg-primary/10 text-primary hover:bg-primary/20"
              )}
            >
              <Plus className="w-4 h-4" />
              Add
            </button>
          </div>
          
          <div className="space-y-3">
            {tradingSettings.quickAmounts.map((amount, index) => {
              const isLocked = isSettingLocked('trading', `quickAmounts.${index}`)
              return (
                <div key={index} className="flex items-center gap-3">
                  <input
                    type="text"
                    value={amount.label}
                    onChange={(e) => handleQuickAmountUpdate(index, { 
                      label: e.target.value.slice(0, 12).toUpperCase() 
                    })}
                    disabled={isLocked}
                    className={cn(
                      "w-28 px-2 py-1.5 bg-card-interactive border border-border rounded text-sm font-medium",
                      isLocked && "opacity-50 cursor-not-allowed"
                    )}
                    placeholder="Label"
                  />
                  
                  <select
                    value={amount.type}
                    onChange={(e) => handleQuickAmountUpdate(index, { 
                      type: e.target.value as 'percentage' | 'sol'
                    })}
                    disabled={isLocked}
                    className={cn(
                      "px-2 py-1.5 bg-card-interactive border border-border rounded text-sm",
                      isLocked && "opacity-50 cursor-not-allowed"
                    )}
                  >
                    <option value="percentage">%</option>
                    <option value="sol">SOL</option>
                  </select>
                  
                  <div className="flex items-center gap-1 flex-1">
                    <input
                      type="number"
                      min={amount.type === 'percentage' ? "1" : "0.01"}
                      max={amount.type === 'percentage' ? "100" : "1000"}
                      step={amount.type === 'percentage' ? "1" : "0.01"}
                      value={amount.value}
                      onChange={(e) => {
                        const newValue = parseFloat(e.target.value) || 0
                        const maxValue = amount.type === 'percentage' ? 100 : 1000
                        const minValue = amount.type === 'percentage' ? 1 : 0.01
                        handleQuickAmountUpdate(index, { 
                          value: Math.min(maxValue, Math.max(minValue, newValue))
                        })
                      }}
                      disabled={isLocked}
                      className={cn(
                        "w-20 px-2 py-1.5 bg-card-interactive border border-border rounded text-sm text-right",
                        isLocked && "opacity-50 cursor-not-allowed"
                      )}
                    />
                    {amount.type === 'percentage' ? (
                      <Percent className="w-4 h-4 text-muted-foreground" />
                    ) : (
                      <DollarSign className="w-4 h-4 text-muted-foreground" />
                    )}
                  </div>
                  
                  <LockButton category="trading" settingKey={`quickAmounts.${index}`} />
                  
                  <button
                    onClick={() => handleRemoveQuickAmount(index)}
                    disabled={tradingSettings.quickAmounts.length <= 1 || isLocked}
                    className={cn(
                      "p-1.5 rounded transition-colors",
                      tradingSettings.quickAmounts.length <= 1 || isLocked
                        ? "text-muted-foreground cursor-not-allowed opacity-50"
                        : "text-red-500 hover:bg-red-500/10"
                    )}
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              )
            })}
          </div>
        </div>
      </div>
      
      {/* Trading Behaviors */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
            <Zap className="w-4 h-4 text-orange-500" />
            Trading Behaviors
          </h4>
          
          <div className="space-y-3">
            <label className="flex items-center justify-between">
              <span className="text-sm text-foreground">Simulate transactions first</span>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => updateSettings('trading', { 
                    simulateFirst: !tradingSettings.simulateFirst 
                  })}
                  disabled={isSettingLocked('trading', 'simulateFirst')}
                  className={cn(
                    "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
                    isSettingLocked('trading', 'simulateFirst')
                      ? "opacity-50 cursor-not-allowed"
                      : tradingSettings.simulateFirst ? "bg-primary" : "bg-muted"
                  )}
                >
                  <span
                    className={cn(
                      "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                      tradingSettings.simulateFirst ? "translate-x-6" : "translate-x-1"
                    )}
                  />
                </button>
                <LockButton category="trading" settingKey="simulateFirst" />
              </div>
            </label>
            
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm text-foreground">
                  Auto-approve transactions below (SOL)
                </label>
                <LockButton category="trading" settingKey="autoApproveBelow" />
              </div>
              <div className="flex items-center gap-2">
                <input
                  type="number"
                  min="0"
                  max="100"
                  step="0.01"
                  value={tradingSettings.autoApproveBelow}
                  onChange={(e) => updateSettings('trading', { 
                    autoApproveBelow: Math.max(0, parseFloat(e.target.value) || 0)
                  })}
                  disabled={isSettingLocked('trading', 'autoApproveBelow')}
                  className={cn(
                    "flex-1 px-3 py-2 bg-card-interactive border border-border rounded-md text-sm",
                    isSettingLocked('trading', 'autoApproveBelow') && "opacity-50 cursor-not-allowed"
                  )}
                />
                <DollarSign className="w-5 h-5 text-muted-foreground" />
              </div>
              {tradingSettings.autoApproveBelow > 0 && (
                <p className="flex items-center gap-1 text-xs text-yellow-500 mt-2">
                  <AlertCircle className="w-3 h-3" />
                  Transactions below {tradingSettings.autoApproveBelow} SOL will be auto-approved
                </p>
              )}
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm text-foreground">
                  Default position size (% of portfolio)
                </label>
                <LockButton category="trading" settingKey="defaultPositionSize" />
              </div>
              <input
                type="number"
                min="1"
                max="100"
                step="1"
                value={tradingSettings.defaultPositionSize}
                onChange={(e) => updateSettings('trading', { 
                  defaultPositionSize: Math.min(100, Math.max(1, parseInt(e.target.value) || 1))
                })}
                disabled={isSettingLocked('trading', 'defaultPositionSize')}
                className={cn(
                  "w-full px-3 py-2 bg-card-interactive border border-border rounded-md text-sm",
                  isSettingLocked('trading', 'defaultPositionSize') && "opacity-50 cursor-not-allowed"
                )}
              />
            </div>
          </div>
        </div>
      </div>
      
      {/* Preset Configurations */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-foreground">Quick Presets</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <button
              onClick={() => useSettingsStore.getState().applyPreset('conservative')}
              className="px-4 py-3 bg-card-interactive hover:bg-card-elevated border border-border hover:border-primary/50 rounded-lg transition-all text-sm"
            >
              <div className="font-medium text-foreground">Conservative</div>
              <div className="text-xs text-muted-foreground mt-1">
                Low risk, small positions
              </div>
            </button>
            <button
              onClick={() => useSettingsStore.getState().applyPreset('balanced')}
              className="px-4 py-3 bg-card-interactive hover:bg-card-elevated border border-border hover:border-primary/50 rounded-lg transition-all text-sm"
            >
              <div className="font-medium text-foreground">Balanced</div>
              <div className="text-xs text-muted-foreground mt-1">
                Moderate risk, standard positions
              </div>
            </button>
            <button
              onClick={() => useSettingsStore.getState().applyPreset('aggressive')}
              className="px-4 py-3 bg-card-interactive hover:bg-card-elevated border border-border hover:border-primary/50 rounded-lg transition-all text-sm"
            >
              <div className="font-medium text-foreground">Aggressive</div>
              <div className="text-xs text-muted-foreground mt-1">
                High risk, large positions
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}