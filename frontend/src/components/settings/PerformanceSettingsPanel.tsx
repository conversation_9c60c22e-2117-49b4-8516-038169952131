'use client'

import { useSettingsStore } from '@/stores/settingsStore'
import { 
  Zap, 
  Wifi, 
  Database, 
  Clock, 
  Activity,
  HardDrive,
  Network,
  Timer,
  Gauge,
  BarChart3,
  Lock,
  LockOpen
} from 'lucide-react'
import { cn } from '@/lib/utils'

export function PerformanceSettingsPanel() {
  const { settings, updateSettings, isSettingLocked, toggleSettingLock } = useSettingsStore()
  const performanceSettings = settings.performance
  
  const LockButton = ({ category, settingKey }: { category: string; settingKey: string }) => {
    const locked = isSettingLocked(category, settingKey)
    const IconComponent = locked ? Lock : LockOpen
    
    return (
      <button
        onClick={() => toggleSettingLock(category, settingKey)}
        className={cn(
          "p-1 rounded transition-colors",
          locked 
            ? "text-red-500 hover:bg-red-500/10" 
            : "text-muted-foreground hover:bg-muted"
        )}
        title={locked ? "Unlock setting" : "Lock setting"}
      >
        <IconComponent className="w-4 h-4" />
      </button>
    )
  }
  
  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-xl font-semibold text-foreground flex items-center gap-2">
          <Zap className="w-6 h-6 text-orange-500" />
          Performance Settings
        </h3>
        <p className="text-sm text-muted-foreground mt-1">
          Optimize application performance and data handling
        </p>
      </div>
      
      {/* Real-time Data */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
            <Activity className="w-4 h-4 text-blue-500" />
            Real-time Data Updates
          </h4>
          
          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm text-foreground">
                  WebSocket Heartbeat Interval (milliseconds)
                </label>
                <LockButton category="performance" settingKey="websocketHeartbeat" />
              </div>
              <div className="flex items-center gap-4">
                <input
                  type="range"
                  min="5000"
                  max="60000"
                  step="1000"
                  value={performanceSettings.websocketHeartbeat}
                  onChange={(e) => updateSettings('performance', { 
                    websocketHeartbeat: parseInt(e.target.value) 
                  })}
                  disabled={isSettingLocked('performance', 'websocketHeartbeat')}
                  className={cn(
                    "flex-1 h-2 bg-card-interactive rounded-lg appearance-none",
                    isSettingLocked('performance', 'websocketHeartbeat') 
                      ? "cursor-not-allowed opacity-50" 
                      : "cursor-pointer"
                  )}
                />
                <div className="min-w-[80px] text-sm text-muted-foreground">
                  {performanceSettings.websocketHeartbeat}ms
                </div>
              </div>
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                <span>5s</span>
                <span>30s</span>
                <span>60s</span>
              </div>
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm text-foreground">
                  Price Update Frequency (milliseconds)
                </label>
                <LockButton category="performance" settingKey="priceUpdateFrequency" />
              </div>
              <div className="flex items-center gap-4">
                <input
                  type="range"
                  min="100"
                  max="10000"
                  step="100"
                  value={performanceSettings.priceUpdateFrequency}
                  onChange={(e) => updateSettings('performance', { 
                    priceUpdateFrequency: parseInt(e.target.value) 
                  })}
                  disabled={isSettingLocked('performance', 'priceUpdateFrequency')}
                  className={cn(
                    "flex-1 h-2 bg-card-interactive rounded-lg appearance-none",
                    isSettingLocked('performance', 'priceUpdateFrequency') 
                      ? "cursor-not-allowed opacity-50" 
                      : "cursor-pointer"
                  )}
                />
                <div className="min-w-[80px] text-sm text-muted-foreground">
                  {performanceSettings.priceUpdateFrequency}ms
                </div>
              </div>
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                <span>100ms</span>
                <span>1s</span>
                <span>10s</span>
              </div>
            </div>
            
            <label className="flex items-center justify-between">
              <div>
                <span className="text-sm text-foreground">Auto-refresh data</span>
                <p className="text-xs text-muted-foreground mt-1">
                  Automatically refresh data at regular intervals
                </p>
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => updateSettings('performance', { 
                    autoRefreshEnabled: !performanceSettings.autoRefreshEnabled 
                  })}
                  disabled={isSettingLocked('performance', 'autoRefreshEnabled')}
                  className={cn(
                    "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
                    isSettingLocked('performance', 'autoRefreshEnabled')
                      ? "opacity-50 cursor-not-allowed"
                      : performanceSettings.autoRefreshEnabled ? "bg-primary" : "bg-muted"
                  )}
                >
                  <span
                    className={cn(
                      "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                      performanceSettings.autoRefreshEnabled ? "translate-x-6" : "translate-x-1"
                    )}
                  />
                </button>
                <LockButton category="performance" settingKey="autoRefreshEnabled" />
              </div>
            </label>
          </div>
        </div>
      </div>
      
      {/* Caching Settings */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
            <Database className="w-4 h-4 text-green-500" />
            Caching & Storage
          </h4>
          
          <div className="space-y-4">
            <label className="flex items-center justify-between">
              <div>
                <span className="text-sm text-foreground">Enable caching</span>
                <p className="text-xs text-muted-foreground mt-1">
                  Cache data locally to improve performance
                </p>
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => updateSettings('performance', { 
                    cacheEnabled: !performanceSettings.cacheEnabled 
                  })}
                  disabled={isSettingLocked('performance', 'cacheEnabled')}
                  className={cn(
                    "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
                    isSettingLocked('performance', 'cacheEnabled')
                      ? "opacity-50 cursor-not-allowed"
                      : performanceSettings.cacheEnabled ? "bg-primary" : "bg-muted"
                  )}
                >
                  <span
                    className={cn(
                      "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                      performanceSettings.cacheEnabled ? "translate-x-6" : "translate-x-1"
                    )}
                  />
                </button>
                <LockButton category="performance" settingKey="cacheEnabled" />
              </div>
            </label>
            
            {performanceSettings.cacheEnabled && (
              <>
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm text-foreground">
                      Cache Duration (seconds)
                    </label>
                    <LockButton category="performance" settingKey="cacheDuration" />
                  </div>
                  <input
                    type="number"
                    min="60"
                    max="3600"
                    step="60"
                    value={performanceSettings.cacheDuration}
                    onChange={(e) => updateSettings('performance', { 
                      cacheDuration: Math.min(3600, Math.max(60, parseInt(e.target.value) || 60))
                    })}
                    disabled={isSettingLocked('performance', 'cacheDuration')}
                    className={cn(
                      "w-full px-3 py-2 bg-card-interactive border border-border rounded-md text-sm",
                      isSettingLocked('performance', 'cacheDuration') && "opacity-50 cursor-not-allowed"
                    )}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    How long to keep data in cache (1 minute to 1 hour)
                  </p>
                </div>
                
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm text-foreground">
                      Data Retention Days
                    </label>
                    <LockButton category="performance" settingKey="dataRetentionDays" />
                  </div>
                  <input
                    type="number"
                    min="1"
                    max="365"
                    value={performanceSettings.dataRetentionDays}
                    onChange={(e) => updateSettings('performance', { 
                      dataRetentionDays: Math.min(365, Math.max(1, parseInt(e.target.value) || 1))
                    })}
                    disabled={isSettingLocked('performance', 'dataRetentionDays')}
                    className={cn(
                      "w-full px-3 py-2 bg-card-interactive border border-border rounded-md text-sm",
                      isSettingLocked('performance', 'dataRetentionDays') && "opacity-50 cursor-not-allowed"
                    )}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Keep historical data for analysis
                  </p>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
      
      {/* Network Settings */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
            <Network className="w-4 h-4 text-purple-500" />
            Network & API
          </h4>
          
          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm text-foreground">
                  Maximum Concurrent Requests
                </label>
                <LockButton category="performance" settingKey="maxConcurrentRequests" />
              </div>
              <div className="flex items-center gap-4">
                <input
                  type="range"
                  min="1"
                  max="50"
                  value={performanceSettings.maxConcurrentRequests}
                  onChange={(e) => updateSettings('performance', { 
                    maxConcurrentRequests: parseInt(e.target.value) 
                  })}
                  disabled={isSettingLocked('performance', 'maxConcurrentRequests')}
                  className={cn(
                    "flex-1 h-2 bg-card-interactive rounded-lg appearance-none",
                    isSettingLocked('performance', 'maxConcurrentRequests') 
                      ? "cursor-not-allowed opacity-50" 
                      : "cursor-pointer"
                  )}
                />
                <div className="min-w-[60px] text-sm text-muted-foreground">
                  {performanceSettings.maxConcurrentRequests}
                </div>
              </div>
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                <span>1</span>
                <span>25</span>
                <span>50</span>
              </div>
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm text-foreground">
                  Rate Limit Buffer (%)
                </label>
                <LockButton category="performance" settingKey="rateLimitBuffer" />
              </div>
              <div className="flex items-center gap-4">
                <input
                  type="range"
                  min="0"
                  max="100"
                  step="5"
                  value={performanceSettings.rateLimitBuffer}
                  onChange={(e) => updateSettings('performance', { 
                    rateLimitBuffer: parseInt(e.target.value) 
                  })}
                  disabled={isSettingLocked('performance', 'rateLimitBuffer')}
                  className={cn(
                    "flex-1 h-2 bg-card-interactive rounded-lg appearance-none",
                    isSettingLocked('performance', 'rateLimitBuffer') 
                      ? "cursor-not-allowed opacity-50" 
                      : "cursor-pointer"
                  )}
                />
                <div className="min-w-[60px] text-sm text-muted-foreground">
                  {performanceSettings.rateLimitBuffer}%
                </div>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Safety buffer to avoid hitting API rate limits
              </p>
            </div>
            
            <label className="flex items-center justify-between">
              <div>
                <span className="text-sm text-foreground">Low Bandwidth Mode</span>
                <p className="text-xs text-muted-foreground mt-1">
                  Reduce data usage for slower connections
                </p>
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => updateSettings('performance', { 
                    lowBandwidthMode: !performanceSettings.lowBandwidthMode 
                  })}
                  disabled={isSettingLocked('performance', 'lowBandwidthMode')}
                  className={cn(
                    "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
                    isSettingLocked('performance', 'lowBandwidthMode')
                      ? "opacity-50 cursor-not-allowed"
                      : performanceSettings.lowBandwidthMode ? "bg-primary" : "bg-muted"
                  )}
                >
                  <span
                    className={cn(
                      "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                      performanceSettings.lowBandwidthMode ? "translate-x-6" : "translate-x-1"
                    )}
                  />
                </button>
                <LockButton category="performance" settingKey="lowBandwidthMode" />
              </div>
            </label>
          </div>
        </div>
      </div>
      
      {/* Performance Metrics */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
            <Gauge className="w-4 h-4 text-cyan-500" />
            Performance Monitoring
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-card-interactive rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Wifi className="w-4 h-4 text-green-500" />
                <span className="text-sm font-medium text-foreground">Connection Status</span>
              </div>
              <div className="text-xs text-muted-foreground">
                WebSocket: Connected<br />
                Latency: ~45ms
              </div>
            </div>
            
            <div className="bg-card-interactive rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <HardDrive className="w-4 h-4 text-blue-500" />
                <span className="text-sm font-medium text-foreground">Cache Status</span>
              </div>
              <div className="text-xs text-muted-foreground">
                Size: 12.4 MB<br />
                Hit Rate: 87%
              </div>
            </div>
            
            <div className="bg-card-interactive rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <BarChart3 className="w-4 h-4 text-purple-500" />
                <span className="text-sm font-medium text-foreground">API Usage</span>
              </div>
              <div className="text-xs text-muted-foreground">
                Requests/min: 45<br />
                Rate Limit: 1000/min
              </div>
            </div>
            
            <div className="bg-card-interactive rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Timer className="w-4 h-4 text-orange-500" />
                <span className="text-sm font-medium text-foreground">Updates</span>
              </div>
              <div className="text-xs text-muted-foreground">
                Price updates: 1.2s<br />
                Auto-refresh: {performanceSettings.autoRefreshInterval}s
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Advanced Settings */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
            <Clock className="w-4 h-4 text-yellow-500" />
            Advanced Settings
          </h4>
          
          <div className="space-y-3">
            {performanceSettings.autoRefreshEnabled && (
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm text-foreground">
                    Auto-refresh Interval (seconds)
                  </label>
                  <LockButton category="performance" settingKey="autoRefreshInterval" />
                </div>
                <input
                  type="number"
                  min="10"
                  max="3600"
                  step="10"
                  value={performanceSettings.autoRefreshInterval}
                  onChange={(e) => updateSettings('performance', { 
                    autoRefreshInterval: Math.min(3600, Math.max(10, parseInt(e.target.value) || 10))
                  })}
                  disabled={isSettingLocked('performance', 'autoRefreshInterval')}
                  className={cn(
                    "w-full px-3 py-2 bg-card-interactive border border-border rounded-md text-sm",
                    isSettingLocked('performance', 'autoRefreshInterval') && "opacity-50 cursor-not-allowed"
                  )}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  How often to refresh data automatically
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}