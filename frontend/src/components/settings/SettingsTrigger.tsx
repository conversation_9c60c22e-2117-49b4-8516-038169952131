'use client'

import { useState, useEffect } from 'react'
import { Settings } from 'lucide-react'
import { useSettingsStore } from '@/stores/settingsStore'
import { SettingsModal } from './SettingsModal'
import { ClientOnly } from '../ClientOnly'
import { cn } from '@/lib/utils'

interface SettingsTriggerProps {
  className?: string
  showLabel?: boolean
  size?: 'sm' | 'md' | 'lg'
}

export function SettingsTrigger({ 
  className, 
  showLabel = false,
  size = 'md' 
}: SettingsTriggerProps) {
  const [isModalOpen, setIsModalOpen] = useState(false)
  
  const sizeClasses = {
    sm: 'p-1.5',
    md: 'p-2',
    lg: 'p-3'
  }
  
  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  }
  
  return (
    <>
      <button
        onClick={() => setIsModalOpen(true)}
        className={cn(
          "relative text-muted-foreground hover:text-foreground transition-colors rounded-lg hover:bg-card-interactive border border-transparent hover:border-elevated",
          sizeClasses[size],
          className
        )}
      >
        <Settings className={iconSizes[size]} />
        {showLabel && (
          <span className="ml-2 font-medium">Settings</span>
        )}
        <ClientOnly>
          <UnsavedChangesIndicator />
        </ClientOnly>
      </button>
      
      <ClientOnly>
        <SettingsModal 
          isOpen={isModalOpen} 
          onClose={() => setIsModalOpen(false)} 
        />
      </ClientOnly>
    </>
  )
}

function UnsavedChangesIndicator() {
  const hasUnsavedChanges = useSettingsStore((state) => state.hasUnsavedChanges)
  
  if (!hasUnsavedChanges) return null
  
  return (
    <span className="absolute -top-1 -right-1 w-2 h-2 bg-yellow-500 rounded-full" />
  )
}