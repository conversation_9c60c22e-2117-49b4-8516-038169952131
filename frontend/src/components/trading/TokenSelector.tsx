'use client'

import { useState, useCallback, useEffect, useMemo, memo, useRef } from 'react'
import { Check, ChevronDown, Plus, AlertCircle, Star, X, Pencil, Shield } from 'lucide-react'
import { cn, validateAndSanitizeTokenAddress, debounce, loadRecentTokensFromStorage, saveRecentTokensToStorage, addToRecentTokens, type RecentToken, loadCustomPopularTokensFromStorage, saveCustomPopularTokensToStorage, addToCustomPopularTokens, removeFromCustomPopularTokens, isTokenInCustomPopular, type CustomPopularToken, loadCustomTokenNamesFromStorage, saveCustomTokenNamesToStorage, setCustomTokenName, removeCustomTokenName, getCustomTokenName, type CustomTokenName, loadUserVerifiedTokensFromStorage, saveUserVerifiedTokensToStorage, addUserVerifiedToken, removeUserVerifiedToken, isTokenUserVerified, getUserTokenVerification, type UserVerifiedToken } from '@/lib/utils'
import { createDebouncedSaver } from '@/lib/debounced-storage'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { StableDropdown } from '@/components/ui/stable-dropdown'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '@/components/ui/command'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog'

interface Token {
  address: string
  symbol: string
  name: string
  logoURI?: string
  decimals: number
  verified?: boolean
}

interface TokenSelectorProps {
  selectedToken?: Token
  onTokenSelect: (token: Token) => void
  placeholder?: string
  className?: string
  showAddressInput?: boolean
}

const DEFAULT_POPULAR_TOKENS: Token[] = [
  {
    address: 'So11111111111111111111111111111111111111112',
    symbol: 'SOL',
    name: 'Solana',
    decimals: 9,
    verified: true
  },
  {
    address: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
    symbol: 'USDC',
    name: 'USD Coin',
    decimals: 6,
    verified: true
  },
  {
    address: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
    symbol: 'USDT',
    name: 'Tether USD',
    decimals: 6,
    verified: true
  },
  {
    address: '4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R',
    symbol: 'RAY',
    name: 'Raydium',
    decimals: 6,
    verified: true
  },
  {
    address: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
    symbol: 'BONK',
    name: 'Bonk',
    decimals: 5,
    verified: true
  },
  {
    address: 'EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm',
    symbol: 'WIF',
    name: 'dogwifhat',
    decimals: 6,
    verified: true
  }
]

// Custom hook for managing recent tokens with debounced saving
function useRecentTokens() {
  const [recentTokens, setRecentTokens] = useState<RecentToken[]>(() => {
    // Initialize with localStorage data
    return loadRecentTokensFromStorage()
  })

  // Create debounced saver for recent tokens
  const debouncedSaveRecentTokens = useMemo(
    () => createDebouncedSaver('memetrader-recent-tokens', 300),
    []
  )

  // Function to add a token to recent tokens
  const addRecentToken = useCallback((token: Token) => {
    setRecentTokens(prevTokens => {
      const updatedTokens = addToRecentTokens(token, prevTokens)
      // Use debounced save instead of immediate save
      debouncedSaveRecentTokens(updatedTokens)
      return updatedTokens
    })
  }, [debouncedSaveRecentTokens])

  return { recentTokens, addRecentToken }
}

// Custom hook for managing user verified tokens
function useUserVerifiedTokens() {
  const [userVerifiedTokens, setUserVerifiedTokens] = useState<Record<string, UserVerifiedToken>>(() => {
    // Initialize with localStorage data
    return loadUserVerifiedTokensFromStorage()
  })

  // Load user verified tokens from localStorage on mount and listen for changes
  useEffect(() => {
    const loadVerifications = () => {
      setUserVerifiedTokens(loadUserVerifiedTokensFromStorage())
    }
    
    // Listen for storage changes to sync across tabs/components
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'memetrader-user-verified-tokens') {
        loadVerifications()
      }
    }
    
    window.addEventListener('storage', handleStorageChange)
    
    // Also listen for custom events from the same tab
    const handleUserVerifiedTokensChange = () => {
      loadVerifications()
    }
    
    window.addEventListener('userVerifiedTokensChanged', handleUserVerifiedTokensChange)
    
    return () => {
      window.removeEventListener('storage', handleStorageChange)
      window.removeEventListener('userVerifiedTokensChanged', handleUserVerifiedTokensChange)
    }
  }, [])

  // Function to add user verification for a token
  const addUserVerification = useCallback((address: string, userNotes: string = '') => {
    setUserVerifiedTokens(prevVerifications => {
      const updatedVerifications = addUserVerifiedToken(address, userNotes, prevVerifications)
      saveUserVerifiedTokensToStorage(updatedVerifications)
      return updatedVerifications
    })
  }, [])

  // Function to remove user verification for a token
  const removeUserVerification = useCallback((address: string) => {
    setUserVerifiedTokens(prevVerifications => {
      const updatedVerifications = removeUserVerifiedToken(address, prevVerifications)
      saveUserVerifiedTokensToStorage(updatedVerifications)
      return updatedVerifications
    })
  }, [])

  // Function to check if token is user verified
  const isUserVerified = useCallback((address: string): boolean => {
    return isTokenUserVerified(address, userVerifiedTokens)
  }, [userVerifiedTokens])

  // Function to get user verification info
  const getUserVerification = useCallback((address: string): UserVerifiedToken | undefined => {
    return getUserTokenVerification(address, userVerifiedTokens)
  }, [userVerifiedTokens])

  return { userVerifiedTokens, addUserVerification, removeUserVerification, isUserVerified, getUserVerification }
}

// Custom hook for managing custom token names
function useCustomTokenNames() {
  const [customTokenNames, setCustomTokenNames] = useState<Record<string, CustomTokenName>>(() => {
    // Initialize with localStorage data
    return loadCustomTokenNamesFromStorage()
  })

  // Function to set a custom name for a token
  const setTokenName = useCallback((address: string, customName: string, originalSymbol: string) => {
    setCustomTokenNames(prevNames => {
      const updatedNames = setCustomTokenName(address, customName, originalSymbol, prevNames)
      saveCustomTokenNamesToStorage(updatedNames)
      return updatedNames
    })
  }, [])

  // Function to remove a custom name for a token
  const removeTokenName = useCallback((address: string) => {
    setCustomTokenNames(prevNames => {
      const updatedNames = removeCustomTokenName(address, prevNames)
      saveCustomTokenNamesToStorage(updatedNames)
      return updatedNames
    })
  }, [])

  // Function to get custom name for a token
  const getTokenCustomName = useCallback((address: string): CustomTokenName | undefined => {
    return getCustomTokenName(address, customTokenNames)
  }, [customTokenNames])

  return { customTokenNames, setTokenName, removeTokenName, getTokenCustomName }
}

// Custom hook for managing custom popular tokens
function useCustomPopularTokens() {
  const [customPopularTokens, setCustomPopularTokens] = useState<CustomPopularToken[]>(() => {
    // Initialize with localStorage data
    return loadCustomPopularTokensFromStorage()
  })
  const [feedback, setFeedback] = useState<{type: 'success' | 'error', message: string} | null>(null)

  // Create debounced saver for custom popular tokens
  const debouncedSavePopularTokens = useMemo(
    () => createDebouncedSaver('memetrader-custom-popular-tokens', 300),
    []
  )

  // Function to add a token to custom popular tokens
  const addToPopular = useCallback((token: Token) => {
    setCustomPopularTokens(prevTokens => {
      // Check if token already exists
      if (isTokenInCustomPopular(token.address, prevTokens)) {
        setFeedback({ type: 'error', message: 'Token is already in popular list' })
        setTimeout(() => setFeedback(null), 3000)
        return prevTokens
      }

      const updatedTokens = addToCustomPopularTokens(token, prevTokens)
      // Use debounced save instead of immediate save
      debouncedSavePopularTokens(updatedTokens)
      
      setFeedback({ type: 'success', message: `${token.symbol} added to popular tokens!` })
      setTimeout(() => setFeedback(null), 3000)
      
      return updatedTokens
    })
  }, [debouncedSavePopularTokens])

  // Function to remove a token from custom popular tokens
  const removeFromPopular = useCallback((tokenAddress: string, tokenSymbol: string) => {
    setCustomPopularTokens(prevTokens => {
      const updatedTokens = removeFromCustomPopularTokens(tokenAddress, prevTokens)
      // Use debounced save instead of immediate save
      debouncedSavePopularTokens(updatedTokens)
      
      setFeedback({ type: 'success', message: `${tokenSymbol} removed from popular tokens!` })
      setTimeout(() => setFeedback(null), 3000)
      
      return updatedTokens
    })
  }, [debouncedSavePopularTokens])

  // Function to get merged popular tokens (default + custom) - memoized to prevent recalculations
  const getAllPopularTokens = useCallback((): (Token & {isCustom?: boolean})[] => {
    const defaultTokenAddresses = new Set(DEFAULT_POPULAR_TOKENS.map(t => t.address))
    
    // Filter out custom tokens that duplicate default ones
    const uniqueCustomTokens = customPopularTokens.filter(token => 
      !defaultTokenAddresses.has(token.address)
    )

    // Merge custom tokens first, then default tokens
    return [
      ...uniqueCustomTokens.map(token => ({ ...token, isCustom: true })),
      ...DEFAULT_POPULAR_TOKENS.map(token => ({ ...token, isCustom: false }))
    ]
  }, [customPopularTokens])

  // Function to check if a token is already in popular list
  const isInPopular = useCallback((tokenAddress: string): boolean => {
    const allPopular = getAllPopularTokens()
    return allPopular.some(token => token.address === tokenAddress)
  }, [getAllPopularTokens])

  return { 
    customPopularTokens, 
    addToPopular, 
    removeFromPopular,
    getAllPopularTokens, 
    isInPopular,
    feedback 
  }
}

const TokenDisplay = memo(function TokenDisplay({ 
  token, 
  showCheck = false, 
  isCustom = false, 
  onRemove,
  showRemoveButton = false,
  customName,
  onEditName,
  showEditButton = false,
  userVerified = false,
  onToggleUserVerification,
  showVerificationButton = false
}: { 
  token: Token; 
  showCheck?: boolean; 
  isCustom?: boolean;
  onRemove?: (tokenAddress: string, tokenSymbol: string) => void;
  showRemoveButton?: boolean;
  customName?: CustomTokenName;
  onEditName?: () => void;
  showEditButton?: boolean;
  userVerified?: boolean;
  onToggleUserVerification?: () => void;
  showVerificationButton?: boolean;
}) {
  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation() // Prevent token selection when clicking remove
    if (onRemove) {
      onRemove(token.address, token.symbol)
    }
  }

  return (
    <div className="flex items-center space-x-2 w-full group">
      <div className="relative">
        <div className="w-7 h-7 rounded-full bg-gradient-to-br from-primary/80 to-secondary/80 flex items-center justify-center text-white text-xs font-bold shadow-sm">
          {token.symbol.charAt(0)}
        </div>
        {token.verified && (
          <div className="absolute -top-0.5 -right-0.5 w-2.5 h-2.5 rounded-full bg-gradient-to-r from-green-400 to-green-500 flex items-center justify-center" title="Officially Verified">
            <Check className="w-1.5 h-1.5 text-white" />
          </div>
        )}
        {!token.verified && userVerified && (
          <div className="absolute -top-0.5 -right-0.5 w-2.5 h-2.5 rounded-full bg-gradient-to-r from-blue-400 to-blue-500 flex items-center justify-center" title="User Verified">
            <Shield className="w-1.5 h-1.5 text-white" />
          </div>
        )}
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-center space-x-1.5">
          <span className="text-sm font-bold">
            {customName ? customName.customName : token.symbol}
          </span>
          {customName && (
            <span className="text-xs text-muted-foreground">({token.symbol})</span>
          )}
          {isCustom && (
            <Star className="w-2.5 h-2.5 text-amber-500 fill-current" />
          )}
          <div className="w-0.5 h-0.5 rounded-full bg-primary/60"></div>
        </div>
        <span className="text-xs text-muted-foreground font-medium truncate">{token.name}</span>
      </div>
      <div className="flex items-center space-x-1">
        {showCheck && (
          <Check className="w-3.5 h-3.5 text-primary" />
        )}
        {showEditButton && (
          <button
            onClick={(e) => {
              e.stopPropagation()
              if (onEditName) onEditName()
            }}
            className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 hover:bg-primary/10 rounded-full"
            title={`Edit name for ${token.symbol}`}
          >
            <Pencil className="w-3 h-3 text-primary hover:text-primary/80" />
          </button>
        )}
        {showVerificationButton && !token.verified && (
          <button
            onClick={(e) => {
              e.stopPropagation()
              if (onToggleUserVerification) onToggleUserVerification()
            }}
            className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 hover:bg-blue-500/10 rounded-full"
            title={userVerified ? `Remove verification for ${token.symbol}` : `Mark ${token.symbol} as verified`}
          >
            <Shield className={cn(
              "w-3 h-3 transition-colors",
              userVerified 
                ? "text-blue-500 hover:text-blue-600" 
                : "text-muted-foreground hover:text-blue-500"
            )} />
          </button>
        )}
        {showRemoveButton && isCustom && (
          <button
            onClick={handleRemove}
            className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 hover:bg-destructive/10 rounded-full"
            title={`Remove ${token.symbol} from popular tokens`}
          >
            <X className="w-3 h-3 text-destructive hover:text-destructive/80" />
          </button>
        )}
      </div>
    </div>
  )
})

function AddressInput({ 
  onValidate, 
  onAddToPopular, 
  isInPopular,
  onSetCustomName
}: { 
  onValidate: (token: Token) => void
  onAddToPopular: (token: Token) => void
  isInPopular: (address: string) => boolean
  onSetCustomName: (address: string, customName: string, originalSymbol: string) => void
}) {
  const [address, setAddress] = useState('')
  const [isValidating, setIsValidating] = useState(false)
  const [error, setError] = useState('')
  const [validationStatus, setValidationStatus] = useState<'idle' | 'validating' | 'valid' | 'invalid'>('idle')
  const [validatedToken, setValidatedToken] = useState<Token | null>(null)
  const [isAddingToPopular, setIsAddingToPopular] = useState(false)
  const [customName, setCustomName] = useState('')
  const [showCustomNameInput, setShowCustomNameInput] = useState(false)

  // Simulate token lookup - this would be replaced with actual API call
  const lookupToken = async (address: string): Promise<Token> => {
    await new Promise(resolve => setTimeout(resolve, 800))

    // Simulate potential API failure
    if (Math.random() < 0.1) {
      throw new Error('Token lookup failed. Please try again.')
    }

    return {
      address: address,
      symbol: 'UNVERIFIED',
      name: 'Unverified Token',
      decimals: 6,
      verified: false
    }
  }

  const performValidation = async (inputAddress: string) => {
    if (!inputAddress.trim()) {
      setValidationStatus('idle')
      setError('')
      setValidatedToken(null)
      return
    }

    setIsValidating(true)
    setValidationStatus('validating')
    setError('')
    setValidatedToken(null)

    try {
      const validation = validateAndSanitizeTokenAddress(inputAddress)

      if (!validation.isValid) {
        setValidationStatus('invalid')
        setError(validation.error || 'Invalid address format')
        return
      }

      // Proceed with token lookup
      const token = await lookupToken(validation.sanitizedAddress!)
      setValidationStatus('valid')
      setValidatedToken(token)

      // Don't auto-select, let user choose to add to popular or validate
    } catch (error) {
      setValidationStatus('invalid')
      setError(error instanceof Error ? error.message : 'Validation failed')
      setValidatedToken(null)
    } finally {
      setIsValidating(false)
    }
  }

  // Debounced validation for typing
  const debouncedValidation = useCallback(
    debounce((address: string) => performValidation(address), 500),
    []
  )

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setAddress(value)

    if (value.trim()) {
      debouncedValidation(value)
    } else {
      setValidationStatus('idle')
      setError('')
    }
  }

  // Handle paste event for automatic validation
  const handlePaste = async (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault()

    try {
      const pastedText = e.clipboardData.getData('text/plain')
      setAddress(pastedText)

      // Immediately validate pasted content
      if (pastedText.trim()) {
        await performValidation(pastedText)
      }
    } catch (error) {
      console.error('Failed to handle paste:', error)
    }
  }

  // Handle manual paste button
  const handleManualPaste = async () => {
    try {
      const text = await navigator.clipboard.readText()
      setAddress(text)

      if (text.trim()) {
        await performValidation(text)
      }
    } catch (error) {
      console.error('Failed to paste:', error)
      setError('Failed to access clipboard')
    }
  }

  // Handle adding token to popular list
  const handleAddToPopular = async () => {
    if (!validatedToken) return

    setIsAddingToPopular(true)
    try {
      onAddToPopular(validatedToken)
    } catch (error) {
      console.error('Failed to add to popular:', error)
    } finally {
      setIsAddingToPopular(false)
    }
  }

  // Handle using the validated token
  const handleUseToken = () => {
    if (!validatedToken) return
    
    // Save custom name if provided
    if (customName.trim()) {
      onSetCustomName(validatedToken.address, customName, validatedToken.symbol)
    }
    
    onValidate(validatedToken)
    setAddress('')
    setValidationStatus('idle')
    setValidatedToken(null)
    setCustomName('')
    setShowCustomNameInput(false)
  }

  const getInputClassName = () => {
    const baseClass = "flex-1 transition-all duration-200"
    switch (validationStatus) {
      case 'validating':
        return cn(baseClass, "border-blue-300 ring-1 ring-blue-200")
      case 'valid':
        return cn(baseClass, "border-green-300 ring-1 ring-green-200")
      case 'invalid':
        return cn(baseClass, "border-red-300 ring-1 ring-red-200")
      default:
        return baseClass
    }
  }

  const getStatusIcon = () => {
    switch (validationStatus) {
      case 'validating':
        return <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full" />
      case 'valid':
        return <Check className="w-4 h-4 text-green-500" />
      case 'invalid':
        return <AlertCircle className="w-4 h-4 text-red-500" />
      default:
        return null
    }
  }

  return (
    <div className="p-2 space-y-2 border-t">
      <div className="flex items-center space-x-1.5">
        <Plus className="w-3.5 h-3.5 text-muted-foreground" />
        <span className="text-sm font-medium">Add Custom Token</span>
      </div>

      <div className="space-y-1.5">
        <div className="flex space-x-2 items-center">
          <div className="relative flex-1">
            <Input
              placeholder="Paste or type Solana token address..."
              value={address}
              onChange={handleInputChange}
              onPaste={handlePaste}
              className={getInputClassName()}
              disabled={isValidating}
            />
            {getStatusIcon() && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                {getStatusIcon()}
              </div>
            )}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleManualPaste}
            disabled={isValidating}
          >
            Paste
          </Button>
        </div>

        {error && (
          <div className="flex items-center space-x-1 text-xs text-destructive">
            <AlertCircle className="w-3 h-3" />
            <span>{error}</span>
          </div>
        )}

        {validationStatus === 'validating' && (
          <div className="text-xs text-muted-foreground
          flex items-center space-x-1">
            <div className="animate-spin w-3 h-3 border border-current border-t-transparent rounded-full" />
            <span>Validating address...</span>
          </div>
        )}

        {validationStatus === 'valid' && validatedToken && !error && (
          <div className="space-y-2">
            <div className="text-xs text-green-600 flex items-center space-x-1">
              <Check className="w-3 h-3" />
              <span>Token found: {validatedToken.symbol} - {validatedToken.name}</span>
            </div>
            
            {showCustomNameInput && (
              <div className="space-y-1">
                <label className="text-xs text-muted-foreground">Custom Name (Optional)</label>
                <Input
                  placeholder={`Enter custom name for ${validatedToken.symbol}`}
                  value={customName}
                  onChange={(e) => setCustomName(e.target.value)}
                  className="h-8 text-sm"
                />
              </div>
            )}
            
            <div className="flex space-x-2">
              <Button
                onClick={handleUseToken}
                size="sm"
                className="flex-1"
              >
                Use Token {customName.trim() && `as "${customName}"`}
              </Button>
              
              {!showCustomNameInput && (
                <Button
                  onClick={() => setShowCustomNameInput(true)}
                  variant="outline"
                  size="sm"
                  className="flex items-center space-x-1"
                >
                  <Pencil className="w-3 h-3" />
                  <span>Name</span>
                </Button>
              )}
              
              {!isInPopular(validatedToken.address) && (
                <Button
                  onClick={handleAddToPopular}
                  disabled={isAddingToPopular}
                  variant="outline"
                  size="sm"
                  className="flex items-center space-x-1"
                >
                  <Star className="w-3 h-3" />
                  <span>{isAddingToPopular ? 'Adding...' : 'Popular'}</span>
                </Button>
              )}
              
              {isInPopular(validatedToken.address) && (
                <div className="flex items-center space-x-1 text-xs text-muted-foreground px-2">
                  <Star className="w-3 h-3 fill-current" />
                  <span>In popular</span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

// Edit Token Name Dialog Component
function EditTokenNameDialog({
  isOpen,
  onClose,
  token,
  currentCustomName,
  onSave,
  userVerified = false,
  onToggleUserVerification
}: {
  isOpen: boolean
  onClose: () => void
  token: Token | null
  currentCustomName?: CustomTokenName
  onSave: (address: string, customName: string, originalSymbol: string) => void
  userVerified?: boolean
  onToggleUserVerification?: () => void
}) {
  const [customName, setCustomName] = useState('')

  useEffect(() => {
    if (currentCustomName) {
      setCustomName(currentCustomName.customName)
    } else {
      setCustomName('')
    }
  }, [currentCustomName])

  const handleSave = () => {
    if (token && customName.trim()) {
      onSave(token.address, customName, token.symbol)
      onClose()
    }
  }

  const handleRemove = () => {
    if (token && currentCustomName) {
      onSave(token.address, '', token.symbol) // Empty name removes it
      onClose()
    }
  }

  if (!token) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Token Name</DialogTitle>
          <DialogDescription>
            Give this token a custom name for easier identification.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <label htmlFor="token-info" className="text-sm font-medium">
              Token Information
            </label>
            <div className="p-3 rounded-lg bg-muted">
              <TokenDisplay token={token} userVerified={userVerified} />
            </div>
          </div>
          <div className="grid gap-2">
            <label htmlFor="custom-name" className="text-sm font-medium">
              Custom Name
            </label>
            <Input
              id="custom-name"
              placeholder={`Enter custom name for ${token.symbol}`}
              value={customName}
              onChange={(e) => setCustomName(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleSave()
                }
              }}
            />
            <p className="text-xs text-muted-foreground">
              This name will be displayed instead of {token.symbol} in the token selector.
            </p>
          </div>
          
          {!token.verified && onToggleUserVerification && (
            <div className="grid gap-2">
              <label className="text-sm font-medium">
                Token Verification
              </label>
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-muted/50">
                <Shield className={cn(
                  "w-5 h-5",
                  userVerified ? "text-blue-500" : "text-muted-foreground"
                )} />
                <div className="flex-1">
                  <div className="text-sm font-medium">
                    {userVerified ? "User Verified" : "Mark as Verified"}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {userVerified 
                      ? "You have marked this token as trusted" 
                      : "Mark this token as verified if you trust it"
                    }
                  </div>
                </div>
                <Button
                  variant={userVerified ? "destructive" : "default"}
                  size="sm"
                  onClick={onToggleUserVerification}
                >
                  {userVerified ? "Remove" : "Verify"}
                </Button>
              </div>
            </div>
          )}
        </div>
        <DialogFooter>
          {currentCustomName && (
            <Button
              variant="destructive"
              onClick={handleRemove}
              className="mr-auto"
            >
              Remove Custom Name
            </Button>
          )}
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={!customName.trim()}>
            Save Name
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export function TokenSelector({
  selectedToken,
  onTokenSelect,
  placeholder = "Select token",
  className,
  showAddressInput = false
}: TokenSelectorProps) {
  const [open, setOpen] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [tokenToEdit, setTokenToEdit] = useState<Token | null>(null)
  const buttonRef = useRef<HTMLButtonElement>(null)
  const { recentTokens, addRecentToken } = useRecentTokens()
  const { getAllPopularTokens, addToPopular, removeFromPopular, isInPopular, feedback } = useCustomPopularTokens()
  const { getTokenCustomName, setTokenName, removeTokenName } = useCustomTokenNames()
  const { isUserVerified, addUserVerification, removeUserVerification } = useUserVerifiedTokens()

  // Memoize popular tokens to prevent recalculations
  const popularTokens = useMemo(() => getAllPopularTokens(), [getAllPopularTokens])

  // Memoize trigger content to prevent anchor changes
  const triggerContent = useMemo(() => {
    if (selectedToken) {
      return (
        <TokenDisplay 
          token={selectedToken} 
          customName={getTokenCustomName(selectedToken.address)}
          userVerified={isUserVerified(selectedToken.address)}
        />
      )
    }
    return (
      <div className="flex items-center space-x-2">
        <div className="w-7 h-7 rounded-full bg-gradient-to-br from-muted to-muted/60 flex items-center justify-center border-2 border-dashed border-border">
          <Plus className="w-3.5 h-3.5 text-muted-foreground" />
        </div>
        <span className="text-sm font-medium text-muted-foreground">{placeholder}</span>
      </div>
    )
  }, [selectedToken, getTokenCustomName, isUserVerified, placeholder])

  const handleTokenSelect = useCallback((token: Token) => {
    addRecentToken(token) // Add to recent tokens
    onTokenSelect(token)
    setOpen(false)
  }, [addRecentToken, onTokenSelect])

  const handleCustomTokenValidate = useCallback((token: Token) => {
    addRecentToken(token) // Add to recent tokens
    onTokenSelect(token)
    setOpen(false)
  }, [addRecentToken, onTokenSelect])

  const handleEditTokenName = useCallback((token: Token) => {
    setTokenToEdit(token)
    setEditDialogOpen(true)
  }, [])

  const handleSaveCustomName = useCallback((address: string, customName: string, originalSymbol: string) => {
    if (customName.trim()) {
      setTokenName(address, customName, originalSymbol)
    } else {
      removeTokenName(address)
    }
  }, [setTokenName, removeTokenName])

  const handleToggleUserVerification = useCallback((token: Token) => {
    if (isUserVerified(token.address)) {
      removeUserVerification(token.address)
    } else {
      addUserVerification(token.address, `Manually verified ${token.symbol}`)
    }
  }, [isUserVerified, removeUserVerification, addUserVerification])

  return (
    <div className={cn("w-full", className)}>
      <StableDropdown
        open={open}
        onOpenChange={setOpen}
        trigger={
          <Button
            ref={buttonRef}
            variant="outline"
            role="combobox"
            className="w-full justify-between h-auto p-3 bg-gradient-to-r from-background/80 to-muted/20 border-border/50 hover:from-muted/40 hover:to-muted/20 hover:border-primary/30 transition-all duration-300 shadow-sm hover:shadow-md"
          >
            {triggerContent}
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        }
      >

        <div className="w-[90vw] max-w-md p-0">
          <Command>
            <CommandInput placeholder="Search tokens..." className="h-8" />
            
            {feedback && (
              <div className={cn(
                "px-3 py-2 text-xs flex items-center space-x-1 border-b",
                feedback.type === 'success' ? "text-green-600 bg-green-50 border-green-200" : "text-red-600 bg-red-50 border-red-200"
              )}>
                {feedback.type === 'success' ? (
                  <Check className="w-3 h-3" />
                ) : (
                  <AlertCircle className="w-3 h-3" />
                )}
                <span>{feedback.message}</span>
              </div>
            )}
            
            <CommandList className="max-h-[400px] overflow-y-auto">
              <CommandEmpty>No tokens found.</CommandEmpty>

              {recentTokens.length > 0 && (
                <>
                  <CommandGroup heading="Recent">
                    <div className="grid grid-cols-2 sm:grid-cols-2 gap-1.5 px-3 py-1.5">
                      {recentTokens.map((token) => (
                        <CommandItem
                          key={token.address}
                          value={`${token.symbol} ${token.name}`}
                          onSelect={() => handleTokenSelect(token)}
                          className="py-2 px-2 hover:bg-accent hover:text-accent-foreground rounded-md"
                        >
                          <TokenDisplay
                            token={token}
                            showCheck={selectedToken?.address === token.address}
                            isCustom={false}
                            customName={getTokenCustomName(token.address)}
                            onEditName={() => handleEditTokenName(token)}
                            showEditButton={true}
                            userVerified={isUserVerified(token.address)}
                            onToggleUserVerification={() => handleToggleUserVerification(token)}
                            showVerificationButton={true}
                          />
                        </CommandItem>
                      ))}
                    </div>
                  </CommandGroup>
                  <CommandSeparator />
                </>
              )}

              <CommandGroup heading="Popular Tokens">
                <div className="grid grid-cols-2 sm:grid-cols-2 gap-1.5 px-3 py-1.5">
                  {popularTokens.map((token) => (
                    <CommandItem
                      key={token.address}
                      value={`${token.symbol} ${token.name}`}
                      onSelect={() => handleTokenSelect(token)}
                      className="py-2 px-2 hover:bg-accent hover:text-accent-foreground rounded-md"
                    >
                      <TokenDisplay
                        token={token}
                        showCheck={selectedToken?.address === token.address}
                        isCustom={token.isCustom || false}
                        onRemove={removeFromPopular}
                        showRemoveButton={true}
                        customName={getTokenCustomName(token.address)}
                        onEditName={() => handleEditTokenName(token)}
                        showEditButton={true}
                        userVerified={isUserVerified(token.address)}
                        onToggleUserVerification={() => handleToggleUserVerification(token)}
                        showVerificationButton={true}
                      />
                    </CommandItem>
                  ))}
                </div>
              </CommandGroup>

              {showAddressInput && (
                <>
                  <CommandSeparator />
                  <AddressInput 
                    onValidate={handleCustomTokenValidate} 
                    onAddToPopular={addToPopular}
                    isInPopular={isInPopular}
                    onSetCustomName={setTokenName}
                  />
                </>
              )}
            </CommandList>
          </Command>
        </div>
      </StableDropdown>
      
      <EditTokenNameDialog
        isOpen={editDialogOpen}
        onClose={() => {
          setEditDialogOpen(false)
          setTokenToEdit(null)
        }}
        token={tokenToEdit}
        currentCustomName={tokenToEdit ? getTokenCustomName(tokenToEdit.address) : undefined}
        onSave={handleSaveCustomName}
        userVerified={tokenToEdit ? isUserVerified(tokenToEdit.address) : false}
        onToggleUserVerification={tokenToEdit ? () => handleToggleUserVerification(tokenToEdit) : undefined}
      />
    </div>
  )
}
