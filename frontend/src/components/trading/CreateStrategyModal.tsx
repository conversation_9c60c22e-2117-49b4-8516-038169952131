'use client'

import { useState, useEffect } from 'react'
import { X, Plus, GripVertical, Trash2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'

interface ProfitTarget {
  id: string
  target: number
  sellPercentage: number
  color: string
}

interface Strategy {
  id: string
  name: string
  description: string
  usageCount: number
  winRate: number
  isDefault: boolean
  locked: boolean
  stopLoss: {
    percentage: number
  } | null
  profitTargets: {
    target: number
    sellPercentage: number
  }[]
  trailingStop: {
    percentage: number
  } | null
  moonBag: {
    percentage: number
    targetGain: number
  } | null
}

interface CreateStrategyModalProps {
  isOpen: boolean
  onClose: () => void
  onCreateStrategy: (strategy: Omit<Strategy, 'id' | 'usageCount' | 'winRate'>) => void
  editStrategy?: Strategy | null
  onEditComplete?: () => void
}

const colorOptions = [
  'bg-green-500',
  'bg-blue-500', 
  'bg-purple-500',
  'bg-orange-500',
  'bg-pink-500',
  'bg-indigo-500',
  'bg-teal-500',
  'bg-yellow-500'
]

export function CreateStrategyModal({
  isOpen,
  onClose,
  onCreateStrategy,
  editStrategy,
  onEditComplete
}: CreateStrategyModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    stopLossPercentage: 15,
    trailingStopPercentage: 15,
    moonBagPercentage: 25,
    targetGainPercentage: 500,
    locked: false,
    isDefault: false
  })

  const [profitTargets, setProfitTargets] = useState<ProfitTarget[]>([
    {
      id: '1',
      target: 50,
      sellPercentage: 25,
      color: colorOptions[0]
    }
  ])

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null)

  // Initialize form with edit data
  useEffect(() => {
    if (editStrategy) {
      setFormData({
        name: editStrategy.name,
        description: editStrategy.description,
        stopLossPercentage: editStrategy.stopLoss?.percentage || 15,
        trailingStopPercentage: editStrategy.trailingStop?.percentage || 15,
        moonBagPercentage: editStrategy.moonBag?.percentage || 25,
        targetGainPercentage: editStrategy.moonBag?.targetGain || 500,
        locked: editStrategy.locked,
        isDefault: editStrategy.isDefault
      })

      setProfitTargets(editStrategy.profitTargets.map((target, index) => ({
        id: (index + 1).toString(),
        target: target.target,
        sellPercentage: target.sellPercentage,
        color: colorOptions[index % colorOptions.length]
      })))
    } else {
      // Reset form for new strategy
      setFormData({
        name: '',
        description: '',
        stopLossPercentage: 15,
        trailingStopPercentage: 15,
        moonBagPercentage: 25,
        targetGainPercentage: 500,
        locked: false,
        isDefault: false
      })
      setProfitTargets([{
        id: '1',
        target: 50,
        sellPercentage: 25,
        color: colorOptions[0]
      }])
    }
    setErrors({})
  }, [editStrategy, isOpen])

  const handleAddMilestone = () => {
    const newId = (profitTargets.length + 1).toString()
    const newColor = colorOptions[profitTargets.length % colorOptions.length]
    setProfitTargets(prev => [...prev, {
      id: newId,
      target: 100,
      sellPercentage: 25,
      color: newColor
    }])
  }

  const handleRemoveMilestone = (id: string) => {
    setProfitTargets(prev => prev.filter(target => target.id !== id))
  }

  const handleMilestoneChange = (id: string, field: 'target' | 'sellPercentage', value: number) => {
    setProfitTargets(prev => prev.map(target =>
      target.id === id ? { ...target, [field]: value } : target
    ))
  }

  const handleDragStart = (index: number) => {
    setDraggedIndex(index)
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault()
    if (draggedIndex === null) return

    const newTargets = [...profitTargets]
    const draggedItem = newTargets[draggedIndex]
    newTargets.splice(draggedIndex, 1)
    newTargets.splice(dropIndex, 0, draggedItem)
    
    setProfitTargets(newTargets)
    setDraggedIndex(null)
  }

  // Calculate investment preview
  const investmentAmount = 1000
  const totalSellPercentage = profitTargets.reduce((sum, target) => sum + target.sellPercentage, 0)
  const remainingPercentage = Math.max(0, 100 - totalSellPercentage)
  
  const previewSold = profitTargets.reduce((sum, target) => {
    return sum + (investmentAmount * target.sellPercentage / 100)
  }, 0)
  
  const previewMoonBag = investmentAmount * (formData.moonBagPercentage / 100)

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Strategy name is required'
    }

    if (formData.stopLossPercentage < 1 || formData.stopLossPercentage > 50) {
      newErrors.stopLoss = 'Stop loss must be between 1% and 50%'
    }

    if (totalSellPercentage > 100) {
      newErrors.profitTargets = 'Total sell percentage cannot exceed 100%'
    }

    if (profitTargets.some(target => target.target <= 0)) {
      newErrors.profitTargets = 'All profit targets must be positive'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = () => {
    if (!validateForm()) return

    try {
      const strategy = {
        name: formData.name,
        description: formData.description,
        locked: formData.locked,
        isDefault: formData.isDefault,
        stopLoss: { percentage: formData.stopLossPercentage },
        profitTargets: profitTargets.map(target => ({
          target: target.target,
          sellPercentage: target.sellPercentage
        })),
        trailingStop: { percentage: formData.trailingStopPercentage },
        moonBag: formData.moonBagPercentage > 0 ? {
          percentage: formData.moonBagPercentage,
          targetGain: formData.targetGainPercentage
        } : null
      }

      onCreateStrategy(strategy)
      onClose()
      if (editStrategy && onEditComplete) {
        onEditComplete()
      }
    } catch (error) {
      console.error('Error saving strategy:', error)
      setErrors({ submit: 'Failed to save strategy. Please try again.' })
    }
  }

  const handleClose = () => {
    setErrors({})
    onClose()
    if (editStrategy && onEditComplete) {
      onEditComplete()
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-card border border-border rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <div>
            <h2 className="text-2xl font-bold">
              {editStrategy ? 'Edit Strategy' : 'Create New Strategy'}
            </h2>
            <p className="text-muted-foreground mt-1">
              Build a custom exit strategy for your trades.
            </p>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClose}
            className="rounded-full w-8 h-8 p-0"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        <div className="p-6 space-y-6">
          {/* Strategy Name */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Name</label>
            <Input
              placeholder="e.g., Conservative, Aggressive"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className={cn("", errors.name && "border-red-500")}
            />
            {errors.name && (
              <p className="text-xs text-red-500">{errors.name}</p>
            )}
          </div>

          {/* Description */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Description</label>
            <Input
              placeholder="Brief description of the strategy"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            />
          </div>

          {/* Exit Milestones */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">Exit Milestones</label>
              <Button
                variant="outline"
                size="sm"
                onClick={handleAddMilestone}
                className="flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                Add Milestone
              </Button>
            </div>

            {errors.profitTargets && (
              <p className="text-xs text-red-500">{errors.profitTargets}</p>
            )}

            <div className="space-y-3">
              {profitTargets.map((target, index) => (
                <div
                  key={target.id}
                  draggable
                  onDragStart={() => handleDragStart(index)}
                  onDragOver={handleDragOver}
                  onDrop={(e) => handleDrop(e, index)}
                  className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg group cursor-move"
                >
                  <GripVertical className="w-4 h-4 text-muted-foreground" />
                  <div className={cn("w-3 h-3 rounded-full", target.color)} />
                  <span className="text-sm font-medium">Sell</span>
                  <Input
                    type="number"
                    value={target.sellPercentage}
                    onChange={(e) => handleMilestoneChange(target.id, 'sellPercentage', parseInt(e.target.value) || 0)}
                    className="w-20 h-8"
                  />
                  <span className="text-sm">% at +</span>
                  <Input
                    type="number"
                    value={target.target}
                    onChange={(e) => handleMilestoneChange(target.id, 'target', parseInt(e.target.value) || 0)}
                    className="w-20 h-8"
                  />
                  <span className="text-sm">% gain</span>
                  {profitTargets.length > 1 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveMilestone(target.id)}
                      className="ml-auto p-1 h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity text-red-500 hover:text-red-600"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Strategy Parameters */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Stop Loss %</label>
              <Input
                type="number"
                value={formData.stopLossPercentage}
                onChange={(e) => setFormData(prev => ({ ...prev, stopLossPercentage: parseInt(e.target.value) || 0 }))}
                className={cn("", errors.stopLoss && "border-red-500")}
              />
              {errors.stopLoss && (
                <p className="text-xs text-red-500">{errors.stopLoss}</p>
              )}
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Trailing Stop %</label>
              <Input
                type="number"
                value={formData.trailingStopPercentage}
                onChange={(e) => setFormData(prev => ({ ...prev, trailingStopPercentage: parseInt(e.target.value) || 0 }))}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Moon Bag %</label>
              <Input
                type="number"
                value={formData.moonBagPercentage}
                onChange={(e) => setFormData(prev => ({ ...prev, moonBagPercentage: parseInt(e.target.value) || 0 }))}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Target Gain %</label>
              <Input
                type="number"
                value={formData.targetGainPercentage}
                onChange={(e) => setFormData(prev => ({ ...prev, targetGainPercentage: parseInt(e.target.value) || 0 }))}
              />
            </div>
          </div>

          {/* Investment Preview */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">$1,000 Investment Preview</h3>
            
            <div className="flex flex-wrap gap-2">
              {profitTargets.map((target, index) => (
                <Badge
                  key={target.id}
                  className="bg-green-500/20 text-green-400 border border-green-500/30"
                >
                  ${Math.round((investmentAmount * target.sellPercentage) / 100)} at +{target.target}%
                </Badge>
              ))}
            </div>

            <div className="flex items-center justify-between p-4 bg-muted/30 rounded-lg">
              <span className="text-sm text-muted-foreground">
                Total Sold: ${Math.round(previewSold)} | Moon Bag: ${Math.round(previewMoonBag)} ({formData.moonBagPercentage}%)
              </span>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-border">
          {errors.submit && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-600 text-sm">{errors.submit}</p>
            </div>
          )}
          <div className="flex items-center justify-end gap-3">
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button onClick={handleSubmit}>
              {editStrategy ? 'Update Strategy' : 'Create Strategy'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}