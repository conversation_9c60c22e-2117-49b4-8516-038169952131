'use client'

import { SwapOptimizer } from '@/lib/swapOptimizer'
import { useMemo } from 'react'

interface SwapOptimizationDisplayProps {
  tokenInAddress: string
  tokenOutAddress: string
  amount: number
  slippage: number
  className?: string
}

export const SwapOptimizationDisplay = ({
  tokenInAddress,
  tokenOutAddress, 
  amount,
  slippage,
  className = ''
}: SwapOptimizationDisplayProps) => {
  const optimization = useMemo(() => {
    if (!tokenInAddress || !tokenOutAddress) return null
    
    const swapRoute = SwapOptimizer.getOptimizedConfig(tokenInAddress, tokenOutAddress)
    const validation = SwapOptimizer.validateSwap(tokenInAddress, tokenOutAddress, amount, slippage)
    const limits = SwapOptimizer.getTradeSizeLimits(tokenInAddress, tokenOutAddress)
    
    return { swapRoute, validation, limits }
  }, [tokenInAddress, tokenOutAddress, amount, slippage])

  if (!optimization) return null

  const { swapRoute, validation, limits } = optimization
  const isOptimized = SwapOptimizer.isOptimizedPair(tokenInAddress, tokenOutAddress)

  return (
    <div className={`bg-gray-50 rounded-lg p-3 text-sm space-y-2 ${className}`}>
      {/* Optimization Status */}
      <div className="flex items-center justify-between">
        <span className="text-gray-600">Swap Optimization</span>
        <span className={`px-2 py-1 rounded text-xs font-medium ${
          isOptimized 
            ? 'bg-green-100 text-green-700' 
            : 'bg-blue-100 text-blue-700'
        }`}>
          {isOptimized ? 'Optimized' : 'Standard'}
        </span>
      </div>
      
      {/* Route Information */}
      <div className="text-xs text-gray-500">
        {swapRoute.reasoning}
      </div>
      
      {/* Optimization Details */}
      <div className="grid grid-cols-2 gap-4 text-xs">
        <div>
          <span className="text-gray-500">MEV Protection:</span>
          <span className="ml-1 font-medium">{swapRoute.config.mevProtectionLevel}</span>
        </div>
        <div>
          <span className="text-gray-500">Optimal Slippage:</span>
          <span className="ml-1 font-medium">{(swapRoute.config.slippageToleranceBps / 100).toFixed(2)}%</span>
        </div>
      </div>
      
      {/* Trade Size Recommendations */}
      <div className="text-xs">
        <span className="text-gray-500">Optimal Size:</span>
        <span className="ml-1 font-medium">${limits.optimalTradeUSD.toLocaleString()}</span>
        <span className="text-gray-400 ml-1">
          (${limits.minTradeUSD} - ${limits.maxTradeUSD.toLocaleString()})
        </span>
      </div>
      
      {/* Validation Warnings */}
      {validation.warnings.length > 0 && (
        <div className="space-y-1">
          {validation.warnings.map((warning, index) => (
            <div key={index} className="flex items-center text-xs text-amber-600">
              <span className="mr-1">⚠️</span>
              {warning}
            </div>
          ))}
        </div>
      )}
      
      {/* Suggestions */}
      {validation.suggestions.length > 0 && (
        <div className="space-y-1">
          {validation.suggestions.map((suggestion, index) => (
            <div key={index} className="flex items-center text-xs text-blue-600">
              <span className="mr-1">💡</span>
              {suggestion}
            </div>
          ))}
        </div>
      )}
      
      {/* Optimizations Applied */}
      {validation.optimizations.length > 0 && (
        <div className="space-y-1">
          {validation.optimizations.map((optimization, index) => (
            <div key={index} className="flex items-center text-xs text-green-600">
              <span className="mr-1">✅</span>
              {optimization}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}