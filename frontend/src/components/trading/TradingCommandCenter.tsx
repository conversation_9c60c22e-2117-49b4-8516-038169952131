'use client'

import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card'

export function TradingCommandCenter() {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Trading Command Center</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Trading Presets */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Trading Presets</h3>
              <div className="flex space-x-2">
                {['DEFAULT', 'VOL', 'DEAD', 'NUN', 'P5'].map((preset) => (
                  <button
                    key={preset}
                    className={`preset-tab ${preset === 'DEFAULT' ? 'active' : 'inactive'}`}
                  >
                    {preset}
                  </button>
                ))}
              </div>
              
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Priority Fee</span>
                  <span className="text-sm font-medium">0.01 SOL</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Slippage Limit</span>
                  <span className="text-sm font-medium">1.0%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">MEV Protection</span>
                  <span className="text-sm font-medium">Standard</span>
                </div>
              </div>
            </div>

            {/* Swap Interface */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Swap Interface</h3>
              
              <div className="space-y-3">
                <div>
                  <label className="text-sm text-muted-foreground">From</label>
                  <div className="input-field mt-1">
                    <input 
                      type="text" 
                      placeholder="Enter amount"
                      className="w-full bg-transparent border-none outline-none"
                    />
                  </div>
                </div>
                
                <div>
                  <label className="text-sm text-muted-foreground">To</label>
                  <div className="input-field mt-1">
                    <input 
                      type="text" 
                      placeholder="Select token"
                      className="w-full bg-transparent border-none outline-none"
                    />
                  </div>
                </div>
                
                <button className="btn-primary w-full">
                  Connect Wallet to Trade
                </button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-sm text-muted-foreground">Portfolio Value</div>
            <div className="text-2xl font-bold">$0.00</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="text-sm text-muted-foreground">Active Positions</div>
            <div className="text-2xl font-bold">0</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="text-sm text-muted-foreground">24h PnL</div>
            <div className="text-2xl font-bold neutral-text">$0.00</div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
