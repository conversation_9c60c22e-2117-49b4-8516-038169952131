'use client'

import { useState, useEffect } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { TrendingUp, Shield, Zap, Target, Calculator, DollarSign } from 'lucide-react'

interface PositionSizerProps {
  portfolioValue?: number
  onPositionCalculated?: (amount: number) => void
  className?: string
}

interface RiskPreset {
  id: string
  name: string
  displayName: string
  riskPerTrade: number
  positionSizePercentage: number
  color: string
  icon: React.ReactNode
  description: string
}

interface PositionSizerState {
  riskPerTrade: number
  positionSizePercentage: number
  activePreset: string
  enabled: boolean
}

const riskPresets: RiskPreset[] = [
  {
    id: 'conservative',
    name: 'Conservative',
    displayName: 'SAFE',
    riskPerTrade: 0.5,
    positionSizePercentage: 3,
    color: 'from-green-500 to-emerald-500',
    icon: <Shield className="w-4 h-4" />,
    description: 'Low risk, capital preservation focused'
  },
  {
    id: 'balanced',
    name: 'Balanced',
    displayName: 'BAL',
    riskPerTrade: 2,
    positionSizePercentage: 5,
    color: 'from-yellow-500 to-orange-500',
    icon: <Target className="w-4 h-4" />,
    description: 'Moderate risk, balanced approach'
  },
  {
    id: 'aggressive',
    name: 'Aggressive',
    displayName: 'RISK',
    riskPerTrade: 5,
    positionSizePercentage: 8,
    color: 'from-red-500 to-pink-500',
    icon: <Zap className="w-4 h-4" />,
    description: 'Higher risk, growth focused'
  }
]

const STORAGE_KEY = 'memetrader-position-sizer-settings'

export function PositionSizer({ 
  portfolioValue = 12847.23, 
  onPositionCalculated, 
  className 
}: PositionSizerProps) {
  const [state, setState] = useState<PositionSizerState>({
    riskPerTrade: 2,
    positionSizePercentage: 5,
    activePreset: 'balanced',
    enabled: true
  })

  // Load settings from localStorage on mount
  useEffect(() => {
    const saved = localStorage.getItem(STORAGE_KEY)
    if (saved) {
      try {
        const parsed = JSON.parse(saved)
        setState(parsed)
      } catch (error) {
        console.error('Failed to load position sizer settings:', error)
      }
    }
  }, [])

  // Save settings to localStorage on state changes
  useEffect(() => {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(state))
  }, [state])

  // Calculate position metrics
  const positionAmount = portfolioValue * (state.positionSizePercentage / 100)
  const maxRiskAmount = portfolioValue * (state.riskPerTrade / 100)
  const stopLossDistance = (maxRiskAmount / positionAmount) * 100

  // Notify parent component of position calculation (only when enabled)
  useEffect(() => {
    if (onPositionCalculated) {
      onPositionCalculated(state.enabled ? positionAmount : 0)
    }
  }, [positionAmount, onPositionCalculated, state.enabled])

  const handlePresetSelect = (preset: RiskPreset) => {
    if (state.activePreset === preset.id && state.enabled) {
      // If clicking the same preset that's already active, disable the Position Sizer
      setState(prev => ({
        ...prev,
        enabled: false,
        activePreset: 'none'
      }))
    } else {
      // Enable and apply the preset
      setState({
        riskPerTrade: preset.riskPerTrade,
        positionSizePercentage: preset.positionSizePercentage,
        activePreset: preset.id,
        enabled: true
      })
    }
  }

  const handleToggleEnabled = () => {
    setState(prev => ({
      ...prev,
      enabled: !prev.enabled,
      activePreset: !prev.enabled ? prev.activePreset : 'none'
    }))
  }

  const handleRiskPerTradeChange = (value: number) => {
    setState(prev => ({
      ...prev,
      riskPerTrade: value,
      activePreset: 'custom',
      enabled: true
    }))
  }

  const handlePositionSizeChange = (value: number) => {
    setState(prev => ({
      ...prev,
      positionSizePercentage: value,
      activePreset: 'custom',
      enabled: true
    }))
  }

  const getRiskLevelColor = () => {
    if (state.riskPerTrade <= 1) return 'text-green-400'
    if (state.riskPerTrade <= 3) return 'text-yellow-400'
    return 'text-red-400'
  }

  const getRiskLevelBadge = () => {
    if (state.riskPerTrade <= 1) return 'M SAFE'
    if (state.riskPerTrade <= 3) return 'M BAL'
    return 'M RISK'
  }

  const activePresetData = riskPresets.find(p => p.id === state.activePreset)

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className={cn(
            "w-1 h-6 rounded-full transition-all duration-300",
            state.enabled 
              ? "bg-gradient-to-b from-blue-500 to-purple-500" 
              : "bg-gradient-to-b from-muted to-muted-foreground"
          )}></div>
          <h3 className={cn(
            "text-lg font-bold bg-clip-text text-transparent transition-all duration-300",
            state.enabled
              ? "bg-gradient-to-r from-blue-500 to-purple-500"
              : "bg-gradient-to-r from-muted-foreground to-muted-foreground"
          )}>
            Position Sizer
          </h3>
          <Badge className={cn(
            "text-xs px-2 py-1 transition-all duration-300",
            state.enabled
              ? "bg-blue-500/20 text-blue-400 border border-blue-500/30"
              : "bg-muted/20 text-muted-foreground border border-muted/30"
          )}>
            <Calculator className="w-3 h-3 mr-1" />
            {state.enabled ? 'Active' : 'Disabled'}
          </Badge>
        </div>
        
        {/* Toggle Button */}
        <button
          onClick={handleToggleEnabled}
          className={cn(
            "px-3 py-1.5 text-xs font-medium rounded-lg border transition-all duration-200 hover:scale-105",
            state.enabled
              ? "bg-red-500/20 text-red-400 border-red-500/30 hover:bg-red-500/30"
              : "bg-green-500/20 text-green-400 border-green-500/30 hover:bg-green-500/30"
          )}
          title={state.enabled ? 'Disable Position Sizer' : 'Enable Position Sizer'}
        >
          {state.enabled ? 'Disable' : 'Enable'}
        </button>
      </div>

      {/* Main Position Sizer Card */}
      <div className={cn(
        "backdrop-blur-sm border rounded-xl p-5 shadow-lg transition-all duration-300",
        state.enabled
          ? "bg-gradient-to-br from-card/70 to-card/30 border-border/40"
          : "bg-gradient-to-br from-muted/20 to-muted/10 border-muted/30 opacity-60"
      )}>
        {/* Portfolio Value Display */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-muted-foreground font-medium">Portfolio Value</span>
            <Badge className={cn("text-xs px-2 py-1", getRiskLevelColor().replace('text-', 'text-'), getRiskLevelColor().replace('text-', 'bg-').replace('-400', '-500/20'), getRiskLevelColor().replace('text-', 'border-').replace('-400', '-500/30'))}>
              {getRiskLevelBadge()}
            </Badge>
          </div>
          <div className="text-2xl font-bold text-foreground flex items-center">
            <DollarSign className="w-5 h-5 mr-1 text-green-400" />
            {portfolioValue.toLocaleString('en-US', { 
              minimumFractionDigits: 2, 
              maximumFractionDigits: 2 
            })}
          </div>
        </div>

        {/* Risk Presets */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <div className="text-sm font-medium text-muted-foreground">Risk Presets</div>
            {state.enabled && (
              <div className="text-xs text-muted-foreground">Click active preset to disable</div>
            )}
          </div>
          <div className="grid grid-cols-3 gap-2">
            {riskPresets.map((preset) => {
              const isActive = state.activePreset === preset.id && state.enabled
              return (
                <button
                  key={preset.id}
                  onClick={() => handlePresetSelect(preset)}
                  className={cn(
                    "p-3 rounded-lg border transition-all duration-200 hover:scale-[1.02] relative",
                    isActive
                      ? `bg-gradient-to-r ${preset.color} text-white border-transparent shadow-lg`
                      : state.enabled
                        ? "bg-background/60 border-border/50 hover:border-primary/30 text-muted-foreground hover:text-foreground"
                        : "bg-background/30 border-border/30 text-muted-foreground/50 opacity-50"
                  )}
                  disabled={!state.enabled && state.activePreset !== preset.id}
                >
                  <div className="flex flex-col items-center space-y-1">
                    {preset.icon}
                    <span className="text-xs font-medium">{preset.displayName}</span>
                    <span className="text-xs opacity-80">{preset.riskPerTrade}%</span>
                  </div>
                  {isActive && (
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-white/20 rounded-full flex items-center justify-center">
                      <span className="text-xs">×</span>
                    </div>
                  )}
                </button>
              )
            })}
          </div>
        </div>

        {/* Risk Controls */}
        <div className="space-y-4 mb-6">
          {/* Risk Per Trade */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-muted-foreground">Risk Per Trade</span>
              <div className="flex items-center space-x-2">
                <input
                  type="number"
                  min="0.1"
                  max="10"
                  step="0.1"
                  value={state.riskPerTrade}
                  onChange={(e) => handleRiskPerTradeChange(parseFloat(e.target.value) || 0)}
                  className="w-16 px-2 py-1 text-xs bg-background/80 border border-border/50 rounded text-right focus:border-primary/50 focus:ring-1 focus:ring-primary/20 transition-all"
                />
                <span className="text-xs text-muted-foreground">%</span>
              </div>
            </div>
            <div className="relative">
              <input
                type="range"
                min="0.1"
                max="10"
                step="0.1"
                value={state.riskPerTrade}
                onChange={(e) => handleRiskPerTradeChange(parseFloat(e.target.value))}
                className="w-full h-2 bg-muted/60 rounded-lg appearance-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary/30 transition-all duration-200"
                style={{
                  background: `linear-gradient(to right, ${state.riskPerTrade <= 1 ? '#10b981' : state.riskPerTrade <= 3 ? '#f59e0b' : '#ef4444'} 0%, ${state.riskPerTrade <= 1 ? '#10b981' : state.riskPerTrade <= 3 ? '#f59e0b' : '#ef4444'} ${((state.riskPerTrade - 0.1) / 9.9) * 100}%, hsl(var(--muted)) ${((state.riskPerTrade - 0.1) / 9.9) * 100}%, hsl(var(--muted)) 100%)`
                }}
              />
            </div>
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Conservative</span>
              <span>Aggressive</span>
            </div>
          </div>

          {/* Position Size */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-muted-foreground">Position Size</span>
              <div className="flex items-center space-x-2">
                <input
                  type="number"
                  min="0.5"
                  max="20"
                  step="0.5"
                  value={state.positionSizePercentage}
                  onChange={(e) => handlePositionSizeChange(parseFloat(e.target.value) || 0)}
                  className="w-16 px-2 py-1 text-xs bg-background/80 border border-border/50 rounded text-right focus:border-primary/50 focus:ring-1 focus:ring-primary/20 transition-all"
                />
                <span className="text-xs text-muted-foreground">%</span>
              </div>
            </div>
            <div className="relative">
              <input
                type="range"
                min="0.5"
                max="20"
                step="0.5"
                value={state.positionSizePercentage}
                onChange={(e) => handlePositionSizeChange(parseFloat(e.target.value))}
                className="w-full h-2 bg-muted/60 rounded-lg appearance-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary/30 transition-all duration-200"
                style={{
                  background: `linear-gradient(to right, hsl(var(--primary)) 0%, hsl(var(--primary)) ${((state.positionSizePercentage - 0.5) / 19.5) * 100}%, hsl(var(--muted)) ${((state.positionSizePercentage - 0.5) / 19.5) * 100}%, hsl(var(--muted)) 100%)`
                }}
              />
            </div>
          </div>
        </div>

        {/* Calculated Results */}
        <div className="bg-gradient-to-r from-primary/10 to-secondary/10 border border-primary/20 rounded-lg p-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <div className="text-xs text-muted-foreground">Position Amount</div>
              <div className="text-lg font-bold text-foreground flex items-center">
                <TrendingUp className="w-4 h-4 mr-1 text-green-400" />
                ${positionAmount.toLocaleString('en-US', { 
                  minimumFractionDigits: 2, 
                  maximumFractionDigits: 2 
                })}
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-xs text-muted-foreground">Max Risk Amount</div>
              <div className={cn("text-lg font-bold flex items-center", getRiskLevelColor())}>
                <Shield className="w-4 h-4 mr-1" />
                ${maxRiskAmount.toLocaleString('en-US', { 
                  minimumFractionDigits: 2, 
                  maximumFractionDigits: 2 
                })}
              </div>
            </div>
          </div>
          
          {/* Additional Info */}
          <div className="mt-3 pt-3 border-t border-border/30">
            <div className="flex items-center justify-between text-xs">
              <span className="text-muted-foreground">Recommended Stop Loss:</span>
              <span className="font-medium text-foreground">
                -{stopLossDistance.toFixed(1)}%
              </span>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-4 flex items-center justify-between">
          <div className="text-xs text-muted-foreground">
            {state.enabled 
              ? (activePresetData?.description || 'Custom settings')
              : 'Position Sizer disabled - using balance percentages'
            }
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPositionCalculated?.(state.enabled ? positionAmount : 0)}
            disabled={!state.enabled}
            className={cn(
              "text-xs flex items-center gap-1 transition-all duration-200",
              state.enabled
                ? "hover:bg-primary/5 hover:border-primary/30"
                : "opacity-50 cursor-not-allowed"
            )}
          >
            <Calculator className="w-3 h-3" />
            {state.enabled ? 'Apply to Swap' : 'Disabled'}
          </Button>
        </div>
      </div>
    </div>
  )
}