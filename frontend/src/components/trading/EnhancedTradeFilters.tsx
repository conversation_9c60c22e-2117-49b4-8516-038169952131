'use client'

import { useState, useEffect } from 'react'
import { Search, Filter, SortAsc, SortDesc, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Position, PendingPosition, PositionType, SortField, SortDirection, StatusFilter, PnlFilter, OrderTypeFilter } from '@/types/trading'

interface FilterState {
  searchTerm: string
  positionType: PositionType
  statusFilter: StatusFilter
  pnlFilter: PnlFilter
  orderTypeFilter: OrderTypeFilter
  sortField: SortField
  sortDirection: SortDirection
}

interface EnhancedTradeFiltersProps {
  openPositions: Position[]
  pendingPositions: PendingPosition[]
  onOpenPositionsFilterChange: (filteredPositions: Position[]) => void
  onPendingPositionsFilterChange: (filteredPositions: PendingPosition[]) => void
}

export function EnhancedTradeFilters({ 
  openPositions, 
  pendingPositions, 
  onOpenPositionsFilterChange, 
  onPendingPositionsFilterChange 
}: EnhancedTradeFiltersProps) {
  const [filters, setFilters] = useState<FilterState>({
    searchTerm: '',
    positionType: 'all',
    statusFilter: 'all',
    pnlFilter: 'all',
    orderTypeFilter: 'all',
    sortField: 'pnl',
    sortDirection: 'desc'
  })

  // Filter options with counts
  const positionTypeOptions = [
    { value: 'all' as PositionType, label: 'All Positions', count: openPositions.length + pendingPositions.length },
    { value: 'open' as PositionType, label: 'Open Positions', count: openPositions.length },
    { value: 'pending' as PositionType, label: 'Pending Orders', count: pendingPositions.length },
  ]

  const statusOptions = [
    { value: 'all' as StatusFilter, label: 'All Status', count: openPositions.length },
    { value: 'taking_profits' as StatusFilter, label: 'Taking Profits', count: openPositions.filter(p => p.status === 'taking_profits').length },
    { value: 'approaching_target' as StatusFilter, label: 'Approaching Target', count: openPositions.filter(p => p.status === 'approaching_target').length },
    { value: 'moon_bag' as StatusFilter, label: 'Moon Bag', count: openPositions.filter(p => p.status === 'moon_bag').length },
    { value: 'trailing' as StatusFilter, label: 'Trailing', count: openPositions.filter(p => p.status === 'trailing').length },
  ]

  const pnlOptions = [
    { value: 'all' as PnlFilter, label: 'All P&L', count: openPositions.length },
    { value: 'profitable' as PnlFilter, label: 'Profitable', count: openPositions.filter(p => p.pnlPercentage > 0).length },
    { value: 'breakeven' as PnlFilter, label: 'Break Even', count: openPositions.filter(p => p.pnlPercentage >= -5 && p.pnlPercentage <= 5).length },
    { value: 'losing' as PnlFilter, label: 'Losing', count: openPositions.filter(p => p.pnlPercentage < -5).length },
  ]

  const orderTypeOptions = [
    { value: 'all' as OrderTypeFilter, label: 'All Orders', count: pendingPositions.length },
    { value: 'buy_limit' as OrderTypeFilter, label: 'Buy Limit', count: pendingPositions.filter(p => p.orderType === 'buy_limit').length },
    { value: 'sell_limit' as OrderTypeFilter, label: 'Sell Limit', count: pendingPositions.filter(p => p.orderType === 'sell_limit').length },
  ]

  // Apply filters and sorting
  useEffect(() => {
    // Filter open positions
    let filteredOpen = [...openPositions]
    
    if (filters.searchTerm.trim()) {
      filteredOpen = filteredOpen.filter(position =>
        position.token.symbol.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
        position.token.name.toLowerCase().includes(filters.searchTerm.toLowerCase())
      )
    }

    if (filters.statusFilter !== 'all') {
      filteredOpen = filteredOpen.filter(position => position.status === filters.statusFilter)
    }

    if (filters.pnlFilter !== 'all') {
      switch (filters.pnlFilter) {
        case 'profitable':
          filteredOpen = filteredOpen.filter(position => position.pnlPercentage > 0)
          break
        case 'breakeven':
          filteredOpen = filteredOpen.filter(position => position.pnlPercentage >= -5 && position.pnlPercentage <= 5)
          break
        case 'losing':
          filteredOpen = filteredOpen.filter(position => position.pnlPercentage < -5)
          break
      }
    }

    // Sort open positions
    filteredOpen.sort((a, b) => {
      let aValue: number | string
      let bValue: number | string

      switch (filters.sortField) {
        case 'pnl':
          aValue = a.pnlPercentage
          bValue = b.pnlPercentage
          break
        case 'value':
          aValue = a.currentValue
          bValue = b.currentValue
          break
        case 'symbol':
          aValue = a.token.symbol
          bValue = b.token.symbol
          break
        case 'entry':
          aValue = a.entryValue
          bValue = b.entryValue
          break
        case 'progress':
          aValue = a.progressToNextExit.current
          bValue = b.progressToNextExit.current
          break
        default:
          aValue = a.pnlPercentage
          bValue = b.pnlPercentage
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return filters.sortDirection === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue)
      }

      return filters.sortDirection === 'asc' 
        ? (aValue as number) - (bValue as number)
        : (bValue as number) - (aValue as number)
    })

    // Filter pending positions
    let filteredPending = [...pendingPositions]
    
    if (filters.searchTerm.trim()) {
      filteredPending = filteredPending.filter(position =>
        position.token.symbol.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
        position.token.name.toLowerCase().includes(filters.searchTerm.toLowerCase())
      )
    }

    if (filters.orderTypeFilter !== 'all') {
      filteredPending = filteredPending.filter(position => position.orderType === filters.orderTypeFilter)
    }

    // Sort pending positions
    filteredPending.sort((a, b) => {
      let aValue: number | string
      let bValue: number | string

      switch (filters.sortField) {
        case 'symbol':
          aValue = a.token.symbol
          bValue = b.token.symbol
          break
        case 'target_price':
          aValue = a.targetPrice
          bValue = b.targetPrice
          break
        case 'time_created':
          aValue = a.timeCreated.getTime()
          bValue = b.timeCreated.getTime()
          break
        case 'value':
          aValue = a.totalValue
          bValue = b.totalValue
          break
        default:
          aValue = a.timeCreated.getTime()
          bValue = b.timeCreated.getTime()
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return filters.sortDirection === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue)
      }

      return filters.sortDirection === 'asc' 
        ? (aValue as number) - (bValue as number)
        : (bValue as number) - (aValue as number)
    })

    onOpenPositionsFilterChange(filteredOpen)
    onPendingPositionsFilterChange(filteredPending)
  }, [filters, openPositions, pendingPositions, onOpenPositionsFilterChange, onPendingPositionsFilterChange])

  const handleSortChange = (field: SortField) => {
    setFilters(prev => ({
      ...prev,
      sortField: field,
      sortDirection: prev.sortField === field && prev.sortDirection === 'desc' ? 'asc' : 'desc'
    }))
  }

  const clearFilters = () => {
    setFilters({
      searchTerm: '',
      positionType: 'all',
      statusFilter: 'all',
      pnlFilter: 'all',
      orderTypeFilter: 'all',
      sortField: 'pnl',
      sortDirection: 'desc'
    })
  }

  const hasActiveFilters = filters.searchTerm.trim() || filters.positionType !== 'all' || 
                          filters.statusFilter !== 'all' || filters.pnlFilter !== 'all' || 
                          filters.orderTypeFilter !== 'all'

  return (
    <div className="bg-gradient-to-br from-card/80 to-card/40 backdrop-blur-sm border border-border/50 rounded-2xl shadow-xl p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Filter className="w-5 h-5 text-muted-foreground" />
          <h3 className="text-lg font-semibold text-foreground">Filter & Sort</h3>
        </div>
        {hasActiveFilters && (
          <Button variant="ghost" size="sm" onClick={clearFilters}>
            <X className="w-4 h-4 mr-1" />
            Clear All
          </Button>
        )}
      </div>

      <div className="space-y-6">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <input
            type="text"
            placeholder="Search by token symbol or name..."
            value={filters.searchTerm}
            onChange={(e) => setFilters(prev => ({ ...prev, searchTerm: e.target.value }))}
            className="w-full pl-10 pr-4 py-2 bg-background/50 border border-border rounded-lg text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
          />
        </div>

        {/* Position Type Filter */}
        <div>
          <div className="text-sm font-medium text-muted-foreground mb-3">Position Type</div>
          <div className="flex flex-wrap gap-2">
            {positionTypeOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => setFilters(prev => ({ ...prev, positionType: option.value }))}
                className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                  filters.positionType === option.value
                    ? 'bg-primary/20 text-primary border border-primary/30'
                    : 'bg-muted/20 text-muted-foreground border border-muted/30 hover:bg-muted/30'
                }`}
              >
                {option.label}
                {option.count > 0 && (
                  <span className="ml-1 text-xs opacity-75">({option.count})</span>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Status Filter (for open positions) */}
        {(filters.positionType === 'all' || filters.positionType === 'open') && (
          <div>
            <div className="text-sm font-medium text-muted-foreground mb-3">Status</div>
            <div className="flex flex-wrap gap-2">
              {statusOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => setFilters(prev => ({ ...prev, statusFilter: option.value }))}
                  className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                    filters.statusFilter === option.value
                      ? 'bg-primary/20 text-primary border border-primary/30'
                      : 'bg-muted/20 text-muted-foreground border border-muted/30 hover:bg-muted/30'
                  }`}
                >
                  {option.label}
                  {option.count > 0 && (
                    <span className="ml-1 text-xs opacity-75">({option.count})</span>
                  )}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* P&L Filter (for open positions) */}
        {(filters.positionType === 'all' || filters.positionType === 'open') && (
          <div>
            <div className="text-sm font-medium text-muted-foreground mb-3">Performance</div>
            <div className="flex flex-wrap gap-2">
              {pnlOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => setFilters(prev => ({ ...prev, pnlFilter: option.value }))}
                  className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                    filters.pnlFilter === option.value
                      ? 'bg-primary/20 text-primary border border-primary/30'
                      : 'bg-muted/20 text-muted-foreground border border-muted/30 hover:bg-muted/30'
                  }`}
                >
                  {option.label}
                  {option.count > 0 && (
                    <span className="ml-1 text-xs opacity-75">({option.count})</span>
                  )}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Order Type Filter (for pending positions) */}
        {(filters.positionType === 'all' || filters.positionType === 'pending') && (
          <div>
            <div className="text-sm font-medium text-muted-foreground mb-3">Order Type</div>
            <div className="flex flex-wrap gap-2">
              {orderTypeOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => setFilters(prev => ({ ...prev, orderTypeFilter: option.value }))}
                  className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                    filters.orderTypeFilter === option.value
                      ? 'bg-primary/20 text-primary border border-primary/30'
                      : 'bg-muted/20 text-muted-foreground border border-muted/30 hover:bg-muted/30'
                  }`}
                >
                  {option.label}
                  {option.count > 0 && (
                    <span className="ml-1 text-xs opacity-75">({option.count})</span>
                  )}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Sort Options */}
        <div>
          <div className="text-sm font-medium text-muted-foreground mb-3">Sort By</div>
          <div className="flex flex-wrap gap-2">
            {[
              { field: 'pnl' as SortField, label: 'P&L %', showFor: ['all', 'open'] },
              { field: 'value' as SortField, label: 'Position Value', showFor: ['all', 'open', 'pending'] },
              { field: 'symbol' as SortField, label: 'Token Symbol', showFor: ['all', 'open', 'pending'] },
              { field: 'entry' as SortField, label: 'Entry Value', showFor: ['all', 'open'] },
              { field: 'progress' as SortField, label: 'Exit Progress', showFor: ['all', 'open'] },
              { field: 'target_price' as SortField, label: 'Target Price', showFor: ['all', 'pending'] },
              { field: 'time_created' as SortField, label: 'Time Created', showFor: ['all', 'pending'] },
            ]
              .filter(option => option.showFor.includes(filters.positionType))
              .map((option) => (
                <button
                  key={option.field}
                  onClick={() => handleSortChange(option.field)}
                  className={`flex items-center gap-1 px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                    filters.sortField === option.field
                      ? 'bg-primary/20 text-primary border border-primary/30'
                      : 'bg-muted/20 text-muted-foreground border border-muted/30 hover:bg-muted/30'
                  }`}
                >
                  {option.label}
                  {filters.sortField === option.field && (
                    filters.sortDirection === 'asc' ? (
                      <SortAsc className="w-3 h-3" />
                    ) : (
                      <SortDesc className="w-3 h-3" />
                    )
                  )}
                </button>
              ))}
          </div>
        </div>

        {/* Active Filters Summary */}
        {hasActiveFilters && (
          <div className="pt-4 border-t border-border/30">
            <div className="text-sm font-medium text-muted-foreground mb-2">Active Filters:</div>
            <div className="flex flex-wrap gap-2">
              {filters.searchTerm.trim() && (
                <Badge className="bg-primary/20 text-primary border border-primary/30">
                  Search: "{filters.searchTerm}"
                </Badge>
              )}
              {filters.positionType !== 'all' && (
                <Badge className="bg-primary/20 text-primary border border-primary/30">
                  Type: {positionTypeOptions.find(o => o.value === filters.positionType)?.label}
                </Badge>
              )}
              {filters.statusFilter !== 'all' && (
                <Badge className="bg-primary/20 text-primary border border-primary/30">
                  Status: {statusOptions.find(o => o.value === filters.statusFilter)?.label}
                </Badge>
              )}
              {filters.pnlFilter !== 'all' && (
                <Badge className="bg-primary/20 text-primary border border-primary/30">
                  P&L: {pnlOptions.find(o => o.value === filters.pnlFilter)?.label}
                </Badge>
              )}
              {filters.orderTypeFilter !== 'all' && (
                <Badge className="bg-primary/20 text-primary border border-primary/30">
                  Order: {orderTypeOptions.find(o => o.value === filters.orderTypeFilter)?.label}
                </Badge>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}