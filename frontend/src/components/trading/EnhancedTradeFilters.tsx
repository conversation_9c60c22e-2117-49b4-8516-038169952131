'use client'

import { useState, useEffect } from 'react'
import { Search, Filter, SortAsc, SortDesc, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Position, SortField, SortDirection, StatusFilter, PnlFilter } from '@/types/trading'

interface FilterState {
  searchTerm: string
  statusFilter: StatusFilter
  pnlFilter: PnlFilter
  sortField: SortField
  sortDirection: SortDirection
}

interface EnhancedTradeFiltersProps {
  openPositions: Position[]
  onOpenPositionsFilterChange: (filteredPositions: Position[]) => void
}

export function EnhancedTradeFilters({ 
  openPositions, 
  onOpenPositionsFilterChange
}: EnhancedTradeFiltersProps) {
  const [filters, setFilters] = useState<FilterState>({
    searchTerm: '',
    statusFilter: 'all',
    pnlFilter: 'all',
    sortField: 'pnl',
    sortDirection: 'desc'
  })

  // Filter options with counts
  const statusOptions = [
    { value: 'all' as StatusFilter, label: 'All Positions', count: openPositions.length },
    { value: 'taking_profits' as StatusFilter, label: 'Taking Profits', count: openPositions.filter(p => p.status === 'taking_profits').length },
    { value: 'approaching_target' as StatusFilter, label: 'Approaching Target', count: openPositions.filter(p => p.status === 'approaching_target').length },
    { value: 'moon_bag' as StatusFilter, label: 'Moon Bag', count: openPositions.filter(p => p.status === 'moon_bag').length },
    { value: 'trailing' as StatusFilter, label: 'Trailing', count: openPositions.filter(p => p.status === 'trailing').length },
  ]

  const pnlOptions = [
    { value: 'all' as PnlFilter, label: 'All Performance', count: openPositions.length },
    { value: 'profitable' as PnlFilter, label: 'Profitable', count: openPositions.filter(p => p.pnlPercentage > 0).length },
    { value: 'breakeven' as PnlFilter, label: 'Break Even', count: openPositions.filter(p => p.pnlPercentage >= -5 && p.pnlPercentage <= 5).length },
    { value: 'losing' as PnlFilter, label: 'Losing', count: openPositions.filter(p => p.pnlPercentage < -5).length },
  ]

  // Apply filters and sorting
  useEffect(() => {
    let filteredOpen = [...openPositions]
    
    // Apply search filter
    if (filters.searchTerm.trim()) {
      filteredOpen = filteredOpen.filter(position =>
        position.token.symbol.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
        position.token.name.toLowerCase().includes(filters.searchTerm.toLowerCase())
      )
    }

    // Apply status filter
    if (filters.statusFilter !== 'all') {
      filteredOpen = filteredOpen.filter(position => position.status === filters.statusFilter)
    }

    // Apply P&L filter
    if (filters.pnlFilter !== 'all') {
      switch (filters.pnlFilter) {
        case 'profitable':
          filteredOpen = filteredOpen.filter(position => position.pnlPercentage > 0)
          break
        case 'breakeven':
          filteredOpen = filteredOpen.filter(position => position.pnlPercentage >= -5 && position.pnlPercentage <= 5)
          break
        case 'losing':
          filteredOpen = filteredOpen.filter(position => position.pnlPercentage < -5)
          break
      }
    }

    // Sort positions
    filteredOpen.sort((a, b) => {
      let aValue: number | string
      let bValue: number | string

      switch (filters.sortField) {
        case 'pnl':
          aValue = a.pnlPercentage
          bValue = b.pnlPercentage
          break
        case 'value':
          aValue = a.currentValue
          bValue = b.currentValue
          break
        case 'symbol':
          aValue = a.token.symbol
          bValue = b.token.symbol
          break
        case 'entry':
          aValue = a.entryValue
          bValue = b.entryValue
          break
        case 'progress':
          aValue = a.progressToNextExit.current
          bValue = b.progressToNextExit.current
          break
        default:
          aValue = a.pnlPercentage
          bValue = b.pnlPercentage
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return filters.sortDirection === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue)
      }

      return filters.sortDirection === 'asc' 
        ? (aValue as number) - (bValue as number)
        : (bValue as number) - (aValue as number)
    })

    onOpenPositionsFilterChange(filteredOpen)
  }, [filters, openPositions, onOpenPositionsFilterChange])

  const clearFilters = () => {
    setFilters({
      searchTerm: '',
      statusFilter: 'all',
      pnlFilter: 'all',
      sortField: 'pnl',
      sortDirection: 'desc'
    })
  }

  const hasActiveFilters = filters.searchTerm.trim() || 
                          filters.statusFilter !== 'all' || 
                          filters.pnlFilter !== 'all'

  return (
    <div className="bg-gradient-to-br from-card/60 to-card/30 backdrop-blur-sm border border-border/40 rounded-xl shadow-lg p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Filter className="w-5 h-5 text-primary" />
          <h3 className="text-lg font-semibold text-foreground">Filters</h3>
          <Badge className="bg-primary/20 text-primary border border-primary/30">
            {openPositions.length}
          </Badge>
        </div>
        {hasActiveFilters && (
          <Button variant="ghost" size="sm" onClick={clearFilters}>
            <X className="w-4 h-4 mr-1" />
            Clear
          </Button>
        )}
      </div>

      <div className="space-y-3">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <input
            type="text"
            placeholder="Search positions..."
            value={filters.searchTerm}
            onChange={(e) => setFilters(prev => ({ ...prev, searchTerm: e.target.value }))}
            className="w-full pl-10 pr-4 py-2 bg-background/50 border border-border rounded-lg text-sm text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary/50"
          />
        </div>

        {/* Status Filter */}
        <div className="flex items-center gap-2 flex-wrap">
          {statusOptions.map((option) => (
            <Button
              key={option.value}
              variant={filters.statusFilter === option.value ? "default" : "outline"}
              size="sm"
              onClick={() => setFilters(prev => ({ ...prev, statusFilter: option.value }))}
              className="text-xs h-7"
            >
              {option.label} ({option.count})
            </Button>
          ))}
        </div>

        {/* P&L Filter */}
        <div className="flex items-center gap-2 flex-wrap">
          {pnlOptions.map((option) => (
            <Button
              key={option.value}
              variant={filters.pnlFilter === option.value ? "default" : "outline"}
              size="sm"
              onClick={() => setFilters(prev => ({ ...prev, pnlFilter: option.value }))}
              className="text-xs h-7"
            >
              {option.label} ({option.count})
            </Button>
          ))}
        </div>

        {/* Sort Options */}
        <div className="flex items-center gap-2 flex-wrap">
          <span className="text-xs text-muted-foreground">Sort:</span>
          {[
            { field: 'pnl' as SortField, label: 'P&L %' },
            { field: 'value' as SortField, label: 'Value' },
            { field: 'symbol' as SortField, label: 'Symbol' },
            { field: 'entry' as SortField, label: 'Entry' },
            { field: 'progress' as SortField, label: 'Progress' },
          ].map((option) => (
            <Button
              key={option.field}
              variant="outline"
              size="sm"
              onClick={() => setFilters(prev => ({
                ...prev,
                sortField: option.field,
                sortDirection: prev.sortField === option.field && prev.sortDirection === 'desc' ? 'asc' : 'desc'
              }))}
              className="text-xs h-7"
            >
              {option.label}
              {filters.sortField === option.field && (
                filters.sortDirection === 'desc' ? 
                  <SortDesc className="w-3 h-3 ml-1" /> : 
                  <SortAsc className="w-3 h-3 ml-1" />
              )}
            </Button>
          ))}
        </div>
      </div>
    </div>
  )
}