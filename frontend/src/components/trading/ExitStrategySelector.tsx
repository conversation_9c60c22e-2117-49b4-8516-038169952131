'use client'

import { useState, useEffect } from 'react'
import { Plus, Target, TrendingDown, TrendingUp, Shield, Zap } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { useExitStrategyStore } from '@/stores/exitStrategyStore.tsx'

interface ExitStrategy {
  id: string
  name: string
  description: string
  usageCount: number
  winRate: number
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
  stopLoss: { percentage: number } | null
  profitTargets: { target: number; sellPercentage: number }[]
  trailingStop: { percentage: number } | null
  moonBag: { percentage: number; targetGain: number } | null
}

interface ExitStrategySelectorProps {
  selectedStrategy: ExitStrategy | null
  onStrategySelect: (strategy: ExitStrategy) => void
  onCreateCustom: () => void
  className?: string
  autoOpen?: boolean
}


const getRiskLevelColor = (riskLevel: ExitStrategy['riskLevel']) => {
  switch (riskLevel) {
    case 'LOW':
      return 'bg-green-500/20 text-green-400 border-green-500/30'
    case 'MEDIUM':
      return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
    case 'HIGH':
      return 'bg-red-500/20 text-red-400 border-red-500/30'
    default:
      return 'bg-muted/20 text-muted-foreground border-muted/30'
  }
}

const getRiskIcon = (riskLevel: ExitStrategy['riskLevel']) => {
  switch (riskLevel) {
    case 'LOW':
      return <Shield className="w-3 h-3" />
    case 'MEDIUM':
      return <Target className="w-3 h-3" />
    case 'HIGH':
      return <Zap className="w-3 h-3" />
    default:
      return <Target className="w-3 h-3" />
  }
}

export function ExitStrategySelector({ 
  selectedStrategy, 
  onStrategySelect, 
  onCreateCustom,
  className,
  autoOpen = false
}: ExitStrategySelectorProps) {
  const [isModalOpen, setIsModalOpen] = useState(autoOpen)
  const [tempSelectedStrategy, setTempSelectedStrategy] = useState<ExitStrategy | null>(selectedStrategy)
  
  // Get strategies from store
  const { strategies, incrementUsage } = useExitStrategyStore()

  // Auto-open modal when autoOpen prop changes
  useEffect(() => {
    if (autoOpen && !selectedStrategy) {
      setIsModalOpen(true)
    }
  }, [autoOpen, selectedStrategy])

  // Auto-select default strategy only on initial load if no strategy is selected
  useEffect(() => {
    if (!selectedStrategy && strategies.length > 0) {
      const defaultStrategy = strategies.find(strategy => strategy.isDefault)
      if (defaultStrategy) {
        console.log('ExitStrategySelector: Auto-selecting default strategy:', defaultStrategy.name)
        onStrategySelect(defaultStrategy)
      }
    }
  }, [strategies.length]) // Only depend on strategies.length to avoid re-running on every strategy change

  // Show modal when no strategy is selected and trying to trade
  const handleOpenModal = () => {
    setIsModalOpen(true)
    setTempSelectedStrategy(selectedStrategy)
  }

  const handleConfirm = () => {
    if (tempSelectedStrategy) {
      onStrategySelect(tempSelectedStrategy)
      // Increment usage count when strategy is selected for trading
      incrementUsage(tempSelectedStrategy.id)
    }
    setIsModalOpen(false)
  }

  const handleCancel = () => {
    setTempSelectedStrategy(selectedStrategy)
    setIsModalOpen(false)
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Strategy Selection Card */}
      <div className="bg-gradient-to-br from-card/60 to-card/30 backdrop-blur-sm border border-border/40 rounded-xl p-5 shadow-lg">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="w-1 h-6 bg-gradient-to-b from-red-500 to-orange-500 rounded-full"></div>
            <div>
              <h3 className="text-xl font-bold bg-gradient-to-r from-red-500 to-orange-500 bg-clip-text text-transparent">
                Exit Strategy Required
              </h3>
            </div>
          </div>
          <Badge variant="destructive" className="flex items-center gap-1 flex-shrink-0">
            <Shield className="w-3 h-3" />
            Required
          </Badge>
        </div>

        {/* Selected Strategy Details */}
        {selectedStrategy ? (
          <div className="mb-4 p-4 bg-gradient-to-r from-primary/10 to-secondary/10 border border-primary/20 rounded-lg">
            <div className="space-y-3">
              {/* Strategy Name and Risk Level */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-semibold text-foreground">{selectedStrategy.name}</span>
                  <Badge className={cn("text-xs px-2 py-0.5", getRiskLevelColor(selectedStrategy.riskLevel))}>
                    {getRiskIcon(selectedStrategy.riskLevel)}
                    {selectedStrategy.riskLevel} Risk
                  </Badge>
                </div>
                <span className="text-xs text-muted-foreground">
                  Win Rate: <span className="text-green-400 font-medium">{selectedStrategy.winRate}%</span>
                </span>
              </div>
              
              {/* Strategy Parameters */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  {selectedStrategy.stopLoss && (
                    <div className="flex items-center gap-1 text-xs">
                      <TrendingDown className="w-3 h-3 text-red-400" />
                      <span className="text-red-400 font-medium">
                        Stop Loss: -{selectedStrategy.stopLoss.percentage}%
                      </span>
                    </div>
                  )}
                  
                  {selectedStrategy.trailingStop && (
                    <div className="flex items-center gap-1 text-xs">
                      <TrendingDown className="w-3 h-3 text-orange-400" />
                      <span className="text-orange-400 font-medium">
                        Trailing: {selectedStrategy.trailingStop.percentage}%
                      </span>
                    </div>
                  )}
                </div>
                
                <div className="space-y-1">
                  {selectedStrategy.profitTargets.length > 0 && (
                    <div className="flex items-center gap-1 text-xs">
                      <TrendingUp className="w-3 h-3 text-green-400" />
                      <span className="text-green-400 font-medium">
                        {selectedStrategy.profitTargets.length} Profit Targets
                      </span>
                    </div>
                  )}
                  
                  {selectedStrategy.moonBag && (
                    <div className="flex items-center gap-1 text-xs">
                      <Target className="w-3 h-3 text-purple-400" />
                      <span className="text-purple-400 font-medium">
                        Moon Bag: {selectedStrategy.moonBag.percentage}%
                      </span>
                    </div>
                  )}
                </div>
              </div>
              
              {/* Strategy Description */}
              <p className="text-xs text-muted-foreground italic border-t border-border/30 pt-2">
                "{selectedStrategy.description}"
              </p>
            </div>
          </div>
        ) : (
          <div className="mb-4 p-4 bg-muted/20 border border-dashed border-muted-foreground/30 rounded-lg">
            <p className="text-sm text-muted-foreground text-center">
              No exit strategy selected - required before trading
            </p>
          </div>
        )}

        {/* Action Button */}
        <Button
          onClick={handleOpenModal}
          variant={selectedStrategy ? "outline" : "default"}
          className={cn(
            "w-full",
            selectedStrategy 
              ? "hover:bg-primary/5 hover:border-primary/30" 
              : "bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90"
          )}
        >
          {selectedStrategy ? 'Change Strategy' : 'Select Exit Strategy'}
        </Button>
      </div>

      {/* Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full max-h-[80vh] overflow-y-auto">
            {/* Header */}
            <div className="p-6 border-b border-gray-700">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-8 h-8 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 0110 5zm0 9a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                  </svg>
                </div>
                <h2 className="text-2xl font-bold text-white">Select Exit Strategy</h2>
              </div>
              <p className="text-gray-400">
                You must assign an exit strategy before executing any buy order.
              </p>
            </div>

            {/* Strategy List */}
            <div className="p-6 space-y-3">
              {strategies.map((strategy) => (
                <div
                  key={strategy.id}
                  onClick={() => setTempSelectedStrategy(strategy)}
                  className={cn(
                    "p-4 rounded-lg cursor-pointer transition-all duration-200 border",
                    tempSelectedStrategy?.id === strategy.id
                      ? "bg-gray-700 border-gray-600"
                      : "bg-gray-800/50 border-gray-700 hover:bg-gray-700/50"
                  )}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <h3 className="text-lg font-semibold text-white">{strategy.name}</h3>
                      {strategy.isDefault && (
                        <Badge className="bg-blue-500/20 text-blue-400 border border-blue-500/30 text-xs px-1.5 py-0.5">
                          <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
                          </svg>
                          Default
                        </Badge>
                      )}
                    </div>
                    {tempSelectedStrategy?.id === strategy.id && (
                      <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                        <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                  </div>
                  
                  <div className="space-y-1">
                    {/* Stop Loss */}
                    {strategy.stopLoss && (
                      <div className="text-gray-400 text-sm">
                        <span className="text-red-400">SL:</span> -{strategy.stopLoss.percentage}%
                        {strategy.trailingStop && (
                          <span className="ml-2">
                            <span className="text-orange-400">Trailing:</span> {strategy.trailingStop.percentage}%
                          </span>
                        )}
                      </div>
                    )}
                    
                    {/* Profit Targets */}
                    {strategy.profitTargets.length > 0 && (
                      <div className="text-gray-400 text-sm">
                        <span className="text-green-400">TP:</span>{' '}
                        {strategy.profitTargets.map((target, index) => (
                          <span key={index}>
                            {target.target}% ({target.sellPercentage}%)
                            {index < strategy.profitTargets.length - 1 ? ', ' : ''}
                          </span>
                        ))}
                      </div>
                    )}
                    
                    {/* Moon Bag */}
                    {strategy.moonBag && (
                      <div className="text-gray-400 text-sm">
                        <span className="text-purple-400">Moon Bag:</span> Keep {strategy.moonBag.percentage}% for {strategy.moonBag.targetGain}%+
                      </div>
                    )}
                    
                    {/* Trailing Only */}
                    {strategy.trailingStop && !strategy.stopLoss && (
                      <div className="text-gray-400 text-sm">
                        <span className="text-orange-400">Trailing stop:</span> {strategy.trailingStop.percentage}% below peak
                      </div>
                    )}
                    
                    {/* Win Rate */}
                    <div className="text-gray-500 text-xs mt-2">
                      Win Rate: <span className="text-green-400">{strategy.winRate}%</span> • 
                      Used: <span className="text-gray-300">{strategy.usageCount}x</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Action Buttons */}
            <div className="p-6 border-t border-gray-700 flex gap-3">
              <Button
                variant="outline"
                onClick={handleCancel}
                className="flex-1 bg-gray-700 border-gray-600 text-white hover:bg-gray-600"
              >
                Cancel
              </Button>
              <Button
                onClick={handleConfirm}
                disabled={!tempSelectedStrategy}
                className="flex-1 bg-green-600 hover:bg-green-700 text-white"
              >
                Continue
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}