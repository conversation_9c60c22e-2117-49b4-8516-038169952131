'use client'

import { Check } from 'lucide-react'
import { cn } from '@/lib/utils'

interface ProgressIndicatorProps {
  milestones: number[]
  currentProgress: number
  completedExits: number
  className?: string
}

export function ProgressIndicator({ 
  milestones, 
  currentProgress, 
  completedExits, 
  className 
}: ProgressIndicatorProps) {
  const nextMilestone = milestones[completedExits] || milestones[milestones.length - 1]
  const progressToNextMilestone = completedExits < milestones.length 
    ? Math.min(currentProgress, nextMilestone) 
    : currentProgress

  return (
    <div className={cn("space-y-3", className)}>
      {/* Progress Header */}
      <div className="flex items-center justify-between text-sm text-muted-foreground">
        <span>Progress to Next Exit</span>
        <span>{currentProgress.toFixed(1)}% / {nextMilestone}%</span>
      </div>
      
      {/* Clean Progress Bar */}
      <div className="h-2 bg-muted/30 rounded-full overflow-hidden">
        <div 
          className="h-full bg-gradient-to-r from-orange-500 to-amber-400 transition-all duration-500 ease-out"
          style={{ width: `${(progressToNextMilestone / nextMilestone) * 100}%` }}
        />
      </div>
      
      {/* Exit Milestones */}
      <div className="space-y-2">
        <div className="text-sm text-muted-foreground">Exit Milestones</div>
        <div className="flex items-center gap-2 flex-wrap">
          {milestones.map((milestone, index) => {
            const isCompleted = index < completedExits
            
            return (
              <div
                key={milestone}
                className={cn(
                  "flex items-center gap-1.5 px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-200",
                  isCompleted
                    ? "bg-green-500/20 text-green-400 border border-green-500/30"
                    : "bg-muted/20 text-muted-foreground border border-muted/30"
                )}
              >
                {isCompleted && <Check className="w-3 h-3" />}
                <span>+{milestone}%</span>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}