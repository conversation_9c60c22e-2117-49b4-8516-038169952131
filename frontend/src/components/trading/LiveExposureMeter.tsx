'use client'

import { Activity, TrendingUp } from 'lucide-react'
import { Badge } from '@/components/ui/badge'

interface ExposureData {
  currentExposure: number
  maximumLimit: number
  capitalUsage: number
  activePositions: number
  availableCapital: number
  positionsUnderLimit: number
}

interface LiveExposureMeterProps {
  data: ExposureData
}

export function LiveExposureMeter({ data }: LiveExposureMeterProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  return (
    <div className="bg-gradient-to-br from-card/80 to-card/40 backdrop-blur-sm border border-border/50 rounded-2xl shadow-xl p-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center">
            <Activity className="w-6 h-6 text-white" />
          </div>
          <h2 className="text-2xl font-bold text-foreground">Live Exposure Meter</h2>
        </div>
        <Badge className="bg-green-500/20 text-green-400 border border-green-500/30 px-3 py-1 flex items-center gap-2">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
          Active
        </Badge>
      </div>

      {/* Exposure Summary */}
      <div className="grid grid-cols-2 gap-8 mb-8">
        <div>
          <div className="text-sm text-muted-foreground mb-2">Current Exposure</div>
          <div className="text-4xl font-bold text-foreground">
            {formatCurrency(data.currentExposure)}
          </div>
        </div>
        <div className="text-right">
          <div className="text-sm text-muted-foreground mb-2">Maximum Limit</div>
          <div className="text-4xl font-bold text-muted-foreground">
            {formatCurrency(data.maximumLimit)}
          </div>
        </div>
      </div>

      {/* Capital Usage Bar */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-3">
          <span className="text-lg font-medium text-muted-foreground">Capital Usage</span>
          <span className="text-2xl font-bold text-foreground">{data.capitalUsage.toFixed(1)}%</span>
        </div>
        
        <div className="relative">
          <div className="w-full h-4 bg-muted/30 rounded-full overflow-hidden">
            <div 
              className="h-full bg-gradient-to-r from-orange-500 to-orange-600 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${Math.min(data.capitalUsage, 100)}%` }}
            />
          </div>
          <div className="flex justify-between text-sm text-muted-foreground mt-2">
            <span>$0</span>
            <span>{formatCurrency(data.maximumLimit)}</span>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 gap-6">
        <div className="bg-gradient-to-br from-muted/20 to-muted/10 border border-border/30 rounded-xl p-6">
          <div className="text-center">
            <div className="text-sm text-muted-foreground mb-2">Active Positions</div>
            <div className="text-4xl font-bold text-foreground mb-4">{data.activePositions}</div>
            <Badge className="bg-green-500/20 text-green-400 border border-green-500/30">
              {data.positionsUnderLimit} below limit
            </Badge>
          </div>
        </div>

        <div className="bg-gradient-to-br from-muted/20 to-muted/10 border border-border/30 rounded-xl p-6">
          <div className="text-center">
            <div className="text-sm text-muted-foreground mb-2">Available Capital</div>
            <div className="text-4xl font-bold text-green-400 mb-4">
              {formatCurrency(data.availableCapital)}
            </div>
            <Badge className="bg-green-500/20 text-green-400 border border-green-500/30">
              Ready for trades
            </Badge>
          </div>
        </div>
      </div>

      {/* Risk Indicator */}
      <div className="mt-6 pt-6 border-t border-border/30">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4 text-muted-foreground" />
            <span className="text-muted-foreground">Risk Level:</span>
          </div>
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${
              data.capitalUsage > 90 ? 'bg-red-500' : 
              data.capitalUsage > 75 ? 'bg-yellow-500' : 
              'bg-green-500'
            }`} />
            <span className={`font-medium ${
              data.capitalUsage > 90 ? 'text-red-400' : 
              data.capitalUsage > 75 ? 'text-yellow-400' : 
              'text-green-400'
            }`}>
              {data.capitalUsage > 90 ? 'High' : 
               data.capitalUsage > 75 ? 'Medium' : 
               'Low'}
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}