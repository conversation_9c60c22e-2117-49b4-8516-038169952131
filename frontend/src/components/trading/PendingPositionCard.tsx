'use client'

import { Edit3, X, Clock } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { PendingPosition } from '@/types/trading'
import { 
  formatCurrency, 
  formatPrice, 
  formatTokenAmount, 
  formatTimeAgo, 
  getTokenColor, 
  getOrderTypeLabel 
} from '@/utils/trading'
import { cn } from '@/lib/utils'

interface PendingPositionCardProps {
  position: PendingPosition
  onModify?: (positionId: string) => void
  onCancel?: (positionId: string) => void
}

export function PendingPositionCard({ position, onModify, onCancel }: PendingPositionCardProps) {
  const priceComparison = position.currentPrice - position.targetPrice
  const priceComparisonPercent = (priceComparison / position.targetPrice) * 100
  const isAboveTarget = position.orderType === 'buy_limit' ? priceComparison > 0 : priceComparison < 0

  return (
    <div className="bg-gradient-to-br from-card/60 to-card/30 backdrop-blur-sm border border-border/40 rounded-xl shadow-lg p-4 hover:shadow-xl transition-all duration-300">
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          <div className={cn("w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm", getTokenColor(position.token.symbol))}>
            {position.token.symbol.charAt(0)}
          </div>
          <div>
            <div className="flex items-center gap-2 mb-0.5">
              <h3 className="text-lg font-semibold text-foreground">{position.token.symbol}</h3>
              <Badge className={cn(
                "text-xs px-1.5 py-0.5",
                position.orderType === 'buy_limit' 
                  ? "bg-green-500/20 text-green-400 border border-green-500/30"
                  : "bg-red-500/20 text-red-400 border border-red-500/30"
              )}>
                {getOrderTypeLabel(position.orderType)}
              </Badge>
            </div>
            <div className="text-xs text-muted-foreground">
              {formatTokenAmount(position.quantity)} tokens
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-1">
          <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse" />
          <span className="text-xs text-orange-400 font-medium">Pending</span>
        </div>
      </div>

      {/* Price Information */}
      <div className="grid grid-cols-2 gap-3 mb-3">
        <div>
          <div className="text-xs text-muted-foreground mb-1">Target Price</div>
          <div className="text-xs font-mono text-foreground font-semibold">
            {formatPrice(position.targetPrice)}
          </div>
        </div>
        <div>
          <div className="text-xs text-muted-foreground mb-1">Current Price</div>
          <div className="text-xs font-mono text-foreground">
            {formatPrice(position.currentPrice)}
          </div>
        </div>
      </div>
      
      {/* Price Difference */}
      <div className="flex items-center justify-between mb-3 p-2 bg-muted/10 rounded-lg">
        <div className="text-xs text-muted-foreground">Price Difference</div>
        <div className={cn(
          "text-xs font-medium",
          isAboveTarget ? "text-green-400" : "text-red-400"
        )}>
          {priceComparison >= 0 ? '+' : ''}{formatPrice(Math.abs(priceComparison))} 
          ({priceComparisonPercent >= 0 ? '+' : ''}{priceComparisonPercent.toFixed(2)}%)
        </div>
      </div>

      {/* Order Details */}
      <div className="grid grid-cols-2 gap-3 mb-3">
        <div>
          <div className="text-xs text-muted-foreground mb-1">Total Value</div>
          <div className="text-sm font-bold text-foreground">
            {formatCurrency(position.totalValue, 0)}
          </div>
        </div>
        <div>
          <div className="text-xs text-muted-foreground mb-1">Time Created</div>
          <div className="text-xs text-foreground flex items-center gap-1">
            <Clock className="w-3 h-3" />
            {formatTimeAgo(position.timeCreated)}
          </div>
        </div>
      </div>

      {/* Execution Indicator */}
      <div className="mb-3">
        <div className="flex items-center gap-2">
          <div className="flex-1 h-1.5 bg-muted/30 rounded-full overflow-hidden">
            <div className="h-full bg-orange-500 animate-pulse" style={{ width: '60%' }} />
          </div>
          <span className="text-xs text-orange-400">Waiting</span>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between">
        <div className="text-xs text-muted-foreground">
          Created {formatTimeAgo(position.timeCreated)}
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onModify?.(position.id)}
            className="text-xs px-2 py-1 h-6 hover:bg-blue-500/10 hover:border-blue-500/30 hover:text-blue-400"
          >
            <Edit3 className="w-3 h-3 mr-1" />
            Modify
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onCancel?.(position.id)}
            className="text-xs px-2 py-1 h-6 hover:bg-red-500/10 hover:border-red-500/30 hover:text-red-400"
          >
            <X className="w-3 h-3 mr-1" />
            Cancel
          </Button>
        </div>
      </div>
    </div>
  )
}