'use client'

import { useState, useEffect, forwardRef, useImperative<PERSON><PERSON>le, useCallback, useMemo } from 'react'
import { TokenSelector } from './TokenSelector'
import { ExitStrategySelector } from './ExitStrategySelector'
import { CreateStrategyModal } from './CreateStrategyModal'

import { cn, loadCustomTokenNamesFromStorage, getCustomTokenName, formatSOL, formatSOLBalance, parseSOLAmount, type CustomTokenName } from '@/lib/utils'
import { useExitStrategyStore } from '@/stores/exitStrategyStore'
import { useSettingsStore } from '@/stores/settingsStore'
import { useWebSocketStore, usePriceSubscription, useAutoConnect } from '@/stores/websocketStore'
import { usePositionStore } from '@/stores/positionStore'
import { useWalletBalance, useSOLBalance } from '@/hooks/useWalletBalance'

interface Token {
  address: string
  symbol: string
  name: string
  logoURI?: string
  decimals: number
  verified?: boolean
}

const SOL_TOKEN: Token = {
  address: 'So11111111111111111111111111111111111111112',
  symbol: 'S<PERSON>',
  name: '<PERSON>ana',
  decimals: 9,
  verified: true
}

interface SwapInterfaceProps {
  className?: string
}

interface TradingPreset {
  id: string
  name: string
  displayPriorityFee: string
  displaySlippageLimit: string
  displayMevProtection: string
  priorityFeeLamports: number
  slippageBasisPoints: number
  slippagePercentage: number
  mevProtectionEnabled: boolean
  active: boolean
}

interface ExitStrategy {
  id: string
  name: string
  description: string
  usageCount: number
  winRate: number
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
  stopLoss: { percentage: number } | null
  profitTargets: { target: number; sellPercentage: number }[]
  trailingStop: { percentage: number } | null
  moonBag: { percentage: number; targetGain: number } | null
}

export interface SwapInterfaceRef {
  setFromToken: (token: Token) => void
  setToToken: (token: Token) => void
}

export const SwapInterface = forwardRef<SwapInterfaceRef, SwapInterfaceProps>(({ className }, ref) => {
  const [fromToken, setFromToken] = useState<Token>(SOL_TOKEN)
  const [toToken, setToToken] = useState<Token | undefined>()
  const [fromAmount, setFromAmount] = useState('')
  const [toAmount, setToAmount] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [activePreset, setActivePreset] = useState('DEFAULT')
  const [slippageTolerance, setSlippageTolerance] = useState(1.0)
  const [priorityFeeLamports, setPriorityFeeLamports] = useState(10000000) // 0.01 SOL default - matches DEFAULT preset
  const [mevProtectionEnabled, setMevProtectionEnabled] = useState(true)
  const [customTokenNames, setCustomTokenNames] = useState<Record<string, CustomTokenName>>({})
  const [isCreateStrategyModalOpen, setIsCreateStrategyModalOpen] = useState(false)
  const [showStrategyModal, setShowStrategyModal] = useState(false)
  const [liveQuote, setLiveQuote] = useState<any>(null)
  const [quoteLoading, setQuoteLoading] = useState(false)
  const [transactionStatus, setTransactionStatus] = useState<string | null>(null)
  const [executingTrade, setExecutingTrade] = useState(false)


  // Use store for strategy management
  const { strategies, selectedStrategy, setSelectedStrategy, addStrategy } = useExitStrategyStore()
  
  // Use settings store for quick amount buttons
  const { settings } = useSettingsStore()
  
  // Real-time features - temporarily disabled
  const wsStatus = useAutoConnect(false)
  const { addPosition, totalValue, totalPnl } = usePositionStore()
  
  // Real wallet balance
  const { balance: walletBalance, solBalance, getTokenBalance, loading: balanceLoading } = useWalletBalance(0.5, true)
  
  // Price subscriptions for selected tokens
  const tokenAddresses = useMemo(() => 
    [fromToken.address, toToken?.address].filter(Boolean) as string[], 
    [fromToken.address, toToken?.address]
  )
  const { prices, connected } = usePriceSubscription(tokenAddresses, false) // Temporarily disabled real-time updates
  
  // Expose methods to parent component via ref
  useImperativeHandle(ref, () => ({
    setFromToken: (token: Token) => {
      // Force reload custom names to ensure we have the latest data
      setCustomTokenNames(loadCustomTokenNamesFromStorage())
      setFromToken(token)
    },
    setToToken: (token: Token) => {
      // Force reload custom names to ensure we have the latest data
      setCustomTokenNames(loadCustomTokenNamesFromStorage())
      setToToken(token)
    }
  }), [])

  // Auto-select default strategy only on initial load if none is selected
  useEffect(() => {
    if (!selectedStrategy && strategies.length > 0) {
      const defaultStrategy = strategies.find(strategy => strategy.isDefault)
      if (defaultStrategy) {
        console.log('SwapInterface: Auto-selecting default strategy:', defaultStrategy.name)
        setSelectedStrategy(defaultStrategy)
      }
    }
  }, [strategies.length, selectedStrategy, setSelectedStrategy]) // Include missing dependencies

  // Load custom token names on mount and listen for changes
  useEffect(() => {
    const loadNames = () => {
      setCustomTokenNames(loadCustomTokenNamesFromStorage())
    }

    loadNames()

    // Listen for storage changes to sync across tabs/components
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'memetrader-custom-token-names') {
        loadNames()
      }
    }

    window.addEventListener('storage', handleStorageChange)

    // Also listen for custom events from the same tab
    const handleCustomNamesChange = () => {
      console.log('SwapInterface: Custom token names changed, reloading...')
      loadNames()
    }

    window.addEventListener('customTokenNamesChanged', handleCustomNamesChange)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
      window.removeEventListener('customTokenNamesChanged', handleCustomNamesChange)
    }
  }, [])

  // Helper function to get display name for a token
  const getTokenDisplayName = (token: Token): string => {
    const customName = getCustomTokenName(token.address, customTokenNames)
    return customName ? customName.customName : token.symbol
  }

  // Helper function to get display label with original symbol
  const getTokenDisplayLabel = (token: Token): string => {
    const customName = getCustomTokenName(token.address, customTokenNames)
    return customName ? `${customName.customName} (${token.symbol})` : token.symbol
  }

  // Helper function to get a unique identifier for a token (for debugging/clarity)
  const getTokenUniqueId = (token: Token): string => {
    return `${token.address.slice(0, 8)}...${token.address.slice(-4)}`
  }

  // Helper function to get safe display info for unclear tokens
  const getTokenSafeDisplay = (token: Token): { name: string; id: string; hasCustomName: boolean } => {
    const customName = getCustomTokenName(token.address, customTokenNames)
    const hasCustomName = !!customName
    const name = hasCustomName ? `${customName.customName} (${token.symbol})` : token.symbol
    const id = getTokenUniqueId(token)

    return { name, id, hasCustomName }
  }

  const tradingPresets: TradingPreset[] = [
    {
      id: 'default',
      name: 'DEFAULT',
      displayPriorityFee: '0.01 SOL',
      displaySlippageLimit: '1.0%',
      displayMevProtection: 'Standard',
      priorityFeeLamports: 10000000, // 0.01 SOL = 10,000,000 lamports
      slippageBasisPoints: 100, // 1.0% = 100 basis points
      slippagePercentage: 1.0,
      mevProtectionEnabled: true,
      active: true
    },
    {
      id: 'vol',
      name: 'VOL',
      displayPriorityFee: '0.02 SOL',
      displaySlippageLimit: '2.0%',
      displayMevProtection: 'Enhanced',
      priorityFeeLamports: 20000000, // 0.02 SOL = 20,000,000 lamports
      slippageBasisPoints: 200, // 2.0% = 200 basis points
      slippagePercentage: 2.0,
      mevProtectionEnabled: true,
      active: false
    },
    {
      id: 'dead',
      name: 'DEAD',
      displayPriorityFee: '0.005 SOL',
      displaySlippageLimit: '0.5%',
      displayMevProtection: 'Maximum',
      priorityFeeLamports: 5000000, // 0.005 SOL = 5,000,000 lamports
      slippageBasisPoints: 50, // 0.5% = 50 basis points
      slippagePercentage: 0.5,
      mevProtectionEnabled: true,
      active: false
    },
    {
      id: 'nun',
      name: 'NUN',
      displayPriorityFee: '0.015 SOL',
      displaySlippageLimit: '1.5%',
      displayMevProtection: 'Standard',
      priorityFeeLamports: 15000000, // 0.015 SOL = 15,000,000 lamports
      slippageBasisPoints: 150, // 1.5% = 150 basis points
      slippagePercentage: 1.5,
      mevProtectionEnabled: true,
      active: false
    },
    {
      id: 'p5',
      name: 'P5',
      displayPriorityFee: '0.03 SOL',
      displaySlippageLimit: '3.0%',
      displayMevProtection: 'Enhanced',
      priorityFeeLamports: 30000000, // 0.03 SOL = 30,000,000 lamports
      slippageBasisPoints: 300, // 3.0% = 300 basis points
      slippagePercentage: 3.0,
      mevProtectionEnabled: true,
      active: false
    }
  ]

  const handleSwapTokens = () => {
    const tempToken = fromToken
    setFromToken(toToken || SOL_TOKEN)
    setToToken(tempToken)

    // Swap amounts
    const tempAmount = fromAmount
    setFromAmount(toAmount)
    setToAmount(tempAmount)
  }

  const handleFromAmountChange = useCallback((value: string) => {
    setFromAmount(value)
    
    // Get live quote if both tokens are selected
    if (value && toToken && parseFloat(value) > 0) {
      getLiveQuote(value)
    } else {
      setToAmount('')
      setLiveQuote(null)
    }
  }, [toToken])
  
  // Get live quote from Jupiter API (fallback when backend is not available)
  const getLiveQuote = useCallback(async (amount: string) => {
    if (!toToken || !amount || parseFloat(amount) <= 0) return
    
    setQuoteLoading(true)
    try {
      // Try backend first
      const backendResponse = await fetch('/api/trading/quote/live', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify({
          tokenIn: fromToken.address,
          tokenOut: toToken.address,
          amount: parseFloat(amount) * Math.pow(10, fromToken.decimals),
          slippage: slippageTolerance / 100
        })
      })
      
      if (backendResponse.ok) {
        const data = await backendResponse.json()
        if (data.success) {
          setLiveQuote(data.data)
          const outputAmount = parseFloat(data.data.outAmount) / Math.pow(10, toToken.decimals)
          setToAmount(formatSOL(outputAmount))
          return
        }
      }
      
      // Fallback to Jupiter API directly
      const jupiterUrl = `https://quote-api.jup.ag/v6/quote?inputMint=${fromToken.address}&outputMint=${toToken.address}&amount=${parseFloat(amount) * Math.pow(10, fromToken.decimals)}&slippageBps=${Math.floor(slippageTolerance * 100)}`
      
      console.log('Calling Jupiter API fallback:', jupiterUrl)
      
      const jupiterResponse = await fetch(jupiterUrl)
      if (jupiterResponse.ok) {
        const jupiterData = await jupiterResponse.json()
        
        console.log('Jupiter API response:', {
          priceImpactPct: jupiterData.priceImpactPct,
          inAmount: jupiterData.inAmount,
          outAmount: jupiterData.outAmount
        })
        
        // Transform Jupiter response to match expected format
        const transformedQuote = {
          ...jupiterData,
          priceImpactPct: parseFloat(jupiterData.priceImpactPct || '0')
        }
        
        setLiveQuote(transformedQuote)
        const outputAmount = parseFloat(jupiterData.outAmount) / Math.pow(10, toToken.decimals)
        setToAmount(formatSOL(outputAmount))
      } else {
        console.error('Jupiter API failed:', jupiterResponse.status, await jupiterResponse.text())
      }
      
    } catch (error) {
      console.error('Failed to get live quote:', error)
    } finally {
      setQuoteLoading(false)
    }
  }, [fromToken, toToken, slippageTolerance])
  
  // Execute trade with real-time monitoring
  const executeTrade = useCallback(async () => {
    if (!selectedStrategy || !fromToken || !toToken || !fromAmount || !liveQuote) return
    
    setExecutingTrade(true)
    setTransactionStatus('Preparing transaction...')
    
    try {
      // First create exit strategy if it's a new one
      let strategyId = selectedStrategy.id
      if (!strategyId) {
        setTransactionStatus('Creating exit strategy...')
        const strategyResponse = await fetch('/api/trading/strategies', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
          },
          body: JSON.stringify({
            type: 'CUSTOM',
            stopLoss: selectedStrategy.stopLoss,
            profitTargets: selectedStrategy.profitTargets,
            moonBag: selectedStrategy.moonBag,
            customName: selectedStrategy.name
          })
        })
        
        if (strategyResponse.ok) {
          const strategyData = await strategyResponse.json()
          strategyId = strategyData.data.strategyId
        } else {
          throw new Error('Failed to create exit strategy')
        }
      }
      
      setTransactionStatus('Executing swap...')
      
      // Get valid auth token
      const { getValidAuthToken } = await import('@/lib/auth')
      const authToken = await getValidAuthToken()
      
      if (!authToken) {
        throw new Error('Authentication failed')
      }
      
      // Execute the trade
      const tradeResponse = await fetch('/api/trading/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify({
          tokenIn: fromToken.address,
          tokenOut: toToken.address,
          amount: parseFloat(fromAmount) * Math.pow(10, fromToken.decimals),
          slippage: slippageTolerance / 100,
          preset: activePreset,
          strategyId: strategyId
        })
      })
      
      if (tradeResponse.ok) {
        const tradeData = await tradeResponse.json()
        
        if (tradeData.success) {
          setTransactionStatus('Transaction confirmed!')
          
          // Subscribe to transaction status updates
          if (tradeData.data.transactionHash) {
            useWebSocketStore.getState().sendMessage({
              type: 'transaction_update',
              data: {
                action: 'subscribe',
                transactionHashes: [tradeData.data.transactionHash]
              }
            })
          }
          
          // Reset form
          setFromAmount('')
          setToAmount('')
          setLiveQuote(null)
          
          // Show success for 3 seconds then clear
          setTimeout(() => {
            setTransactionStatus(null)
          }, 3000)
          
        } else {
          throw new Error(tradeData.error || 'Trade execution failed')
        }
      } else {
        const errorData = await tradeResponse.json()
        throw new Error(errorData.message || 'Trade execution failed')
      }
      
    } catch (error) {
      console.error('Trade execution failed:', error)
      setTransactionStatus(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
      
      // Clear error after 5 seconds
      setTimeout(() => {
        setTransactionStatus(null)
      }, 5000)
    } finally {
      setExecutingTrade(false)
    }
  }, [selectedStrategy, fromToken, toToken, fromAmount, liveQuote, slippageTolerance, activePreset])

  // Dynamic quick amount handler based on settings
  const handleQuickAmount = (quickAmount: any) => {
    // Get actual balance for the from token
    const balance = getTokenBalance(fromToken.address)
    
    if (quickAmount.type === 'sol') {
      // Fixed SOL amount
      const formattedAmount = formatSOL(quickAmount.value)
      handleFromAmountChange(formattedAmount)
    } else if (quickAmount.type === 'percentage') {
      // Percentage of balance
      const amount = balance * (quickAmount.value / 100)
      const formattedAmount = formatSOL(amount)
      handleFromAmountChange(formattedAmount)
    }
  }



  const getUSDValue = (amount: string, token: Token) => {
    if (!amount || !token) return '$0.00'

    // Use real-time prices if available
    const realTimePrice = prices[token.address]
    if (realTimePrice && realTimePrice.price) {
      const usdValue = parseFloat(amount) * realTimePrice.price
      return `$${usdValue.toFixed(2)}`
    }

    // Fallback to mock prices for demonstration
    const mockPrices: Record<string, number> = {
      'SOL': 185.5,
      'USDC': 1.0,
      'USDT': 1.0,
      'RAY': 2.45,
      'BONK': 0.000025,
      'WIF': 1.85
    }

    const price = mockPrices[token.symbol] || 0
    const usdValue = parseFloat(amount) * price
    return `$${usdValue.toFixed(2)}`
  }

  const currentPreset = tradingPresets.find(p => p.name === activePreset) || tradingPresets[0]

  // Handler for preset selection that updates all trading parameters
  const handlePresetSelect = (presetName: string) => {
    const preset = tradingPresets.find(p => p.name === presetName)
    if (preset) {
      setActivePreset(presetName)
      setSlippageTolerance(preset.slippagePercentage)
      setPriorityFeeLamports(preset.priorityFeeLamports)
      setMevProtectionEnabled(preset.mevProtectionEnabled)
    }
  }

  return (
    <div className={cn("space-y-6 overflow-visible", className)}>
      <div className="flex items-center space-x-3">
        <div className="w-1 h-6 bg-gradient-to-b from-primary to-secondary rounded-full"></div>
        <h3 className="text-xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
          Professional Swap Interface
        </h3>
      </div>

      {/* Trading Presets Section */}
      <div className="bg-gradient-to-br from-card/60 to-card/30 backdrop-blur-sm border border-border/40 rounded-xl p-5 shadow-lg">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-lg font-semibold text-foreground">Trading Presets</h4>
            <div className="text-xs text-muted-foreground bg-muted/30 px-2 py-1 rounded-full">
              Active: <span className="font-medium text-primary">{activePreset}</span>
            </div>
          </div>

          {/* Preset Tabs */}
          <div className="flex space-x-2 overflow-x-auto pb-1">
            {tradingPresets.map((preset) => (
              <button
                key={preset.id}
                onClick={() => handlePresetSelect(preset.name)}
                className={cn(
                  "px-4 py-2 rounded-lg font-medium transition-all duration-200 border whitespace-nowrap",
                  "hover:scale-105 hover:shadow-md",
                  preset.name === activePreset
                    ? "bg-primary text-primary-foreground border-primary shadow-lg shadow-primary/25"
                    : "bg-background/60 text-muted-foreground border-border/50 hover:border-primary/30 hover:text-foreground"
                )}
              >
                {preset.name}
              </button>
            ))}
          </div>

          {/* Preset Details */}
          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-1">
              <div className="text-xs text-muted-foreground">Priority Fee</div>
              <div className="text-sm font-medium text-foreground bg-background/40 px-2 py-1 rounded border border-border/30">
                {currentPreset.displayPriorityFee}
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-xs text-muted-foreground">Slippage Limit</div>
              <div className="text-sm font-medium text-foreground bg-background/40 px-2 py-1 rounded border border-border/30">
                {currentPreset.displaySlippageLimit}
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-xs text-muted-foreground">MEV Protection</div>
              <div className="text-sm font-medium text-foreground bg-background/40 px-2 py-1 rounded border border-border/30">
                {currentPreset.displayMevProtection}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="relative">
        {/* Main Swap Container */}
        <div className="relative bg-gradient-to-br from-card/80 to-card/40 backdrop-blur-sm border border-border/50 rounded-2xl p-6 shadow-2xl overflow-visible">
          {/* From Token Section */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-muted-foreground flex items-center space-x-2">
                <span>From</span>
                <div className="w-2 h-2 rounded-full bg-primary/60 animate-pulse"></div>
              </label>
              <div className="text-xs text-muted-foreground bg-muted/30 px-2 py-1 rounded-full">
                {(() => {
                  const fromDisplay = getTokenSafeDisplay(fromToken)
                  const displayName = fromDisplay.hasCustomName || fromToken.symbol !== 'UNVERIFIED'
                    ? fromDisplay.name
                    : `${fromDisplay.name} (${fromDisplay.id})`
                  const currentBalance = getTokenBalance(fromToken.address)
                  const balanceFormatted = formatSOLBalance(currentBalance)
                  return (
                    <>Balance: <span className="font-medium text-foreground">{balanceLoading ? 'Loading...' : `${balanceFormatted} ${displayName}`}</span></>
                  )
                })()}
              </div>
            </div>

            <div className="relative group overflow-visible">
              <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative bg-background/80 backdrop-blur-sm border border-border/50 rounded-xl p-4 hover:border-primary/30 transition-all duration-300 overflow-visible">
                <div className="flex items-center justify-between mb-4">
                  <TokenSelector
                    selectedToken={fromToken}
                    onTokenSelect={setFromToken}
                    className="w-auto"
                    showAddressInput={true}
                  />

                  <div className="flex flex-col items-end space-y-1">
                    <input
                      type="text"
                      placeholder="0.00"
                      value={fromAmount}
                      onChange={(e) => handleFromAmountChange(e.target.value)}
                      className="bg-transparent border-none outline-none text-right text-2xl font-bold w-40 placeholder:text-muted-foreground/50"
                    />
                    <div className="text-sm text-muted-foreground font-medium">
                      {getUSDValue(fromAmount, fromToken)}
                    </div>
                  </div>
                </div>

                {/* Quick Amount Buttons */}
                {settings?.trading?.quickAmounts && (
                  <div className="flex flex-wrap justify-center mt-4 p-3 rounded-lg bg-gradient-to-r from-muted/30 to-muted/20 border border-border/30" style={{ gap: '0.1rem' }}>
                    {settings.trading.quickAmounts.map((qa, i) => (
                      <button 
                        key={i} 
                        className="px-2 py-1 text-xs font-medium rounded-lg transition-all duration-200 hover:scale-105 bg-card/80 hover:bg-card text-card-foreground border border-border hover:border-border/60 shadow-sm hover:shadow-md backdrop-blur-sm"
                        onClick={() => handleQuickAmount(qa)}
                        title={`${qa.type}: ${qa.value}`}
                      >
                        {qa.label}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Enhanced Swap Direction Button */}
          <div className="flex justify-center py-4">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-primary to-secondary rounded-full blur-md opacity-30"></div>
              <button
                onClick={handleSwapTokens}
                className="relative p-4 rounded-full bg-gradient-to-r from-primary/90 to-secondary/90 hover:from-primary hover:to-secondary border border-primary/30 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 group"
              >
                <svg className="w-5 h-5 text-white group-hover:rotate-180 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                </svg>
              </button>
            </div>
          </div>

          {/* To Token Section */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-muted-foreground flex items-center space-x-2">
                <span>To</span>
                <div className="w-2 h-2 rounded-full bg-secondary/60 animate-pulse"></div>
              </label>
              {toToken && (
                <div className="text-xs text-muted-foreground bg-muted/30 px-2 py-1 rounded-full">
                  {(() => {
                    const toDisplay = getTokenSafeDisplay(toToken)
                    const displayName = toDisplay.hasCustomName || toToken.symbol !== 'UNVERIFIED'
                      ? toDisplay.name
                      : `${toDisplay.name} (${toDisplay.id})`
                    const currentBalance = getTokenBalance(toToken.address)
                    const balanceFormatted = formatSOLBalance(currentBalance)
                    return (
                      <>Balance: <span className="font-medium text-foreground">{balanceLoading ? 'Loading...' : `${balanceFormatted} ${displayName}`}</span></>
                    )
                  })()}
                </div>
              )}
            </div>

            <div className="relative group overflow-visible">
              <div className="absolute inset-0 bg-gradient-to-r from-secondary/20 to-primary/20 rounded-xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative bg-background/80 backdrop-blur-sm border border-border/50 rounded-xl p-4 hover:border-secondary/30 transition-all duration-300 overflow-visible">
                <div className="flex items-center justify-between">
                  <TokenSelector
                    selectedToken={toToken}
                    onTokenSelect={setToToken}
                    placeholder="Select token"
                    className="w-auto"
                    showAddressInput={true}
                  />

                  <div className="flex flex-col items-end space-y-1">
                    <div className="text-2xl font-bold text-right min-w-[160px]">
                      {toAmount ? formatSOL(toAmount) : '0'}
                    </div>
                    <div className="text-sm text-muted-foreground font-medium">
                      {toToken ? getUSDValue(toAmount, toToken) : '$0.00'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Trade Information */}
        {fromToken && toToken && fromAmount && (
          <div className="mt-6 space-y-4">
            <div className="bg-gradient-to-r from-muted/40 to-muted/20 backdrop-blur-sm border border-border/30 rounded-xl p-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-muted-foreground font-medium">Exchange Rate</span>
                    <span className="text-sm font-bold">
                      {(() => {
                        const fromDisplay = getTokenSafeDisplay(fromToken)
                        const toDisplay = getTokenSafeDisplay(toToken)
                        
                        let rate = '185.5' // fallback
                        if (liveQuote && liveQuote.inAmount && liveQuote.outAmount) {
                          const inAmount = parseFloat(liveQuote.inAmount) / Math.pow(10, fromToken.decimals)
                          const outAmount = parseFloat(liveQuote.outAmount) / Math.pow(10, toToken.decimals)
                          rate = (outAmount / inAmount).toFixed(4)
                        }

                        // Show address snippets for unclear token pairs
                        if (!fromDisplay.hasCustomName && !toDisplay.hasCustomName &&
                            (fromToken.symbol === 'UNVERIFIED' || toToken.symbol === 'UNVERIFIED')) {
                          return `1 ${fromDisplay.name} (${fromDisplay.id}) = ${rate} ${toDisplay.name} (${toDisplay.id})`
                        }

                        return `1 ${fromDisplay.name} = ${rate} ${toDisplay.name}`
                      })()}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-muted-foreground font-medium">Price Impact</span>
                    <span className={cn(
                      "text-sm font-bold flex items-center space-x-1",
                      liveQuote?.priceImpactPct ? (
                        liveQuote.priceImpactPct < 1 ? "text-green-400" :
                        liveQuote.priceImpactPct < 3 ? "text-yellow-400" : "text-red-400"
                      ) : "text-green-400"
                    )}>
                      <span>{liveQuote?.priceImpactPct ? `${liveQuote.priceImpactPct.toFixed(2)}%` : '< 0.1%'}</span>
                      <div className={cn(
                        "w-2 h-2 rounded-full",
                        liveQuote?.priceImpactPct ? (
                          liveQuote.priceImpactPct < 1 ? "bg-green-400" :
                          liveQuote.priceImpactPct < 3 ? "bg-yellow-400" : "bg-red-400"
                        ) : "bg-green-400"
                      )}></div>
                    </span>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-muted-foreground font-medium">Min. Received</span>
                    <span className="text-sm font-bold">
                      {(() => {
                        const toDisplay = getTokenSafeDisplay(toToken)
                        const rawAmount = parseSOLAmount(toAmount)
                        const minReceived = formatSOL(rawAmount * 0.995)

                        // Show address snippet for unclear tokens
                        if (!toDisplay.hasCustomName && toToken.symbol === 'UNVERIFIED') {
                          return `${minReceived} ${toDisplay.name} (${toDisplay.id})`
                        }

                        return `${minReceived} ${toDisplay.name}`
                      })()}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-muted-foreground font-medium">Network Fee</span>
                    <span className="text-sm font-bold">~{formatSOL(0.00025)} SOL</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Enhanced Trade Button */}
        <div className="mt-6">
          <button
            onClick={() => {
              if (!selectedStrategy && fromToken && toToken && fromAmount) {
                setShowStrategyModal(true)
                return
              }
              // Handle actual trade execution here
              if (selectedStrategy && fromToken && toToken && fromAmount) {
                executeTrade()
              }
            }}
            disabled={!fromToken || !toToken || !fromAmount || isLoading || executingTrade}
            className={cn(
              "relative w-full px-6 py-4 rounded-xl font-bold text-lg transition-all duration-300 overflow-hidden group",
              (!fromToken || !toToken || !fromAmount)
                ? "bg-muted/50 text-muted-foreground cursor-not-allowed border border-border/30"
                : "bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90 text-white shadow-lg hover:shadow-xl hover:scale-[1.02] border border-primary/30"
            )}
          >
            {!(!fromToken || !toToken || !fromAmount || !selectedStrategy) && (
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            )}
            <div className="relative flex items-center justify-center space-x-3">
              {(isLoading || executingTrade) ? (
                <>
                  <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  <span>{transactionStatus || 'Processing Transaction...'}</span>
                </>
              ) : !fromToken || !toToken ? (
                <>
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  <span>Select tokens to continue</span>
                </>
              ) : !fromAmount ? (
                <>
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                  </svg>
                  <span>Enter amount</span>
                </>
              ) : !selectedStrategy ? (
                <>
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                  <span>Select exit strategy</span>
                </>
              ) : (
                <>
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  <span>
                    {(() => {
                      const fromDisplay = getTokenSafeDisplay(fromToken)
                      const toDisplay = getTokenSafeDisplay(toToken)

                      // For unclear tokens, show address snippet
                      if (!fromDisplay.hasCustomName && fromToken.symbol === 'UNVERIFIED' && !toDisplay.hasCustomName && toToken.symbol === 'UNVERIFIED') {
                        return `Swap ${fromDisplay.name} (${fromDisplay.id}) → ${toDisplay.name} (${toDisplay.id})`
                      }

                      return `Swap ${fromDisplay.name} → ${toDisplay.name}`
                    })()}
                  </span>
                </>
              )}
            </div>
          </button>
        </div>

        {/* Enhanced Advanced Settings */}
        <div className="mt-6">
          <details className="group">
            <summary className="flex items-center justify-between p-4 bg-gradient-to-r from-muted/30 to-muted/20 backdrop-blur-sm border border-border/30 rounded-xl cursor-pointer hover:from-muted/40 hover:to-muted/30 transition-all duration-300">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-primary/20 to-secondary/20 flex items-center justify-center">
                  <svg className="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <span className="text-sm font-semibold">Advanced Settings</span>
              </div>
              <svg className="w-5 h-5 transition-transform duration-300 group-open:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </summary>

            <div className="mt-4 p-5 bg-gradient-to-br from-background/80 to-muted/20 backdrop-blur-sm border border-border/30 rounded-xl space-y-5">
              <div className="space-y-3">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-muted-foreground">Slippage Tolerance</span>
                      <div className="w-1 h-1 rounded-full bg-primary"></div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-bold text-primary">{formatSOL(slippageTolerance, 1)}%</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="relative">
                      <input
                        type="range"
                        min="0.1"
                        max="10"
                        step="0.1"
                        value={slippageTolerance}
                        onChange={(e) => setSlippageTolerance(parseFloat(e.target.value))}
                        className="w-full h-2 bg-muted/60 rounded-lg appearance-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary/30 transition-all duration-200"
                        style={{
                          background: `linear-gradient(to right, hsl(var(--primary)) 0%, hsl(var(--primary)) ${((slippageTolerance - 0.1) / 9.9) * 100}%, hsl(var(--muted)) ${((slippageTolerance - 0.1) / 9.9) * 100}%, hsl(var(--muted)) 100%)`
                        }}
                      />
                    </div>

                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>0.1%</span>
                      <span className="text-center">Safe</span>
                      <span className="text-center">Risky</span>
                      <span>10%</span>
                    </div>

                    <div className="flex justify-center space-x-2 mt-2">
                      {[0.5, 1.0, 2.0, 5.0].map((preset) => (
                        <button
                          key={preset}
                          onClick={() => setSlippageTolerance(preset)}
                          className={cn(
                            "px-2 py-1 text-xs font-medium rounded-md transition-all duration-200 hover:scale-105",
                            Math.abs(slippageTolerance - preset) < 0.05
                              ? "bg-primary text-primary-foreground shadow-sm"
                              : "bg-muted/60 hover:bg-muted text-muted-foreground hover:text-foreground"
                          )}
                        >
                          {preset}%
                        </button>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-muted-foreground">MEV Protection</span>
                    <div className="w-1 h-1 rounded-full bg-secondary"></div>
                  </div>
                  <select
                    value={mevProtectionEnabled ? currentPreset.displayMevProtection : 'Disabled'}
                    onChange={(e) => setMevProtectionEnabled(e.target.value !== 'Disabled')}
                    className="px-3 py-1.5 text-xs font-medium bg-background/80 border border-border/50 rounded-lg focus:border-primary/50 focus:ring-2 focus:ring-primary/20 transition-all"
                  >
                    <option value="Disabled">Disabled</option>
                    <option value="Standard">Standard</option>
                    <option value="Enhanced">Enhanced</option>
                    <option value="Maximum">Maximum</option>
                  </select>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-muted-foreground">Priority Fee</span>
                    <div className="w-1 h-1 rounded-full bg-primary"></div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={formatSOL(priorityFeeLamports / 1000000000)}
                      onChange={(e) => {
                        const solValue = parseSOLAmount(e.target.value)
                        setPriorityFeeLamports(Math.round(solValue * 1000000000))
                      }}
                      className="w-32 px-3 py-1.5 text-xs font-medium bg-background/80 border border-border/50 rounded-lg text-right focus:border-primary/50 focus:ring-2 focus:ring-primary/20 transition-all"
                    />
                    <span className="text-xs text-muted-foreground font-medium">SOL</span>
                  </div>
                </div>
              </div>
            </div>
          </details>
        </div>
        
        {/* Real-time Status Panel */}
        {connected && (
          <div className="mt-6 bg-gradient-to-r from-muted/40 to-muted/20 backdrop-blur-sm border border-border/30 rounded-xl p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 rounded-full bg-green-400 animate-pulse"></div>
                <span className="text-sm font-medium text-foreground">Real-time Data Active</span>
              </div>
              <div className="text-xs text-muted-foreground">
                Portfolio: ${walletBalance?.totalValueUSD.toFixed(2) || '0.00'} ({totalPnl >= 0 ? '+' : ''}{formatSOL(totalPnl)})
              </div>
            </div>
            
            {fromToken && toToken && (
              <div className="grid grid-cols-2 gap-4 text-xs">
                <div>
                  <span className="text-muted-foreground">Live {getTokenDisplayName(fromToken)} Price:</span>
                  <span className="ml-2 font-medium">
                    {prices[fromToken.address]?.price ? 
                      `$${prices[fromToken.address].price.toFixed(4)}` : 
                      'Loading...'
                    }
                  </span>
                </div>
                <div>
                  <span className="text-muted-foreground">Live {getTokenDisplayName(toToken)} Price:</span>
                  <span className="ml-2 font-medium">
                    {prices[toToken.address]?.price ? 
                      `$${prices[toToken.address].price.toFixed(4)}` : 
                      'Loading...'
                    }
                  </span>
                </div>
              </div>
            )}
            
            {quoteLoading && (
              <div className="flex items-center space-x-2 mt-2 text-xs text-muted-foreground">
                <div className="w-3 h-3 border border-current border-t-transparent rounded-full animate-spin"></div>
                <span>Getting live quote...</span>
              </div>
            )}
          </div>
        )}
        
        {/* Transaction Status */}
        {transactionStatus && (
          <div className="mt-4 p-4 bg-gradient-to-r from-blue-500/20 to-blue-600/20 border border-blue-500/30 rounded-xl">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 border-2 border-blue-400 border-t-transparent rounded-full animate-spin"></div>
              <span className="text-sm font-medium text-blue-400">{transactionStatus}</span>
            </div>
          </div>
        )}

        {/* Exit Strategy Selector */}
        <div className="mt-6">
          <ExitStrategySelector
            selectedStrategy={selectedStrategy}
            onStrategySelect={(strategy) => {
              setSelectedStrategy(strategy)
              setShowStrategyModal(false)
            }}
            onCreateCustom={() => setIsCreateStrategyModalOpen(true)}
            autoOpen={showStrategyModal}
          />
        </div>
      </div>

      {/* Create Strategy Modal */}
      <CreateStrategyModal
        isOpen={isCreateStrategyModalOpen}
        onClose={() => setIsCreateStrategyModalOpen(false)}
        onCreateStrategy={(strategy) => {
          // Add strategy to store and set as selected
          addStrategy({
            name: strategy.name,
            description: strategy.description || '',
            riskLevel: strategy.locked ? 'LOW' : 'MEDIUM',
            stopLoss: strategy.stopLoss,
            profitTargets: strategy.profitTargets,
            trailingStop: strategy.trailingStop,
            moonBag: strategy.moonBag,
            isDefault: strategy.isDefault || false,
            locked: strategy.locked || false
          })
          setIsCreateStrategyModalOpen(false)
        }}
      />
    </div>
  )
})
