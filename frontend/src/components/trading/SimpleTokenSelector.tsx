'use client'

import { useState, useRef, useEffect } from 'react'
import { cn } from '@/lib/utils'

interface Token {
  address: string
  symbol: string
  name: string
  decimals: number
  verified?: boolean
}

interface SimpleTokenSelectorProps {
  selectedToken?: Token
  onTokenSelect: (token: Token) => void
  placeholder?: string
  className?: string
}

const POPULAR_TOKENS: Token[] = [
  {
    address: 'So11111111111111111111111111111111111111112',
    symbol: 'SOL',
    name: '<PERSON><PERSON>',
    decimals: 9,
    verified: true
  },
  {
    address: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
    symbol: 'USDC',
    name: 'USD Coin',
    decimals: 6,
    verified: true
  },
  {
    address: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
    symbol: 'USDT',
    name: 'Tether <PERSON>',
    decimals: 6,
    verified: true
  },
  {
    address: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
    symbol: 'BON<PERSON>',
    name: 'Bon<PERSON>',
    decimals: 5,
    verified: true
  }
]

export function SimpleTokenSelector({
  selectedToken,
  onTokenSelect,
  placeholder = "Select token",
  className
}: SimpleTokenSelectorProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const dropdownRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleTokenSelect = (token: Token) => {
    onTokenSelect(token)
    setIsOpen(false)
    setSearchQuery('')
  }

  const filteredTokens = POPULAR_TOKENS.filter(token =>
    token.symbol.toLowerCase().includes(searchQuery.toLowerCase()) ||
    token.name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className={cn("relative", className)} ref={dropdownRef}>
      {/* Button */}
      <button
        onClick={() => {
          console.log('Button clicked, current isOpen:', isOpen)
          setIsOpen(!isOpen)
        }}
        className="w-full px-4 py-3 text-left bg-gradient-to-r from-background/80 to-muted/20 border border-border/50 rounded-xl hover:from-muted/40 hover:to-muted/20 hover:border-primary/30 transition-all duration-300 shadow-sm hover:shadow-md flex items-center justify-between group"
      >
        <div className="flex items-center space-x-3">
          {selectedToken ? (
            <>
              <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center text-sm font-bold">
                {selectedToken.symbol.charAt(0)}
              </div>
              <div>
                <div className="font-medium">{selectedToken.symbol}</div>
                <div className="text-xs text-muted-foreground">{selectedToken.name}</div>
              </div>
            </>
          ) : (
            <span className="text-muted-foreground">{placeholder}</span>
          )}
        </div>
        <svg
          className={cn("w-4 h-4 transition-transform", isOpen && "rotate-180")}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-gradient-to-br from-card/95 to-background/95 backdrop-blur-xl border border-border/50 rounded-xl shadow-2xl z-[999999] max-h-80 overflow-y-auto animate-in slide-in-from-top-2 duration-200">
          <div className="p-3">
            {/* Search */}
            <input
              type="text"
              placeholder="Search tokens..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-3 py-2 text-sm bg-background border border-border rounded-lg outline-none focus:ring-2 focus:ring-primary/30"
            />
          </div>

          {/* Token List */}
          <div className="max-h-60 overflow-y-auto">
            {filteredTokens.map((token) => (
              <button
                key={token.address}
                onClick={() => handleTokenSelect(token)}
                className="w-full px-3 py-3 text-left hover:bg-gradient-to-r hover:from-primary/10 hover:to-secondary/10 transition-all duration-200 flex items-center space-x-3 rounded-lg mx-1"
              >
                <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center text-sm font-bold">
                  {token.symbol.charAt(0)}
                </div>
                <div>
                  <div className="font-medium">{token.symbol}</div>
                  <div className="text-xs text-muted-foreground">{token.name}</div>
                </div>
                {token.verified && (
                  <div className="ml-auto">
                    <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                )}
              </button>
            ))}

            {filteredTokens.length === 0 && (
              <div className="px-3 py-4 text-center text-muted-foreground text-sm">
                No tokens found
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
