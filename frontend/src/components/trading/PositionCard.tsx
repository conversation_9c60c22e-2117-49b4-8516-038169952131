'use client'

import { useState, useEffect } from 'react'
import { TrendingUp, Rocket, Target, AlertCircle, X } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface Position {
  id: string
  token: {
    symbol: string
    name: string
    address: string
  }
  amount: number
  entryPrice: number
  currentPrice: number
  entryValue: number
  currentValue: number
  pnlPercentage: number
  pnlDollar: number
  trailingStop: number
  exitMilestones: number[]
  completedExits: number
  moonBag: {
    percentage: number
    targetGain: number
    currentProgress: number
  }
  status: 'taking_profits' | 'approaching_target' | 'moon_bag' | 'trailing'
  progressToNextExit: {
    current: number
    next: number
  }
}

interface PositionCardProps {
  position: Position
  onClose?: (positionId: string) => void
  onModify?: (positionId: string) => void
}

export function PositionCard({ position, onClose, onModify }: PositionCardProps) {
  const [openedTime, setOpenedTime] = useState<string>('1h')

  // Initialize opened time on client-side only to avoid hydration mismatch
  useEffect(() => {
    setOpenedTime(`${Math.floor(Math.random() * 5 + 1)}h`)
  }, [])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 8,
      maximumFractionDigits: 8,
    }).format(price)
  }

  const formatTokenAmount = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  // Calculate stop loss and take profit prices
  const stopLossPrice = position.trailingStop
  const takeProfitPrice = position.entryPrice * (1 + (position.exitMilestones[0] || 50) / 100)

  // Get token color based on symbol
  const getTokenColor = (symbol: string) => {
    const colors = {
      'PEPE': 'bg-green-500',
      'BONK': 'bg-orange-500', 
      'DOGE': 'bg-purple-500',
      'SHIB': 'bg-red-500'
    }
    return colors[symbol as keyof typeof colors] || 'bg-blue-500'
  }

  return (
    <div className="bg-gradient-to-br from-card/80 to-card/40 backdrop-blur-sm border border-border/50 rounded-lg shadow-sm p-4 hover:shadow-md transition-all duration-200">
      <div className="flex items-center justify-between mb-3">
        {/* Left side - Token info */}
        <div className="flex items-center gap-3">
          <div className={cn("w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm", getTokenColor(position.token.symbol))}>
            {position.token.symbol.charAt(0)}
          </div>
          <div>
            <div className="flex items-center gap-2 mb-0.5">
              <h3 className="text-lg font-bold text-foreground">{position.token.symbol}</h3>
              <Badge className="bg-orange-500/20 text-orange-400 border border-orange-500/30 text-xs px-2 py-0.5">
                Buy
              </Badge>
            </div>
            <div className="text-xs text-muted-foreground">
              {formatTokenAmount(position.amount)} tokens
            </div>
          </div>
        </div>

        {/* Right side - P&L and Close button */}
        <div className="flex items-center gap-3">
          <div className="text-right">
            <div className={cn("text-lg font-bold", position.pnlPercentage >= 0 ? "text-green-400" : "text-red-400")}>
              {position.pnlPercentage >= 0 ? '+' : ''}{formatCurrency(position.pnlDollar)}
            </div>
            <div className={cn("text-xs", position.pnlPercentage >= 0 ? "text-green-400" : "text-red-400")}>
              {position.pnlPercentage >= 0 ? '+' : ''}{position.pnlPercentage.toFixed(1)}%
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onClose?.(position.id)}
            className="w-6 h-6 p-0 text-muted-foreground hover:text-foreground"
          >
            <X className="w-3 h-3" />
          </Button>
        </div>
      </div>

      {/* Price information grid */}
      <div className="grid grid-cols-4 gap-3 mb-3">
        <div>
          <div className="text-xs text-muted-foreground mb-0.5">Entry Price</div>
          <div className="text-xs font-mono text-foreground">
            {formatPrice(position.entryPrice)}
          </div>
        </div>
        <div>
          <div className="text-xs text-muted-foreground mb-0.5">Current Price</div>
          <div className="text-xs font-mono text-foreground">
            {formatPrice(position.currentPrice)}
          </div>
        </div>
        <div>
          <div className="text-xs text-muted-foreground mb-0.5">Stop Loss</div>
          <div className="text-xs font-mono text-red-400">
            {formatPrice(stopLossPrice)}
          </div>
        </div>
        <div>
          <div className="text-xs text-muted-foreground mb-0.5">Take Profit</div>
          <div className="text-xs font-mono text-green-400">
            {formatPrice(takeProfitPrice)}
          </div>
        </div>
      </div>

      {/* Footer with timestamp and action buttons */}
      <div className="flex items-center justify-between">
        <div className="text-xs text-muted-foreground">
          Opened {openedTime} ago
        </div>
        <div className="flex items-center gap-1.5">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onModify?.(position.id)}
            className="text-xs px-2 py-1 h-6"
          >
            Modify
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onClose?.(position.id)}
            className="text-xs px-2 py-1 h-6"
          >
            Close
          </Button>
        </div>
      </div>
    </div>
  )
}