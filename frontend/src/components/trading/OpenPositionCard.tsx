'use client'

import { Plus, X } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ProgressIndicator } from './ProgressIndicator'
import { Position } from '@/types/trading'
import { 
  formatCurrency, 
  formatPrice, 
  formatTokenAmount, 
  formatTimeAgo, 
  getTokenColor, 
  getStatusColor, 
  getStatusLabel 
} from '@/utils/trading'
import { cn } from '@/lib/utils'

interface OpenPositionCardProps {
  position: Position
  onClose?: (positionId: string) => void
  onAddToPosition?: (positionId: string) => void
}

export function OpenPositionCard({ position, onClose, onAddToPosition }: OpenPositionCardProps) {
  return (
    <div className="bg-gradient-to-br from-card/80 to-card/40 backdrop-blur-sm border border-border/50 rounded-xl shadow-lg p-4 hover:shadow-xl transition-all duration-300">
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          <div className={cn("w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm", getTokenColor(position.token.symbol))}>
            {position.token.symbol.charAt(0)}
          </div>
          <div>
            <div className="flex items-center gap-2 mb-0.5">
              <h3 className="text-lg font-bold text-foreground">{position.token.symbol}</h3>
              <Badge className={cn("text-xs px-1.5 py-0.5", getStatusColor(position.status))}>
                {getStatusLabel(position.status)}
              </Badge>
            </div>
            <div className="text-xs text-muted-foreground">
              {formatTokenAmount(position.amount)} tokens
            </div>
          </div>
        </div>
        
        <div className="text-right">
          <div className={cn("text-lg font-bold", position.pnlPercentage >= 0 ? "text-green-400" : "text-red-400")}>
            {position.pnlPercentage >= 0 ? '+' : ''}{position.pnlPercentage.toFixed(1)}%
          </div>
          <div className={cn("text-xs", position.pnlPercentage >= 0 ? "text-green-400" : "text-red-400")}>
            {position.pnlPercentage >= 0 ? '+' : ''}{formatCurrency(position.pnlDollar)}
          </div>
        </div>
      </div>

      {/* Progress to Next Exit */}
      <div className="mb-4">
        <ProgressIndicator 
          milestones={position.exitMilestones}
          currentProgress={position.progressToNextExit.current}
          completedExits={position.completedExits}
        />
      </div>

      {/* Price Info */}
      <div className="grid grid-cols-2 gap-3 mb-3">
        <div>
          <div className="text-xs text-muted-foreground mb-1">Entry Price</div>
          <div className="text-xs font-mono text-foreground">{formatPrice(position.entryPrice)}</div>
        </div>
        <div>
          <div className="text-xs text-muted-foreground mb-1">Current Price</div>
          <div className="text-xs font-mono text-foreground">{formatPrice(position.currentPrice)}</div>
        </div>
      </div>
      
      {/* Moon Bag */}
      <div className="mb-3 p-2 bg-muted/10 rounded-lg">
        <div className="flex justify-between items-center text-xs">
          <span className="text-muted-foreground">Moon Bag ({position.moonBag.percentage}%)</span>
          <span className="text-foreground">
            +{position.moonBag.currentProgress.toFixed(1)}% / +{position.moonBag.targetGain}%
          </span>
        </div>
      </div>

      {/* Position Metrics */}
      <div className="grid grid-cols-2 gap-2 mb-3">
        <div>
          <div className="text-xs text-muted-foreground mb-1">Position Value</div>
          <div className="text-xs font-semibold text-foreground">
            {formatCurrency(position.currentValue, 0)}
          </div>
        </div>
        <div>
          <div className="text-xs text-muted-foreground mb-1">Trailing Stop</div>
          <div className="text-xs font-mono text-red-400">
            {formatPrice(position.trailingStop)}
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between">
        <div className="text-xs text-muted-foreground">
          {position.completedExits}/{position.exitMilestones.length} exits • {formatTimeAgo(position.timeOpened)}
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onAddToPosition?.(position.id)}
            className="text-xs px-2 py-1 h-6 hover:bg-green-500/10 hover:border-green-500/30 hover:text-green-400"
          >
            <Plus className="w-3 h-3 mr-1" />
            Add
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onClose?.(position.id)}
            className="text-xs px-2 py-1 h-6 hover:bg-red-500/10 hover:border-red-500/30 hover:text-red-400"
          >
            <X className="w-3 h-3 mr-1" />
            Close
          </Button>
        </div>
      </div>
    </div>
  )
}