'use client'

import { useState, useEffect } from 'react'
import { Search, Filter, SortAsc, SortDesc, X, ChevronUp, ChevronDown } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { FilterChip } from '@/components/ui/FilterChip'
import { Position, PendingPosition, PositionType, SortField, SortDirection, StatusFilter, PnlFilter, OrderTypeFilter } from '@/types/trading'
import { cn } from '@/lib/utils'

interface FilterState {
  searchTerm: string
  positionType: PositionType
  statusFilter: StatusFilter
  pnlFilter: PnlFilter
  orderTypeFilter: OrderTypeFilter
  sortField: SortField
  sortDirection: SortDirection
}

interface ComprehensiveTradeFiltersProps {
  openPositions: Position[]
  pendingPositions: PendingPosition[]
  onOpenPositionsFilterChange: (filteredPositions: Position[]) => void
  onPendingPositionsFilterChange: (filteredPositions: PendingPosition[]) => void
}

export function ComprehensiveTradeFilters({ 
  openPositions, 
  pendingPositions, 
  onOpenPositionsFilterChange, 
  onPendingPositionsFilterChange 
}: ComprehensiveTradeFiltersProps) {
  const [filters, setFilters] = useState<FilterState>({
    searchTerm: '',
    positionType: 'all',
    statusFilter: 'all',
    pnlFilter: 'all',
    orderTypeFilter: 'all',
    sortField: 'pnl',
    sortDirection: 'desc'
  })
  
  const [showMoonBagOnly, setShowMoonBagOnly] = useState(false)
  const [profitFilter, setProfitFilter] = useState<'all' | 'profitable' | 'losing'>('all')

  const [isExpanded, setIsExpanded] = useState(true)

  // Calculate comprehensive counts
  const getCounts = () => {
    const totalPositions = openPositions.length + pendingPositions.length
    
    // Position type counts
    const positionTypeCounts = {
      all: totalPositions,
      open: openPositions.length,
      pending: pendingPositions.length
    }

    // Status counts (for open positions)
    const statusCounts = {
      all: openPositions.length,
      taking_profits: openPositions.filter(p => p.status === 'taking_profits').length,
      approaching_target: openPositions.filter(p => p.status === 'approaching_target').length,
      moon_bag: openPositions.filter(p => p.status === 'moon_bag').length,
      trailing: openPositions.filter(p => p.status === 'trailing').length,
    }

    // Performance counts (for open positions) 
    const pnlCounts = {
      all: openPositions.length,
      profitable: openPositions.filter(p => p.pnlPercentage > 0).length,
      breakeven: openPositions.filter(p => p.pnlPercentage >= -5 && p.pnlPercentage <= 5).length,
      losing: openPositions.filter(p => p.pnlPercentage < -5).length,
    }

    // Order type counts (for pending positions)
    const orderTypeCounts = {
      all: pendingPositions.length,
      buy_limit: pendingPositions.filter(p => p.orderType === 'buy_limit').length,
      sell_limit: pendingPositions.filter(p => p.orderType === 'sell_limit').length,
    }

    return { positionTypeCounts, statusCounts, pnlCounts, orderTypeCounts }
  }

  const { positionTypeCounts, statusCounts, pnlCounts, orderTypeCounts } = getCounts()

  // Filter options with counts
  const positionTypeOptions = [
    { value: 'all' as PositionType, label: 'All', count: positionTypeCounts.all },
    { value: 'open' as PositionType, label: 'Open', count: positionTypeCounts.open },
    { value: 'pending' as PositionType, label: 'Pending', count: positionTypeCounts.pending },
  ]

  const statusOptions = [
    { value: 'all' as StatusFilter, label: 'All', count: statusCounts.all },
    { value: 'taking_profits' as StatusFilter, label: 'Taking Profits', count: statusCounts.taking_profits },
    { value: 'approaching_target' as StatusFilter, label: 'Approaching Target', count: statusCounts.approaching_target },
    { value: 'moon_bag' as StatusFilter, label: 'Moon Bag', count: statusCounts.moon_bag },
    { value: 'trailing' as StatusFilter, label: 'Trailing', count: statusCounts.trailing },
  ]

  const pnlOptions = [
    { value: 'all' as PnlFilter, label: 'All', count: pnlCounts.all },
    { value: 'profitable' as PnlFilter, label: 'Profitable', count: pnlCounts.profitable },
    { value: 'breakeven' as PnlFilter, label: 'Break Even', count: pnlCounts.breakeven },
    { value: 'losing' as PnlFilter, label: 'Losing', count: pnlCounts.losing },
  ]

  const orderTypeOptions = [
    { value: 'all' as OrderTypeFilter, label: 'All', count: orderTypeCounts.all },
    { value: 'buy_limit' as OrderTypeFilter, label: 'Buy Limit', count: orderTypeCounts.buy_limit },
    { value: 'sell_limit' as OrderTypeFilter, label: 'Sell Limit', count: orderTypeCounts.sell_limit },
  ]

  const sortOptions = [
    { field: 'pnl' as SortField, label: 'P&L %', showFor: ['all', 'open'] },
    { field: 'value' as SortField, label: 'Position Value', showFor: ['all', 'open', 'pending'] },
    { field: 'symbol' as SortField, label: 'Token Symbol', showFor: ['all', 'open', 'pending'] },
    { field: 'entry' as SortField, label: 'Entry Value', showFor: ['all', 'open'] },
    { field: 'progress' as SortField, label: 'Exit Progress', showFor: ['all', 'open'] },
    { field: 'target_price' as SortField, label: 'Target Price', showFor: ['all', 'pending'] },
    { field: 'time_created' as SortField, label: 'Time Created', showFor: ['all', 'pending'] },
  ]

  // Handle position type change
  const handlePositionTypeChange = (newType: PositionType) => {
    setFilters(prev => ({
      ...prev,
      positionType: newType,
      // Reset sort field if current sort is not valid for new position type
      sortField: sortOptions.find(opt => 
        opt.field === prev.sortField && opt.showFor.includes(newType)
      ) ? prev.sortField : (newType === 'pending' ? 'time_created' : 'pnl'),
    }))
    
    // Clear open position specific filters when switching to pending
    if (newType === 'pending') {
      setShowMoonBagOnly(false)
      setProfitFilter('all')
    }
  }

  // Apply filters and sorting
  useEffect(() => {
    // Filter open positions based on position type selection
    let filteredOpen = filters.positionType === 'pending' ? [] : [...openPositions]
    
    if (filteredOpen.length > 0) {
      if (filters.searchTerm.trim()) {
        filteredOpen = filteredOpen.filter(position =>
          position.token.symbol.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
          position.token.name.toLowerCase().includes(filters.searchTerm.toLowerCase())
        )
      }

      if (filters.statusFilter !== 'all') {
        filteredOpen = filteredOpen.filter(position => position.status === filters.statusFilter)
      }
      
      if (showMoonBagOnly) {
        filteredOpen = filteredOpen.filter(position => position.status === 'moon_bag')
      }
      
      if (profitFilter !== 'all') {
        if (profitFilter === 'profitable') {
          filteredOpen = filteredOpen.filter(position => position.pnlPercentage > 0)
        } else if (profitFilter === 'losing') {
          filteredOpen = filteredOpen.filter(position => position.pnlPercentage < 0)
        }
      }
    }

    if (filters.pnlFilter !== 'all') {
      switch (filters.pnlFilter) {
        case 'profitable':
          filteredOpen = filteredOpen.filter(position => position.pnlPercentage > 0)
          break
        case 'breakeven':
          filteredOpen = filteredOpen.filter(position => position.pnlPercentage >= -5 && position.pnlPercentage <= 5)
          break
        case 'losing':
          filteredOpen = filteredOpen.filter(position => position.pnlPercentage < -5)
          break
      }
    }

    // Sort open positions
    filteredOpen.sort((a, b) => {
      let aValue: number | string
      let bValue: number | string

      switch (filters.sortField) {
        case 'pnl':
          aValue = a.pnlPercentage
          bValue = b.pnlPercentage
          break
        case 'value':
          aValue = a.currentValue
          bValue = b.currentValue
          break
        case 'symbol':
          aValue = a.token.symbol
          bValue = b.token.symbol
          break
        case 'entry':
          aValue = a.entryValue
          bValue = b.entryValue
          break
        case 'progress':
          aValue = a.progressToNextExit.current
          bValue = b.progressToNextExit.current
          break
        default:
          aValue = a.pnlPercentage
          bValue = b.pnlPercentage
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return filters.sortDirection === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue)
      }

      return filters.sortDirection === 'asc' 
        ? (aValue as number) - (bValue as number)
        : (bValue as number) - (aValue as number)
    })

    // Filter pending positions based on position type selection
    let filteredPending = filters.positionType === 'open' ? [] : [...pendingPositions]
    
    if (filteredPending.length > 0) {
      if (filters.searchTerm.trim()) {
        filteredPending = filteredPending.filter(position =>
          position.token.symbol.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
          position.token.name.toLowerCase().includes(filters.searchTerm.toLowerCase())
        )
      }

      if (filters.orderTypeFilter !== 'all') {
        filteredPending = filteredPending.filter(position => position.orderType === filters.orderTypeFilter)
      }
    }

    // Sort pending positions
    filteredPending.sort((a, b) => {
      let aValue: number | string
      let bValue: number | string

      switch (filters.sortField) {
        case 'symbol':
          aValue = a.token.symbol
          bValue = b.token.symbol
          break
        case 'target_price':
          aValue = a.targetPrice
          bValue = b.targetPrice
          break
        case 'time_created':
          aValue = a.timeCreated.getTime()
          bValue = b.timeCreated.getTime()
          break
        case 'value':
          aValue = a.totalValue
          bValue = b.totalValue
          break
        default:
          aValue = a.timeCreated.getTime()
          bValue = b.timeCreated.getTime()
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return filters.sortDirection === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue)
      }

      return filters.sortDirection === 'asc' 
        ? (aValue as number) - (bValue as number)
        : (bValue as number) - (aValue as number)
    })

    onOpenPositionsFilterChange(filteredOpen)
    onPendingPositionsFilterChange(filteredPending)
  }, [filters, showMoonBagOnly, profitFilter, openPositions, pendingPositions, onOpenPositionsFilterChange, onPendingPositionsFilterChange])

  const handleSortChange = (field: SortField) => {
    setFilters(prev => ({
      ...prev,
      sortField: field,
      sortDirection: prev.sortField === field && prev.sortDirection === 'desc' ? 'asc' : 'desc'
    }))
  }

  const clearFilters = () => {
    setFilters({
      searchTerm: '',
      positionType: 'all',
      statusFilter: 'all',
      pnlFilter: 'all',
      orderTypeFilter: 'all',
      sortField: 'pnl',
      sortDirection: 'desc'
    })
    setShowMoonBagOnly(false)
    setProfitFilter('all')
  }

  const hasActiveFilters = filters.searchTerm.trim() || 
                          filters.positionType !== 'all' || 
                          showMoonBagOnly ||
                          profitFilter !== 'all'

  const showOpenFilters = filters.positionType === 'all' || filters.positionType === 'open'
  const showPendingFilters = filters.positionType === 'all' || filters.positionType === 'pending'

  return (
    <div className="bg-gradient-to-br from-card/90 to-card/50 backdrop-blur-sm border border-border/60 rounded-2xl shadow-2xl p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <Filter className="w-6 h-6 text-primary" />
          <h3 className="text-xl font-bold text-foreground">Filter & Sort</h3>
          <Badge className="bg-primary/20 text-primary border border-primary/30 px-2 py-1">
            {positionTypeCounts.all} Total
          </Badge>
        </div>
        <div className="flex items-center gap-2">
          {hasActiveFilters && (
            <Button variant="ghost" size="sm" onClick={clearFilters}>
              <X className="w-4 h-4 mr-1" />
              Clear All
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
          </Button>
        </div>
      </div>

      {isExpanded && (
        <div className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search by token symbol or name..."
              value={filters.searchTerm}
              onChange={(e) => setFilters(prev => ({ ...prev, searchTerm: e.target.value }))}
              className="w-full pl-10 pr-4 py-2.5 bg-background/50 border border-border rounded-lg text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
            />
          </div>

          {/* Position Type Filter */}
          <div className="flex items-center gap-3">
            <span className="text-sm font-medium text-muted-foreground min-w-[80px]">Position:</span>
            <div className="flex items-center gap-2 flex-wrap">
              {positionTypeOptions.map((option) => (
                <FilterChip
                  key={option.value}
                  label={option.label}
                  count={option.count}
                  active={filters.positionType === option.value}
                  onClick={() => handlePositionTypeChange(option.value)}
                />
              ))}
              {/* Moon Bag Filter - only shows for open positions */}
              {(filters.positionType === 'all' || filters.positionType === 'open') && (
                <FilterChip
                  label="Moon Bag"
                  count={statusCounts.moon_bag}
                  active={showMoonBagOnly}
                  onClick={() => setShowMoonBagOnly(!showMoonBagOnly)}
                />
              )}
              {/* Profit filters - only show for open positions */}
              {(filters.positionType === 'all' || filters.positionType === 'open') && (
                <>
                  <FilterChip
                    label="Profitable"
                    count={pnlCounts.profitable}
                    active={profitFilter === 'profitable'}
                    onClick={() => setProfitFilter(profitFilter === 'profitable' ? 'all' : 'profitable')}
                  />
                  <FilterChip
                    label="Losing"
                    count={pnlCounts.losing}
                    active={profitFilter === 'losing'}
                    onClick={() => setProfitFilter(profitFilter === 'losing' ? 'all' : 'losing')}
                  />
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}