'use client'

import { useState, useEffect } from 'react'
import { Search, Filter, SortAsc, SortDesc, X, ChevronUp, ChevronDown } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { FilterChip } from '@/components/ui/FilterChip'
import { Position, SortField, SortDirection, StatusFilter, PnlFilter } from '@/types/trading'
import { cn } from '@/lib/utils'

interface FilterState {
  searchTerm: string
  statusFilter: StatusFilter
  pnlFilter: PnlFilter
  sortField: SortField
  sortDirection: SortDirection
}

interface ComprehensiveTradeFiltersProps {
  openPositions: Position[]
  onOpenPositionsFilterChange: (filteredPositions: Position[]) => void
}

export function ComprehensiveTradeFilters({ 
  openPositions, 
  onOpenPositionsFilterChange
}: ComprehensiveTradeFiltersProps) {
  const [filters, setFilters] = useState<FilterState>({
    searchTerm: '',
    statusFilter: 'all',
    pnlFilter: 'all',
    sortField: 'pnl',
    sortDirection: 'desc'
  })
  
  const [showMoonBagOnly, setShowMoonBagOnly] = useState(false)
  const [profitFilter, setProfitFilter] = useState<'all' | 'profitable' | 'losing'>('all')

  const [isExpanded, setIsExpanded] = useState(true)

  // Calculate counts
  const getCounts = () => {
    // Status counts
    const statusCounts = {
      all: openPositions.length,
      taking_profits: openPositions.filter(p => p.status === 'taking_profits').length,
      approaching_target: openPositions.filter(p => p.status === 'approaching_target').length,
      moon_bag: openPositions.filter(p => p.status === 'moon_bag').length,
      trailing: openPositions.filter(p => p.status === 'trailing').length,
    }

    // Performance counts
    const pnlCounts = {
      all: openPositions.length,
      profitable: openPositions.filter(p => p.pnlPercentage > 0).length,
      breakeven: openPositions.filter(p => p.pnlPercentage >= -5 && p.pnlPercentage <= 5).length,
      losing: openPositions.filter(p => p.pnlPercentage < -5).length,
    }

    return { statusCounts, pnlCounts }
  }

  const { statusCounts, pnlCounts } = getCounts()

  // Filter options with counts
  const statusOptions = [
    { value: 'all' as StatusFilter, label: 'All', count: statusCounts.all },
    { value: 'taking_profits' as StatusFilter, label: 'Taking Profits', count: statusCounts.taking_profits },
    { value: 'approaching_target' as StatusFilter, label: 'Approaching Target', count: statusCounts.approaching_target },
    { value: 'moon_bag' as StatusFilter, label: 'Moon Bag', count: statusCounts.moon_bag },
    { value: 'trailing' as StatusFilter, label: 'Trailing', count: statusCounts.trailing },
  ]

  const pnlOptions = [
    { value: 'all' as PnlFilter, label: 'All', count: pnlCounts.all },
    { value: 'profitable' as PnlFilter, label: 'Profitable', count: pnlCounts.profitable },
    { value: 'breakeven' as PnlFilter, label: 'Break Even', count: pnlCounts.breakeven },
    { value: 'losing' as PnlFilter, label: 'Losing', count: pnlCounts.losing },
  ]

  const sortOptions = [
    { field: 'pnl' as SortField, label: 'P&L %' },
    { field: 'value' as SortField, label: 'Position Value' },
    { field: 'symbol' as SortField, label: 'Token Symbol' },
    { field: 'entry' as SortField, label: 'Entry Value' },
    { field: 'progress' as SortField, label: 'Exit Progress' },
  ]

  // Apply filters and sorting
  useEffect(() => {
    let filteredOpen = [...openPositions]
    
    if (filteredOpen.length > 0) {
      if (filters.searchTerm.trim()) {
        filteredOpen = filteredOpen.filter(position =>
          position.token.symbol.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
          position.token.name.toLowerCase().includes(filters.searchTerm.toLowerCase())
        )
      }

      if (filters.statusFilter !== 'all') {
        filteredOpen = filteredOpen.filter(position => position.status === filters.statusFilter)
      }
      
      if (showMoonBagOnly) {
        filteredOpen = filteredOpen.filter(position => position.status === 'moon_bag')
      }
      
      if (profitFilter !== 'all') {
        if (profitFilter === 'profitable') {
          filteredOpen = filteredOpen.filter(position => position.pnlPercentage > 0)
        } else if (profitFilter === 'losing') {
          filteredOpen = filteredOpen.filter(position => position.pnlPercentage < 0)
        }
      }
    }

    if (filters.pnlFilter !== 'all') {
      switch (filters.pnlFilter) {
        case 'profitable':
          filteredOpen = filteredOpen.filter(position => position.pnlPercentage > 0)
          break
        case 'breakeven':
          filteredOpen = filteredOpen.filter(position => position.pnlPercentage >= -5 && position.pnlPercentage <= 5)
          break
        case 'losing':
          filteredOpen = filteredOpen.filter(position => position.pnlPercentage < -5)
          break
      }
    }

    // Sort positions
    filteredOpen.sort((a, b) => {
      let aValue: number | string
      let bValue: number | string

      switch (filters.sortField) {
        case 'pnl':
          aValue = a.pnlPercentage
          bValue = b.pnlPercentage
          break
        case 'value':
          aValue = a.currentValue
          bValue = b.currentValue
          break
        case 'symbol':
          aValue = a.token.symbol
          bValue = b.token.symbol
          break
        case 'entry':
          aValue = a.entryValue
          bValue = b.entryValue
          break
        case 'progress':
          aValue = a.progressToNextExit.current
          bValue = b.progressToNextExit.current
          break
        default:
          aValue = a.pnlPercentage
          bValue = b.pnlPercentage
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return filters.sortDirection === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue)
      }

      return filters.sortDirection === 'asc' 
        ? (aValue as number) - (bValue as number)
        : (bValue as number) - (aValue as number)
    })

    onOpenPositionsFilterChange(filteredOpen)
  }, [filters, showMoonBagOnly, profitFilter, openPositions, onOpenPositionsFilterChange])

  const handleSortChange = (field: SortField) => {
    setFilters(prev => ({
      ...prev,
      sortField: field,
      sortDirection: prev.sortField === field && prev.sortDirection === 'desc' ? 'asc' : 'desc'
    }))
  }

  const clearFilters = () => {
    setFilters({
      searchTerm: '',
      statusFilter: 'all',
      pnlFilter: 'all',
      sortField: 'pnl',
      sortDirection: 'desc'
    })
    setShowMoonBagOnly(false)
    setProfitFilter('all')
  }

  const hasActiveFilters = filters.searchTerm.trim() || 
                          showMoonBagOnly ||
                          profitFilter !== 'all' ||
                          filters.statusFilter !== 'all' ||
                          filters.pnlFilter !== 'all'

  return (
    <div className="bg-gradient-to-br from-card/90 to-card/50 backdrop-blur-sm border border-border/60 rounded-2xl shadow-2xl p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <Filter className="w-6 h-6 text-primary" />
          <h3 className="text-xl font-bold text-foreground">Filter & Sort</h3>
          <Badge className="bg-primary/20 text-primary border border-primary/30 px-2 py-1">
            {openPositions.length} Positions
          </Badge>
        </div>
        <div className="flex items-center gap-2">
          {hasActiveFilters && (
            <Button variant="ghost" size="sm" onClick={clearFilters}>
              <X className="w-4 h-4 mr-1" />
              Clear All
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
          </Button>
        </div>
      </div>

      {isExpanded && (
        <div className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search by token symbol or name..."
              value={filters.searchTerm}
              onChange={(e) => setFilters(prev => ({ ...prev, searchTerm: e.target.value }))}
              className="w-full pl-10 pr-4 py-2.5 bg-background/50 border border-border rounded-lg text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
            />
          </div>

          {/* Status Filters */}
          <div className="flex items-center gap-3">
            <span className="text-sm font-medium text-muted-foreground min-w-[60px]">Status:</span>
            <div className="flex items-center gap-2 flex-wrap">
              {statusOptions.map((option) => (
                <FilterChip
                  key={option.value}
                  label={option.label}
                  count={option.count}
                  active={filters.statusFilter === option.value}
                  onClick={() => setFilters(prev => ({ ...prev, statusFilter: option.value }))}
                />
              ))}
            </div>
          </div>

          {/* Performance Filters */}
          <div className="flex items-center gap-3">
            <span className="text-sm font-medium text-muted-foreground min-w-[60px]">P&L:</span>
            <div className="flex items-center gap-2 flex-wrap">
              {pnlOptions.map((option) => (
                <FilterChip
                  key={option.value}
                  label={option.label}
                  count={option.count}
                  active={filters.pnlFilter === option.value}
                  onClick={() => setFilters(prev => ({ ...prev, pnlFilter: option.value }))}
                />
              ))}
            </div>
          </div>

          {/* Sort Options */}
          <div className="flex items-center gap-3">
            <span className="text-sm font-medium text-muted-foreground min-w-[60px]">Sort:</span>
            <div className="flex items-center gap-2 flex-wrap">
              {sortOptions.map((option) => (
                <Button
                  key={option.field}
                  variant="outline"
                  size="sm"
                  onClick={() => handleSortChange(option.field)}
                  className={cn(
                    "text-xs h-7",
                    filters.sortField === option.field && "bg-primary/20 border-primary/30 text-primary"
                  )}
                >
                  {option.label}
                  {filters.sortField === option.field && (
                    filters.sortDirection === 'desc' ? 
                      <SortDesc className="w-3 h-3 ml-1" /> : 
                      <SortAsc className="w-3 h-3 ml-1" />
                  )}
                </Button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}