'use client'

import { useState, useMemo, useEffect } from 'react'
import { SortAsc, SortDesc, TrendingUp, DollarSign } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Position } from '@/types/trading'
import { cn } from '@/lib/utils'

type SortBy = 'pnl' | 'value'
type SortDirection = 'asc' | 'desc'

interface SimpleSortFiltersProps {
  openPositions: Position[]
  onSortedPositionsChange: (sortedPositions: Position[]) => void
}

export function ComprehensiveTradeFilters({
  openPositions,
  onSortedPositionsChange
}: SimpleSortFiltersProps) {
  const [sortBy, setSortBy] = useState<SortBy>('pnl')
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc')

  const sorted = useMemo(() => {
    return [...openPositions].sort((a, b) => {
      let aValue: number
      let bValue: number

      if (sortBy === 'pnl') {
        aValue = a.pnlPercentage
        bValue = b.pnlPercentage
      } else {
        aValue = a.currentValue
        bValue = b.currentValue
      }

      return sortDirection === 'desc'
        ? bValue - aValue
        : aValue - bValue
    })
  }, [openPositions, sortBy, sortDirection])

  // Notify parent when sorted changes
  useEffect(() => {
    onSortedPositionsChange(sorted)
  }, [sorted, onSortedPositionsChange])

  const handleSortChange = (newSortBy: SortBy) => {
    if (sortBy === newSortBy) {
      setSortDirection(prev => prev === 'desc' ? 'asc' : 'desc')
    } else {
      setSortBy(newSortBy)
      setSortDirection('desc')
    }
  }

  return (
    <div className="bg-gradient-to-br from-card/90 to-card/50 backdrop-blur-sm border border-border/60 rounded-2xl shadow-2xl p-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <h3 className="text-xl font-bold text-foreground">Sort Positions</h3>
          <Badge className="bg-primary/20 text-primary border border-primary/30 px-2 py-1">
            {openPositions.length} Positions
          </Badge>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleSortChange('pnl')}
            className={cn(
              "text-sm h-8",
              sortBy === 'pnl' && "bg-primary/20 border-primary/30 text-primary"
            )}
          >
            <TrendingUp className="w-4 h-4 mr-1" />
            P&L %
            {sortBy === 'pnl' && (
              sortDirection === 'desc'
                ? <SortDesc className="w-3 h-3 ml-1" />
                : <SortAsc className="w-3 h-3 ml-1" />
            )}
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => handleSortChange('value')}
            className={cn(
              "text-sm h-8",
              sortBy === 'value' && "bg-primary/20 border-primary/30 text-primary"
            )}
          >
            <DollarSign className="w-4 h-4 mr-1" />
            Value
            {sortBy === 'value' && (
              sortDirection === 'desc'
                ? <SortDesc className="w-3 h-3 ml-1" />
                : <SortAsc className="w-3 h-3 ml-1" />
            )}
          </Button>
        </div>
      </div>
    </div>
  )
}
