'use client'

import { useState, useEffect } from 'react'
import { Shield, AlertTriangle, Eye, EyeOff, Edit3, Check, X, Plus, Search, Copy } from 'lucide-react'
import { loadUserVerifiedTokensFromStorage, loadRecentTokensFromStorage, loadCustomTokenNamesFromStorage, getCustomTokenName, saveUserVerifiedTokensToStorage, addUserVerifiedToken, saveCustomTokenNamesToStorage, setCustomTokenName, validateAndSanitizeTokenAddress, type UserVerifiedToken, type RecentToken, type CustomTokenName } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'

interface FeatureShowcaseProps {
  onTokenDoubleClick?: (token: RecentToken, target: 'from' | 'to') => void
}

export function FeatureShowcase({ onTokenDoubleClick }: FeatureShowcaseProps = {}) {
  const [userVerifiedTokens, setUserVerifiedTokens] = useState<Record<string, UserVerifiedToken>>({})
  const [recentTokens, setRecentTokens] = useState<RecentToken[]>([])
  const [customTokenNames, setCustomTokenNames] = useState<Record<string, CustomTokenName>>({})
  const [showUnverifiedOnly, setShowUnverifiedOnly] = useState(true)
  const [editingToken, setEditingToken] = useState<string | null>(null)
  const [editingName, setEditingName] = useState('')
  const [showManageDialog, setShowManageDialog] = useState<string | null>(null)
  const [showAddTokenDialog, setShowAddTokenDialog] = useState(false)
  const [newTokenAddress, setNewTokenAddress] = useState('')
  const [newTokenName, setNewTokenName] = useState('')
  const [isValidatingNewToken, setIsValidatingNewToken] = useState(false)
  const [newTokenError, setNewTokenError] = useState('')
  const [copiedAddress, setCopiedAddress] = useState<string | null>(null)

  useEffect(() => {
    const loadData = () => {
      const userVerified = loadUserVerifiedTokensFromStorage()
      const recent = loadRecentTokensFromStorage()
      const customNames = loadCustomTokenNamesFromStorage()
      
      setUserVerifiedTokens(userVerified)
      setRecentTokens(recent)
      setCustomTokenNames(customNames)
    }
    
    loadData()
    
    // Listen for changes
    const handleChange = () => {
      loadData()
    }
    
    // Listen for both user verification and custom name changes
    window.addEventListener('userVerifiedTokensChanged', handleChange)
    window.addEventListener('customTokenNamesChanged', handleChange)
    window.addEventListener('storage', handleChange)
    
    return () => {
      window.removeEventListener('userVerifiedTokensChanged', handleChange)
      window.removeEventListener('customTokenNamesChanged', handleChange)
      window.removeEventListener('storage', handleChange)
    }
  }, [])

  // Get unverified tokens from recent tokens
  const unverifiedTokens = recentTokens.filter(token => 
    token && token.address && !token.verified && !userVerifiedTokens[token.address]
  )

  // Get user verified tokens by matching with recent tokens
  const userVerifiedList = recentTokens.filter(token => 
    token && token.address && userVerifiedTokens[token.address]
  )

  const shouldShowPendingTab = unverifiedTokens.length > 0
  const displayTokens = showUnverifiedOnly ? unverifiedTokens : userVerifiedList
  
  // Auto-switch to verified tab if no pending tokens and we're showing pending
  useEffect(() => {
    if (!shouldShowPendingTab && showUnverifiedOnly) {
      setShowUnverifiedOnly(false)
    }
  }, [shouldShowPendingTab, showUnverifiedOnly])

  // Helper function to get display name for a token (custom name or original)
  const getTokenDisplayName = (token: RecentToken): string => {
    const customName = getCustomTokenName(token.address, customTokenNames)
    return customName ? customName.customName : (token.symbol || 'Unverified Token')
  }

  // Helper function to get display label with context
  const getTokenDisplayLabel = (token: RecentToken): string => {
    const customName = getCustomTokenName(token.address, customTokenNames)
    if (customName) {
      return `${customName.customName} (${token.symbol || 'Unverified Token'})`
    }
    return token.symbol || 'Unverified Token'
  }

  // Helper function to get full name for display
  const getTokenFullName = (token: RecentToken): string => {
    const customName = getCustomTokenName(token.address, customTokenNames)
    return customName ? `${customName.customName} - ${token.name || 'Unverified Token'}` : (token.name || 'Unverified Token')
  }

  // Handle verifying a token
  const handleVerifyToken = (token: RecentToken) => {
    const updatedVerifications = addUserVerifiedToken(token.address, '', userVerifiedTokens)
    setUserVerifiedTokens(updatedVerifications)
    saveUserVerifiedTokensToStorage(updatedVerifications)
  }

  // Handle setting custom name for a token
  const handleSetCustomName = (address: string, customName: string, originalSymbol: string) => {
    if (customName.trim()) {
      const updatedNames = setCustomTokenName(address, customName, originalSymbol, customTokenNames)
      setCustomTokenNames(updatedNames)
      saveCustomTokenNamesToStorage(updatedNames)
    }
    setEditingToken(null)
    setEditingName('')
  }

  // Handle starting edit mode
  const handleStartEdit = (token: RecentToken) => {
    setEditingToken(token.address)
    const customName = getCustomTokenName(token.address, customTokenNames)
    setEditingName(customName?.customName || token.symbol || '')
  }

  // Handle canceling edit
  const handleCancelEdit = () => {
    setEditingToken(null)
    setEditingName('')
  }

  // Simulate token lookup for manual addition
  const lookupToken = async (address: string): Promise<RecentToken> => {
    await new Promise(resolve => setTimeout(resolve, 800))

    // Simulate potential API failure
    if (Math.random() < 0.1) {
      throw new Error('Token lookup failed. Please try again.')
    }

    return {
      address: address,
      symbol: 'UNVERIFIED',
      name: 'Unverified Token',
      decimals: 6,
      verified: false,
      addedAt: Date.now()
    }
  }

  // Handle adding new token manually
  const handleAddNewToken = async () => {
    if (!newTokenAddress.trim()) {
      setNewTokenError('Please enter a token address')
      return
    }

    setIsValidatingNewToken(true)
    setNewTokenError('')

    try {
      const validation = validateAndSanitizeTokenAddress(newTokenAddress)
      
      if (!validation.isValid) {
        setNewTokenError(validation.error || 'Invalid address format')
        return
      }

      // Check if token is already user verified
      if (userVerifiedTokens[validation.sanitizedAddress!]) {
        setNewTokenError('This token is already in your verified list')
        return
      }

      // Lookup token info
      const token = await lookupToken(validation.sanitizedAddress!)
      
      // Add to recent tokens if not already there
      const existingRecentToken = recentTokens.find(t => t.address === token.address)
      if (!existingRecentToken) {
        const updatedRecentTokens = [token, ...recentTokens].slice(0, 8)
        setRecentTokens(updatedRecentTokens)
        
        // Save to localStorage
        if (typeof window !== 'undefined') {
          localStorage.setItem('memetrader-recent-tokens', JSON.stringify(updatedRecentTokens))
        }
      }
      
      // Add to user verified tokens
      const updatedVerifications = addUserVerifiedToken(token.address, '', userVerifiedTokens)
      setUserVerifiedTokens(updatedVerifications)
      saveUserVerifiedTokensToStorage(updatedVerifications)
      
      // Set custom name if provided
      if (newTokenName.trim()) {
        const updatedNames = setCustomTokenName(token.address, newTokenName, token.symbol, customTokenNames)
        setCustomTokenNames(updatedNames)
        saveCustomTokenNamesToStorage(updatedNames)
      }
      
      // Reset form and close dialog
      setNewTokenAddress('')
      setNewTokenName('')
      setShowAddTokenDialog(false)
      
      // Switch to verified tab to show the new token
      setShowUnverifiedOnly(false)
      
    } catch (error) {
      setNewTokenError(error instanceof Error ? error.message : 'Failed to add token')
    } finally {
      setIsValidatingNewToken(false)
    }
  }

  // Handle double-click on token
  const handleTokenDoubleClick = (token: RecentToken, event: React.MouseEvent) => {
    // Don't trigger if clicking on action buttons
    if ((event.target as HTMLElement).closest('button')) {
      return
    }
    
    if (onTokenDoubleClick) {
      // Default to 'to' token, but could be made configurable
      onTokenDoubleClick(token, 'to')
    }
  }

  // Handle copying token address to clipboard
  const handleCopyAddress = (address: string, event: React.MouseEvent) => {
    event.stopPropagation() // Prevent double-click from triggering
    
    navigator.clipboard.writeText(address).then(() => {
      setCopiedAddress(address)
      // Clear the feedback after 2 seconds
      setTimeout(() => setCopiedAddress(null), 2000)
    }).catch((error) => {
      console.error('Failed to copy address:', error)
      // Fallback for older browsers
      try {
        const textArea = document.createElement('textarea')
        textArea.value = address
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        setCopiedAddress(address)
        setTimeout(() => setCopiedAddress(null), 2000)
      } catch (fallbackError) {
        console.error('Fallback copy failed:', fallbackError)
      }
    })
  }

  return (
    <div className="mt-8 p-6 bg-gradient-to-br from-card/80 to-background/40 backdrop-blur-sm border border-border/50 rounded-2xl shadow-xl">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-2 h-8 bg-gradient-to-b from-blue-400 to-blue-500 rounded-full"></div>
          <h3 className="text-xl font-bold bg-gradient-to-r from-blue-400 to-blue-500 bg-clip-text text-transparent">
            User Verified Tokens
          </h3>
        </div>
        
        <div className="flex items-center space-x-2">
          {shouldShowPendingTab && (
            <Button
              variant={showUnverifiedOnly ? "default" : "outline"}
              size="sm"
              onClick={() => setShowUnverifiedOnly(true)}
              className="flex items-center space-x-2"
            >
              <AlertTriangle className="w-4 h-4" />
              <span>Pending ({unverifiedTokens.length})</span>
            </Button>
          )}
          <Button
            variant={!showUnverifiedOnly ? "default" : "outline"}
            size="sm"
            onClick={() => setShowUnverifiedOnly(false)}
            className="flex items-center space-x-2"
          >
            <Shield className="w-4 h-4" />
            <span>User Verified ({userVerifiedList.length})</span>
          </Button>
          
          <Dialog open={showAddTokenDialog} onOpenChange={setShowAddTokenDialog}>
            <DialogTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center space-x-2"
              >
                <Plus className="w-4 h-4" />
                <span>Add Token</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Add Token to Verified List</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Token Address</label>
                  <Input
                    value={newTokenAddress}
                    onChange={(e) => {
                      setNewTokenAddress(e.target.value)
                      setNewTokenError('')
                    }}
                    placeholder="Enter Solana token address..."
                    className="w-full"
                    disabled={isValidatingNewToken}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">Custom Name (Optional)</label>
                  <Input
                    value={newTokenName}
                    onChange={(e) => setNewTokenName(e.target.value)}
                    placeholder="Enter custom name for easier identification"
                    className="w-full"
                    disabled={isValidatingNewToken}
                  />
                </div>
                {newTokenError && (
                  <div className="text-sm text-red-600 bg-red-50 p-2 rounded border border-red-200">
                    {newTokenError}
                  </div>
                )}
                <div className="flex space-x-2">
                  <Button
                    onClick={handleAddNewToken}
                    disabled={isValidatingNewToken || !newTokenAddress.trim()}
                    className="flex-1"
                  >
                    {isValidatingNewToken ? (
                      <>
                        <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
                        Adding...
                      </>
                    ) : (
                      <>
                        <Shield className="w-4 h-4 mr-2" />
                        Add & Verify
                      </>
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setShowAddTokenDialog(false)
                      setNewTokenAddress('')
                      setNewTokenName('')
                      setNewTokenError('')
                    }}
                    disabled={isValidatingNewToken}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {displayTokens.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-muted/50 flex items-center justify-center">
            {showUnverifiedOnly ? (
              <Shield className="w-8 h-8 text-muted-foreground" />
            ) : (
              <AlertTriangle className="w-8 h-8 text-muted-foreground" />
            )}
          </div>
          <p className="text-muted-foreground">
            {showUnverifiedOnly 
              ? "No pending tokens found. Start trading to see tokens here."
              : "No user verified tokens yet. Use the 'Add Token' button to manually add tokens you trust."
            }
          </p>
        </div>
      ) : (
        <div className="space-y-3">
          {displayTokens.slice(0, 8).map((token) => {
            const isUserVerified = userVerifiedTokens[token.address]
            
            return (
              <div 
                key={token.address} 
                className="group flex items-center justify-between p-4 bg-gradient-to-r from-muted/30 to-muted/10 rounded-lg border border-border/30 hover:border-border/50 transition-colors cursor-pointer"
                onDoubleClick={(e) => handleTokenDoubleClick(token, e)}
                title="Double-click to add to Swap Interface"
              >
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-br from-primary/80 to-secondary/80 flex items-center justify-center text-white text-sm font-bold shadow-sm">
                      {getTokenDisplayName(token).charAt(0).toUpperCase()}
                    </div>
                    {isUserVerified && (
                      <div className="absolute -top-1 -right-1 w-4 h-4 rounded-full bg-gradient-to-r from-blue-400 to-blue-500 flex items-center justify-center">
                        <Shield className="w-2.5 h-2.5 text-white" />
                      </div>
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <span className="font-semibold text-sm">{getTokenDisplayLabel(token)}</span>
                      {!showUnverifiedOnly && (
                        <span className="text-xs text-muted-foreground bg-blue-500/10 px-2 py-1 rounded-full">
                          User Verified
                        </span>
                      )}
                    </div>
                    <div className="text-xs text-muted-foreground truncate">
                      {getTokenFullName(token)}
                    </div>
                    <div className="flex items-center space-x-1 text-xs text-muted-foreground/70">
                      <span className="font-mono">
                        {token.address?.slice(0, 8)}...{token.address?.slice(-4)}
                      </span>
                      <button
                        onClick={(e) => handleCopyAddress(token.address, e)}
                        className="p-0.5 hover:bg-muted/50 rounded transition-colors opacity-0 group-hover:opacity-100"
                        title={copiedAddress === token.address ? "Copied!" : "Copy address"}
                      >
                        {copiedAddress === token.address ? (
                          <Check className="w-3 h-3 text-green-500" />
                        ) : (
                          <Copy className="w-3 h-3 text-muted-foreground hover:text-foreground" />
                        )}
                      </button>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  {showUnverifiedOnly ? (
                    <div className="flex items-center space-x-2">
                      <Dialog open={showManageDialog === token.address} onOpenChange={(open) => {
                        setShowManageDialog(open ? token.address : null)
                        if (open) {
                          const customName = getCustomTokenName(token.address, customTokenNames)
                          setEditingName(customName?.customName || '')
                        }
                      }}>
                        <DialogTrigger asChild>
                          <Button size="sm" variant="outline" className="h-7 px-2 text-xs">
                            <Plus className="w-3 h-3 mr-1" />
                            Manage
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="sm:max-w-md">
                          <DialogHeader>
                            <DialogTitle>Manage Token</DialogTitle>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div>
                              <label className="text-sm font-medium mb-2 block">Custom Name (Optional)</label>
                              <Input
                                value={editingName}
                                onChange={(e) => setEditingName(e.target.value)}
                                placeholder={token.symbol || 'Enter custom name'}
                                className="w-full"
                              />
                            </div>
                            <div className="flex space-x-2">
                              <Button
                                onClick={() => {
                                  if (editingName.trim()) {
                                    handleSetCustomName(token.address, editingName, token.symbol || 'Unknown')
                                  }
                                  handleVerifyToken(token)
                                  setShowManageDialog(null)
                                  setEditingName('')
                                }}
                                className="flex-1"
                              >
                                <Shield className="w-4 h-4 mr-2" />
                                Verify & Save
                              </Button>
                              <Button
                                variant="outline"
                                onClick={() => {
                                  setShowManageDialog(null)
                                  setEditingName('')
                                }}
                              >
                                Cancel
                              </Button>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                      <div className="flex items-center space-x-1 text-xs text-orange-500">
                        <AlertTriangle className="w-3 h-3" />
                        <span>Pending</span>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      {editingToken === token.address ? (
                        <div className="flex items-center space-x-1">
                          <Input
                            value={editingName}
                            onChange={(e) => setEditingName(e.target.value)}
                            className="h-6 px-2 text-xs w-24"
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                handleSetCustomName(token.address, editingName, token.symbol || 'Unknown')
                              } else if (e.key === 'Escape') {
                                handleCancelEdit()
                              }
                            }}
                            autoFocus
                          />
                          <Button
                            size="sm"
                            variant="ghost"
                            className="h-6 w-6 p-0"
                            onClick={() => handleSetCustomName(token.address, editingName, token.symbol || 'Unknown')}
                          >
                            <Check className="w-3 h-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            className="h-6 w-6 p-0"
                            onClick={handleCancelEdit}
                          >
                            <X className="w-3 h-3" />
                          </Button>
                        </div>
                      ) : (
                        <>
                          <Button
                            size="sm"
                            variant="ghost"
                            className="h-6 w-6 p-0"
                            onClick={() => handleStartEdit(token)}
                          >
                            <Edit3 className="w-3 h-3" />
                          </Button>
                          <div className="text-xs text-blue-500">
                            <span className="bg-blue-500/10 px-2 py-1 rounded-full">U-Verified</span>
                          </div>
                        </>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )
          })}
          
          {displayTokens.length > 8 && (
            <div className="text-center pt-4">
              <span className="text-sm text-muted-foreground">
                Showing 8 of {displayTokens.length} tokens
              </span>
            </div>
          )}
        </div>
      )}

      <div className="mt-6 p-4 bg-gradient-to-r from-orange-500/10 to-red-500/10 rounded-lg border border-orange-500/20">
        <div className="flex items-start space-x-3">
          <AlertTriangle className="w-5 h-5 text-orange-500 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="font-semibold text-sm text-orange-600 mb-1">Security Notice</h4>
          </div>
        </div>
      </div>
    </div>
  )
}
