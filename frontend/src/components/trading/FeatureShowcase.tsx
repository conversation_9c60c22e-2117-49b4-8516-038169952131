'use client'

import { useState, useEffect } from 'react'
import { Shield, AlertTriangle, Eye, EyeOff } from 'lucide-react'
import { loadUserVerifiedTokensFromStorage, loadRecentTokensFromStorage, loadCustomTokenNamesFromStorage, getCustomTokenName, type UserVerifiedToken, type RecentToken, type CustomTokenName } from '@/lib/utils'
import { Button } from '@/components/ui/button'

export function FeatureShowcase() {
  const [userVerifiedTokens, setUserVerifiedTokens] = useState<Record<string, UserVerifiedToken>>({})
  const [recentTokens, setRecentTokens] = useState<RecentToken[]>([])
  const [customTokenNames, setCustomTokenNames] = useState<Record<string, CustomTokenName>>({})
  const [showUnverifiedOnly, setShowUnverifiedOnly] = useState(true)

  useEffect(() => {
    const loadData = () => {
      const userVerified = loadUserVerifiedTokensFromStorage()
      const recent = loadRecentTokensFromStorage()
      const customNames = loadCustomTokenNamesFromStorage()
      
      setUserVerifiedTokens(userVerified)
      setRecentTokens(recent)
      setCustomTokenNames(customNames)
    }
    
    loadData()
    
    // Listen for changes
    const handleChange = () => {
      loadData()
    }
    
    // Listen for both user verification and custom name changes
    window.addEventListener('userVerifiedTokensChanged', handleChange)
    window.addEventListener('customTokenNamesChanged', handleChange)
    window.addEventListener('storage', handleChange)
    
    return () => {
      window.removeEventListener('userVerifiedTokensChanged', handleChange)
      window.removeEventListener('customTokenNamesChanged', handleChange)
      window.removeEventListener('storage', handleChange)
    }
  }, [])

  // Get unverified tokens from recent tokens
  const unverifiedTokens = recentTokens.filter(token => 
    token && token.address && !token.verified && !userVerifiedTokens[token.address]
  )

  // Get user verified tokens by matching with recent tokens
  const userVerifiedList = recentTokens.filter(token => 
    token && token.address && userVerifiedTokens[token.address]
  )

  const displayTokens = showUnverifiedOnly ? unverifiedTokens : userVerifiedList

  // Helper function to get display name for a token (custom name or original)
  const getTokenDisplayName = (token: RecentToken): string => {
    const customName = getCustomTokenName(token.address, customTokenNames)
    return customName ? customName.customName : (token.symbol || 'Unverified Token')
  }

  // Helper function to get display label with context
  const getTokenDisplayLabel = (token: RecentToken): string => {
    const customName = getCustomTokenName(token.address, customTokenNames)
    if (customName) {
      return `${customName.customName} (${token.symbol || 'Unverified Token'})`
    }
    return token.symbol || 'Unverified Token'
  }

  // Helper function to get full name for display
  const getTokenFullName = (token: RecentToken): string => {
    const customName = getCustomTokenName(token.address, customTokenNames)
    return customName ? `${customName.customName} - ${token.name || 'Unverified Token'}` : (token.name || 'Unverified Token')
  }

  return (
    <div className="mt-8 p-6 bg-gradient-to-br from-card/80 to-background/40 backdrop-blur-sm border border-border/50 rounded-2xl shadow-xl">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-2 h-8 bg-gradient-to-b from-orange-400 to-red-500 rounded-full"></div>
          <h3 className="text-xl font-bold bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent">
            Unverified Tokens
          </h3>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant={showUnverifiedOnly ? "default" : "outline"}
            size="sm"
            onClick={() => setShowUnverifiedOnly(true)}
            className="flex items-center space-x-2"
          >
            <AlertTriangle className="w-4 h-4" />
            <span>Unverified ({unverifiedTokens.length})</span>
          </Button>
          <Button
            variant={!showUnverifiedOnly ? "default" : "outline"}
            size="sm"
            onClick={() => setShowUnverifiedOnly(false)}
            className="flex items-center space-x-2"
          >
            <Shield className="w-4 h-4" />
            <span>User Verified ({userVerifiedList.length})</span>
          </Button>
        </div>
      </div>

      {displayTokens.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-muted/50 flex items-center justify-center">
            {showUnverifiedOnly ? (
              <Shield className="w-8 h-8 text-muted-foreground" />
            ) : (
              <AlertTriangle className="w-8 h-8 text-muted-foreground" />
            )}
          </div>
          <p className="text-muted-foreground">
            {showUnverifiedOnly 
              ? "No unverified tokens found. Start trading to see tokens here."
              : "No user verified tokens yet. Verify tokens you trust for easier identification."
            }
          </p>
        </div>
      ) : (
        <div className="space-y-3">
          {displayTokens.slice(0, 8).map((token) => {
            const isUserVerified = userVerifiedTokens[token.address]
            
            return (
              <div 
                key={token.address} 
                className="flex items-center justify-between p-4 bg-gradient-to-r from-muted/30 to-muted/10 rounded-lg border border-border/30 hover:border-border/50 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-br from-primary/80 to-secondary/80 flex items-center justify-center text-white text-sm font-bold shadow-sm">
                      {getTokenDisplayName(token).charAt(0).toUpperCase()}
                    </div>
                    {isUserVerified && (
                      <div className="absolute -top-1 -right-1 w-4 h-4 rounded-full bg-gradient-to-r from-blue-400 to-blue-500 flex items-center justify-center">
                        <Shield className="w-2.5 h-2.5 text-white" />
                      </div>
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <span className="font-semibold text-sm">{getTokenDisplayLabel(token)}</span>
                      {!showUnverifiedOnly && (
                        <span className="text-xs text-muted-foreground bg-blue-500/10 px-2 py-1 rounded-full">
                          User Verified
                        </span>
                      )}
                    </div>
                    <div className="text-xs text-muted-foreground truncate">
                      {getTokenFullName(token)}
                    </div>
                    <div className="text-xs text-muted-foreground/70 font-mono">
                      {token.address?.slice(0, 8)}...{token.address?.slice(-4)}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  {showUnverifiedOnly && (
                    <div className="flex items-center space-x-1 text-xs text-orange-500">
                      <AlertTriangle className="w-3 h-3" />
                      <span>Unverified</span>
                    </div>
                  )}
                  {!showUnverifiedOnly && isUserVerified && (
                    <div className="text-xs text-muted-foreground">
                      Verified {new Date(isUserVerified.verifiedAt).toLocaleDateString()}
                    </div>
                  )}
                </div>
              </div>
            )
          })}
          
          {displayTokens.length > 8 && (
            <div className="text-center pt-4">
              <span className="text-sm text-muted-foreground">
                Showing 8 of {displayTokens.length} tokens
              </span>
            </div>
          )}
        </div>
      )}

      <div className="mt-6 p-4 bg-gradient-to-r from-orange-500/10 to-red-500/10 rounded-lg border border-orange-500/20">
        <div className="flex items-start space-x-3">
          <AlertTriangle className="w-5 h-5 text-orange-500 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="font-semibold text-sm text-orange-600 mb-1">Security Notice</h4>
          </div>
        </div>
      </div>
    </div>
  )
}
