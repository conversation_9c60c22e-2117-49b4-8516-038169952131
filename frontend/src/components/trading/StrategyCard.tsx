'use client'

import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>, Star, Co<PERSON>, Trash2, <PERSON>, Settings, TrendingUp, TrendingDown, Activity, Rocket } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'

interface ProfitTarget {
  target: number
  sellPercentage: number
}

interface Strategy {
  id: string
  name: string
  description: string
  usageCount: number
  winRate: number
  isDefault: boolean
  locked: boolean
  stopLoss: {
    percentage: number
  } | null
  profitTargets: ProfitTarget[]
  trailingStop: {
    percentage: number
  } | null
  moonBag: {
    percentage: number
    targetGain: number
  } | null
}

interface StrategyCardProps {
  strategy: Strategy
  onToggleLock: () => void
  onSetDefault: () => void
  onDelete: () => void
  onDuplicate: () => void
  onEdit: () => void
}

export function StrategyCard({
  strategy,
  onToggleLock,
  onSetDefault,
  onDelete,
  onDuplicate,
  onEdit
}: StrategyCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const totalSellPercentage = strategy.profitTargets.reduce((sum, target) => sum + target.sellPercentage, 0)
  const moonBagPercentage = strategy.moonBag?.percentage || 0
  const holdingPercentage = 100 - totalSellPercentage

  // Calculate preview for $1000 investment
  const investmentAmount = 1000
  const previewCalculations = strategy.profitTargets.map(target => ({
    ...target,
    dollarAmount: (investmentAmount * target.sellPercentage) / 100,
    atPrice: target.target
  }))

  return (
    <div className={cn(
      "group relative bg-gradient-to-br from-card/80 to-card/40 backdrop-blur-sm border rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 overflow-hidden",
      strategy.locked 
        ? "border-red-500/30 bg-gradient-to-br from-red-50/5 to-card/40" 
        : "border-border/50"
    )}>
      {/* Header */}
      <div className="p-8 pb-6">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-3">
            <h3 className="text-xl font-bold text-foreground">{strategy.name}</h3>
            {strategy.isDefault && (
              <Badge className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white border-0 flex items-center gap-1">
                <Star className="w-3 h-3 fill-current" />
                Default
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleLock}
              className={cn(
                "flex items-center gap-2 px-3 py-1.5 transition-all duration-200 rounded-md font-medium text-xs",
                strategy.locked 
                  ? "text-red-500 hover:text-red-600 hover:bg-red-500/10 bg-red-500/5 border border-red-500/20" 
                  : "text-green-500 hover:text-green-600 hover:bg-green-500/10 bg-green-500/5 border border-green-500/20"
              )}
            >
              {strategy.locked ? (
                <>
                  <Lock className="w-4 h-4" />
                  <span className="text-xs font-medium">Locked</span>
                </>
              ) : (
                <>
                  <LockOpen className="w-4 h-4" />
                  <span className="text-xs font-medium">Unlocked</span>
                </>
              )}
            </Button>
            <Button variant="ghost" size="sm" className="p-2">
              <Settings className="w-5 h-5" />
            </Button>
          </div>
        </div>

        <p className="text-muted-foreground text-sm mb-3">{strategy.description}</p>
        <p className="text-xs text-muted-foreground">Used {strategy.usageCount} times</p>
      </div>

      {/* Strategy Configuration */}
      <div className="px-8 space-y-5">
        {/* Stop Loss */}
        {strategy.stopLoss && (
          <div className="bg-gradient-to-r from-red-500/10 to-red-600/10 border border-red-500/20 rounded-xl p-5">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <TrendingDown className="w-5 h-5 text-red-500" />
                <span className="text-sm font-medium text-red-400">Stop Loss</span>
              </div>
              <span className="text-xl font-bold text-red-500">-{strategy.stopLoss.percentage}%</span>
            </div>
          </div>
        )}

        {/* Profit Targets */}
        {strategy.profitTargets.length > 0 && (
          <div className="bg-gradient-to-r from-green-500/10 to-green-600/10 border border-green-500/20 rounded-xl p-5">
            <div className="flex items-center gap-3 mb-4">
              <TrendingUp className="w-5 h-5 text-green-500" />
              <span className="text-sm font-medium text-green-400">Take Profits</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {strategy.profitTargets.map((target, index) => (
                <Badge
                  key={index}
                  className="bg-green-500/20 text-green-400 border border-green-500/30 hover:bg-green-500/30 transition-colors px-3 py-1.5 text-xs font-medium whitespace-nowrap"
                  title={`Sell ${target.sellPercentage}% of position when price reaches +${target.target}% gain`}
                >
                  +{target.target}% (Sell {target.sellPercentage}%)
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Trailing Stop */}
        {strategy.trailingStop && (
          <div className="bg-gradient-to-r from-blue-500/10 to-blue-600/10 border border-blue-500/20 rounded-xl p-5">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Activity className="w-5 h-5 text-blue-500" />
                <span className="text-sm font-medium text-blue-400">Trailing Stop</span>
              </div>
              <span className="text-lg font-bold text-blue-500">{strategy.trailingStop.percentage}% below peak</span>
            </div>
          </div>
        )}

        {/* Moon Bag */}
        {strategy.moonBag && (
          <div className="bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border border-yellow-500/20 rounded-xl p-5">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Rocket className="w-5 h-5 text-yellow-500" />
                <span className="text-sm font-medium text-yellow-400">Moon Bag</span>
              </div>
              <span className="text-xl font-bold text-yellow-500">{strategy.moonBag.percentage}%</span>
            </div>
            <p className="text-sm text-yellow-400 mt-3">
              Hold for +{strategy.moonBag.targetGain}% or trailing stop
            </p>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="mt-auto p-8 pt-6">
        <div className="flex items-center justify-between text-sm mb-6">
          <span className="text-muted-foreground">Used in {strategy.usageCount} trades</span>
          <span className="text-green-400 font-medium">Win Rate: {strategy.winRate}%</span>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={strategy.locked ? undefined : onEdit}
            disabled={strategy.locked}
            className={cn(
              "flex items-center gap-2 flex-1 transition-all duration-200",
              strategy.locked && "opacity-50 cursor-not-allowed bg-muted/50 text-muted-foreground hover:bg-muted/50 hover:text-muted-foreground"
            )}
            title={strategy.locked ? "Strategy is locked - unlock to edit" : "Edit strategy"}
          >
            <Edit className="w-3 h-3" />
            Edit
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={onDuplicate}
            className="px-3"
            title="Duplicate strategy (creates unlocked copy)"
          >
            <Copy className="w-4 h-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={strategy.locked ? undefined : onDelete}
            disabled={strategy.locked}
            className={cn(
              "px-3 transition-all duration-200",
              strategy.locked 
                ? "opacity-50 cursor-not-allowed text-muted-foreground hover:text-muted-foreground hover:bg-transparent"
                : "text-red-500 hover:text-red-600 hover:bg-red-50"
            )}
            title={strategy.locked ? "Strategy is locked - unlock to delete" : "Delete strategy"}
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>

        {/* Set as Default Button */}
        {!strategy.isDefault && (
          <Button
            variant="outline"
            size="sm"
            onClick={strategy.locked ? undefined : onSetDefault}
            disabled={strategy.locked}
            className={cn(
              "w-full mt-4 transition-all duration-200",
              strategy.locked && "opacity-50 cursor-not-allowed bg-muted/50 text-muted-foreground hover:bg-muted/50 hover:text-muted-foreground"
            )}
            title={strategy.locked ? "Strategy is locked - unlock to set as default" : "Set as default strategy"}
          >
            Set as Default
          </Button>
        )}
      </div>

      {/* Expanded Details (for future enhancement) */}
      {isExpanded && (
        <div className="absolute inset-0 bg-card/95 backdrop-blur-sm border border-border rounded-2xl p-6 z-10">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-bold">{strategy.name}</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(false)}
            >
              ×
            </Button>
          </div>

          {/* Detailed breakdown would go here */}
          <div className="space-y-4">
            {/* Investment Preview */}
            <div className="space-y-2">
              <h4 className="font-medium">$1,000 Investment Preview</h4>
              {previewCalculations.map((calc, index) => (
                <div key={index} className="flex justify-between text-sm">
                  <span>Target {index + 1}: +{calc.target}%</span>
                  <span className="text-green-400">${calc.dollarAmount} (Sell {calc.sellPercentage}%)</span>
                </div>
              ))}
              {strategy.moonBag && (
                <div className="flex justify-between text-sm border-t pt-2">
                  <span>Moon Bag: {strategy.moonBag.percentage}%</span>
                  <span className="text-yellow-400">${(investmentAmount * strategy.moonBag.percentage) / 100}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}