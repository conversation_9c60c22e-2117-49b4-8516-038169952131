'use client'

import { useState, useEffect } from 'react'
import { Search, Filter, SortAsc, SortDesc } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

interface Position {
  id: string
  token: {
    symbol: string
    name: string
    address: string
  }
  amount: number
  entryPrice: number
  currentPrice: number
  entryValue: number
  currentValue: number
  pnlPercentage: number
  pnlDollar: number
  trailingStop: number
  exitMilestones: number[]
  completedExits: number
  moonBag: {
    percentage: number
    targetGain: number
    currentProgress: number
  }
  status: 'taking_profits' | 'approaching_target' | 'moon_bag' | 'trailing'
  progressToNextExit: {
    current: number
    next: number
  }
}

interface TradeFiltersProps {
  positions: Position[]
  onFilterChange: (filteredPositions: Position[]) => void
}

type SortField = 'pnl' | 'value' | 'symbol' | 'entry' | 'progress'
type SortDirection = 'asc' | 'desc'

export function TradeFilters({ positions, onFilterChange }: TradeFiltersProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [pnlFilter, setPnlFilter] = useState<string>('all')
  const [sortField, setSortField] = useState<SortField>('pnl')
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc')

  const statusOptions = [
    { value: 'all', label: 'All Status', count: positions.length },
    { value: 'taking_profits', label: 'Taking Profits', count: positions.filter(p => p.status === 'taking_profits').length },
    { value: 'approaching_target', label: 'Approaching Target', count: positions.filter(p => p.status === 'approaching_target').length },
    { value: 'moon_bag', label: 'Moon Bag', count: positions.filter(p => p.status === 'moon_bag').length },
    { value: 'trailing', label: 'Trailing', count: positions.filter(p => p.status === 'trailing').length },
  ]

  const pnlOptions = [
    { value: 'all', label: 'All P&L', count: positions.length },
    { value: 'profitable', label: 'Profitable', count: positions.filter(p => p.pnlPercentage > 0).length },
    { value: 'breakeven', label: 'Break Even', count: positions.filter(p => p.pnlPercentage >= -5 && p.pnlPercentage <= 5).length },
    { value: 'losing', label: 'Losing', count: positions.filter(p => p.pnlPercentage < -5).length },
  ]

  useEffect(() => {
    let filtered = [...positions]

    // Apply search filter
    if (searchTerm.trim()) {
      filtered = filtered.filter(position =>
        position.token.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
        position.token.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(position => position.status === statusFilter)
    }

    // Apply P&L filter
    if (pnlFilter !== 'all') {
      switch (pnlFilter) {
        case 'profitable':
          filtered = filtered.filter(position => position.pnlPercentage > 0)
          break
        case 'breakeven':
          filtered = filtered.filter(position => position.pnlPercentage >= -5 && position.pnlPercentage <= 5)
          break
        case 'losing':
          filtered = filtered.filter(position => position.pnlPercentage < -5)
          break
      }
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: number | string
      let bValue: number | string

      switch (sortField) {
        case 'pnl':
          aValue = a.pnlPercentage
          bValue = b.pnlPercentage
          break
        case 'value':
          aValue = a.currentValue
          bValue = b.currentValue
          break
        case 'symbol':
          aValue = a.token.symbol
          bValue = b.token.symbol
          break
        case 'entry':
          aValue = a.entryValue
          bValue = b.entryValue
          break
        case 'progress':
          aValue = a.progressToNextExit.current
          bValue = b.progressToNextExit.current
          break
        default:
          aValue = a.pnlPercentage
          bValue = b.pnlPercentage
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue)
      }

      return sortDirection === 'asc' 
        ? (aValue as number) - (bValue as number)
        : (bValue as number) - (aValue as number)
    })

    onFilterChange(filtered)
  }, [searchTerm, statusFilter, pnlFilter, sortField, sortDirection, positions, onFilterChange])

  const handleSortChange = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('desc')
    }
  }

  const clearFilters = () => {
    setSearchTerm('')
    setStatusFilter('all')
    setPnlFilter('all')
    setSortField('pnl')
    setSortDirection('desc')
  }

  const hasActiveFilters = searchTerm.trim() || statusFilter !== 'all' || pnlFilter !== 'all'

  return (
    <div className="bg-gradient-to-br from-card/80 to-card/40 backdrop-blur-sm border border-border/50 rounded-2xl shadow-xl p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Filter className="w-5 h-5 text-muted-foreground" />
          <h3 className="text-lg font-semibold text-foreground">Filter & Sort</h3>
        </div>
        {hasActiveFilters && (
          <Button variant="ghost" size="sm" onClick={clearFilters}>
            Clear All
          </Button>
        )}
      </div>

      <div className="space-y-6">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <input
            type="text"
            placeholder="Search by token symbol or name..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-background/50 border border-border rounded-lg text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
          />
        </div>

        {/* Status Filter */}
        <div>
          <div className="text-sm font-medium text-muted-foreground mb-3">Status</div>
          <div className="flex flex-wrap gap-2">
            {statusOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => setStatusFilter(option.value)}
                className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                  statusFilter === option.value
                    ? 'bg-primary/20 text-primary border border-primary/30'
                    : 'bg-muted/20 text-muted-foreground border border-muted/30 hover:bg-muted/30'
                }`}
              >
                {option.label}
                {option.count > 0 && (
                  <span className="ml-1 text-xs opacity-75">({option.count})</span>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* P&L Filter */}
        <div>
          <div className="text-sm font-medium text-muted-foreground mb-3">Performance</div>
          <div className="flex flex-wrap gap-2">
            {pnlOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => setPnlFilter(option.value)}
                className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                  pnlFilter === option.value
                    ? 'bg-primary/20 text-primary border border-primary/30'
                    : 'bg-muted/20 text-muted-foreground border border-muted/30 hover:bg-muted/30'
                }`}
              >
                {option.label}
                {option.count > 0 && (
                  <span className="ml-1 text-xs opacity-75">({option.count})</span>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Sort Options */}
        <div>
          <div className="text-sm font-medium text-muted-foreground mb-3">Sort By</div>
          <div className="flex flex-wrap gap-2">
            {[
              { field: 'pnl' as SortField, label: 'P&L %' },
              { field: 'value' as SortField, label: 'Position Value' },
              { field: 'symbol' as SortField, label: 'Token Symbol' },
              { field: 'entry' as SortField, label: 'Entry Value' },
              { field: 'progress' as SortField, label: 'Exit Progress' },
            ].map((option) => (
              <button
                key={option.field}
                onClick={() => handleSortChange(option.field)}
                className={`flex items-center gap-1 px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                  sortField === option.field
                    ? 'bg-primary/20 text-primary border border-primary/30'
                    : 'bg-muted/20 text-muted-foreground border border-muted/30 hover:bg-muted/30'
                }`}
              >
                {option.label}
                {sortField === option.field && (
                  sortDirection === 'asc' ? (
                    <SortAsc className="w-3 h-3" />
                  ) : (
                    <SortDesc className="w-3 h-3" />
                  )
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Active Filters Summary */}
        {hasActiveFilters && (
          <div className="pt-4 border-t border-border/30">
            <div className="text-sm font-medium text-muted-foreground mb-2">Active Filters:</div>
            <div className="flex flex-wrap gap-2">
              {searchTerm.trim() && (
                <Badge className="bg-primary/20 text-primary border border-primary/30">
                  Search: "{searchTerm}"
                </Badge>
              )}
              {statusFilter !== 'all' && (
                <Badge className="bg-primary/20 text-primary border border-primary/30">
                  Status: {statusOptions.find(o => o.value === statusFilter)?.label}
                </Badge>
              )}
              {pnlFilter !== 'all' && (
                <Badge className="bg-primary/20 text-primary border border-primary/30">
                  P&L: {pnlOptions.find(o => o.value === pnlFilter)?.label}
                </Badge>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}