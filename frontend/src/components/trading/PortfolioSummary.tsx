'use client'

import { useState, useEffect } from 'react'
import { TrendingUp, TrendingDown, DollarSign, Target, Activity, Award } from 'lucide-react'
import { Badge } from '@/components/ui/badge'

interface Position {
  id: string
  token: {
    symbol: string
    name: string
    address: string
  }
  amount: number
  entryPrice: number
  currentPrice: number
  entryValue: number
  currentValue: number
  pnlPercentage: number
  pnlDollar: number
  trailingStop: number
  exitMilestones: number[]
  completedExits: number
  moonBag: {
    percentage: number
    targetGain: number
    currentProgress: number
  }
  status: 'taking_profits' | 'approaching_target' | 'moon_bag' | 'trailing'
  progressToNextExit: {
    current: number
    next: number
  }
}

interface PortfolioSummaryProps {
  positions: Position[]
}

export function PortfolioSummary({ positions }: PortfolioSummaryProps) {
  const [dailyPerformance, setDailyPerformance] = useState<string>('0.00')

  // Initialize daily performance on client-side only to avoid hydration mismatch
  useEffect(() => {
    setDailyPerformance((Math.random() * 5 + 1).toFixed(2))
  }, [])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  // Calculate portfolio metrics
  const totalEntryValue = positions.reduce((sum, pos) => sum + pos.entryValue, 0)
  const totalCurrentValue = positions.reduce((sum, pos) => sum + pos.currentValue, 0)
  const totalPnlDollar = totalCurrentValue - totalEntryValue
  const totalPnlPercentage = totalEntryValue > 0 ? (totalPnlDollar / totalEntryValue) * 100 : 0

  const winningPositions = positions.filter(pos => pos.pnlPercentage > 0).length
  const winRate = positions.length > 0 ? (winningPositions / positions.length) * 100 : 0

  const totalExitsCompleted = positions.reduce((sum, pos) => sum + pos.completedExits, 0)
  const totalPossibleExits = positions.reduce((sum, pos) => sum + pos.exitMilestones.length, 0)

  const averagePosition = positions.length > 0 ? totalCurrentValue / positions.length : 0
  const largestPosition = positions.length > 0 ? Math.max(...positions.map(pos => pos.currentValue)) : 0

  return (
    <div className="bg-gradient-to-br from-card/80 to-card/40 backdrop-blur-sm border border-border/50 rounded-2xl shadow-xl p-8">
      <div className="flex items-center justify-between mb-8">
        <h2 className="text-2xl font-bold text-foreground">Portfolio Summary</h2>
        <Badge className="bg-primary/20 text-primary border border-primary/30 px-3 py-1">
          {positions.length} Active Position{positions.length !== 1 ? 's' : ''}
        </Badge>
      </div>

      {/* Main Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* Total Portfolio Value */}
        <div className="bg-gradient-to-br from-muted/20 to-muted/10 border border-border/30 rounded-xl p-6">
          <div className="flex items-center gap-3 mb-3">
            <DollarSign className="w-5 h-5 text-blue-500" />
            <span className="text-sm font-medium text-muted-foreground">Portfolio Value</span>
          </div>
          <div className="text-2xl font-bold text-foreground">
            {formatCurrency(totalCurrentValue)}
          </div>
          <div className="text-sm text-muted-foreground">
            Entry: {formatCurrency(totalEntryValue)}
          </div>
        </div>

        {/* Total P&L */}
        <div className="bg-gradient-to-br from-muted/20 to-muted/10 border border-border/30 rounded-xl p-6">
          <div className="flex items-center gap-3 mb-3">
            {totalPnlDollar >= 0 ? (
              <TrendingUp className="w-5 h-5 text-green-500" />
            ) : (
              <TrendingDown className="w-5 h-5 text-red-500" />
            )}
            <span className="text-sm font-medium text-muted-foreground">Total P&L</span>
          </div>
          <div className={`text-2xl font-bold ${totalPnlDollar >= 0 ? 'text-green-400' : 'text-red-400'}`}>
            {totalPnlDollar >= 0 ? '+' : ''}{formatCurrency(totalPnlDollar)}
          </div>
          <div className={`text-sm ${totalPnlPercentage >= 0 ? 'text-green-400' : 'text-red-400'}`}>
            {totalPnlPercentage >= 0 ? '+' : ''}{totalPnlPercentage.toFixed(2)}%
          </div>
        </div>

        {/* Win Rate */}
        <div className="bg-gradient-to-br from-muted/20 to-muted/10 border border-border/30 rounded-xl p-6">
          <div className="flex items-center gap-3 mb-3">
            <Award className="w-5 h-5 text-yellow-500" />
            <span className="text-sm font-medium text-muted-foreground">Win Rate</span>
          </div>
          <div className="text-2xl font-bold text-foreground">
            {winRate.toFixed(1)}%
          </div>
          <div className="text-sm text-muted-foreground">
            {winningPositions} of {positions.length} winning
          </div>
        </div>

        {/* Exit Progress */}
        <div className="bg-gradient-to-br from-muted/20 to-muted/10 border border-border/30 rounded-xl p-6">
          <div className="flex items-center gap-3 mb-3">
            <Target className="w-5 h-5 text-orange-500" />
            <span className="text-sm font-medium text-muted-foreground">Exit Progress</span>
          </div>
          <div className="text-2xl font-bold text-foreground">
            {totalExitsCompleted}/{totalPossibleExits}
          </div>
          <div className="text-sm text-muted-foreground">
            Exits completed
          </div>
        </div>
      </div>

      {/* Secondary Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Average Position Size */}
        <div className="text-center p-4 border border-border/30 rounded-lg">
          <div className="text-sm text-muted-foreground mb-1">Avg Position</div>
          <div className="text-lg font-bold text-foreground">
            {formatCurrency(averagePosition)}
          </div>
        </div>

        {/* Largest Position */}
        <div className="text-center p-4 border border-border/30 rounded-lg">
          <div className="text-sm text-muted-foreground mb-1">Largest Position</div>
          <div className="text-lg font-bold text-foreground">
            {formatCurrency(largestPosition)}
          </div>
        </div>

        {/* Daily Performance */}
        <div className="text-center p-4 border border-border/30 rounded-lg">
          <div className="text-sm text-muted-foreground mb-1">24h Performance</div>
          <div className="text-lg font-bold text-green-400">
            +{dailyPerformance}%
          </div>
        </div>
      </div>

      {/* Performance Indicator */}
      <div className="mt-6 pt-6 border-t border-border/30">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Activity className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">Portfolio Health:</span>
          </div>
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${
              winRate > 70 ? 'bg-green-500' : 
              winRate > 50 ? 'bg-yellow-500' : 
              'bg-orange-500'
            }`} />
            <span className={`text-sm font-medium ${
              winRate > 70 ? 'text-green-400' : 
              winRate > 50 ? 'text-yellow-400' : 
              'text-orange-400'
            }`}>
              {winRate > 70 ? 'Excellent' : 
               winRate > 50 ? 'Good' : 
               'Moderate'}
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}