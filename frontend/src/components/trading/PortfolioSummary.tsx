'use client'

import { TrendingUp, TrendingDown, DollarSign, Target, Activity, Award } from 'lucide-react'
import { Badge } from '@/components/ui/badge'

import { DashboardPosition, PortfolioMetrics, PerformanceData } from '@/stores/dashboardStore'

interface PortfolioSummaryProps {
  positions: DashboardPosition[]
  portfolioMetrics: PortfolioMetrics
  performanceData: PerformanceData
}

export function PortfolioSummary({ positions, portfolioMetrics, performanceData }: PortfolioSummaryProps) {
  // Handle undefined portfolioMetrics by providing defaults
  const safePortfolioMetrics = portfolioMetrics || {
    totalValue: 0,
    totalPnL: 0,
    totalPnLPercentage: 0,
    dailyChange: 0,
    dailyChangePercentage: 0,
    currentAllocation: 0,
    availableCapital: 0,
    capitalUsagePercentage: 0,
    activePositions: 0,
    maxAllocation: 0
  }

  const safePerformanceData = performanceData || {
    totalPnL24h: 0,
    yesterdayChange: 0,
    winRate: 0,
    winRateChange: 0,
    avgHoldTime: 0,
    holdTimeChange: 0,
    totalTrades: 0,
    successfulTrades: 0,
    profitFactor: 0,
    sharpeRatio: 0,
    maxDrawdown: 0
  }
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const formatCurrencyPrecise = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount)
  }

  // Calculate position-specific metrics
  const winningPositions = positions.filter(pos => pos.pnlPercentage > 0).length
  const averagePosition = positions.length > 0 ? safePortfolioMetrics.totalValue / positions.length : 0
  const largestPosition = positions.length > 0 ? Math.max(...positions.map(pos => pos.currentValue)) : 0
  
  // Mock exit data based on position performance (would be real data in production)
  const totalExitsCompleted = positions.filter(pos => pos.pnlPercentage > 50).length
  const totalPossibleExits = positions.length * 4 // Assume 4 potential exit points per position

  return (
    <div className="section-container shadow-2xl backdrop-blur-sm"
         style={{
           background: 'linear-gradient(135deg, hsl(var(--section-primary)) 0%, hsl(var(--section-secondary)) 50%, hsl(var(--section-tertiary)) 100%)',
           border: '1px solid hsl(var(--border-elevated))'
         }}>
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-foreground">Portfolio Overview</h2>
      </div>

      {/* Primary Portfolio Value Display */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        <div>
          <div className="text-sm text-muted-foreground mb-2">Current Portfolio Value</div>
          <div className="text-4xl font-bold text-foreground mb-3">
            ${safePortfolioMetrics.totalValue.toLocaleString()}
          </div>
          <div className="flex items-center gap-2">
            <div className={`text-lg font-semibold ${safePortfolioMetrics.totalPnLPercentage >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {safePortfolioMetrics.totalPnLPercentage >= 0 ? '+' : ''}{safePortfolioMetrics.totalPnLPercentage.toFixed(2)}%
            </div>
            <div className="text-sm text-muted-foreground">total return</div>
          </div>
        </div>
        <div>
          <div className="text-sm text-muted-foreground mb-2">24h Performance</div>
          <div className={`text-3xl font-bold mb-3 ${safePerformanceData.totalPnL24h >= 0 ? 'text-green-400' : 'text-red-400'}`}>
            {safePerformanceData.totalPnL24h >= 0 ? '+' : ''}{formatCurrencyPrecise(safePerformanceData.totalPnL24h)}
          </div>
          <div className={`text-lg font-semibold ${safePortfolioMetrics.dailyChangePercentage >= 0 ? 'text-green-400' : 'text-red-400'}`}>
            {safePortfolioMetrics.dailyChangePercentage >= 0 ? '+' : ''}{safePortfolioMetrics.dailyChangePercentage.toFixed(2)}% today
          </div>
        </div>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
        {/* Win Rate */}
        <div className="bg-gradient-to-br from-yellow-500/10 to-yellow-600/5 border border-yellow-500/20 rounded-xl p-4 text-center">
          <Award className="w-6 h-6 text-yellow-500 mx-auto mb-2" />
          <div className="text-2xl font-bold text-foreground">{safePerformanceData.winRate.toFixed(1)}%</div>
          <div className="text-xs text-muted-foreground">Win Rate</div>
          <div className="text-xs text-muted-foreground mt-1">{winningPositions} of {positions.length}</div>
        </div>

        {/* Exit Progress */}
        <div className="bg-gradient-to-br from-orange-500/10 to-orange-600/5 border border-orange-500/20 rounded-xl p-4 text-center">
          <Target className="w-6 h-6 text-orange-500 mx-auto mb-2" />
          <div className="text-2xl font-bold text-foreground">{totalExitsCompleted}/{totalPossibleExits}</div>
          <div className="text-xs text-muted-foreground">Exit Progress</div>
          <div className="text-xs text-muted-foreground mt-1">Completed</div>
        </div>

        {/* Average Position */}
        <div className="bg-gradient-to-br from-blue-500/10 to-blue-600/5 border border-blue-500/20 rounded-xl p-4 text-center">
          <DollarSign className="w-6 h-6 text-blue-500 mx-auto mb-2" />
          <div className="text-2xl font-bold text-foreground">{formatCurrency(averagePosition)}</div>
          <div className="text-xs text-muted-foreground">Avg Position</div>
        </div>

        {/* Largest Position */}
        <div className="bg-gradient-to-br from-purple-500/10 to-purple-600/5 border border-purple-500/20 rounded-xl p-4 text-center">
          <TrendingUp className="w-6 h-6 text-purple-500 mx-auto mb-2" />
          <div className="text-2xl font-bold text-foreground">{formatCurrency(largestPosition)}</div>
          <div className="text-xs text-muted-foreground">Largest Position</div>
        </div>
      </div>

      {/* Capital Allocation Section */}
      <div className="grid grid-cols-3 gap-6 mb-8 p-6 bg-gradient-to-br from-muted/10 to-muted/5 border border-border/30 rounded-xl">
        <div className="text-center">
          <div className="text-sm text-muted-foreground mb-2">Capital Usage</div>
          <div className="text-2xl font-bold text-foreground">
            {safePortfolioMetrics.capitalUsagePercentage.toFixed(1)}%
          </div>
          <div className="text-xs text-muted-foreground">
            ${safePortfolioMetrics.currentAllocation.toLocaleString()} allocated
          </div>
        </div>
        <div className="text-center">
          <div className="text-sm text-muted-foreground mb-2">Available Capital</div>
          <div className="text-2xl font-bold text-green-400">
            {formatCurrency(safePortfolioMetrics.availableCapital)}
          </div>
          <div className="text-xs text-muted-foreground">
            Ready to deploy
          </div>
        </div>
        <div className="text-center">
          <div className="text-sm text-muted-foreground mb-2">Total Trades</div>
          <div className="text-2xl font-bold text-foreground">
            {safePerformanceData.totalTrades}
          </div>
          <div className="text-xs text-muted-foreground">
            {safePerformanceData.successfulTrades} successful
          </div>
        </div>
      </div>

      {/* Portfolio Health Indicator */}
      <div className="flex items-center justify-between pt-6 border-t border-border/30">
        <div className="flex items-center gap-2">
          <Activity className="w-5 h-5 text-muted-foreground" />
          <span className="text-lg font-medium text-muted-foreground">Portfolio Health</span>
        </div>
        <div className="flex items-center gap-3">
          <div className={`w-3 h-3 rounded-full ${
            safePerformanceData.winRate > 70 ? 'bg-green-500' : 
            safePerformanceData.winRate > 50 ? 'bg-yellow-500' : 
            'bg-orange-500'
          }`} />
          <Badge className={`text-sm font-medium ${
            safePerformanceData.winRate > 70 ? 'bg-green-500/20 text-green-400 border-green-500/30' : 
            safePerformanceData.winRate > 50 ? 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30' : 
            'bg-orange-500/20 text-orange-400 border-orange-500/30'
          }`}>
            {safePerformanceData.winRate > 70 ? 'Excellent Performance' : 
             safePerformanceData.winRate > 50 ? 'Good Performance' : 
             'Moderate Performance'}
          </Badge>
        </div>
      </div>
    </div>
  )
}