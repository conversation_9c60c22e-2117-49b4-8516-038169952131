'use client'

import { TrendingUp, TrendingDown, Award, Clock, BarChart3, FileText } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

interface PerformanceData {
  totalPnL24h: number
  yesterdayChange: number
  winRate: number
  winRateChange: number
  avgHoldTime: number
  holdTimeChange: number
  totalTrades: number
  successfulTrades: number
}

interface PerformanceMetricsProps {
  data: PerformanceData
}

export function PerformanceMetrics({ data }: PerformanceMetricsProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount)
  }

  const formatTime = (hours: number) => {
    if (hours < 1) {
      return `${Math.round(hours * 60)}m`
    } else if (hours < 24) {
      return `${hours.toFixed(1)}h`
    } else {
      return `${Math.round(hours / 24)}d ${Math.round(hours % 24)}h`
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-semibold text-foreground mb-4">Performance Metrics</h2>
        
        <div className="bg-gradient-to-br from-card/80 to-card/40 backdrop-blur-sm border border-border/50 rounded-2xl shadow-xl p-6">
          {/* Header with Action Buttons */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                <BarChart3 className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-foreground">Trading Performance</h3>
                <p className="text-sm text-muted-foreground">Key performance indicators</p>
              </div>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" className="text-xs">
                <BarChart3 className="w-3 h-3 mr-1" />
                View Detailed Analytics
              </Button>
              <Button variant="outline" size="sm" className="text-xs">
                <FileText className="w-3 h-3 mr-1" />
                Export Report
              </Button>
            </div>
          </div>

          {/* Key Metrics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Total P&L (24h) */}
            <div className="bg-gradient-to-br from-green-500/10 to-green-600/5 border border-green-500/20 rounded-xl p-4">
              <div className="flex items-center gap-3 mb-3">
                <TrendingUp className="w-5 h-5 text-green-500" />
                <span className="text-sm font-medium text-muted-foreground">Total P&L (24h)</span>
              </div>
              <div className="text-2xl font-bold text-green-400 mb-2">
                +{formatCurrency(data.totalPnL24h)}
              </div>
              <div className="flex items-center gap-2">
                <div className={`text-sm font-medium ${data.yesterdayChange >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {data.yesterdayChange >= 0 ? '+' : ''}{data.yesterdayChange.toFixed(1)}%
                </div>
                <span className="text-xs text-muted-foreground">vs yesterday</span>
              </div>
            </div>

            {/* Win Rate */}
            <div className="bg-gradient-to-br from-yellow-500/10 to-yellow-600/5 border border-yellow-500/20 rounded-xl p-4">
              <div className="flex items-center gap-3 mb-3">
                <Award className="w-5 h-5 text-yellow-500" />
                <span className="text-sm font-medium text-muted-foreground">Win Rate</span>
              </div>
              <div className="text-2xl font-bold text-foreground mb-2">
                {data.winRate.toFixed(1)}%
              </div>
              <div className="flex items-center gap-2">
                <div className={`text-sm font-medium ${data.winRateChange >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {data.winRateChange >= 0 ? '+' : ''}{data.winRateChange.toFixed(1)}%
                </div>
                <span className="text-xs text-muted-foreground">vs last week</span>
              </div>
              <div className="text-xs text-muted-foreground mt-1">
                {data.successfulTrades} of {data.totalTrades} trades
              </div>
            </div>

            {/* Average Hold Time */}
            <div className="bg-gradient-to-br from-orange-500/10 to-orange-600/5 border border-orange-500/20 rounded-xl p-4">
              <div className="flex items-center gap-3 mb-3">
                <Clock className="w-5 h-5 text-orange-500" />
                <span className="text-sm font-medium text-muted-foreground">Avg Hold Time</span>
              </div>
              <div className="text-2xl font-bold text-foreground mb-2">
                {formatTime(data.avgHoldTime)}
              </div>
              <div className="flex items-center gap-2">
                <div className={`text-sm font-medium ${data.holdTimeChange >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {data.holdTimeChange >= 0 ? '+' : ''}{formatTime(Math.abs(data.holdTimeChange))}
                </div>
                <span className="text-xs text-muted-foreground">week-over-week</span>
              </div>
            </div>
          </div>

          {/* Additional Performance Stats */}
          <div className="mt-6 pt-6 border-t border-border/30">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-sm text-muted-foreground mb-1">Total Trades</div>
                <div className="text-lg font-bold text-foreground">{data.totalTrades}</div>
              </div>
              <div className="text-center">
                <div className="text-sm text-muted-foreground mb-1">Successful</div>
                <div className="text-lg font-bold text-green-400">{data.successfulTrades}</div>
              </div>
              <div className="text-center">
                <div className="text-sm text-muted-foreground mb-1">Failed</div>
                <div className="text-lg font-bold text-red-400">{data.totalTrades - data.successfulTrades}</div>
              </div>
              <div className="text-center">
                <div className="text-sm text-muted-foreground mb-1">Success Rate</div>
                <div className="text-lg font-bold text-foreground">
                  {((data.successfulTrades / data.totalTrades) * 100).toFixed(1)}%
                </div>
              </div>
            </div>
          </div>

          {/* Performance Status Indicator */}
          <div className="mt-6 pt-6 border-t border-border/30">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">Performance Status:</span>
              </div>
              <div className="flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${
                  data.winRate > 75 ? 'bg-green-500' : 
                  data.winRate > 60 ? 'bg-yellow-500' : 
                  'bg-orange-500'
                }`} />
                <Badge className={`${
                  data.winRate > 75 ? 'bg-green-500/20 text-green-400 border-green-500/30' : 
                  data.winRate > 60 ? 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30' : 
                  'bg-orange-500/20 text-orange-400 border-orange-500/30'
                }`}>
                  {data.winRate > 75 ? 'Excellent' : 
                   data.winRate > 60 ? 'Good' : 
                   'Needs Improvement'}
                </Badge>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}