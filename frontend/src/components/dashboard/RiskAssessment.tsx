'use client'

import { <PERSON>, AlertTriangle, Wifi, Clock, DollarSign, TrendingDown } from 'lucide-react'
import { Badge } from '@/components/ui/badge'

import { RiskAssessment as RiskAssessmentData } from '@/stores/dashboardStore'

interface RiskAssessmentProps {
  data: RiskAssessmentData
}

export function RiskAssessment({ data }: RiskAssessmentProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const getRiskBackground = (score: string) => {
    switch (score.toLowerCase()) {
      case 'low':
        return 'bg-success-section'
      case 'medium':
        return 'bg-warning-section'
      case 'high':
      case 'critical':
        return 'bg-danger-section'
      default:
        return 'section-container'
    }
  }

  const getRiskColor = (score: string) => {
    switch (score.toLowerCase()) {
      case 'low':
        return {
          bg: 'from-green-500/10 to-green-600/5',
          border: 'border-green-500/20',
          text: 'text-green-400',
          dot: 'bg-green-500'
        }
      case 'medium':
        return {
          bg: 'from-yellow-500/10 to-yellow-600/5',
          border: 'border-yellow-500/20',
          text: 'text-yellow-400',
          dot: 'bg-yellow-500'
        }
      case 'high':
        return {
          bg: 'from-red-500/10 to-red-600/5',
          border: 'border-red-500/20',
          text: 'text-red-400',
          dot: 'bg-red-500'
        }
      default:
        return {
          bg: 'from-muted/20 to-muted/10',
          border: 'border-border/30',
          text: 'text-muted-foreground',
          dot: 'bg-muted-foreground'
        }
    }
  }

  const riskColors = getRiskColor(data.riskScore)

  const getConnectionColor = (status: string) => {
    if (status.toLowerCase().includes('ready')) {
      return 'text-green-400'
    } else if (status.toLowerCase().includes('connecting')) {
      return 'text-yellow-400'
    } else {
      return 'text-red-400'
    }
  }

  return (
    <div className={`${getRiskBackground(data.riskScore)} shadow-2xl backdrop-blur-sm rounded-2xl p-6`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center">
            <Shield className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-foreground">Risk Assessment</h3>
            <p className="text-sm text-muted-foreground">Real-time risk monitoring</p>
          </div>
        </div>
        <Badge className={`${riskColors.text} border px-3 py-1`}>
          <div className={`w-2 h-2 rounded-full ${riskColors.dot} mr-2`} />
          {data.riskScore} Risk
        </Badge>
      </div>

      {/* Risk Score Display */}
      <div className={`bg-gradient-to-br ${riskColors.bg} border ${riskColors.border} rounded-xl p-4 mb-6`}>
        <div className="flex items-center justify-between">
          <div>
            <div className="text-sm text-muted-foreground mb-2">Current Risk Score</div>
            <div className={`text-3xl font-bold ${riskColors.text}`}>
              {data.riskScore}
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-muted-foreground mb-2">Change (24h)</div>
            <div className={`text-xl font-bold flex items-center gap-1 ${
              data.riskChange < 0 ? 'text-green-400' : 'text-red-400'
            }`}>
              {data.riskChange < 0 ? (
                <TrendingDown className="w-4 h-4" />
              ) : (
                <AlertTriangle className="w-4 h-4" />
              )}
              {data.riskChange > 0 ? '+' : ''}{data.riskChange.toFixed(1)}%
            </div>
          </div>
        </div>
      </div>

      {/* Risk Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        {/* Trading Limit */}
        <div className="bg-gradient-to-br from-muted/20 to-muted/10 border border-border/30 rounded-xl p-4">
          <div className="flex items-center gap-3 mb-3">
            <DollarSign className="w-4 h-4 text-blue-500" />
            <span className="text-sm font-medium text-muted-foreground">Maximum Limit</span>
          </div>
          <div className="text-2xl font-bold text-foreground mb-2">
            {formatCurrency(data.tradingLimit)}
          </div>
          <div className="text-sm text-muted-foreground">
            Trading limit
          </div>
        </div>

        {/* Limit Usage */}
        <div className="bg-gradient-to-br from-muted/20 to-muted/10 border border-border/30 rounded-xl p-4">
          <div className="flex items-center gap-3 mb-3">
            <AlertTriangle className="w-4 h-4 text-orange-500" />
            <span className="text-sm font-medium text-muted-foreground">Limit Usage</span>
          </div>
          <div className="text-2xl font-bold text-orange-400 mb-2">
            {data.limitUsage.toFixed(1)}%
          </div>
          <div className="text-sm text-muted-foreground">
            of maximum limit
          </div>
        </div>
      </div>

      {/* Usage Progress Bar */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-3">
          <span className="text-sm font-medium text-muted-foreground">Capital Usage</span>
          <span className="text-sm font-bold text-foreground">{data.limitUsage.toFixed(1)}%</span>
        </div>
        
        <div className="relative">
          <div className="w-full h-3 bg-muted/30 rounded-full overflow-hidden">
            <div 
              className={`h-full rounded-full transition-all duration-500 ease-out ${
                data.limitUsage > 90 ? 'bg-gradient-to-r from-red-500 to-red-600' :
                data.limitUsage > 75 ? 'bg-gradient-to-r from-yellow-500 to-yellow-600' :
                'bg-gradient-to-r from-green-500 to-green-600'
              }`}
              style={{ width: `${Math.min(data.limitUsage, 100)}%` }}
            />
          </div>
          <div className="flex justify-between text-xs text-muted-foreground mt-2">
            <span>$0</span>
            <span>{formatCurrency(data.tradingLimit)}</span>
          </div>
        </div>
      </div>

      {/* Available Capital */}
      <div className="bg-gradient-to-br from-green-500/10 to-green-600/5 border border-green-500/20 rounded-xl p-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <div className="text-sm text-muted-foreground mb-2">Available Capital</div>
            <div className="text-2xl font-bold text-green-400">
              {formatCurrency(data.availableCapital)}
            </div>
          </div>
          <div className="text-right">
            <Badge className="bg-green-500/20 text-green-400 border border-green-500/30">
              Ready to Trade
            </Badge>
          </div>
        </div>
      </div>

      {/* Status Indicators */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Connection Status */}
        <div className="bg-gradient-to-br from-muted/20 to-muted/10 border border-border/30 rounded-xl p-4">
          <div className="flex items-center gap-3 mb-2">
            <Wifi className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm font-medium text-muted-foreground">Connection</span>
          </div>
          <div className={`text-lg font-bold ${getConnectionColor(data.connectionStatus)}`}>
            {data.connectionStatus}
          </div>
        </div>

        {/* Last Update */}
        <div className="bg-gradient-to-br from-muted/20 to-muted/10 border border-border/30 rounded-xl p-4">
          <div className="flex items-center gap-3 mb-2">
            <Clock className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm font-medium text-muted-foreground">Last Update</span>
          </div>
          <div className="text-lg font-bold text-foreground">
            {data.lastUpdate}
          </div>
        </div>
      </div>

      {/* Risk Alerts */}
      {data.alerts.length > 0 && (
        <div className="mt-6 p-4 bg-gradient-to-br from-red-500/10 to-red-600/5 border border-red-500/20 rounded-xl">
          <div className="flex items-center gap-2 mb-3">
            <AlertTriangle className="w-4 h-4 text-red-400" />
            <span className="text-sm font-medium text-red-400">Risk Alerts</span>
          </div>
          <div className="space-y-2">
            {data.alerts.map((alert, index) => (
              <p key={index} className="text-sm text-red-300">{alert}</p>
            ))}
          </div>
        </div>
      )}

      {/* Recommendations */}
      {data.recommendations.length > 0 && (
        <div className="mt-4 p-4 bg-gradient-to-br from-blue-500/10 to-blue-600/5 border border-blue-500/20 rounded-xl">
          <div className="flex items-center gap-2 mb-3">
            <Shield className="w-4 h-4 text-blue-400" />
            <span className="text-sm font-medium text-blue-400">Recommendations</span>
          </div>
          <div className="space-y-2">
            {data.recommendations.map((recommendation, index) => (
              <p key={index} className="text-sm text-blue-300">{recommendation}</p>
            ))}
          </div>
        </div>
      )}

      {/* Fallback Risk Warning for high usage */}
      {data.limitUsage > 85 && data.alerts.length === 0 && (
        <div className="mt-6 p-4 bg-gradient-to-br from-yellow-500/10 to-yellow-600/5 border border-yellow-500/20 rounded-xl">
          <div className="flex items-center gap-2">
            <AlertTriangle className="w-4 h-4 text-yellow-400" />
            <span className="text-sm font-medium text-yellow-400">Risk Warning</span>
          </div>
          <p className="text-sm text-muted-foreground mt-2">
            You are approaching your trading limit. Consider reducing position sizes or closing some positions.
          </p>
        </div>
      )}
    </div>
  )
}