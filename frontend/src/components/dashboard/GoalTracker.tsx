'use client'

import { useState } from 'react'
import { Target, TrendingUp, Calendar, DollarSign, Clock, BarChart3 } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

interface DailyPnL {
  date: string
  pnl: number
}

interface GoalData {
  targetAmount: number
  currentAmount: number
  dailyPnL: DailyPnL[]
  averageDaily7d: number
  averageDaily30d: number
  estimatedDaysConservative: number
  estimatedDaysOptimistic: number
  dailyAmountNeeded: number
  progressPercentage: number
}

interface GoalTrackerProps {
  data: GoalData
}

export function GoalTracker({ data }: GoalTrackerProps) {
  const [timeframe, setTimeframe] = useState<'7d' | '30d'>('7d')

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const formatCurrencyPrecise = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount)
  }

  const remainingAmount = data.targetAmount - data.currentAmount
  const averageDaily = timeframe === '7d' ? data.averageDaily7d : data.averageDaily30d
  const estimatedDays = timeframe === '7d' ? 
    Math.ceil(remainingAmount / data.averageDaily7d) : 
    Math.ceil(remainingAmount / data.averageDaily30d)

  // Calculate recent P&L trend
  const recentPnL = data.dailyPnL.slice(-7)
  const totalRecentPnL = recentPnL.reduce((sum, day) => sum + day.pnl, 0)
  const avgRecentPnL = totalRecentPnL / recentPnL.length

  return (
    <div className="bg-gradient-to-br from-card/80 to-card/40 backdrop-blur-sm border border-border/50 rounded-2xl shadow-xl p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
            <Target className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-foreground">100K Goal Tracker</h2>
            <p className="text-sm text-muted-foreground">Track your journey to $100,000</p>
          </div>
        </div>
        <Badge className="bg-purple-500/20 text-purple-400 border border-purple-500/30 px-3 py-1">
          {data.progressPercentage.toFixed(1)}% Complete
        </Badge>
      </div>

      {/* Target Display */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="bg-gradient-to-br from-muted/20 to-muted/10 border border-border/30 rounded-xl p-4">
          <div className="text-sm text-muted-foreground mb-2">Target Amount</div>
          <div className="text-3xl font-bold text-foreground mb-2">
            {formatCurrency(data.targetAmount)}
          </div>
          <div className="text-sm text-muted-foreground">Ultimate goal</div>
        </div>
        <div className="bg-gradient-to-br from-muted/20 to-muted/10 border border-border/30 rounded-xl p-4">
          <div className="text-sm text-muted-foreground mb-2">Current Amount</div>
          <div className="text-3xl font-bold text-green-400 mb-2">
            {formatCurrency(data.currentAmount)}
          </div>
          <div className="text-sm text-muted-foreground">Portfolio value</div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-3">
          <span className="text-lg font-medium text-muted-foreground">Progress to Goal</span>
          <span className="text-xl font-bold text-foreground">{data.progressPercentage.toFixed(1)}%</span>
        </div>
        
        <div className="relative">
          <div className="w-full h-6 bg-muted/30 rounded-full overflow-hidden">
            <div 
              className="h-full bg-gradient-to-r from-purple-500 to-purple-600 rounded-full transition-all duration-500 ease-out relative"
              style={{ width: `${Math.min(data.progressPercentage, 100)}%` }}
            >
              {/* Animated shine effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse" />
            </div>
          </div>
          
          {/* Milestone markers */}
          <div className="flex justify-between text-xs text-muted-foreground mt-2">
            <span>$0</span>
            <span>$25K</span>
            <span>$50K</span>
            <span>$75K</span>
            <span>$100K</span>
          </div>
        </div>
        
        <div className="mt-3 text-center">
          <div className="text-lg font-bold text-foreground">
            {formatCurrency(remainingAmount)} remaining
          </div>
        </div>
      </div>

      {/* Time Estimation */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        {/* Time Frame Selector */}
        <div className="bg-gradient-to-br from-muted/20 to-muted/10 border border-border/30 rounded-xl p-4">
          <div className="flex items-center gap-2 mb-3">
            <Calendar className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm font-medium text-muted-foreground">Based on</span>
          </div>
          <div className="flex gap-2 mb-3">
            <Button
              variant={timeframe === '7d' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setTimeframe('7d')}
              className="text-xs"
            >
              7 Days
            </Button>
            <Button
              variant={timeframe === '30d' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setTimeframe('30d')}
              className="text-xs"
            >
              30 Days
            </Button>
          </div>
          <div className="text-sm text-muted-foreground">
            Avg: {formatCurrencyPrecise(averageDaily)}/day
          </div>
        </div>

        {/* Estimated Time */}
        <div className="bg-gradient-to-br from-muted/20 to-muted/10 border border-border/30 rounded-xl p-4">
          <div className="flex items-center gap-2 mb-3">
            <Clock className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm font-medium text-muted-foreground">Estimated Time</span>
          </div>
          <div className="text-2xl font-bold text-foreground mb-1">
            {estimatedDays} days
          </div>
          <div className="text-sm text-muted-foreground">
            ~{Math.ceil(estimatedDays / 30)} months
          </div>
        </div>
      </div>

      {/* Daily Targets */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="bg-gradient-to-br from-blue-500/10 to-blue-600/5 border border-blue-500/20 rounded-xl p-4">
          <div className="flex items-center gap-3 mb-3">
            <DollarSign className="w-5 h-5 text-blue-500" />
            <span className="text-sm font-medium text-muted-foreground">Daily Amount Needed</span>
          </div>
          <div className="text-2xl font-bold text-blue-400 mb-2">
            {formatCurrencyPrecise(data.dailyAmountNeeded)}
          </div>
          <div className="text-sm text-muted-foreground">
            To reach goal in 1 year
          </div>
        </div>

        <div className="bg-gradient-to-br from-green-500/10 to-green-600/5 border border-green-500/20 rounded-xl p-4">
          <div className="flex items-center gap-3 mb-3">
            <TrendingUp className="w-5 h-5 text-green-500" />
            <span className="text-sm font-medium text-muted-foreground">Recent Performance</span>
          </div>
          <div className="text-2xl font-bold text-green-400 mb-2">
            {formatCurrencyPrecise(avgRecentPnL)}
          </div>
          <div className="text-sm text-muted-foreground">
            7-day average
          </div>
        </div>
      </div>

      {/* Recent Daily P&L History */}
      <div className="bg-gradient-to-br from-muted/20 to-muted/10 border border-border/30 rounded-xl p-4">
        <div className="flex items-center gap-2 mb-4">
          <BarChart3 className="w-4 h-4 text-muted-foreground" />
          <span className="text-sm font-medium text-muted-foreground">Daily P&L (Last 7 Days)</span>
        </div>
        
        <div className="grid grid-cols-7 gap-1">
          {recentPnL.map((day, index) => (
            <div key={index} className="text-center">
              <div className="text-xs text-muted-foreground mb-1">
                {new Date(day.date).toLocaleDateString('en-US', { weekday: 'short' })}
              </div>
              <div className={`text-xs font-bold p-2 rounded ${
                day.pnl >= 0 ? 'text-green-400 bg-green-500/10' : 'text-red-400 bg-red-500/10'
              }`}>
                {day.pnl >= 0 ? '+' : ''}{formatCurrencyPrecise(day.pnl)}
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-4 pt-4 border-t border-border/30">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">7-day total:</span>
            <span className={`font-bold ${totalRecentPnL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {totalRecentPnL >= 0 ? '+' : ''}{formatCurrencyPrecise(totalRecentPnL)}
            </span>
          </div>
        </div>
      </div>

      {/* Goal Status */}
      <div className="mt-6 pt-6 border-t border-border/30">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Target className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">Goal Status:</span>
          </div>
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${
              avgRecentPnL >= data.dailyAmountNeeded ? 'bg-green-500' : 
              avgRecentPnL >= data.dailyAmountNeeded * 0.7 ? 'bg-yellow-500' : 
              'bg-orange-500'
            }`} />
            <Badge className={`${
              avgRecentPnL >= data.dailyAmountNeeded ? 'bg-green-500/20 text-green-400 border-green-500/30' : 
              avgRecentPnL >= data.dailyAmountNeeded * 0.7 ? 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30' : 
              'bg-orange-500/20 text-orange-400 border-orange-500/30'
            }`}>
              {avgRecentPnL >= data.dailyAmountNeeded ? 'On Track' : 
               avgRecentPnL >= data.dailyAmountNeeded * 0.7 ? 'Close' : 
               'Behind Target'}
            </Badge>
          </div>
        </div>
      </div>
    </div>
  )
}