'use client'

import { useEffect, useRef, useState } from 'react'
import { AlertItem } from './AlertItem'
import { useAlertStore } from '@/stores/alertStore'
import { Alert } from 'shared/src/types/alerts'
import { 
  CheckCircle, 
  Circle, 
  Trash2, 
  Filter,
  Search,
  Loader2,
  AlertCircle,
  ChevronDown,
  RefreshCw
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface AlertFeedProps {
  compact?: boolean
  maxHeight?: string
  showFilters?: boolean
  showBulkActions?: boolean
  autoRefresh?: boolean
  refreshInterval?: number
}

export function AlertFeed({ 
  compact = false,
  maxHeight = "h-96",
  showFilters = false,
  showBulkActions = false,
  autoRefresh = false,
  refreshInterval = 30000
}: AlertFeedProps) {
  const {
    alerts,
    selectedAlerts,
    isLoading,
    error,
    getFilteredAlerts,
    getUnreadCount,
    toggleAlertSelection,
    clearSelection,
    deleteSelectedAlerts,
    markAllAsRead,
    refreshStatistics
  } = useAlertStore()
  
  const [searchTerm, setSearchTerm] = useState('')
  const [localLoading, setLocalLoading] = useState(false)
  const feedRef = useRef<HTMLDivElement>(null)
  
  // Auto-refresh functionality
  useEffect(() => {
    if (!autoRefresh) return
    
    const interval = setInterval(() => {
      refreshStatistics()
    }, refreshInterval)
    
    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval, refreshStatistics])
  
  // Get filtered alerts
  const filteredAlerts = getFilteredAlerts()
  
  // Further filter by search term
  const displayAlerts = searchTerm.trim() 
    ? filteredAlerts.filter(alert => 
        alert.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        alert.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
        alert.metadata?.token?.symbol?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : filteredAlerts
  
  const unreadCount = getUnreadCount()
  const selectedCount = selectedAlerts.size
  const allSelected = displayAlerts.length > 0 && displayAlerts.every(alert => selectedAlerts.has(alert.id))
  
  const handleSelectAll = () => {
    if (allSelected) {
      clearSelection()
    } else {
      displayAlerts.forEach(alert => {
        if (!selectedAlerts.has(alert.id)) {
          toggleAlertSelection(alert.id)
        }
      })
    }
  }
  
  const handleRefresh = async () => {
    setLocalLoading(true)
    try {
      refreshStatistics()
      // Simulate refresh delay for better UX
      await new Promise(resolve => setTimeout(resolve, 500))
    } finally {
      setLocalLoading(false)
    }
  }
  
  const handleBulkDelete = () => {
    if (selectedCount > 0) {
      deleteSelectedAlerts()
    }
  }
  
  const handleMarkAllAsRead = () => {
    markAllAsRead()
  }
  
  if (error) {
    return (
      <div className="bg-card-elevated border border-red-500/30 rounded-lg p-6 text-center">
        <AlertCircle className="w-8 h-8 text-red-500 mx-auto mb-3" />
        <h3 className="text-lg font-semibold text-red-500 mb-2">Error Loading Alerts</h3>
        <p className="text-muted-foreground mb-4">{error}</p>
        <button
          onClick={handleRefresh}
          className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
        >
          Try Again
        </button>
      </div>
    )
  }
  
  return (
    <div>
      {/* Search Bar */}
      {showFilters && (
        <div className="p-4 border-b border-border">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search notifications..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-card-interactive border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50"
            />
          </div>
        </div>
      )}
      
      {/* Alert List */}
      <div 
        ref={feedRef}
        className={cn(
          "overflow-y-auto",
          maxHeight
        )}
      >
        {isLoading || localLoading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-6 h-6 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground">Loading alerts...</span>
          </div>
        ) : displayAlerts.length === 0 ? (
          <div className="text-center py-12">
            <AlertCircle className="w-12 h-12 text-muted-foreground mx-auto mb-4 opacity-50" />
            <h3 className="text-lg font-medium text-muted-foreground mb-2">
              {searchTerm.trim() ? 'No alerts found' : 'No alerts yet'}
            </h3>
            <p className="text-sm text-muted-foreground">
              {searchTerm.trim() 
                ? 'Try adjusting your search or filters'
                : 'Alerts will appear here when they are triggered'
              }
            </p>
          </div>
        ) : (
          <div>
            {displayAlerts.map((alert, index) => (
              <AlertItem
                key={alert.id}
                alert={alert}
                selected={selectedAlerts.has(alert.id)}
                onToggleSelection={showBulkActions ? toggleAlertSelection : undefined}
                compact={compact}
              />
            ))}
          </div>
        )}
      </div>
      
      {/* Simplified Footer Stats */}
      {!compact && (
        <div className="px-4 py-2 bg-card-interactive/50 border-t border-border rounded-b-lg">
          <div className="flex items-center justify-center text-xs text-muted-foreground">
            <span>
              {displayAlerts.length} of {alerts.length} alerts
              {unreadCount > 0 && ` • ${unreadCount} unread`}
            </span>
          </div>
        </div>
      )}
    </div>
  )
}