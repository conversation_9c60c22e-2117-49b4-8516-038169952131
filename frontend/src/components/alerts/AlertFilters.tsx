'use client'

import { useState } from 'react'
import { useAlertStore } from '@/stores/alertStore'
import { AlertType, AlertPriority } from 'shared/src/types/enums'
import { AlertFilters as AlertFiltersType } from '@/utils/alertUtils'
import { 
  Filter, 
  X, 
  Calendar,
  Tag,
  AlertCircle,
  CheckCircle,
  Clock,
  Search
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface AlertFiltersProps {
  isOpen: boolean
  onClose: () => void
}

const PRIORITY_OPTIONS = [
  { value: AlertPriority.CRITICAL, label: 'Critical', color: 'text-red-500' },
  { value: AlertPriority.HIGH, label: 'High', color: 'text-orange-500' },
  { value: AlertPriority.MEDIUM, label: 'Medium', color: 'text-yellow-500' },
  { value: AlertPriority.LOW, label: 'Low', color: 'text-blue-500' }
]

const CATEGORY_OPTIONS = [
  { value: AlertType.TRADE, label: 'Trade', icon: '💰' },
  { value: AlertType.EXIT, label: 'Exit', icon: '🚪' },
  { value: AlertType.ERROR, label: 'Error', icon: '⚠️' },
  { value: AlertType.SYSTEM, label: 'System', icon: '⚙️' },
  { value: AlertType.PRICE, label: 'Price', icon: '📈' },
  { value: AlertType.STRATEGY, label: 'Strategy', icon: '🎯' }
]

const TIME_RANGE_OPTIONS = [
  { value: 'today', label: 'Today' },
  { value: '24h', label: 'Last 24 hours' },
  { value: '7d', label: 'Last 7 days' },
  { value: '30d', label: 'Last 30 days' },
  { value: 'custom', label: 'Custom range' }
] as const

const READ_STATUS_OPTIONS = [
  { value: 'all', label: 'All alerts' },
  { value: 'unread', label: 'Unread only' },
  { value: 'read', label: 'Read only' }
] as const

export function AlertFilters({ isOpen, onClose }: AlertFiltersProps) {
  const { filters, updateFilters, clearFilters } = useAlertStore()
  const [localFilters, setLocalFilters] = useState<AlertFiltersType>(filters)
  const [tokenInput, setTokenInput] = useState('')
  
  const handleApplyFilters = () => {
    updateFilters(localFilters)
    onClose()
  }
  
  const handleClearFilters = () => {
    const defaultFilters: AlertFiltersType = {
      categories: [],
      priorities: [],
      readStatus: 'all',
      timeRange: '7d',
      tokens: [],
      searchTerm: ''
    }
    setLocalFilters(defaultFilters)
    clearFilters()
  }
  
  const handleCategoryToggle = (category: AlertType) => {
    setLocalFilters(prev => ({
      ...prev,
      categories: prev.categories.includes(category)
        ? prev.categories.filter(c => c !== category)
        : [...prev.categories, category]
    }))
  }
  
  const handlePriorityToggle = (priority: AlertPriority) => {
    setLocalFilters(prev => ({
      ...prev,
      priorities: prev.priorities.includes(priority)
        ? prev.priorities.filter(p => p !== priority)
        : [...prev.priorities, priority]
    }))
  }
  
  const handleAddToken = () => {
    const token = tokenInput.trim().toUpperCase()
    if (token && !localFilters.tokens.includes(token)) {
      setLocalFilters(prev => ({
        ...prev,
        tokens: [...prev.tokens, token]
      }))
      setTokenInput('')
    }
  }
  
  const handleRemoveToken = (token: string) => {
    setLocalFilters(prev => ({
      ...prev,
      tokens: prev.tokens.filter(t => t !== token)
    }))
  }
  
  const hasActiveFilters = 
    localFilters.categories.length > 0 ||
    localFilters.priorities.length > 0 ||
    localFilters.readStatus !== 'all' ||
    localFilters.timeRange !== '7d' ||
    localFilters.tokens.length > 0 ||
    localFilters.searchTerm.trim() !== ''
  
  if (!isOpen) return null
  
  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div className="bg-card-elevated border rounded-lg w-full max-w-2xl max-h-[90vh] overflow-hidden shadow-xl">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border bg-card-interactive">
          <div className="flex items-center gap-3">
            <Filter className="w-5 h-5 text-primary" />
            <h2 className="text-lg font-semibold text-foreground">Filter Alerts</h2>
            {hasActiveFilters && (
              <span className="px-2 py-1 bg-primary/20 text-primary text-xs font-medium rounded-full">
                Active
              </span>
            )}
          </div>
          <button
            onClick={onClose}
            className="p-2 text-muted-foreground hover:text-foreground transition-colors rounded-md hover:bg-card-elevated"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[70vh] space-y-6">
          {/* Search Term */}
          <div>
            <label className="flex items-center gap-2 text-sm font-medium text-foreground mb-3">
              <Search className="w-4 h-4" />
              Search Term
            </label>
            <input
              type="text"
              placeholder="Search in titles, messages, and tokens..."
              value={localFilters.searchTerm}
              onChange={(e) => setLocalFilters(prev => ({ ...prev, searchTerm: e.target.value }))}
              className="w-full px-3 py-2 bg-card-interactive border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50"
            />
          </div>
          
          {/* Categories */}
          <div>
            <label className="flex items-center gap-2 text-sm font-medium text-foreground mb-3">
              <Tag className="w-4 h-4" />
              Categories
            </label>
            <div className="grid grid-cols-2 gap-2">
              {CATEGORY_OPTIONS.map((option) => (
                <button
                  key={option.value}
                  onClick={() => handleCategoryToggle(option.value)}
                  className={cn(
                    "flex items-center gap-2 px-3 py-2 rounded-md border transition-all text-sm",
                    localFilters.categories.includes(option.value)
                      ? "bg-primary/20 border-primary text-primary"
                      : "bg-card-interactive border-border text-muted-foreground hover:text-foreground hover:border-primary/50"
                  )}
                >
                  <span>{option.icon}</span>
                  <span>{option.label}</span>
                  {localFilters.categories.includes(option.value) && (
                    <CheckCircle className="w-3 h-3 ml-auto" />
                  )}
                </button>
              ))}
            </div>
          </div>
          
          {/* Priorities */}
          <div>
            <label className="flex items-center gap-2 text-sm font-medium text-foreground mb-3">
              <AlertCircle className="w-4 h-4" />
              Priorities
            </label>
            <div className="grid grid-cols-2 gap-2">
              {PRIORITY_OPTIONS.map((option) => (
                <button
                  key={option.value}
                  onClick={() => handlePriorityToggle(option.value)}
                  className={cn(
                    "flex items-center gap-2 px-3 py-2 rounded-md border transition-all text-sm",
                    localFilters.priorities.includes(option.value)
                      ? "bg-primary/20 border-primary text-primary"
                      : "bg-card-interactive border-border text-muted-foreground hover:text-foreground hover:border-primary/50"
                  )}
                >
                  <div className={cn("w-2 h-2 rounded-full", option.color.replace('text-', 'bg-'))} />
                  <span>{option.label}</span>
                  {localFilters.priorities.includes(option.value) && (
                    <CheckCircle className="w-3 h-3 ml-auto" />
                  )}
                </button>
              ))}
            </div>
          </div>
          
          {/* Read Status */}
          <div>
            <label className="flex items-center gap-2 text-sm font-medium text-foreground mb-3">
              <CheckCircle className="w-4 h-4" />
              Read Status
            </label>
            <div className="space-y-2">
              {READ_STATUS_OPTIONS.map((option) => (
                <label key={option.value} className="flex items-center gap-3">
                  <input
                    type="radio"
                    name="readStatus"
                    value={option.value}
                    checked={localFilters.readStatus === option.value}
                    onChange={(e) => setLocalFilters(prev => ({ 
                      ...prev, 
                      readStatus: e.target.value as 'all' | 'read' | 'unread' 
                    }))}
                    className="w-4 h-4 text-primary focus:ring-primary/50"
                  />
                  <span className="text-sm text-foreground">{option.label}</span>
                </label>
              ))}
            </div>
          </div>
          
          {/* Time Range */}
          <div>
            <label className="flex items-center gap-2 text-sm font-medium text-foreground mb-3">
              <Clock className="w-4 h-4" />
              Time Range
            </label>
            <div className="space-y-2">
              {TIME_RANGE_OPTIONS.map((option) => (
                <label key={option.value} className="flex items-center gap-3">
                  <input
                    type="radio"
                    name="timeRange"
                    value={option.value}
                    checked={localFilters.timeRange === option.value}
                    onChange={(e) => setLocalFilters(prev => ({ 
                      ...prev, 
                      timeRange: e.target.value as typeof prev.timeRange
                    }))}
                    className="w-4 h-4 text-primary focus:ring-primary/50"
                  />
                  <span className="text-sm text-foreground">{option.label}</span>
                </label>
              ))}
            </div>
            
            {/* Custom Date Range */}
            {localFilters.timeRange === 'custom' && (
              <div className="mt-3 space-y-3">
                <div>
                  <label className="block text-xs font-medium text-muted-foreground mb-1">
                    Start Date
                  </label>
                  <input
                    type="datetime-local"
                    value={localFilters.customStartDate?.toISOString().slice(0, 16) || ''}
                    onChange={(e) => setLocalFilters(prev => ({
                      ...prev,
                      customStartDate: e.target.value ? new Date(e.target.value) : undefined
                    }))}
                    className="w-full px-3 py-2 bg-card-interactive border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-muted-foreground mb-1">
                    End Date
                  </label>
                  <input
                    type="datetime-local"
                    value={localFilters.customEndDate?.toISOString().slice(0, 16) || ''}
                    onChange={(e) => setLocalFilters(prev => ({
                      ...prev,
                      customEndDate: e.target.value ? new Date(e.target.value) : undefined
                    }))}
                    className="w-full px-3 py-2 bg-card-interactive border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50"
                  />
                </div>
              </div>
            )}
          </div>
          
          {/* Tokens */}
          <div>
            <label className="flex items-center gap-2 text-sm font-medium text-foreground mb-3">
              <Calendar className="w-4 h-4" />
              Tokens
            </label>
            <div className="space-y-3">
              <div className="flex gap-2">
                <input
                  type="text"
                  placeholder="Add token symbol (e.g., PEPE)"
                  value={tokenInput}
                  onChange={(e) => setTokenInput(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleAddToken()}
                  className="flex-1 px-3 py-2 bg-card-interactive border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50"
                />
                <button
                  onClick={handleAddToken}
                  className="px-4 py-2 bg-primary text-primary-foreground rounded-md text-sm font-medium hover:bg-primary/90 transition-colors"
                >
                  Add
                </button>
              </div>
              
              {localFilters.tokens.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {localFilters.tokens.map((token) => (
                    <span
                      key={token}
                      className="inline-flex items-center gap-1 px-2 py-1 bg-primary/20 text-primary text-xs font-medium rounded-md border border-primary/30"
                    >
                      {token}
                      <button
                        onClick={() => handleRemoveToken(token)}
                        className="hover:text-primary/70 transition-colors"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
        
        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-border bg-card-interactive">
          <button
            onClick={handleClearFilters}
            className="px-4 py-2 text-muted-foreground hover:text-foreground transition-colors rounded-md hover:bg-card-elevated border border-border"
          >
            Clear All
          </button>
          <div className="flex items-center gap-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-muted-foreground hover:text-foreground transition-colors rounded-md hover:bg-card-elevated border border-border"
            >
              Cancel
            </button>
            <button
              onClick={handleApplyFilters}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md font-medium hover:bg-primary/90 transition-colors"
            >
              Apply Filters
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}