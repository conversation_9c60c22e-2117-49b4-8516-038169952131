'use client'

import { useState, useRef, useEffect } from 'react'
import { AlertItem } from './AlertItem'
import { useAlertStore } from '@/stores/alertStore'
import { Bell, X } from 'lucide-react'
import { cn } from '@/lib/utils'
import Link from 'next/link'

interface NotificationDropdownProps {
  className?: string
}

export function NotificationDropdown({ className }: NotificationDropdownProps) {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const { alerts, getUnreadCount, markAllAsRead } = useAlertStore()
  
  const unreadCount = getUnreadCount()
  const recentAlerts = alerts.slice(0, 5) // Show only 5 most recent
  
  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])
  
  return (
    <div ref={dropdownRef} className={cn("relative", className)}>
      {/* Notification Bell */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-muted-foreground hover:text-foreground transition-colors rounded-md hover:bg-card-interactive"
      >
        <Bell className="w-5 h-5" />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 w-5 h-5 bg-orange-500 text-white text-xs rounded-full flex items-center justify-center font-bold">
            {unreadCount > 9 ? '9+' : unreadCount}
          </span>
        )}
      </button>
      
      {/* Dropdown */}
      {isOpen && (
        <div className="absolute right-0 top-full mt-2 w-80 bg-card-elevated border rounded-lg shadow-xl z-50">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-border">
            <h3 className="font-semibold text-foreground">Notifications</h3>
            <div className="flex items-center gap-2">
              {unreadCount > 0 && (
                <button
                  onClick={markAllAsRead}
                  className="text-xs text-primary hover:text-primary/80 transition-colors"
                >
                  Mark all read
                </button>
              )}
              <button
                onClick={() => setIsOpen(false)}
                className="p-1 text-muted-foreground hover:text-foreground transition-colors rounded"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
          
          {/* Notifications */}
          <div className="max-h-96 overflow-y-auto">
            {recentAlerts.length === 0 ? (
              <div className="p-6 text-center text-muted-foreground">
                <Bell className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No notifications yet</p>
              </div>
            ) : (
              <div>
                {recentAlerts.map((alert) => (
                  <AlertItem
                    key={alert.id}
                    alert={alert}
                    compact={true}
                  />
                ))}
              </div>
            )}
          </div>
          
          {/* Footer */}
          {alerts.length > 5 && (
            <div className="p-3 border-t border-border">
              <Link
                href="/alerts"
                onClick={() => setIsOpen(false)}
                className="block w-full text-center text-sm text-primary hover:text-primary/80 transition-colors"
              >
                View all notifications ({alerts.length})
              </Link>
            </div>
          )}
        </div>
      )}
    </div>
  )
}