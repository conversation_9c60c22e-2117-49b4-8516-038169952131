'use client'

import { useMemo } from 'react'
import { useAlertStore } from '@/stores/alertStore'
import { AlertType, AlertPriority } from 'shared/src/types/enums'
import { 
  BarChart3, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle,
  Bell,
  Clock,
  Target,
  Activity
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { getAlertPriorityColor, getAlertTypeColor } from '@/utils/alertUtils'

interface StatCardProps {
  title: string
  value: string | number
  icon: React.ComponentType<{ className?: string }>
  color?: string
  trend?: {
    value: number
    isPositive: boolean
  }
  subtitle?: string
}

function StatCard({ title, value, icon: Icon, color = 'text-muted-foreground', trend, subtitle }: StatCardProps) {
  return (
    <div className="bg-card-elevated border rounded-lg p-4 hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between mb-2">
        <Icon className={cn("w-5 h-5", color)} />
        {trend && (
          <div className={cn(
            "flex items-center text-xs font-medium",
            trend.isPositive ? "text-green-500" : "text-red-500"
          )}>
            <TrendingUp className={cn(
              "w-3 h-3 mr-1",
              !trend.isPositive && "rotate-180"
            )} />
            {Math.abs(trend.value)}%
          </div>
        )}
      </div>
      <div className="space-y-1">
        <div className="text-2xl font-bold text-foreground">{value}</div>
        <div className="text-sm font-medium text-muted-foreground">{title}</div>
        {subtitle && (
          <div className="text-xs text-muted-foreground">{subtitle}</div>
        )}
      </div>
    </div>
  )
}

interface CategoryBreakdownProps {
  statistics: ReturnType<typeof useAlertStore.getState>['statistics']
}

function CategoryBreakdown({ statistics }: CategoryBreakdownProps) {
  const categoryData = useMemo(() => {
    return Object.entries(statistics.byCategory).map(([type, count]) => ({
      type: type as AlertType,
      count,
      percentage: statistics.total > 0 ? (count / statistics.total) * 100 : 0
    })).sort((a, b) => b.count - a.count)
  }, [statistics])

  const priorityData = useMemo(() => {
    return Object.entries(statistics.byPriority).map(([priority, count]) => ({
      priority: priority as AlertPriority,
      count,
      percentage: statistics.total > 0 ? (count / statistics.total) * 100 : 0
    })).sort((a, b) => b.count - a.count)
  }, [statistics])

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Category Breakdown */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
          <BarChart3 className="w-5 h-5 text-primary" />
          By Category (Today)
        </h3>
        <div className="space-y-3">
          {categoryData.map(({ type, count, percentage }) => (
            <div key={type} className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className={cn("w-3 h-3 rounded-full", getAlertTypeColor(type).replace('text-', 'bg-'))} />
                <span className="text-sm font-medium text-foreground capitalize">
                  {type.toLowerCase()}
                </span>
              </div>
              <div className="flex items-center gap-3">
                <div className="text-right">
                  <div className="text-sm font-medium text-foreground">{count}</div>
                  <div className="text-xs text-muted-foreground">
                    {percentage.toFixed(1)}%
                  </div>
                </div>
                <div className="w-16 bg-card-interactive rounded-full h-2">
                  <div 
                    className={cn(
                      "h-2 rounded-full transition-all duration-300",
                      getAlertTypeColor(type).replace('text-', 'bg-')
                    )}
                    style={{ width: `${Math.min(percentage, 100)}%` }}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Priority Breakdown */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
          <AlertTriangle className="w-5 h-5 text-primary" />
          By Priority (Today)
        </h3>
        <div className="space-y-3">
          {priorityData.map(({ priority, count, percentage }) => (
            <div key={priority} className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className={cn(
                  "w-3 h-3 rounded-full",
                  getAlertPriorityColor(priority).split(' ')[0].replace('text-', 'bg-')
                )} />
                <span className="text-sm font-medium text-foreground capitalize">
                  {priority.toLowerCase()}
                </span>
              </div>
              <div className="flex items-center gap-3">
                <div className="text-right">
                  <div className="text-sm font-medium text-foreground">{count}</div>
                  <div className="text-xs text-muted-foreground">
                    {percentage.toFixed(1)}%
                  </div>
                </div>
                <div className="w-16 bg-card-interactive rounded-full h-2">
                  <div 
                    className={cn(
                      "h-2 rounded-full transition-all duration-300",
                      getAlertPriorityColor(priority).split(' ')[0].replace('text-', 'bg-')
                    )}
                    style={{ width: `${Math.min(percentage, 100)}%` }}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export function AlertStatistics() {
  const { statistics, alerts } = useAlertStore()
  
  // Calculate additional metrics
  const recentAlerts = useMemo(() => {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
    return alerts.filter(alert => new Date(alert.timestamp) >= oneHourAgo).length
  }, [alerts])
  
  const criticalUnread = useMemo(() => {
    return alerts.filter(alert => 
      !alert.read && alert.priority === AlertPriority.CRITICAL
    ).length
  }, [alerts])
  
  const actionableAlerts = useMemo(() => {
    return alerts.filter(alert => alert.actionable && !alert.read).length
  }, [alerts])
  
  const avgResponseTime = useMemo(() => {
    const readAlerts = alerts.filter(alert => alert.read)
    if (readAlerts.length === 0) return 0
    
    // Mock calculation - in real app this would be based on actual response times
    return Math.floor(Math.random() * 120) + 30 // 30-150 seconds
  }, [alerts])

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          title="Total Alerts"
          value={statistics.total.toLocaleString()}
          icon={Bell}
          color="text-primary"
          subtitle="All time"
        />
        <StatCard
          title="Today's Alerts"
          value={statistics.today}
          icon={Clock}
          color="text-blue-500"
          trend={{ value: 12, isPositive: true }}
          subtitle="Since midnight"
        />
        <StatCard
          title="Unread"
          value={statistics.unread}
          icon={AlertTriangle}
          color={statistics.unread > 0 ? "text-orange-500" : "text-green-500"}
          subtitle={criticalUnread > 0 ? `${criticalUnread} critical` : "All caught up"}
        />
        <StatCard
          title="Auto Exits"
          value={statistics.autoExits}
          icon={Target}
          color="text-green-500"
          subtitle="Today"
        />
      </div>
      
      {/* Secondary Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          title="Recent Activity"
          value={recentAlerts}
          icon={Activity}
          color="text-purple-500"
          subtitle="Last hour"
        />
        <StatCard
          title="Actionable"
          value={actionableAlerts}
          icon={CheckCircle}
          color={actionableAlerts > 0 ? "text-yellow-500" : "text-green-500"}
          subtitle="Require attention"
        />
        <StatCard
          title="Avg Response"
          value={`${avgResponseTime}s`}
          icon={TrendingUp}
          color="text-indigo-500"
          trend={{ value: 8, isPositive: false }}
          subtitle="To alerts"
        />
        <StatCard
          title="Critical Today"
          value={statistics.byPriority[AlertPriority.CRITICAL]}
          icon={AlertTriangle}
          color={statistics.byPriority[AlertPriority.CRITICAL] > 0 ? "text-red-500" : "text-green-500"}
          subtitle="High priority"
        />
      </div>

      {/* Category and Priority Breakdowns */}
      <CategoryBreakdown statistics={statistics} />
      
      {/* Performance Insights */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
          <TrendingUp className="w-5 h-5 text-primary" />
          Performance Insights
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
              Alert Volume
            </h4>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-foreground">Peak Hours</span>
                <span className="text-sm text-muted-foreground">9AM - 11AM EST</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-foreground">Quietest Period</span>
                <span className="text-sm text-muted-foreground">2AM - 6AM EST</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-foreground">Weekly Average</span>
                <span className="text-sm text-muted-foreground">{Math.floor(statistics.total / 7)} per day</span>
              </div>
            </div>
          </div>
          
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
              Response Quality
            </h4>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-foreground">Read Rate</span>
                <span className="text-sm text-muted-foreground">
                  {statistics.total > 0 ? (((statistics.total - statistics.unread) / statistics.total) * 100).toFixed(1) : 0}%
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-foreground">Action Rate</span>
                <span className="text-sm text-muted-foreground">
                  {actionableAlerts > 0 ? '67%' : '100%'}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-foreground">False Positive Rate</span>
                <span className="text-sm text-muted-foreground">2.3%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}