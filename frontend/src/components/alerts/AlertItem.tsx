'use client'

import { useState } from 'react'
import { Alert } from 'shared/src/types/alerts'
import { AlertType } from 'shared/src/types/enums'
import { 
  CheckCircle, 
  Circle, 
  X, 
  ExternalLink, 
  ChevronDown, 
  ChevronUp,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  Settings,
  Target,
  BarChart3
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { 
  getAlertPriorityColor, 
  getAlertTypeColor, 
  formatAlertTimestamp 
} from '@/utils/alertUtils'
import { useAlertStore } from '@/stores/alertStore'

interface AlertItemProps {
  alert: Alert
  selected?: boolean
  onToggleSelection?: (alertId: string) => void
  compact?: boolean
}

const getAlertTypeIcon = (type: AlertType) => {
  const iconMap = {
    [AlertType.TRADE]: DollarSign,
    [AlertType.EXIT]: TrendingUp,
    [AlertType.ERROR]: AlertTriangle,
    [AlertType.SYSTEM]: Settings,
    [AlertType.PRICE]: BarChart3,
    [AlertType.STRATEGY]: Target
  }
  return iconMap[type] || Settings
}

export function AlertItem({ 
  alert, 
  selected = false, 
  onToggleSelection, 
  compact = false 
}: AlertItemProps) {
  const [expanded, setExpanded] = useState(false)
  const { markAsRead, deleteAlert } = useAlertStore()
  
  const handleMarkAsRead = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (!alert.read) {
      markAsRead(alert.id)
    }
  }
  
  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation()
    deleteAlert(alert.id)
  }
  
  const handleToggleSelection = (e: React.MouseEvent) => {
    e.stopPropagation()
    onToggleSelection?.(alert.id)
  }
  
  const handleExpand = () => {
    setExpanded(!expanded)
    if (!alert.read) {
      markAsRead(alert.id)
    }
  }
  
  const priorityColors = getAlertPriorityColor(alert.priority)
  const typeColor = getAlertTypeColor(alert.type)
  const TypeIcon = getAlertTypeIcon(alert.type)
  
  const hasMetadata = alert.metadata && Object.keys(alert.metadata).length > 0
  const hasActions = alert.actionable || hasMetadata
  
  return (
    <div className={cn(
      "border-b border-border/30 hover:bg-card-interactive/30 transition-colors group",
      alert.read && "opacity-60"
    )}>
      <div className="flex items-center gap-3 p-4">
        {/* Status Indicator */}
        <div className={cn(
          "flex-shrink-0 w-2 h-8 rounded-full",
          alert.read ? "bg-border" : priorityColors.split(' ')[0].replace('text-', 'bg-')
        )} />
        
        {/* Alert Icon */}
        <div className={cn(
          "flex-shrink-0 w-8 h-8 rounded-lg flex items-center justify-center",
          getAlertTypeColor(alert.type).replace('text-', 'bg-') + '/10',
          getAlertTypeColor(alert.type)
        )}>
          <TypeIcon className="w-4 h-4" />
        </div>
        
        {/* Main Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between gap-2">
            <div className="flex-1 min-w-0">
              {/* Title and Time */}
              <div className="flex items-center gap-2 mb-1">
                <h3 className={cn(
                  "font-medium text-sm",
                  alert.read ? "text-muted-foreground" : "text-foreground"
                )}>
                  {alert.title}
                </h3>
                <span className="text-xs text-muted-foreground">
                  {formatAlertTimestamp(new Date(alert.timestamp))}
                </span>
              </div>
              
              {/* Message */}
              <p className={cn(
                "text-sm mb-2",
                alert.read ? "text-muted-foreground/80" : "text-muted-foreground"
              )}>
                {alert.message}
              </p>
              
              {/* Trading Details */}
              {hasMetadata && (
                <div className="flex items-center gap-2 text-xs">
                  {alert.metadata?.token?.symbol && (
                    <span className="font-medium text-primary">
                      {alert.metadata.token.symbol}
                    </span>
                  )}
                  {alert.metadata?.amount && (
                    <span className="text-muted-foreground">
                      {typeof alert.metadata.amount === 'number' 
                        ? alert.metadata.amount.toLocaleString()
                        : alert.metadata.amount
                      }
                    </span>
                  )}
                  {alert.metadata?.profit && (
                    <span className={cn(
                      "font-medium",
                      alert.metadata.profit > 0 ? "text-green-500" : "text-red-500"
                    )}>
                      {alert.metadata.profit > 0 ? '+' : ''}{alert.metadata.profit}%
                    </span>
                  )}
                </div>
              )}
            </div>
            
            {/* Quick Action */}
            {!alert.read && (
              <button
                onClick={handleMarkAsRead}
                className="text-muted-foreground hover:text-primary transition-colors p-1.5 rounded-md hover:bg-primary/10 opacity-0 group-hover:opacity-100"
                title="Mark as read"
              >
                <CheckCircle className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}