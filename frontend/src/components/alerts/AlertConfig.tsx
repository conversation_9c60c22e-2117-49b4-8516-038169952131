'use client'

import { useState } from 'react'
import { useAlertStore } from '@/stores/alertStore'
import { useSettingsStore } from '@/stores/settingsStore'
import { AlertConfig as AlertConfigType } from 'shared/src/types/alerts'
import { AlertType, AlertPriority } from 'shared/src/types/enums'
import { 
  Settings, 
  Bell, 
  BellOff, 
  Volume2, 
  VolumeX, 
  Mail, 
  Webhook,
  Monitor,
  Clock,
  Shield,
  Filter,
  Save,
  RotateCcw,
  CheckCircle,
  AlertTriangle,
  Lock,
  LockOpen
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { validateAlertConfig } from '@/utils/alertUtils'

interface ChannelConfigProps {
  channel: AlertConfigType['channels'][0]
  onUpdate: (channel: AlertConfigType['channels'][0]) => void
}

function ChannelConfig({ channel, onUpdate }: ChannelConfigProps) {
  const getChannelIcon = () => {
    switch (channel.type) {
      case 'desktop': return Monitor
      case 'sound': return Volume2
      case 'email': return Mail
      case 'webhook': return Webhook
      default: return Bell
    }
  }
  
  const getChannelLabel = () => {
    switch (channel.type) {
      case 'desktop': return 'Desktop Notifications'
      case 'sound': return 'Sound Alerts'
      case 'email': return 'Email Notifications'
      case 'webhook': return 'Webhook Integration'
      default: return channel.type
    }
  }
  
  const Icon = getChannelIcon()
  
  return (
    <div className="bg-card-interactive border rounded-lg p-4">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-3">
          <Icon className={cn(
            "w-5 h-5",
            channel.enabled ? "text-primary" : "text-muted-foreground"
          )} />
          <span className="font-medium text-foreground">{getChannelLabel()}</span>
        </div>
        <button
          onClick={() => onUpdate({ ...channel, enabled: !channel.enabled })}
          className={cn(
            "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
            channel.enabled ? "bg-primary" : "bg-muted"
          )}
        >
          <span
            className={cn(
              "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
              channel.enabled ? "translate-x-6" : "translate-x-1"
            )}
          />
        </button>
      </div>
      
      {/* Channel-specific configuration */}
      {channel.enabled && (
        <div className="space-y-3 pt-3 border-t border-border">
          {channel.type === 'sound' && (
            <div>
              <label className="block text-sm font-medium text-muted-foreground mb-2">
                Volume
              </label>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={channel.config.volume || 0.7}
                onChange={(e) => onUpdate({
                  ...channel,
                  config: { ...channel.config, volume: parseFloat(e.target.value) }
                })}
                className="w-full h-2 bg-card-elevated rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                <span>Mute</span>
                <span>{Math.round((channel.config.volume || 0.7) * 100)}%</span>
                <span>Max</span>
              </div>
            </div>
          )}
          
          {channel.type === 'email' && (
            <div>
              <label className="block text-sm font-medium text-muted-foreground mb-2">
                Email Address
              </label>
              <input
                type="email"
                placeholder="<EMAIL>"
                value={channel.config.email || ''}
                onChange={(e) => onUpdate({
                  ...channel,
                  config: { ...channel.config, email: e.target.value }
                })}
                className="w-full px-3 py-2 bg-card-elevated border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50"
              />
            </div>
          )}
          
          {channel.type === 'webhook' && (
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-muted-foreground mb-2">
                  Webhook URL
                </label>
                <input
                  type="url"
                  placeholder="https://your-webhook-url.com"
                  value={channel.config.url || ''}
                  onChange={(e) => onUpdate({
                    ...channel,
                    config: { ...channel.config, url: e.target.value }
                  })}
                  className="w-full px-3 py-2 bg-card-elevated border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-muted-foreground mb-2">
                  Secret Key (Optional)
                </label>
                <input
                  type="password"
                  placeholder="webhook-secret-key"
                  value={channel.config.secret || ''}
                  onChange={(e) => onUpdate({
                    ...channel,
                    config: { ...channel.config, secret: e.target.value }
                  })}
                  className="w-full px-3 py-2 bg-card-elevated border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50"
                />
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

interface QuickPresetProps {
  preset: 'minimal' | 'balanced' | 'everything'
  selected: boolean
  onSelect: () => void
}

function QuickPreset({ preset, selected, onSelect }: QuickPresetProps) {
  const presetConfig = {
    minimal: {
      title: 'Minimal',
      description: 'Only critical alerts',
      icon: Shield,
      color: 'text-red-500'
    },
    balanced: {
      title: 'Balanced',
      description: 'Important alerts only',
      icon: Filter,
      color: 'text-yellow-500'
    },
    everything: {
      title: 'Everything',
      description: 'All alert types',
      icon: Bell,
      color: 'text-blue-500'
    }
  }
  
  const config = presetConfig[preset]
  const Icon = config.icon
  
  return (
    <button
      onClick={onSelect}
      className={cn(
        "p-4 rounded-lg border transition-all text-left w-full",
        selected
          ? "bg-primary/10 border-primary text-primary"
          : "bg-card-interactive border-border text-muted-foreground hover:text-foreground hover:border-primary/50"
      )}
    >
      <div className="flex items-center gap-3 mb-2">
        <Icon className={cn("w-5 h-5", selected ? "text-primary" : config.color)} />
        <span className="font-medium">{config.title}</span>
        {selected && <CheckCircle className="w-4 h-4 ml-auto" />}
      </div>
      <p className="text-sm opacity-80">{config.description}</p>
    </button>
  )
}

export function AlertConfig() {
  const { config, updateConfig, setQuickPreset } = useAlertStore()
  const { isSettingLocked, toggleSettingLock } = useSettingsStore()
  const [localConfig, setLocalConfig] = useState<AlertConfigType>(config)
  const [errors, setErrors] = useState<string[]>([])
  const [saved, setSaved] = useState(false)
  
  const LockButton = ({ category, settingKey }: { category: string; settingKey: string }) => {
    const locked = isSettingLocked(category, settingKey)
    const IconComponent = locked ? Lock : LockOpen
    
    return (
      <button
        onClick={() => toggleSettingLock(category, settingKey)}
        className={cn(
          "p-1 rounded transition-colors",
          locked 
            ? "text-red-500 hover:bg-red-500/10" 
            : "text-muted-foreground hover:bg-muted"
        )}
        title={locked ? "Unlock setting" : "Lock setting"}
      >
        <IconComponent className="w-4 h-4" />
      </button>
    )
  }
  
  const handleSave = () => {
    const validationErrors = validateAlertConfig(localConfig)
    
    if (validationErrors.length > 0) {
      setErrors(validationErrors)
      return
    }
    
    setErrors([])
    updateConfig(localConfig)
    setSaved(true)
    setTimeout(() => setSaved(false), 2000)
  }
  
  const handleReset = () => {
    setLocalConfig(config)
    setErrors([])
  }
  
  const handleQuickPreset = (preset: 'minimal' | 'balanced' | 'everything') => {
    setQuickPreset(preset)
    // Refresh local config from store after preset is applied
    setTimeout(() => {
      setLocalConfig(useAlertStore.getState().config)
    }, 100)
  }
  
  const updateChannel = (index: number, updatedChannel: AlertConfigType['channels'][0]) => {
    const newChannels = [...localConfig.channels]
    newChannels[index] = updatedChannel
    setLocalConfig({ ...localConfig, channels: newChannels })
  }
  
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Settings className="w-6 h-6 text-primary" />
          <div>
            <h2 className="text-xl font-semibold text-foreground">Alert Configuration</h2>
            <p className="text-sm text-muted-foreground">
              Customize your alert preferences and notification channels
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={handleReset}
            className="px-3 py-2 text-muted-foreground hover:text-foreground transition-colors rounded-md hover:bg-card-interactive border border-border"
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            Reset
          </button>
          <button
            onClick={handleSave}
            className={cn(
              "px-4 py-2 rounded-md font-medium transition-colors flex items-center gap-2",
              saved
                ? "bg-green-500 text-white"
                : "bg-primary text-primary-foreground hover:bg-primary/90"
            )}
          >
            {saved ? (
              <>
                <CheckCircle className="w-4 h-4" />
                Saved
              </>
            ) : (
              <>
                <Save className="w-4 h-4" />
                Save Changes
              </>
            )}
          </button>
        </div>
      </div>
      
      {/* Errors */}
      {errors.length > 0 && (
        <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <AlertTriangle className="w-5 h-5 text-red-500" />
            <span className="font-medium text-red-500">Configuration Errors</span>
          </div>
          <ul className="text-sm text-red-500 space-y-1">
            {errors.map((error, index) => (
              <li key={index}>• {error}</li>
            ))}
          </ul>
        </div>
      )}
      
      {/* Master Toggle */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {localConfig.enabled ? (
              <Bell className="w-6 h-6 text-primary" />
            ) : (
              <BellOff className="w-6 h-6 text-muted-foreground" />
            )}
            <div>
              <h3 className="text-lg font-semibold text-foreground">Alert System</h3>
              <p className="text-sm text-muted-foreground">
                {localConfig.enabled ? 'Alerts are enabled' : 'All alerts are disabled'}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setLocalConfig({ ...localConfig, enabled: !localConfig.enabled })}
              disabled={isSettingLocked('alerts', 'enabled')}
              className={cn(
                "relative inline-flex h-8 w-14 items-center rounded-full transition-colors",
                isSettingLocked('alerts', 'enabled')
                  ? "opacity-50 cursor-not-allowed"
                  : localConfig.enabled ? "bg-primary" : "bg-muted"
              )}
            >
              <span
                className={cn(
                  "inline-block h-6 w-6 transform rounded-full bg-white transition-transform",
                  localConfig.enabled ? "translate-x-7" : "translate-x-1"
                )}
              />
            </button>
            <LockButton category="alerts" settingKey="enabled" />
          </div>
        </div>
      </div>
      
      {/* Quick Presets */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
          <Filter className="w-5 h-5 text-primary" />
          Quick Presets
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <QuickPreset
            preset="minimal"
            selected={localConfig.filters.minPriority === AlertPriority.HIGH}
            onSelect={() => handleQuickPreset('minimal')}
          />
          <QuickPreset
            preset="balanced"
            selected={localConfig.filters.minPriority === AlertPriority.MEDIUM}
            onSelect={() => handleQuickPreset('balanced')}
          />
          <QuickPreset
            preset="everything"
            selected={localConfig.filters.minPriority === AlertPriority.LOW}
            onSelect={() => handleQuickPreset('everything')}
          />
        </div>
      </div>
      
      {/* Notification Channels */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
          <Bell className="w-5 h-5 text-primary" />
          Notification Channels
        </h3>
        <div className="space-y-4">
          {localConfig.channels.map((channel, index) => (
            <ChannelConfig
              key={`${channel.type}-${index}`}
              channel={channel}
              onUpdate={(updatedChannel) => updateChannel(index, updatedChannel)}
            />
          ))}
        </div>
      </div>
      
      {/* Quiet Hours */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
            <Clock className="w-5 h-5 text-primary" />
            Quiet Hours
          </h3>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setLocalConfig({
                ...localConfig,
                quietHours: { ...localConfig.quietHours, enabled: !localConfig.quietHours.enabled }
              })}
              disabled={isSettingLocked('alerts', 'quietHours.enabled')}
              className={cn(
                "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
                isSettingLocked('alerts', 'quietHours.enabled')
                  ? "opacity-50 cursor-not-allowed"
                  : localConfig.quietHours.enabled ? "bg-primary" : "bg-muted"
              )}
            >
              <span
                className={cn(
                  "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                  localConfig.quietHours.enabled ? "translate-x-6" : "translate-x-1"
                )}
              />
            </button>
            <LockButton category="alerts" settingKey="quietHours.enabled" />
          </div>
        </div>
        
        {localConfig.quietHours.enabled && (
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Suppress non-critical alerts during specified hours
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-muted-foreground">
                    Start Time
                  </label>
                  <LockButton category="alerts" settingKey="quietHours.start" />
                </div>
                <input
                  type="time"
                  value={localConfig.quietHours.start}
                  onChange={(e) => setLocalConfig({
                    ...localConfig,
                    quietHours: { ...localConfig.quietHours, start: e.target.value }
                  })}
                  disabled={isSettingLocked('alerts', 'quietHours.start')}
                  className={cn(
                    "w-full px-3 py-2 bg-card-interactive border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50",
                    isSettingLocked('alerts', 'quietHours.start') && "opacity-50 cursor-not-allowed"
                  )}
                />
              </div>
              
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-muted-foreground">
                    End Time
                  </label>
                  <LockButton category="alerts" settingKey="quietHours.end" />
                </div>
                <input
                  type="time"
                  value={localConfig.quietHours.end}
                  onChange={(e) => setLocalConfig({
                    ...localConfig,
                    quietHours: { ...localConfig.quietHours, end: e.target.value }
                  })}
                  disabled={isSettingLocked('alerts', 'quietHours.end')}
                  className={cn(
                    "w-full px-3 py-2 bg-card-interactive border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50",
                    isSettingLocked('alerts', 'quietHours.end') && "opacity-50 cursor-not-allowed"
                  )}
                />
              </div>
            </div>
            
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={localConfig.quietHours.highPriorityOverride}
                onChange={(e) => setLocalConfig({
                  ...localConfig,
                  quietHours: { ...localConfig.quietHours, highPriorityOverride: e.target.checked }
                })}
                className="w-4 h-4 text-primary focus:ring-primary/50"
              />
              <span className="text-sm text-foreground">
                Allow high priority alerts to override quiet hours
              </span>
            </label>
          </div>
        )}
      </div>
      
      {/* Filter Settings */}
      <div className="bg-card-elevated border rounded-lg p-6">
        <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
          <Filter className="w-5 h-5 text-primary" />
          Alert Filters
        </h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-muted-foreground mb-3">
              Minimum Priority
            </label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {Object.values(AlertPriority).map((priority) => (
                <label key={priority} className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="minPriority"
                    value={priority}
                    checked={localConfig.filters.minPriority === priority}
                    onChange={(e) => setLocalConfig({
                      ...localConfig,
                      filters: { ...localConfig.filters, minPriority: e.target.value as AlertPriority }
                    })}
                    className="w-4 h-4 text-primary focus:ring-primary/50"
                  />
                  <span className="text-sm text-foreground capitalize">
                    {priority.toLowerCase()}
                  </span>
                </label>
              ))}
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-muted-foreground mb-3">
              Alert Categories
            </label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {Object.values(AlertType).map((type) => (
                <label key={type} className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={localConfig.filters.categories.includes(type)}
                    onChange={(e) => {
                      const categories = e.target.checked
                        ? [...localConfig.filters.categories, type]
                        : localConfig.filters.categories.filter(c => c !== type)
                      setLocalConfig({
                        ...localConfig,
                        filters: { ...localConfig.filters, categories }
                      })
                    }}
                    className="w-4 h-4 text-primary focus:ring-primary/50"
                  />
                  <span className="text-sm text-foreground capitalize">
                    {type.toLowerCase()}
                  </span>
                </label>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}