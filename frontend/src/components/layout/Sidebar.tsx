'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useState, useEffect } from 'react'
import { 
  LayoutGrid, 
  Target, 
  TrendingUp, 
  Zap, 
  Calendar, 
  Bell, 
  Settings,
  Menu,
  X,
  BarChart3
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface SidebarItem {
  id: string
  icon: React.ComponentType<{ className?: string }>
  label: string
  href: string
  tooltip: string
  color: string
}

const sidebarItems: SidebarItem[] = [
  {
    id: 'dashboard',
    icon: LayoutGrid,
    label: 'Dashboard',
    href: '/dashboard',
    tooltip: 'Mission Control Dashboard',
    color: 'text-white hover:text-gray-300'
  },
  {
    id: 'trading',
    icon: Target,
    label: 'Trading Center',
    href: '/',
    tooltip: 'Trading Command Center',
    color: 'text-green-500 hover:text-green-400'
  },
  {
    id: 'trades',
    icon: TrendingUp,
    label: 'Active Trades',
    href: '/trades',
    tooltip: 'Active Trades',
    color: 'text-blue-500 hover:text-blue-400'
  },
  {
    id: 'strategies',
    icon: Zap,
    label: 'Exit Strategies',
    href: '/exit-strategies',
    tooltip: 'Exit Strategies',
    color: 'text-orange-500 hover:text-orange-400'
  },
  {
    id: 'transactions',
    icon: BarChart3,
    label: 'Transaction Intelligence',
    href: '/transactions',
    tooltip: 'Transaction Intelligence Center',
    color: 'text-purple-500 hover:text-purple-400'
  },
  {
    id: 'history',
    icon: Calendar,
    label: 'History',
    href: '/history',
    tooltip: 'Transaction History',
    color: 'text-white hover:text-gray-300'
  },
  {
    id: 'alerts',
    icon: Bell,
    label: 'Alerts',
    href: '/alerts',
    tooltip: 'Alert Center',
    color: 'text-white hover:text-gray-300'
  },
  {
    id: 'settings',
    icon: Settings,
    label: 'Settings',
    href: '/settings',
    tooltip: 'Settings',
    color: 'text-white hover:text-gray-300'
  }
]

export function Sidebar() {
  const pathname = usePathname()
  const [isExpanded, setIsExpanded] = useState(false)

  // Load saved state from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('sidebarExpanded')
    if (saved !== null) {
      setIsExpanded(JSON.parse(saved))
    }
  }, [])

  // Save state to localStorage
  useEffect(() => {
    localStorage.setItem('sidebarExpanded', JSON.stringify(isExpanded))
  }, [isExpanded])

  const toggleSidebar = () => {
    setIsExpanded(!isExpanded)
  }

  return (
    <aside className={cn(
      "bg-gray-900 border-r border-gray-800 min-h-screen flex flex-col transition-all duration-300 ease-in-out",
      isExpanded ? "w-64" : "w-16"
    )}>
      {/* Toggle Button */}
      <div className="flex items-center justify-between p-4 border-b border-gray-800">
        {isExpanded && (
          <span className="text-white font-semibold text-lg">MemeTrader Pro</span>
        )}
        <button
          onClick={toggleSidebar}
          className="text-white hover:text-gray-300 transition-colors duration-200 p-1"
          aria-label={isExpanded ? "Collapse sidebar" : "Expand sidebar"}
        >
          {isExpanded ? (
            <X className="w-5 h-5" />
          ) : (
            <Menu className="w-5 h-5" />
          )}
        </button>
      </div>

      {/* Navigation */}
      <nav className="flex flex-col flex-1 py-6 px-3">
        <div className="space-y-2">
          {sidebarItems.map((item) => {
            const isActive = pathname === item.href
            const Icon = item.icon
            
            return (
              <Link
                key={item.id}
                href={item.href}
                className={cn(
                  "group relative flex items-center rounded-lg transition-all duration-200",
                  isExpanded ? "px-3 py-2.5 space-x-3" : "w-10 h-10 justify-center mx-auto",
                  isActive 
                    ? "bg-gray-800 shadow-lg" 
                    : "hover:bg-gray-800/50"
                )}
                title={!isExpanded ? item.tooltip : undefined}
              >
                <Icon className={cn(
                  "w-5 h-5 transition-all duration-200 flex-shrink-0",
                  isActive ? "text-white" : item.color
                )} />
                
                {isExpanded && (
                  <span className={cn(
                    "font-medium transition-colors duration-200 whitespace-nowrap",
                    isActive ? "text-white" : "text-gray-300 group-hover:text-white"
                  )}>
                    {item.label}
                  </span>
                )}

                {/* Tooltip for collapsed mode */}
                {!isExpanded && (
                  <div className="absolute left-full ml-3 px-2 py-1 bg-gray-800 border border-gray-700 rounded-md text-xs font-medium text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                    {item.tooltip}
                    <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-gray-800 border-l border-t border-gray-700 rotate-45" />
                  </div>
                )}

                {/* Active indicator */}
                {isActive && (
                  <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-6 bg-blue-500 rounded-r-full" />
                )}
              </Link>
            )
          })}
        </div>
      </nav>
    </aside>
  )
}
