'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useState, useEffect } from 'react'
import { useAlertStore } from '@/stores/alertStore'
import { 
  LayoutGrid, 
  Target, 
  TrendingUp, 
  Zap, 
  Calendar, 
  Bell, 
  Settings,
  Menu,
  X,
  BarChart3
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { SettingsTrigger } from '../settings/SettingsTrigger'
import { ClientOnly } from '../ClientOnly'

interface SidebarItem {
  id: string
  icon: React.ComponentType<{ className?: string }>
  label: string
  href: string
  tooltip: string
  color: string
}

const sidebarItems: SidebarItem[] = [
  {
    id: 'dashboard',
    icon: LayoutGrid,
    label: 'Dashboard',
    href: '/dashboard',
    tooltip: 'Mission Control Dashboard',
    color: 'text-white hover:text-gray-300'
  },
  {
    id: 'trading',
    icon: Target,
    label: 'Trading Center',
    href: '/',
    tooltip: 'Trading Command Center',
    color: 'text-green-500 hover:text-green-400'
  },
  {
    id: 'trades',
    icon: TrendingUp,
    label: 'Active Trades',
    href: '/trades',
    tooltip: 'Active Trades',
    color: 'text-blue-500 hover:text-blue-400'
  },
  {
    id: 'strategies',
    icon: Zap,
    label: 'Exit Strategies',
    href: '/exit-strategies',
    tooltip: 'Exit Strategies',
    color: 'text-orange-500 hover:text-orange-400'
  },
  {
    id: 'transactions',
    icon: BarChart3,
    label: 'Transaction Intelligence',
    href: '/transactions',
    tooltip: 'Transaction Intelligence Center',
    color: 'text-purple-500 hover:text-purple-400'
  },
  {
    id: 'alerts',
    icon: Bell,
    label: 'Alerts',
    href: '/alerts',
    tooltip: 'Alert Center',
    color: 'text-white hover:text-gray-300'
  }
]

function UnreadCount() {
  const { getUnreadCount } = useAlertStore()
  const unreadCount = getUnreadCount()
  
  if (unreadCount === 0) return null
  
  return (
    <span className="absolute -top-1 -right-1 w-3 h-3 bg-orange-500 text-white text-xs rounded-full flex items-center justify-center font-bold">
      {unreadCount > 9 ? '!' : unreadCount}
    </span>
  )
}

function UnreadCountExpanded() {
  const { getUnreadCount } = useAlertStore()
  const unreadCount = getUnreadCount()
  
  if (unreadCount === 0) return null
  
  return (
    <span className="ml-2 px-1.5 py-0.5 bg-orange-500 text-white text-xs rounded-full font-medium">
      {unreadCount}
    </span>
  )
}

export function Sidebar() {
  const pathname = usePathname()
  const [isExpanded, setIsExpanded] = useState(false)

  // Load saved state from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('sidebarExpanded')
    if (saved !== null) {
      setIsExpanded(JSON.parse(saved))
    }
  }, [])

  // Save state to localStorage
  useEffect(() => {
    localStorage.setItem('sidebarExpanded', JSON.stringify(isExpanded))
  }, [isExpanded])

  const toggleSidebar = () => {
    setIsExpanded(!isExpanded)
  }

  return (
    <aside className={cn(
      "bg-section-primary border-r border-elevated min-h-screen flex flex-col transition-all duration-300 ease-in-out relative shadow-xl",
      isExpanded ? "w-64" : "w-16"
    )}>
      {/* Background gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-section-primary via-section-secondary to-section-tertiary opacity-60"></div>
      <div className="relative z-10 flex flex-col min-h-screen">
        {/* Floating Toggle Button */}
        <button
          onClick={toggleSidebar}
          className="absolute top-4 right-3 z-20 text-foreground hover:text-primary transition-colors duration-200 p-1.5 rounded-md hover:bg-card-interactive border border-transparent hover:border-elevated"
          aria-label={isExpanded ? "Collapse sidebar" : "Expand sidebar"}
        >
          {isExpanded ? (
            <X className="w-4 h-4" />
          ) : (
            <Menu className="w-4 h-4" />
          )}
        </button>

        {/* Navigation */}
        <nav className="flex flex-col flex-1 pt-16 pb-6 px-3">
        <div className="space-y-2">
          {sidebarItems.map((item) => {
            const isActive = pathname === item.href
            const Icon = item.icon
            
            return (
              <Link
                key={item.id}
                href={item.href}
                className={cn(
                  "group relative flex items-center rounded-xl transition-all duration-200 border",
                  isExpanded ? "px-3 py-2.5 space-x-3" : "w-10 h-10 justify-center mx-auto",
                  isActive 
                    ? "bg-card-elevated border-elevated text-primary shadow-lg" 
                    : "bg-transparent border-transparent hover:bg-card-interactive hover:border-elevated text-muted-foreground hover:text-foreground"
                )}
                title={!isExpanded ? item.tooltip : undefined}
              >
                <div className="relative">
                  <Icon className={cn(
                    "w-5 h-5 transition-all duration-200 flex-shrink-0",
                    isActive ? "text-white" : item.color
                  )} />
                  {/* Alert badge for alerts menu item */}
                  {item.id === 'alerts' && (
                    <ClientOnly>
                      <UnreadCount />
                    </ClientOnly>
                  )}
                </div>
                
                {isExpanded && (
                  <div className="flex items-center justify-between flex-1">
                    <span className={cn(
                      "font-medium transition-colors duration-200 whitespace-nowrap",
                      isActive ? "text-white" : "text-gray-300 group-hover:text-white"
                    )}>
                      {item.label}
                    </span>
                    {/* Alert count badge when expanded */}
                    {item.id === 'alerts' && (
                      <ClientOnly>
                        <UnreadCountExpanded />
                      </ClientOnly>
                    )}
                  </div>
                )}

                {/* Tooltip for collapsed mode */}
                {!isExpanded && (
                  <div className="absolute left-full ml-3 px-2 py-1 bg-gray-800 border border-gray-700 rounded-md text-xs font-medium text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                    {item.tooltip}
                    <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-gray-800 border-l border-t border-gray-700 rotate-45" />
                  </div>
                )}

                {/* Active indicator */}
                {isActive && (
                  <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-6 bg-blue-500 rounded-r-full" />
                )}
              </Link>
            )
          })}
          
          {/* Settings Trigger */}
          <div className={cn(
            "mt-4 pt-4 border-t border-elevated",
            isExpanded ? "px-3" : "flex justify-center"
          )}>
            <SettingsTrigger 
              showLabel={isExpanded} 
              className={cn(
                "w-full transition-all duration-200",
                isExpanded ? "justify-start" : "justify-center"
              )}
              size={isExpanded ? "md" : "sm"}
            />
          </div>
        </div>
        </nav>
      </div>
    </aside>
  )
}
