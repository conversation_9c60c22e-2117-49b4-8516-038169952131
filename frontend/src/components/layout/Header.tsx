'use client'

import Link from 'next/link'
import { Settings } from 'lucide-react'
import { PortfolioHeader } from './PortfolioHeader'

export function Header() {
  return (
    <header className="border-b border-gray-800 bg-gray-900/95 backdrop-blur-sm sticky top-0 z-50">
      <div className="container mx-auto px-6 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-8">
            <Link href="/" className="text-xl font-bold bg-gradient-to-r from-green-400 to-cyan-400 bg-clip-text text-transparent hover:from-green-300 hover:to-cyan-300 transition-all">
              MemeTrader Pro
            </Link>
            
            {/* Portfolio Metrics */}
            <PortfolioHeader />
          </div>

          <div className="flex items-center space-x-4">
            <button className="text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-800/50">
              <Settings className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </header>
  )
}
