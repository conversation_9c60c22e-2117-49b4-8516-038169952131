'use client'

import Link from 'next/link'
import { PortfolioHeader } from './PortfolioHeader'
import { NotificationDropdown } from '../alerts/NotificationDropdown'
import { SettingsTrigger } from '../settings/SettingsTrigger'
import { ClientOnly } from '../ClientOnly'

export function Header() {
  return (
    <header className="border-b border-elevated bg-section-primary/80 backdrop-blur-md sticky top-0 z-50 shadow-lg">
      <div className="absolute inset-0 bg-gradient-to-r from-section-primary via-section-secondary to-section-primary opacity-50"></div>
      <div className="relative container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-8">
            <Link href="/" className="text-xl font-bold bg-gradient-to-r from-green-400 to-cyan-400 bg-clip-text text-transparent hover:from-green-300 hover:to-cyan-300 transition-all">
              MemeTrader Pro
            </Link>
            
            {/* Portfolio Metrics */}
            <div className="bg-card-elevated/50 border border-elevated rounded-xl px-4 py-2 backdrop-blur-sm">
              <PortfolioHeader />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <ClientOnly>
              <NotificationDropdown />
            </ClientOnly>
            <SettingsTrigger size="md" />
          </div>
        </div>
      </div>
    </header>
  )
}
