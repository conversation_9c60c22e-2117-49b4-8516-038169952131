'use client'

import { useState, useEffect } from 'react'
import { Badge } from '@/components/ui/badge'
import { formatCurrency } from '@/utils/trading'

interface PortfolioMetrics {
  totalValue: number
  dailyPnL: number
  activePositions: number
}

interface PortfolioHeaderProps {
  className?: string
}

export function PortfolioHeader({ className }: PortfolioHeaderProps) {
  const [portfolioMetrics, setPortfolioMetrics] = useState<PortfolioMetrics>({
    totalValue: 15420.85,
    dailyPnL: 2840.32,
    activePositions: 7
  })

  // Mock real-time updates - in production this would come from WebSocket or API
  useEffect(() => {
    const interval = setInterval(() => {
      setPortfolioMetrics(prev => ({
        ...prev,
        // Small random fluctuations to simulate real-time updates
        totalValue: prev.totalValue + (Math.random() - 0.5) * 0.1,
        dailyPnL: prev.dailyPnL + (Math.random() - 0.5) * 0.5
      }))
    }, 5000) // Update every 5 seconds

    return () => clearInterval(interval)
  }, [])

  return (
    <div className={`flex items-center space-x-6 ${className || ''}`}>
      {/* Total Portfolio Value */}
      <div className="text-white">
        <span className="text-sm text-gray-400">Total Value: </span>
        <span className="font-semibold">{formatCurrency(portfolioMetrics.totalValue, 2)}</span>
      </div>
      
      {/* 24h P&L */}
      <div className="text-white">
        <span className="text-sm text-gray-400">24h P&L: </span>
        <span className={`font-semibold ${portfolioMetrics.dailyPnL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
          {portfolioMetrics.dailyPnL >= 0 ? '+' : ''}{formatCurrency(portfolioMetrics.dailyPnL, 2)}
        </span>
      </div>
      
      {/* Active Positions */}
      <div className="flex items-center">
        <span className="text-sm text-gray-400 mr-2">Positions: </span>
        <Badge className="bg-green-500/20 text-green-400 border border-green-500/30 px-3 py-1 rounded-full">
          {portfolioMetrics.activePositions} Active
        </Badge>
      </div>
    </div>
  )
}