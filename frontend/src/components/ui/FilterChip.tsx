import { cn } from '@/lib/utils'

interface FilterChipProps {
  label: string
  count?: number
  active: boolean
  onClick: () => void
  showCount?: boolean
  className?: string
}

export function FilterChip({ 
  label, 
  count, 
  active, 
  onClick, 
  showCount = true,
  className 
}: FilterChipProps) {
  return (
    <button
      onClick={onClick}
      className={cn(
        "inline-flex items-center gap-1.5 px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-200",
        active 
          ? "bg-primary text-primary-foreground shadow-sm ring-2 ring-primary/20" 
          : "bg-muted/50 text-muted-foreground hover:bg-muted hover:text-foreground",
        className
      )}
    >
      <span>{label}</span>
      {showCount && count !== undefined && (
        <span className={cn(
          "text-xs",
          active ? "opacity-90" : "opacity-60"
        )}>
          ({count})
        </span>
      )}
    </button>
  )
}