'use client'

import Link from 'next/link'
import { useState, useRef, useEffect } from 'react'
import { SwapInterface } from '@/components/trading/SwapInterface'
import { FeatureShowcase } from '@/components/trading/FeatureShowcase'
import { Sidebar } from '@/components/layout/Sidebar'
import { Header } from '@/components/layout/Header'
import { ClientOnly } from '@/components/ClientOnly'
import { type RecentToken, loadCustomTokenNamesFromStorage, getCustomTokenName, type CustomTokenName } from '@/lib/utils'

export default function HomePage() {
  const swapInterfaceRef = useRef<{ setFromToken: (token: any) => void; setToToken: (token: any) => void } | null>(null)
  const [customTokenNames, setCustomTokenNames] = useState<Record<string, CustomTokenName>>({})

  // Load custom token names on mount and listen for changes
  useEffect(() => {
    const loadNames = () => {
      setCustomTokenNames(loadCustomTokenNamesFromStorage())
    }

    loadNames()

    // Listen for storage changes to sync across tabs/components
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'memetrader-custom-token-names') {
        loadNames()
      }
    }

    window.addEventListener('storage', handleStorageChange)

    // Also listen for custom events from the same tab
    const handleCustomNamesChange = () => {
      loadNames()
    }

    window.addEventListener('customTokenNamesChanged', handleCustomNamesChange)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
      window.removeEventListener('customTokenNamesChanged', handleCustomNamesChange)
    }
  }, [])

  // Handle token double-click from FeatureShowcase
  const handleTokenDoubleClick = (token: RecentToken, target: 'from' | 'to') => {
    if (swapInterfaceRef.current) {
      // Pass the original token data without modification
      // SwapInterface ref methods will handle custom name loading
      const convertedToken = {
        address: token.address,
        symbol: token.symbol,
        name: token.name,
        decimals: token.decimals,
        verified: token.verified
      }
      
      if (target === 'from') {
        swapInterfaceRef.current.setFromToken(convertedToken)
      } else {
        swapInterfaceRef.current.setToToken(convertedToken)
      }
    }
  }

  return (
    <div className="min-h-screen bg-background text-foreground">
      <Header />

      <div className="flex">
        <Sidebar />

        <main className="flex-1 p-6">
          <div className="max-w-7xl mx-auto space-y-6">
            {/* Wallet Status */}
            <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 rounded-full bg-muted" />
                    <span className="font-medium">Wallet Disconnected</span>
                  </div>
                </div>
                <button className="bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-md font-medium transition-colors">
                  Connect Wallet
                </button>
              </div>
            </div>

            {/* Main Trading Interface */}
            <div className="space-y-6">
              <div className="rounded-lg border bg-card text-card-foreground shadow-sm overflow-visible">
                <div className="flex flex-col space-y-1.5 p-6">
                  <h3 className="text-2xl font-semibold leading-none tracking-tight">
                    Trading Command Center
                  </h3>
                </div>
                <div className="p-6 pt-0 overflow-visible">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 overflow-visible">
                    {/* Enhanced Swap Interface with Integrated Trading Presets */}
                    <SwapInterface ref={swapInterfaceRef} />

                    {/* Feature Showcase */}
                    <FeatureShowcase onTokenDoubleClick={handleTokenDoubleClick} />
                  </div>
                </div>
              </div>

              {/* Status Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
                  <div className="p-4">
                    <div className="text-sm text-muted-foreground">Portfolio Value</div>
                    <div className="text-2xl font-bold">$0.00</div>
                  </div>
                </div>

                <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
                  <div className="p-4">
                    <div className="text-sm text-muted-foreground">Active Positions</div>
                    <div className="text-2xl font-bold">0</div>
                  </div>
                </div>

                <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
                  <div className="p-4">
                    <div className="text-sm text-muted-foreground">24h PnL</div>
                    <div className="text-2xl font-bold text-muted-foreground">$0.00</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
