'use client'

import Link from 'next/link'
import { SwapInterface } from '@/components/trading/SwapInterface'
import { FeatureShowcase } from '@/components/trading/FeatureShowcase'
import { Sidebar } from '@/components/layout/Sidebar'
import { Header } from '@/components/layout/Header'

export default function HomePage() {
  return (
    <div className="min-h-screen bg-background text-foreground">
      <Header />

      <div className="flex">
        <Sidebar />

        <main className="flex-1 p-6">
          <div className="max-w-7xl mx-auto space-y-6">
            {/* Wallet Status */}
            <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 rounded-full bg-muted" />
                    <span className="font-medium">Wallet Disconnected</span>
                  </div>
                </div>
                <button className="bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-md font-medium transition-colors">
                  Connect Wallet
                </button>
              </div>
            </div>

            {/* Main Trading Interface */}
            <div className="space-y-6">
              <div className="rounded-lg border bg-card text-card-foreground shadow-sm overflow-visible">
                <div className="flex flex-col space-y-1.5 p-6">
                  <h3 className="text-2xl font-semibold leading-none tracking-tight">
                    Trading Command Center
                  </h3>
                </div>
                <div className="p-6 pt-0 overflow-visible">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 overflow-visible">
                    {/* Enhanced Swap Interface with Integrated Trading Presets */}
                    <SwapInterface />

                    {/* Feature Showcase */}
                    <FeatureShowcase />
                  </div>
                </div>
              </div>

              {/* Status Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
                  <div className="p-4">
                    <div className="text-sm text-muted-foreground">Portfolio Value</div>
                    <div className="text-2xl font-bold">$0.00</div>
                  </div>
                </div>

                <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
                  <div className="p-4">
                    <div className="text-sm text-muted-foreground">Active Positions</div>
                    <div className="text-2xl font-bold">0</div>
                  </div>
                </div>

                <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
                  <div className="p-4">
                    <div className="text-sm text-muted-foreground">24h PnL</div>
                    <div className="text-2xl font-bold text-muted-foreground">$0.00</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
