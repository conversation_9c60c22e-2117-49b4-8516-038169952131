import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { ExitStrategyProvider } from '@/stores/exitStrategyStore.tsx'

const inter = Inter({ subsets: ['latin'] })

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: '#00F5A0',
}

export const metadata: Metadata = {
  title: 'MemeTrader Pro',
  description: 'Advanced Solana meme coin trading platform with real-time execution and risk management',
  keywords: ['solana', 'trading', 'defi', 'meme-coins', 'jupiter', 'crypto'],
  authors: [{ name: 'MemeTrader Pro Team' }],
  icons: {
    icon: '/favicon.ico',
    apple: '/apple-touch-icon.png',
  },
  openGraph: {
    title: 'MemeTrader Pro',
    description: 'Advanced Solana meme coin trading platform',
    type: 'website',
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'MemeTrader Pro',
    description: 'Advanced Solana meme coin trading platform',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark">
      <body className={inter.className}>
        <ExitStrategyProvider>
          {children}
        </ExitStrategyProvider>
      </body>
    </html>
  )
}
