'use client'

import { PortfolioSummary } from '@/components/trading/PortfolioSummary'
import { LiveExposureMeter } from '@/components/trading/LiveExposureMeter'
import { PerformanceMetrics } from '@/components/dashboard/PerformanceMetrics'
import { GoalTracker } from '@/components/dashboard/GoalTracker'
import { RiskAssessment } from '@/components/dashboard/RiskAssessment'
import { Sidebar } from '@/components/layout/Sidebar'
import { Header } from '@/components/layout/Header'

// Mock data for dashboard components
const mockPositions = [
  {
    id: '1',
    token: { symbol: 'PEPE', name: '<PERSON><PERSON><PERSON>', address: '6GCwuFQr...' },
    amount: 1250000,
    entryPrice: 0.000012,
    currentPrice: 0.000018,
    entryValue: 395.00,
    currentValue: 578.45,
    pnlPercentage: 46.2,
    pnlDollar: 182.45,
    trailingStop: 0.000015,
    exitMilestones: [50, 100, 150, 200],
    completedExits: 1,
    moonBag: { percentage: 25, targetGain: 500, currentProgress: 46.2 },
    status: 'taking_profits' as const,
    progressToNextExit: { current: 46.2, next: 100 }
  },
  {
    id: '2',
    token: { symbol: 'BONK', name: 'Bonk', address: 'DezXAZ8z...' },
    amount: 2800000,
    entryPrice: 0.000014,
    currentPrice: 0.000017,
    entryValue: 386.00,
    currentValue: 475.32,
    pnlPercentage: 23.1,
    pnlDollar: 89.32,
    trailingStop: 0.000016,
    exitMilestones: [50, 100, 150, 200],
    completedExits: 0,
    moonBag: { percentage: 25, targetGain: 500, currentProgress: 23.1 },
    status: 'approaching_target' as const,
    progressToNextExit: { current: 23.1, next: 50 }
  },
  {
    id: '3',
    token: { symbol: 'DOGE', name: 'Dogecoin', address: 'So11111...' },
    amount: 850,
    entryPrice: 0.64,
    currentPrice: 0.59,
    entryValue: 586.00,
    currentValue: 540.33,
    pnlPercentage: -7.8,
    pnlDollar: -45.67,
    trailingStop: 0.54,
    exitMilestones: [50, 100, 150, 200],
    completedExits: 0,
    moonBag: { percentage: 25, targetGain: 500, currentProgress: -7.8 },
    status: 'trailing' as const,
    progressToNextExit: { current: -7.8, next: 50 }
  },
  {
    id: '4',
    token: { symbol: 'SHIB', name: 'Shiba Inu', address: 'CiKu2pn...' },
    amount: 28500000,
    entryPrice: 0.000022,
    currentPrice: 0.000022,
    entryValue: 615.00,
    currentValue: 627.89,
    pnlPercentage: 2.1,
    pnlDollar: 12.89,
    trailingStop: 0.000020,
    exitMilestones: [50, 100, 150, 200],
    completedExits: 0,
    moonBag: { percentage: 25, targetGain: 500, currentProgress: 2.1 },
    status: 'moon_bag' as const,
    progressToNextExit: { current: 2.1, next: 50 }
  }
]

const mockExposureData = {
  currentExposure: 9680,
  maximumLimit: 12000,
  capitalUsage: 80.7,
  activePositions: 4,
  availableCapital: 2320,
  positionsUnderLimit: 4
}

const mockPerformanceData = {
  totalPnL24h: 247.83,
  yesterdayChange: 1.8,
  winRate: 78.5,
  winRateChange: 5.3,
  avgHoldTime: 4.2,
  holdTimeChange: -0.5,
  totalTrades: 23,
  successfulTrades: 18
}

const mockGoalData = {
  targetAmount: 100000,
  currentAmount: 12840,
  dailyPnL: [
    { date: '2025-07-24', pnl: 156.23 },
    { date: '2025-07-25', pnl: 289.45 },
    { date: '2025-07-26', pnl: -45.67 },
    { date: '2025-07-27', pnl: 378.90 },
    { date: '2025-07-28', pnl: 198.76 },
    { date: '2025-07-29', pnl: 425.12 },
    { date: '2025-07-30', pnl: 247.83 }
  ],
  averageDaily7d: 235.80,
  averageDaily30d: 198.45,
  estimatedDaysConservative: 370,
  estimatedDaysOptimistic: 185,
  dailyAmountNeeded: 238.36,
  progressPercentage: 12.84
}

const mockRiskData = {
  riskScore: 'Medium',
  riskChange: -12.1,
  tradingLimit: 12000,
  limitUsage: 80.7,
  availableCapital: 2320,
  connectionStatus: 'Mainnet Ready',
  lastUpdate: '30s ago'
}

export default function DashboardPage() {
  return (
    <div className="min-h-screen bg-background text-foreground">
      <Header />
      
      <div className="flex">
        <Sidebar />
        
        <main className="flex-1 p-6">
          <div className="max-w-7xl mx-auto space-y-8">
            {/* Page Header */}
            <div className="mb-8">
              <h1 className="text-4xl font-bold text-foreground mb-2">Mission Control</h1>
              <p className="text-lg text-muted-foreground">
                Real-time portfolio management and trading oversight
              </p>
            </div>

            {/* Portfolio Overview & Performance Metrics Row */}
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
              {/* Portfolio Overview Section */}
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold text-foreground mb-4">Portfolio Overview</h2>
                  
                  {/* Portfolio Value Display */}
                  <div className="bg-gradient-to-br from-card/80 to-card/40 backdrop-blur-sm border border-border/50 rounded-2xl shadow-xl p-6 mb-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <div className="text-sm text-muted-foreground mb-2">Current Portfolio Value</div>
                        <div className="text-4xl font-bold text-foreground">$12,840.00</div>
                        <div className="flex items-center gap-2 mt-2">
                          <div className="text-lg font-semibold text-green-400">+2.4%</div>
                          <div className="text-sm text-muted-foreground">today</div>
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-muted-foreground mb-2">vs Yesterday</div>
                        <div className="text-2xl font-bold text-green-400">+$298.50</div>
                        <div className="text-sm text-muted-foreground mt-2">Daily change</div>
                      </div>
                    </div>
                    
                    {/* Capital Allocation Breakdown */}
                    <div className="grid grid-cols-3 gap-4 mt-6 pt-6 border-t border-border/30">
                      <div className="text-center">
                        <div className="text-sm text-muted-foreground mb-1">Current Allocation</div>
                        <div className="text-xl font-bold text-foreground">$9,680</div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm text-muted-foreground mb-1">Capital Usage</div>
                        <div className="text-xl font-bold text-foreground">$0</div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm text-muted-foreground mb-1">Available Capital</div>
                        <div className="text-xl font-bold text-green-400">$3,160.00</div>
                        <div className="text-xs text-muted-foreground">24.6% available</div>
                      </div>
                    </div>
                    
                    {/* Active Positions Counter */}
                    <div className="mt-6 pt-6 border-t border-border/30">
                      <div className="flex items-center justify-between">
                        <div className="text-sm text-muted-foreground">Active Positions</div>
                        <div className="text-2xl font-bold text-foreground">4 positions open</div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <PortfolioSummary positions={mockPositions} />
              </div>

              {/* Performance Metrics & Risk Assessment Column */}
              <div className="space-y-6">
                <PerformanceMetrics data={mockPerformanceData} />
                <RiskAssessment data={mockRiskData} />
              </div>
            </div>

            {/* Exposure Meter & Goal Tracker Row */}
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
              <LiveExposureMeter data={mockExposureData} />
              <GoalTracker data={mockGoalData} />
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}