'use client'

import { useEffect, useState } from 'react'
import { PortfolioSummary } from '@/components/trading/PortfolioSummary'
import { LiveExposureMeter } from '@/components/trading/LiveExposureMeter'
import { PerformanceMetrics } from '@/components/dashboard/PerformanceMetrics'
import { GoalTracker } from '@/components/dashboard/GoalTracker'
import { RiskAssessment } from '@/components/dashboard/RiskAssessment'
import { Sidebar } from '@/components/layout/Sidebar'
import { Header } from '@/components/layout/Header'
import { useDashboardStore } from '@/stores/dashboardStore'
import { RefreshCw, Activity, TrendingUp, AlertTriangle, Target } from 'lucide-react'

export default function DashboardPage() {
  const [isRefreshing, setIsRefreshing] = useState(false)
  
  const {
    positions,
    portfolioMetrics,
    performanceData,
    exposureData,
    riskAssessment,
    goalProgress,
    isLoading,
    lastUpdate,
    error,
    refreshDashboard
  } = useDashboardStore()

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(async () => {
      await refreshDashboard()
    }, 30000)
    
    return () => clearInterval(interval)
  }, [refreshDashboard])

  const handleRefresh = async () => {
    setIsRefreshing(true)
    await refreshDashboard()
    setIsRefreshing(false)
  }

  const formatLastUpdate = (date: Date | null) => {
    if (!date) return 'Never'
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
    
    if (diffInSeconds < 60) return `${diffInSeconds}s ago`
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    return `${Math.floor(diffInSeconds / 3600)}h ago`
  }
  return (
    <div className="min-h-screen bg-background text-foreground">
      <Header />
      
      <div className="flex">
        <Sidebar />
        
        <main className="flex-1 bg-elevated">
          {/* Main content background with subtle pattern */}
          <div className="min-h-screen bg-gradient-to-br from-background via-background-elevated to-background-section">
            <div className="max-w-7xl mx-auto p-6 space-y-8">
            {/* Enhanced Page Header with Status */}
            <div className="flex items-center justify-between mb-8">
              <div>
                <h1 className="text-4xl font-bold text-foreground mb-2">Mission Control</h1>
                <p className="text-lg text-muted-foreground">
                  Real-time portfolio management and trading oversight
                </p>
              </div>
              
              <div className="flex items-center gap-4">
                {/* Connection Status */}
                <div className="flex items-center gap-2 px-3 py-1.5 bg-green-500/10 border border-green-500/20 rounded-lg">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-sm text-green-400">{riskAssessment.connectionStatus}</span>
                </div>
                
                {/* Last Update */}
                <div className="text-sm text-muted-foreground">
                  Updated {formatLastUpdate(lastUpdate)}
                </div>
                
                {/* Refresh Button */}
                <button
                  onClick={handleRefresh}
                  disabled={isLoading || isRefreshing}
                  className="flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white rounded-lg transition-colors"
                >
                  <RefreshCw className={`w-4 h-4 ${(isLoading || isRefreshing) ? 'animate-spin' : ''}`} />
                  Refresh
                </button>
              </div>
            </div>

            {/* Alert Banner */}
            {(error || riskAssessment.alerts.length > 0) && (
              <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-6">
                <div className="flex items-center gap-2 mb-2">
                  <AlertTriangle className="w-5 h-5 text-red-400" />
                  <h3 className="font-semibold text-red-400">
                    {error ? 'System Alert' : 'Risk Alerts'}
                  </h3>
                </div>
                {error && <p className="text-sm text-red-300 mb-2">{error}</p>}
                {riskAssessment.alerts.map((alert, i) => (
                  <p key={i} className="text-sm text-red-300">{alert}</p>
                ))}
              </div>
            )}

            {/* Quick Stats Row */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
              <div className="interactive-container bg-info-section border-interactive rounded-xl p-4 shadow-lg">
                <div className="flex items-center gap-3">
                  <Activity className="w-8 h-8 text-blue-400" />
                  <div>
                    <div className="text-sm text-muted-foreground">Portfolio Value</div>
                    <div className="text-xl font-bold text-foreground">
                      ${portfolioMetrics.totalValue.toLocaleString()}
                    </div>
                    <div className={`text-xs ${portfolioMetrics.totalPnLPercentage >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                      {portfolioMetrics.totalPnLPercentage >= 0 ? '+' : ''}{portfolioMetrics.totalPnLPercentage.toFixed(2)}%
                    </div>
                  </div>
                </div>
              </div>
              
              <div className={`interactive-container border-interactive rounded-xl p-4 shadow-lg ${
                performanceData.totalPnL24h >= 0 ? 'bg-success-section' : 'bg-danger-section'
              }`}>
                <div className="flex items-center gap-3">
                  <TrendingUp className="w-8 h-8 text-green-400" />
                  <div>
                    <div className="text-sm text-muted-foreground">24h P&L</div>
                    <div className={`text-xl font-bold ${performanceData.totalPnL24h >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                      {performanceData.totalPnL24h >= 0 ? '+' : ''}${performanceData.totalPnL24h.toFixed(2)}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Win Rate: {performanceData.winRate.toFixed(1)}%
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="interactive-container bg-section-tertiary border-interactive rounded-xl p-4 shadow-lg">
                <div className="flex items-center gap-3">
                  <Target className="w-8 h-8 text-purple-400" />
                  <div>
                    <div className="text-sm text-muted-foreground">Active Positions</div>
                    <div className="text-xl font-bold text-foreground">{exposureData.activePositions}</div>
                    <div className="text-xs text-muted-foreground">
                      {exposureData.capitalUsage.toFixed(1)}% allocated
                    </div>
                  </div>
                </div>
              </div>
              
              <div className={`interactive-container border-interactive rounded-xl p-4 shadow-lg ${
                exposureData.riskLevel === 'CRITICAL' ? 'bg-danger-section' :
                exposureData.riskLevel === 'HIGH' ? 'bg-warning-section' :
                exposureData.riskLevel === 'MEDIUM' ? 'bg-info-section' :
                'bg-success-section'
              }`}>
                <div className="flex items-center gap-3">
                  <AlertTriangle className={`w-8 h-8 ${
                    exposureData.riskLevel === 'CRITICAL' ? 'text-red-400' :
                    exposureData.riskLevel === 'HIGH' ? 'text-orange-400' :
                    exposureData.riskLevel === 'MEDIUM' ? 'text-yellow-400' :
                    'text-green-400'
                  }`} />
                  <div>
                    <div className="text-sm text-muted-foreground">Risk Level</div>
                    <div className="text-xl font-bold text-foreground">{riskAssessment.riskScore}</div>
                    <div className="text-xs text-muted-foreground">
                      ${exposureData.availableCapital.toLocaleString()} available
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Main Dashboard Grid */}
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
              {/* Portfolio Overview Section */}
              <div className="space-y-6">
                <PortfolioSummary 
                  positions={positions} 
                  portfolioMetrics={portfolioMetrics}
                  performanceData={performanceData}
                />
              </div>

              {/* Performance Metrics & Risk Assessment Column */}
              <div className="space-y-6">
                <PerformanceMetrics data={performanceData} />
                <RiskAssessment data={riskAssessment} />
              </div>
            </div>

            {/* Exposure Meter & Goal Tracker Row */}
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
              <LiveExposureMeter data={exposureData} />
              <GoalTracker data={goalProgress} />
            </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}