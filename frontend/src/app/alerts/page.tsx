'use client'

import { useState, useEffect } from 'react'
import { AlertFeed } from '@/components/alerts/AlertFeed'
import { useAlertStore } from '@/stores/alertStore'
import { useMockAlertStream } from '@/hooks/useWebSocket'
import { Sidebar } from '@/components/layout/Sidebar'
import { Header } from '@/components/layout/Header'
import { 
  Bell, 
  Settings, 
  AlertTriangle,
  CheckCircle
} from 'lucide-react'
import { cn } from '@/lib/utils'

export default function AlertsPage() {
  const [showSettings, setShowSettings] = useState(false)
  
  const { 
    alerts, 
    getUnreadCount,
    generateMockAlerts, 
    markAllAsRead,
    isLoading,
    error 
  } = useAlertStore()
  
  // Mock stream for development
  const mockStream = useMockAlertStream()
  
  const unreadCount = getUnreadCount()
  
  // Generate mock data on first load if no alerts exist
  useEffect(() => {
    if (alerts.length === 0 && !isLoading && !error) {
      generateMockAlerts()
    }
  }, [alerts.length, generateMockAlerts, isLoading, error])

  return (
    <div className="min-h-screen bg-background text-foreground">
      <Header />
      
      <div className="flex">
        <Sidebar />
        
        <main className="flex-1 bg-elevated">
          <div className="min-h-screen bg-gradient-to-br from-background via-background-elevated to-background-section">
            <div className="max-w-7xl mx-auto p-6 space-y-6">
              
              {/* Simplified Header */}
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-4xl font-bold text-foreground mb-2">Notifications</h1>
                  <p className="text-lg text-muted-foreground">
                    Trading alerts and system messages
                  </p>
                </div>
                
                {unreadCount > 0 && (
                  <button
                    onClick={markAllAsRead}
                    className="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
                  >
                    <CheckCircle className="w-4 h-4" />
                    Mark All Read ({unreadCount})
                  </button>
                )}
              </div>

              {/* Alert Banner for unread alerts */}
              {unreadCount > 0 && (
                <div className="bg-orange-500/10 border border-orange-500/20 rounded-lg p-4">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="w-5 h-5 text-orange-400" />
                    <span className="font-medium text-orange-400">
                      {unreadCount} new notification{unreadCount === 1 ? '' : 's'} require your attention
                    </span>
                  </div>
                </div>
              )}

              {/* Main Alert Feed */}
              <div className="bg-card-elevated border rounded-xl shadow-lg">
                <div className="p-4 border-b border-border">
                  <div className="flex items-center justify-between">
                    <h2 className="text-xl font-semibold text-foreground">Recent Activity</h2>
                    <button
                      onClick={() => setShowSettings(!showSettings)}
                      className={cn(
                        "p-2 rounded-md transition-colors",
                        showSettings 
                          ? "bg-primary/20 text-primary" 
                          : "text-muted-foreground hover:text-foreground hover:bg-card-interactive"
                      )}
                      title="Notification settings"
                    >
                      <Settings className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                
                <AlertFeed
                  showFilters={false}
                  showBulkActions={false}
                  autoRefresh={true}
                  refreshInterval={30000}
                  maxHeight="h-[70vh]"
                  compact={true}
                />
              </div>

              {/* Simplified Settings Panel */}
              {showSettings && (
                <div className="bg-card-elevated border rounded-xl shadow-lg p-6">
                  <h3 className="text-lg font-semibold text-foreground mb-4">Notification Settings</h3>
                  <div className="space-y-4">
                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground mb-3">When to notify me</h4>
                      <div className="space-y-2">
                        <label className="flex items-center gap-3">
                          <input type="checkbox" defaultChecked className="w-4 h-4 text-primary focus:ring-primary/50" />
                          <span className="text-sm text-foreground">Trade executions and exits</span>
                        </label>
                        <label className="flex items-center gap-3">
                          <input type="checkbox" defaultChecked className="w-4 h-4 text-primary focus:ring-primary/50" />
                          <span className="text-sm text-foreground">Price target hits</span>
                        </label>
                        <label className="flex items-center gap-3">
                          <input type="checkbox" defaultChecked className="w-4 h-4 text-primary focus:ring-primary/50" />
                          <span className="text-sm text-foreground">Stop loss triggers</span>
                        </label>
                        <label className="flex items-center gap-3">
                          <input type="checkbox" className="w-4 h-4 text-primary focus:ring-primary/50" />
                          <span className="text-sm text-foreground">System maintenance</span>
                        </label>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground mb-3">How to notify me</h4>
                      <div className="space-y-2">
                        <label className="flex items-center gap-3">
                          <input type="checkbox" defaultChecked className="w-4 h-4 text-primary focus:ring-primary/50" />
                          <span className="text-sm text-foreground">Browser notifications</span>
                        </label>
                        <label className="flex items-center gap-3">
                          <input type="checkbox" defaultChecked className="w-4 h-4 text-primary focus:ring-primary/50" />
                          <span className="text-sm text-foreground">Sound alerts</span>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}