import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  console.log('🔄 Fetching active positions for real-time tracking')
  
  try {
    // Get auth token from request headers or localStorage fallback
    const authHeader = request.headers.get('authorization')
    const authToken = authHeader?.replace('Bearer ', '') || process.env.DEFAULT_AUTH_TOKEN || 'demo-token'
    
    // Call backend portfolio positions endpoint
    const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:3001'
    const response = await fetch(`${backendUrl}/api/portfolio/positions?status=ACTIVE&status=PARTIAL`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
    })

    if (!response.ok) {
      console.error(`❌ Backend positions fetch failed: ${response.status} ${response.statusText}`)
      // Return empty positions instead of failing completely
      return NextResponse.json({
        success: true,
        data: []
      })
    }

    const backendData = await response.json()
    console.log(`✅ Fetched ${backendData.data?.positions?.length || 0} active positions`)

    // Transform backend position data to frontend format
    const positions = (backendData.data?.positions || []).map((pos: any) => ({
      id: pos.id,
      tokenAddress: pos.tokenAddress,
      tokenSymbol: pos.tokenSymbol,
      tokenName: pos.tokenName || pos.tokenSymbol,
      entryPrice: parseFloat(pos.entryPrice?.toString() || '0'),
      currentPrice: pos.marketData?.price || parseFloat(pos.currentPrice?.toString() || '0'),
      quantity: parseFloat(pos.quantity?.toString() || '0'),
      entryTimestamp: pos.entryTimestamp,
      status: pos.status,
      pnl: parseFloat(pos.pnl?.toString() || '0'),
      pnlPercentage: parseFloat(pos.pnlPercent?.toString() || '0'),
      marketValue: parseFloat(pos.quantity?.toString() || '0') * (pos.marketData?.price || parseFloat(pos.currentPrice?.toString() || '0')),
      age: pos.age || (Date.now() - new Date(pos.entryTimestamp).getTime()),
      strategy: pos.strategy ? {
        id: pos.strategy.id,
        type: pos.strategy.type,
        executionState: pos.strategy.executionState
      } : undefined,
      lastTransaction: pos.transactions?.[0] ? {
        hash: pos.transactions[0].hash,
        timestamp: pos.transactions[0].timestamp
      } : undefined,
      priceData: pos.marketData ? {
        price: pos.marketData.price,
        change24h: pos.marketData.change24h || 0,
        volume24h: pos.marketData.volume24h || 0,
        timestamp: Date.now()
      } : undefined
    }))

    return NextResponse.json({
      success: true,
      data: positions
    })

  } catch (error) {
    console.error('❌ Error fetching active positions:', error)
    
    // Return demo positions for development/demo mode
    const demoPositions = [
      {
        id: 'pos_1',
        tokenAddress: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
        tokenSymbol: 'USDC',
        tokenName: 'USD Coin',
        entryPrice: 1.0,
        currentPrice: 1.001,
        quantity: 5000,
        entryTimestamp: new Date(Date.now() - 86400000 * 2).toISOString(), // 2 days ago
        status: 'ACTIVE',
        pnl: 5.0,
        pnlPercentage: 0.1,
        marketValue: 5005,
        age: 86400000 * 2,
        strategy: {
          id: 'strategy_1',
          type: 'TAKE_PROFIT',
          executionState: 'MONITORING'
        },
        priceData: {
          price: 1.001,
          change24h: 0.1,
          volume24h: 2450000,
          timestamp: Date.now()
        }
      },
      {
        id: 'pos_2',
        tokenAddress: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB', // USDT
        tokenSymbol: 'USDT',
        tokenName: 'Tether USD',
        entryPrice: 0.999,
        currentPrice: 1.0,
        quantity: 3000,
        entryTimestamp: new Date(Date.now() - 86400000 * 5).toISOString(), // 5 days ago
        status: 'ACTIVE',
        pnl: 3.0,
        pnlPercentage: 0.1,
        marketValue: 3000,
        age: 86400000 * 5,
        strategy: {
          id: 'strategy_2',
          type: 'TRAILING_STOP',
          executionState: 'ACTIVE'
        },
        priceData: {
          price: 1.0,
          change24h: 0.1,
          volume24h: 1850000,
          timestamp: Date.now()
        }
      },
      {
        id: 'pos_3',
        tokenAddress: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263', // BONK
        tokenSymbol: 'BONK',
        tokenName: 'Bonk',
        entryPrice: 0.000021,
        currentPrice: 0.000025,
        quantity: 50000000,
        entryTimestamp: new Date(Date.now() - 86400000 * 7).toISOString(), // 7 days ago
        status: 'ACTIVE',
        pnl: 200,
        pnlPercentage: 19.05,
        marketValue: 1250,
        age: 86400000 * 7,
        strategy: {
          id: 'strategy_3',
          type: 'MOON_BAG',
          executionState: 'MONITORING'
        },
        priceData: {
          price: 0.000025,
          change24h: 8.5,
          volume24h: 45000000,
          timestamp: Date.now()
        }
      },
      {
        id: 'pos_4',
        tokenAddress: 'A8C3xuqscfmyLrte3VmTqrAq8kgMASius9AFNANwpump', // PEPE
        tokenSymbol: 'PEPE',
        tokenName: 'Pepe',
        entryPrice: 0.00001234,
        currentPrice: 0.00001180,
        quantity: 8000000,
        entryTimestamp: new Date(Date.now() - 86400000 * 3).toISOString(), // 3 days ago
        status: 'PARTIAL',
        pnl: -43.2,
        pnlPercentage: -4.38,
        marketValue: 944,
        age: 86400000 * 3,
        strategy: {
          id: 'strategy_4',
          type: 'STOP_LOSS',
          executionState: 'TRIGGERED'
        },
        priceData: {
          price: 0.00001180,
          change24h: -2.5,
          volume24h: 28000000,
          timestamp: Date.now()
        }
      },
      {
        id: 'pos_5',
        tokenAddress: 'So11111111111111111111111111111111111111112', // SOL
        tokenSymbol: 'SOL',
        tokenName: 'Solana',
        entryPrice: 185.50,
        currentPrice: 198.75,
        quantity: 2.5,
        entryTimestamp: new Date(Date.now() - 86400000 * 14).toISOString(), // 14 days ago
        status: 'ACTIVE',
        pnl: 33.125,
        pnlPercentage: 7.14,
        marketValue: 496.875,
        age: 86400000 * 14,
        strategy: {
          id: 'strategy_5',
          type: 'LADDER',
          executionState: 'ACTIVE'
        },
        priceData: {
          price: 198.75,
          change24h: 3.2,
          volume24h: 1250000000,
          timestamp: Date.now()
        }
      },
      {
        id: 'pos_6',
        tokenAddress: 'EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm', // WIF
        tokenSymbol: 'WIF',
        tokenName: 'dogwifhat',
        entryPrice: 2.85,
        currentPrice: 3.12,
        quantity: 175,
        entryTimestamp: new Date(Date.now() - 86400000 * 10).toISOString(), // 10 days ago
        status: 'ACTIVE',
        pnl: 47.25,
        pnlPercentage: 9.47,
        marketValue: 546,
        age: 86400000 * 10,
        strategy: {
          id: 'strategy_6',
          type: 'COMPOSITE',
          executionState: 'MONITORING'
        },
        priceData: {
          price: 3.12,
          change24h: 5.8,
          volume24h: 35000000,
          timestamp: Date.now()
        }
      }
    ]
    
    console.log('🔄 Using demo positions data for development')
    return NextResponse.json({
      success: true,
      data: demoPositions
    })
  }
}