import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  console.log('📊 Fetching real-time portfolio overview')
  
  try {
    // Get auth token from request headers
    const authHeader = request.headers.get('authorization')
    const authToken = authHeader?.replace('Bearer ', '') || process.env.DEFAULT_AUTH_TOKEN || 'demo-token'
    
    const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:3001'
    
    // Fetch portfolio overview and summary in parallel
    const [overviewResponse, summaryResponse] = await Promise.all([
      fetch(`${backendUrl}/api/portfolio/overview`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`,
        },
      }),
      fetch(`${backendUrl}/api/portfolio/summary`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`,
        },
      })
    ])

    let portfolioData: any = {}
    let summaryData: any = {}

    // Handle overview response
    if (overviewResponse.ok) {
      const overview = await overviewResponse.json()
      portfolioData = overview.data || {}
      console.log('✅ Portfolio overview fetched successfully')
    } else {
      console.warn('⚠️ Portfolio overview fetch failed, using defaults')
    }

    // Handle summary response
    if (summaryResponse.ok) {
      const summary = await summaryResponse.json()
      summaryData = summary.data || {}
      console.log('✅ Portfolio summary fetched successfully')
    } else {
      console.warn('⚠️ Portfolio summary fetch failed, using defaults')
    }

    // Combine and format the data for real-time display
    const combinedData = {
      // Core metrics
      totalValue: summaryData.totalValue || portfolioData.overview?.totalValueUsd || 0,
      totalPnl: summaryData.totalPnL || portfolioData.overview?.totalPnlUsd || 0,
      totalPnlPercentage: summaryData.pnlPercentage || portfolioData.overview?.totalPnlPercent || 0,
      totalInvested: summaryData.totalInvested || 0,
      
      // Position metrics
      activePositions: summaryData.activePositions || 0,
      exposurePercentage: summaryData.exposurePercentage || portfolioData.overview?.exposurePercent || 0,
      
      // Risk metrics
      riskScore: portfolioData.overview?.riskScore || 0,
      maxDrawdown: portfolioData.overview?.maxDrawdown || 0,
      
      // Performance metrics
      winRate: portfolioData.performance?.winRate || 0,
      totalTrades: portfolioData.performance?.totalTrades || 0,
      profitFactor: portfolioData.performance?.profitFactor || 0,
      sharpeRatio: portfolioData.performance?.sharpeRatio || 0,
      
      // Risk breakdown
      riskBreakdown: portfolioData.riskBreakdown || {
        concentration: 0,
        correlationRisk: 0,
        liquidityRisk: 0,
        volatilityScore: 0
      },
      
      // Limits and thresholds
      maxExposureLimit: summaryData.maxExposureLimit || 50000,
      
      // Metadata
      lastUpdated: new Date().toISOString(),
      updateFrequency: 30000 // 30 seconds
    }

    return NextResponse.json({
      success: true,
      data: combinedData
    })

  } catch (error) {
    console.error('❌ Error fetching portfolio overview:', error)
    
    // Return default portfolio data for development
    return NextResponse.json({
      success: true,
      data: {
        totalValue: 0,
        totalPnl: 0,
        totalPnlPercentage: 0,
        totalInvested: 0,
        activePositions: 0,
        exposurePercentage: 0,
        riskScore: 0,
        maxDrawdown: 0,
        winRate: 0,
        totalTrades: 0,
        profitFactor: 0,
        sharpeRatio: 0,
        riskBreakdown: {
          concentration: 0,
          correlationRisk: 0,
          liquidityRisk: 0,
          volatilityScore: 0
        },
        maxExposureLimit: 50000,
        lastUpdated: new Date().toISOString(),
        updateFrequency: 30000
      }
    })
  }
}