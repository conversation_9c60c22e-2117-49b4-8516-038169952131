import { NextRequest, NextResponse } from 'next/server'
import { jupiter<PERSON>rice<PERSON>pi } from '@/lib/jupiterPriceApi'

export async function GET(request: NextRequest) {
  console.log('💰 Fetching real-time token prices')
  
  try {
    const { searchParams } = new URL(request.url)
    const tokens = searchParams.get('tokens')
    
    if (!tokens) {
      return NextResponse.json({
        success: false,
        error: 'No tokens specified'
      }, { status: 400 })
    }

    const tokenAddresses = tokens.split(',').filter(Boolean)
    console.log(`🔍 Getting prices for ${tokenAddresses.length} tokens:`, tokenAddresses)

    // Get prices from Jupiter API
    const priceData = await jupiterPriceApi.getPrices(tokenAddresses)
    
    // Transform the data for real-time usage
    const formattedPrices: Record<string, {
      address: string
      symbol: string
      price: number
      change24h: number
      timestamp: number
    }> = {}

    Object.entries(priceData).forEach(([address, data]) => {
      formattedPrices[address] = {
        address,
        symbol: data.mintSymbol || 'UNKNOWN',
        price: data.price,
        change24h: 0, // Jupiter API doesn't provide 24h change directly
        timestamp: Date.now()
      }
    })

    console.log(`✅ Retrieved prices for ${Object.keys(formattedPrices).length} tokens`)

    return NextResponse.json({
      success: true,
      data: {
        prices: formattedPrices,
        timestamp: Date.now(),
        source: 'jupiter',
        updateFrequency: 10000 // 10 seconds for real-time tracking
      }
    })

  } catch (error) {
    console.error('❌ Error fetching real-time prices:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  console.log('💰 Subscribing to real-time price updates')
  
  try {
    const body = await request.json()
    const { tokens, subscriptionId } = body
    
    if (!tokens || !Array.isArray(tokens)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid tokens array'
      }, { status: 400 })
    }

    console.log(`📈 Setting up price subscription for ${tokens.length} tokens:`, tokens)
    
    // Get initial prices for the subscription
    const priceData = await jupiterPriceApi.getPrices(tokens)
    
    // Format the subscription response
    const subscription = {
      subscriptionId: subscriptionId || `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      tokens,
      initialPrices: priceData,
      createdAt: new Date().toISOString(),
      updateInterval: 10000, // 10 seconds
      status: 'active'
    }

    return NextResponse.json({
      success: true,
      data: subscription
    })

  } catch (error) {
    console.error('❌ Error setting up price subscription:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}