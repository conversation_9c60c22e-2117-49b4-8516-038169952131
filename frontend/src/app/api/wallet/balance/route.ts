import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Get minValue query parameter
    const { searchParams } = new URL(request.url)
    const minValue = searchParams.get('minValue') || '0.01' // Reduced from 0.5 to 0.01 to show more tokens
    
    // Get auth token from request
    const authToken = request.headers.get('authorization')?.replace('Bearer ', '')
    
    if (!authToken) {
      return NextResponse.json(
        { success: false, error: 'Missing authorization token' },
        { status: 401 }
      )
    }

    // Proxy to backend API with comprehensive wallet balance
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:3001'
    const response = await fetch(`${backendUrl}/api/wallet/balance?minValue=${minValue}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json',
      }
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Backend wallet balance request failed:', response.status, errorText)
      
      return NextResponse.json(
        { success: false, error: `Backend request failed: ${response.status}` },
        { status: response.status }
      )
    }

    const data = await response.json()
    
    if (!data.success) {
      return NextResponse.json(
        { success: false, error: data.error || 'Backend request failed' },
        { status: 500 }
      )
    }

    // Ensure the data structure is compatible with frontend expectations
    const walletBalance = data.data
    
    return NextResponse.json({
      success: true,
      data: {
        solBalance: walletBalance.solBalance || 0,
        tokenBalances: walletBalance.tokenBalances || [],
        totalValueUSD: walletBalance.totalValueUSD || 0,
        lastUpdated: walletBalance.lastUpdated || Date.now(),
        // Legacy fields for compatibility
        address: process.env.WALLET_ADDRESS || '',
        balance: walletBalance.solBalance || 0,
        balanceLamports: (walletBalance.solBalance || 0) * 1e9,
        symbol: 'SOL'
      }
    })

  } catch (error) {
    console.error('Wallet balance proxy error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}