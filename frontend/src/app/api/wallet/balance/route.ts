import { NextRequest, NextResponse } from 'next/server'
import { Connection, PublicKey, Keypair } from '@solana/web3.js'
import bs58 from 'bs58'

// Force Node.js runtime for Solana operations
export const runtime = 'nodejs'

// Token metadata fetching function
async function fetchTokenMetadata(connection: Connection, mintAddress: string) {
  try {
    console.log(`🔍 Fetching metadata for token: ${mintAddress}`)
    
    // Try multiple sources for token metadata
    
    // 1. Try Helius Token Metadata API first
    const heliusApiKey = process.env.HELIUS_API_KEY
    if (heliusApiKey) {
      try {
        const heliusUrl = `https://mainnet.helius-rpc.com/?api-key=${helius<PERSON>piKey}`
        const response = await fetch(heliusUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            jsonrpc: '2.0',
            id: 1,
            method: 'getAsset',
            params: { id: mintAddress }
          })
        })
        
        if (response.ok) {
          const data = await response.json()
          if (data.result && data.result.content) {
            const content = data.result.content
            return {
              address: mintAddress,
              symbol: content.metadata?.symbol || 'UNKNOWN',
              name: content.metadata?.name || `Token ${mintAddress.substring(0, 12)}...`,
              decimals: data.result.token_info?.decimals || 6,
              logoURI: content.files?.[0]?.uri,
              verified: data.result.authorities?.length > 0
            }
          }
        }
      } catch (heliusError) {
        console.log(`⚠️ Helius metadata failed for ${mintAddress}:`, heliusError.message)
      }
    }
    
    // 2. Try Jupiter Token List API
    try {
      const jupiterResponse = await fetch('https://token.jup.ag/strict')
      if (jupiterResponse.ok) {
        const tokens = await jupiterResponse.json()
        const token = tokens.find((t: any) => t.address === mintAddress)
        if (token) {
          return {
            address: mintAddress,
            symbol: token.symbol,
            name: token.name,
            decimals: token.decimals,
            logoURI: token.logoURI,
            verified: true
          }
        }
      }
    } catch (jupiterError) {
      console.log(`⚠️ Jupiter token list failed for ${mintAddress}:`, jupiterError.message)
    }
    
    // 3. Try on-chain metadata (Metaplex)
    try {
      // This is a simplified approach - in a full implementation you'd use Metaplex SDK
      const metadataPDA = await PublicKey.findProgramAddress(
        [
          Buffer.from('metadata'),
          new PublicKey('metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s').toBuffer(),
          new PublicKey(mintAddress).toBuffer(),
        ],
        new PublicKey('metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s')
      )
      
      const metadataAccount = await connection.getAccountInfo(metadataPDA[0])
      if (metadataAccount) {
        // This would require proper Metaplex parsing, simplified for now
        console.log(`✅ Found on-chain metadata for ${mintAddress}`)
        return {
          address: mintAddress,
          symbol: 'ONCHAIN',
          name: `Token ${mintAddress.substring(0, 8)}...`,
          decimals: 6,
          verified: false
        }
      }
    } catch (metaplexError) {
      console.log(`⚠️ Metaplex metadata failed for ${mintAddress}:`, metaplexError.message)
    }
    
    console.log(`❌ No metadata found for ${mintAddress}`)
    return null
    
  } catch (error) {
    console.log(`❌ Error fetching metadata for ${mintAddress}:`, error.message)
    return null
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get minValue query parameter
    const { searchParams } = new URL(request.url)
    const minValue = parseFloat(searchParams.get('minValue') || '0.01')
    
    // Get auth token from request
    const authToken = request.headers.get('authorization')?.replace('Bearer ', '')
    
    if (!authToken) {
      return NextResponse.json(
        { success: false, error: 'Missing authorization token' },
        { status: 401 }
      )
    }

    console.log('🔗 Direct wallet balance fetch (backend fallback)...')

    // Try backend first, but fallback to direct implementation if backend is down
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:3001'
    const fullUrl = `${backendUrl}/api/wallet/balance?minValue=${minValue}`
    
    try {
      console.log('🔗 Trying backend first:', fullUrl)
      
      const response = await fetch(fullUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        timeout: 5000 // 5 second timeout
      })
      
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          console.log('✅ Backend response successful')
          return NextResponse.json(data)
        }
      }
      
      console.log('⚠️ Backend failed, using direct implementation...')
    } catch (backendError) {
      console.log('⚠️ Backend unavailable, using direct implementation:', backendError.message)
    }

    // Direct implementation when backend is unavailable
    const primaryRpcUrl = process.env.HELIUS_RPC_URL
    const fallbackRpcUrl = process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com'
    const walletPrivateKey = process.env.WALLET_PRIVATE_KEY
    const encryptedPrivateKey = process.env.ENCRYPTED_PRIVATE_KEY
    
    if (!walletPrivateKey && !encryptedPrivateKey) {
      return NextResponse.json(
        { success: false, error: 'Missing wallet private key (either WALLET_PRIVATE_KEY or ENCRYPTED_PRIVATE_KEY required)' },
        { status: 500 }
      )
    }

    // Load wallet - prioritize WALLET_PRIVATE_KEY, fallback to ENCRYPTED_PRIVATE_KEY
    let wallet: Keypair
    try {
      if (walletPrivateKey) {
        wallet = Keypair.fromSecretKey(bs58.decode(walletPrivateKey))
      } else if (encryptedPrivateKey) {
        // For now, treat ENCRYPTED_PRIVATE_KEY as a base58 encoded key
        // In a real implementation, you'd decrypt it with WALLET_PASSWORD
        wallet = Keypair.fromSecretKey(bs58.decode(encryptedPrivateKey))
      } else {
        throw new Error('No valid private key found')
      }
    } catch (keyError) {
      return NextResponse.json(
        { success: false, error: `Invalid private key format: ${keyError.message}` },
        { status: 500 }
      )
    }
    
    // Try primary RPC first, then fallback
    let connection: Connection
    
    try {
      connection = new Connection(primaryRpcUrl || fallbackRpcUrl, 'confirmed')
      
      // Get SOL balance
      const solBalanceLamports = await connection.getBalance(wallet.publicKey)
      const solBalance = solBalanceLamports / 1e9
      
      // Get token accounts
      const tokenAccounts = await connection.getParsedTokenAccountsByOwner(
        wallet.publicKey,
        { programId: new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') }
      )
      
      console.log(`📊 Found ${tokenAccounts.value.length} token accounts`)
      
      const tokenBalances = []
      
      for (const accountInfo of tokenAccounts.value) {
        const parsedInfo = accountInfo.account.data.parsed.info
        const mintAddress = parsedInfo.mint
        const balance = parsedInfo.tokenAmount.uiAmount
        const decimals = parsedInfo.tokenAmount.decimals
        
        if (balance === 0) continue // Skip empty accounts
        
        // Try to fetch token metadata
        let metadata = await fetchTokenMetadata(connection, mintAddress)
        
        // If no metadata found, create fallback metadata to ensure token is shown
        if (!metadata) {
          metadata = {
            address: mintAddress,
            symbol: 'UNKNOWN',
            name: `Token ${mintAddress.substring(0, 8)}...${mintAddress.substring(-4)}`,
            decimals: decimals,
            verified: false
          }
        }
        
        tokenBalances.push({
          address: mintAddress,
          symbol: metadata.symbol || 'UNKNOWN',
          name: metadata.name || `Token ${mintAddress.substring(0, 12)}...`,
          decimals: decimals,
          balance: parsedInfo.tokenAmount.amount,
          uiAmount: balance,
          valueUSD: 0, // No price data in direct mode
          logoURI: metadata.logoURI,
          verified: metadata.verified || false
        })
      }
      
      // Filter by USD value if needed (but since we don't have prices, show all with balance)
      const filteredTokens = tokenBalances.filter(token => {
        if (minValue < 0.1) return token.uiAmount > 0 // Show all tokens with balance for low thresholds
        return token.valueUSD >= minValue
      })
      
      console.log(`📊 Returning ${filteredTokens.length} tokens (${tokenBalances.length} total with balance)`)
      
      return NextResponse.json({
        success: true,
        data: {
          solBalance: solBalance,
          tokenBalances: filteredTokens,
          totalValueUSD: solBalance * 150, // Rough SOL price estimate
          lastUpdated: Date.now(),
          // Legacy fields for compatibility
          address: wallet.publicKey.toString(),
          balance: solBalance,
          balanceLamports: solBalanceLamports,
          symbol: 'SOL'
        }
      })
      
    } catch (primaryError) {
      console.warn('Primary RPC failed, trying fallback:', primaryError)
      throw primaryError
    }

  } catch (error) {
    console.error('❌ Wallet balance error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}