import { NextRequest, NextResponse } from 'next/server'
import { Connection, PublicKey, Keypair } from '@solana/web3.js'
import bs58 from 'bs58'

export async function GET(request: NextRequest) {
  try {
    // Get environment variables
    const primaryRpcUrl = process.env.HELIUS_RPC_URL
    const fallbackRpcUrl = process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com'
    const walletPrivateKey = process.env.WALLET_PRIVATE_KEY
    
    if (!walletPrivateKey) {
      return NextResponse.json(
        { success: false, error: 'Missing wallet private key' },
        { status: 500 }
      )
    }

    // Load wallet
    const wallet = Keypair.fromSecretKey(bs58.decode(walletPrivateKey))
    
    // Try primary RPC first, then fallback
    let connection: Connection
    let balance: number
    
    try {
      connection = new Connection(primaryRpcUrl || fallbackRpcUrl, 'confirmed')
      balance = await connection.getBalance(wallet.publicKey)
    } catch (primaryError) {
      console.warn('Primary RPC failed, trying fallback:', primaryError)
      connection = new Connection(fallbackRpcUrl, 'confirmed')
      balance = await connection.getBalance(wallet.publicKey)
    }
    
    const solBalance = balance / 1e9 // Convert lamports to SOL
    
    return NextResponse.json({
      success: true,
      data: {
        solBalance: solBalance,
        tokenBalances: [], // TODO: Add token balances if needed
        totalValueUSD: solBalance * 230, // Rough SOL price estimate
        lastUpdated: Date.now(),
        // Legacy fields for compatibility
        address: wallet.publicKey.toString(),
        balance: solBalance,
        balanceLamports: balance,
        symbol: 'SOL'
      }
    })

  } catch (error) {
    console.error('Wallet balance error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}