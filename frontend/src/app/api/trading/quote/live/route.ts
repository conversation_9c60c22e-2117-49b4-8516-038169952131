import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { tokenIn, tokenOut, amount, slippage } = await request.json()
    
    if (!tokenIn || !tokenOut || !amount) {
      return NextResponse.json(
        { success: false, error: 'Missing required parameters' },
        { status: 400 }
      )
    }

    // Call Jupiter API for live quote
    const slippageBps = Math.floor((slippage || 0.01) * 10000) // Convert to basis points
    const jupiterUrl = `https://quote-api.jup.ag/v6/quote?inputMint=${tokenIn}&outputMint=${tokenOut}&amount=${amount}&slippageBps=${slippageBps}`
    
    const response = await fetch(jupiterUrl)
    
    if (!response.ok) {
      throw new Error(`Jupiter API failed: ${response.status}`)
    }
    
    const data = await response.json()
    
    // Transform Jupiter response to match expected format
    const enhancedQuote = {
      ...data,
      priceImpactPct: parseFloat(data.priceImpactPct || '0'),
      fees: {
        total: data.platformFee ? parseFloat(data.platformFee.amount || '0') : 0
      },
      routePlan: data.routePlan || []
    }

    return NextResponse.json({
      success: true,
      data: enhancedQuote
    })

  } catch (error) {
    console.error('Trading quote error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}