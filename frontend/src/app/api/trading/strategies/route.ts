import { NextRequest, NextResponse } from 'next/server'

// Mock strategy storage (in production this would be in database)
const strategies = new Map()

export async function GET(request: NextRequest) {
  try {
    // Return default strategies
    const defaultStrategies = [
      {
        id: 'conservative',
        name: 'Conservative',
        description: 'Low risk strategy with small profit targets',
        usageCount: 5,
        winRate: 75,
        riskLevel: 'LOW' as const,
        stopLoss: { percentage: 10 },
        profitTargets: [{ target: 20, sellPercentage: 50 }, { target: 40, sellPercentage: 30 }],
        trailingStop: null,
        moonBag: { percentage: 20, targetGain: 100 }
      },
      {
        id: 'aggressive',
        name: 'Aggressive TP',
        description: 'Higher risk with larger profit targets',
        usageCount: 3,
        winRate: 60,
        riskLevel: 'HIGH' as const,
        stopLoss: { percentage: 15 },
        profitTargets: [{ target: 50, sellPercentage: 40 }, { target: 100, sellPercentage: 40 }],
        trailingStop: { percentage: 5 },
        moonBag: { percentage: 20, targetGain: 200 }
      }
    ]

    return NextResponse.json({
      success: true,
      data: defaultStrategies
    })

  } catch (error) {
    console.error('Get strategies error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { type, stopLoss, profitTargets, moonBag, customName } = await request.json()
    
    // Generate strategy ID
    const strategyId = `custom_${Date.now()}`
    
    const newStrategy = {
      id: strategyId,
      name: customName || 'Custom Strategy',
      description: 'User created strategy',
      usageCount: 0,
      winRate: 0,
      riskLevel: 'MEDIUM' as const,
      stopLoss,
      profitTargets,
      trailingStop: null,
      moonBag,
      type
    }
    
    // Store strategy (in production this would be saved to database)
    strategies.set(strategyId, newStrategy)

    return NextResponse.json({
      success: true,
      data: {
        strategyId,
        strategy: newStrategy
      }
    })

  } catch (error) {
    console.error('Create strategy error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}