import { NextRequest, NextResponse } from 'next/server'
import { Connection, Keypair, VersionedTransaction } from '@solana/web3.js'
import bs58 from 'bs58'
import { getSmartPriorityFee } from '@/lib/heliusPriorityFee'
import { SwapOptimizer } from '@/lib/swapOptimizer'

export async function POST(request: NextRequest) {
  console.log('🚀 Trading execute API called')
  
  try {
    const body = await request.json()
    console.log('📋 Request body:', body)
    
    const { tokenIn, tokenOut, amount, slippage, preset, strategyId, mevProtection } = body
    
    if (!tokenIn || !tokenOut || !amount) {
      console.error('❌ Missing required parameters:', { tokenIn, tokenOut, amount })
      return NextResponse.json(
        { success: false, error: 'Missing required parameters' },
        { status: 400 }
      )
    }

    // Get optimized swap configuration
    const swapRoute = SwapOptimizer.getOptimizedConfig(tokenIn, tokenOut)
    console.log('🎯 Swap optimization:', {
      route: `${swapRoute.tokenIn.symbol} → ${swapRoute.tokenOut.symbol}`,
      reasoning: swapRoute.reasoning,
      optimizedSlippage: swapRoute.config.slippageToleranceBps / 100,
      mevLevel: swapRoute.config.mevProtectionLevel
    })

    // Validate swap parameters
    const validation = SwapOptimizer.validateSwap(tokenIn, tokenOut, amount, slippage || 1)
    if (!validation.isValid) {
      console.warn('⚠️ Swap validation warnings:', validation.warnings)
    }
    if (validation.suggestions.length > 0) {
      console.log('💡 Swap suggestions:', validation.suggestions)
    }

    // Get environment variables
    const solanaRpcUrl = process.env.HELIUS_RPC_URL || process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com'
    const walletPrivateKey = process.env.WALLET_PRIVATE_KEY
    
    console.log('🔧 Environment check:', {
      hasRpcUrl: !!solanaRpcUrl,
      hasWalletKey: !!walletPrivateKey,
      rpcUrl: solanaRpcUrl?.substring(0, 50) + '...'
    })
    
    if (!walletPrivateKey) {
      console.error('❌ Wallet private key not configured')
      return NextResponse.json(
        { success: false, error: 'Wallet not configured' },
        { status: 500 }
      )
    }

    // Create connection and wallet with HTTP-only configuration  
    const connection = new Connection(solanaRpcUrl, {
      commitment: 'confirmed',
      wsEndpoint: undefined, // Disable WebSocket to avoid connection issues
      disableRetryOnRateLimit: false,
      confirmTransactionInitialTimeout: 60000,
    })
    const wallet = Keypair.fromSecretKey(bs58.decode(walletPrivateKey))

    // Use optimized parameters from SwapOptimizer
    const optimizedSlippageBps = Math.floor((slippage || (swapRoute.config.slippageToleranceBps / 100)) * 10000)
    const maxAccounts = swapRoute.config.maxAccounts
    const restrictIntermediateTokens = swapRoute.config.restrictIntermediateTokens
    const dynamicSlippage = swapRoute.config.dynamicSlippage
    
    const quoteUrl = `https://lite-api.jup.ag/swap/v1/quote?inputMint=${tokenIn}&outputMint=${tokenOut}&amount=${amount}&slippageBps=${optimizedSlippageBps}&restrictIntermediateTokens=${restrictIntermediateTokens}&dynamicSlippage=${dynamicSlippage}&maxAccounts=${maxAccounts}`
    
    console.log('📈 Getting Jupiter quote:', { tokenIn, tokenOut, amount, slippageBps: optimizedSlippageBps })
    
    const quoteResponse = await fetch(quoteUrl)
    if (!quoteResponse.ok) {
      const errorText = await quoteResponse.text()
      console.error('❌ Quote failed:', errorText)
      throw new Error(`Failed to get Jupiter quote: ${errorText}`)
    }
    
    const quoteData = await quoteResponse.json()
    console.log('✅ Quote received:', {
      inAmount: quoteData.inAmount,
      outAmount: quoteData.outAmount,
      priceImpact: quoteData.priceImpactPct !== undefined && quoteData.priceImpactPct !== null ? 
        parseFloat(quoteData.priceImpactPct) : 0
    })
    
    // Enhanced MEV protection using SwapOptimizer configuration
    const accountKeys = quoteData.routePlan?.map((route: any) => route.swapInfo.ammKey).slice(0, 5) || ['So11111111111111111111111111111111111111112']
    
    // Use optimized MEV protection settings
    const optimizedMevLevel = mevProtection?.level || swapRoute.config.mevProtectionLevel
    const basePriorityFee = swapRoute.config.priorityFeeLamports
    
    let urgencyLevel = 'medium'
    let maxFeeWillingness = basePriorityFee
    
    // Apply MEV protection if enabled or recommended by optimizer
    const mevEnabled = mevProtection?.enabled || SwapOptimizer.isOptimizedPair(tokenIn, tokenOut)
    if (mevEnabled) {
      console.log('🛡️ MEV protection enabled:', { 
        level: optimizedMevLevel,
        baseFee: basePriorityFee,
        reasoning: swapRoute.reasoning
      })
      
      // Adjust urgency and max fee based on optimized protection level
      switch (optimizedMevLevel) {
        case 'Maximum':
          urgencyLevel = 'critical'
          maxFeeWillingness = Math.max(basePriorityFee * 3, ********) // 3x base fee, min 0.03 SOL
          break
        case 'Enhanced':
          urgencyLevel = 'high'
          maxFeeWillingness = Math.max(basePriorityFee * 2, ********) // 2x base fee, min 0.015 SOL
          break
        case 'Standard':
        default:
          urgencyLevel = 'high'
          maxFeeWillingness = Math.max(basePriorityFee, 8000000) // Base fee, min 0.008 SOL
          break
      }
    }
    
    const priorityFeeRecommendation = await getSmartPriorityFee({
      accountKeys,
      urgency: urgencyLevel,
      maxFeeWillingness,
      rpcUrl: solanaRpcUrl
    })
    
    console.log('🎯 Smart priority fee calculated:', {
      recommendedFee: priorityFeeRecommendation.recommendedFee,
      priorityLevel: priorityFeeRecommendation.priorityLevel,
      reasoning: priorityFeeRecommendation.reasoning,
      confidence: priorityFeeRecommendation.confidence
    })
    
    // Get swap transaction from Jupiter using latest v1 API with intelligent priority fees
    const swapResponse = await fetch('https://lite-api.jup.ag/swap/v1/swap', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        quoteResponse: quoteData,
        userPublicKey: wallet.publicKey.toString(),
        wrapAndUnwrapSol: true,
        dynamicComputeUnitLimit: true,
        dynamicSlippage: true,
        prioritizationFeeLamports: {
          priorityLevelWithMaxLamports: {
            maxLamports: priorityFeeRecommendation.recommendedFee,
            global: false,
            priorityLevel: priorityFeeRecommendation.priorityLevel.toLowerCase()
          }
        },
        useSharedAccounts: true
      })
    })

    if (!swapResponse.ok) {
      throw new Error('Failed to get swap transaction')
    }

    const { swapTransaction } = await swapResponse.json()
    
    // Deserialize transaction
    const transactionBuf = Buffer.from(swapTransaction, 'base64')
    let transaction = VersionedTransaction.deserialize(transactionBuf)
    
    // MEV Protection: Simulate transaction before signing if enabled
    if (mevProtection?.simulateTransaction) {
      console.log('🔍 Simulating transaction for MEV protection...')
      
      try {
        // First sign the transaction for simulation
        transaction.sign([wallet])
        
        const simulation = await connection.simulateTransaction(transaction, {
          replaceRecentBlockhash: true,
          commitment: 'processed'
        })
        
        if (simulation.value.err) {
          console.error('❌ Transaction simulation failed:', simulation.value.err)
          throw new Error(`Transaction simulation failed: ${JSON.stringify(simulation.value.err)}`)
        }
        
        // Check for suspicious behavior that might indicate MEV
        const logs = simulation.value.logs || []
        const computeUnitsUsed = simulation.value.unitsConsumed || 0
        
        // Basic MEV detection heuristics
        const suspiciousPatterns = [
          'Program log: Error: custom program error:', // Failed inner instructions
          'insufficient lamports', // Suspicious balance issues
          'slippage tolerance exceeded' // Potential sandwich attack
        ]
        
        const hasSuspiciousActivity = suspiciousPatterns.some(pattern => 
          logs.some(log => log.includes(pattern))
        )
        
        if (hasSuspiciousActivity) {
          console.warn('⚠️ Suspicious MEV activity detected in simulation')
          if (mevProtection.level === 'Maximum') {
            throw new Error('Transaction blocked: Suspicious MEV activity detected')
          }
        }
        
        console.log('✅ Transaction simulation passed:', {
          computeUnitsUsed,
          logsCount: logs.length,
          suspiciousActivity: hasSuspiciousActivity
        })
        
        // Create a fresh transaction for actual sending (re-sign)
        const freshTransactionBuf = Buffer.from(swapTransaction, 'base64')
        const freshTransaction = VersionedTransaction.deserialize(freshTransactionBuf)
        freshTransaction.sign([wallet])
        
        // Use the fresh transaction for sending
        transaction = freshTransaction
        
      } catch (simError) {
        console.error('❌ Transaction simulation error:', simError)
        if (mevProtection.level === 'Maximum') {
          throw new Error(`MEV protection failed: ${simError instanceof Error ? simError.message : String(simError)}`)
        }
        // For lower protection levels, proceed but log the error
        console.warn('⚠️ Proceeding despite simulation error for lower protection level')
        transaction.sign([wallet])
      }
    } else {
      // Standard signing without simulation
      transaction.sign([wallet])
    }
    
    // Send transaction
    console.log('📡 Sending transaction...')
    const signature = await connection.sendTransaction(transaction, {
      skipPreflight: false,
      preflightCommitment: 'confirmed'
    })
    
    console.log('⏳ Waiting for confirmation:', signature)
    
    // Wait for confirmation using polling method instead of WebSocket
    let confirmed = false
    let attempts = 0
    const maxAttempts = 30 // 30 attempts with 2-second intervals = 60 seconds max
    
    while (!confirmed && attempts < maxAttempts) {
      try {
        const status = await connection.getSignatureStatus(signature)
        if (status.value?.confirmationStatus === 'confirmed' || status.value?.confirmationStatus === 'finalized') {
          if (status.value.err) {
            console.error('❌ Transaction failed:', status.value.err)
            throw new Error(`Transaction failed: ${status.value.err}`)
          }
          confirmed = true
          break
        }
      } catch (error) {
        console.log(`⏳ Confirmation attempt ${attempts + 1}/${maxAttempts}`)
      }
      
      attempts++
      if (!confirmed && attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 2000)) // Wait 2 seconds
      }
    }
    
    if (!confirmed) {
      throw new Error('Transaction confirmation timeout')
    }
    
    console.log('🎉 Transaction confirmed!')

    return NextResponse.json({
      success: true,
      data: {
        transactionHash: signature,
        inputAmount: quoteData.inAmount,
        outputAmount: quoteData.outAmount,
        priceImpact: quoteData.priceImpactPct !== undefined && quoteData.priceImpactPct !== null ? 
          parseFloat(quoteData.priceImpactPct) : 0,
        strategyId: strategyId,
        optimization: {
          route: `${swapRoute.tokenIn.symbol} → ${swapRoute.tokenOut.symbol}`,
          mevProtectionLevel: optimizedMevLevel,
          slippageUsed: optimizedSlippageBps / 100,
          priorityFeeUsed: priorityFeeRecommendation.recommendedFee,
          reasoning: swapRoute.reasoning,
          validationResults: validation,
          isOptimizedPair: SwapOptimizer.isOptimizedPair(tokenIn, tokenOut)
        }
      }
    })

  } catch (error) {
    console.error('Trade execution error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}