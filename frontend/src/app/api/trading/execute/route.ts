import { NextRequest, NextResponse } from 'next/server'
import { Connection, Keypair, VersionedTransaction } from '@solana/web3.js'
import bs58 from 'bs58'

export async function POST(request: NextRequest) {
  console.log('🚀 Trading execute API called')
  
  try {
    const body = await request.json()
    console.log('📋 Request body:', body)
    
    const { tokenIn, tokenOut, amount, slippage, preset, strategyId } = body
    
    if (!tokenIn || !tokenOut || !amount) {
      console.error('❌ Missing required parameters:', { tokenIn, tokenOut, amount })
      return NextResponse.json(
        { success: false, error: 'Missing required parameters' },
        { status: 400 }
      )
    }

    // Get environment variables
    const solanaRpcUrl = process.env.HELIUS_RPC_URL || process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com'
    const walletPrivateKey = process.env.WALLET_PRIVATE_KEY
    
    console.log('🔧 Environment check:', {
      hasRpcUrl: !!solanaRpcUrl,
      hasWalletKey: !!walletPrivateKey,
      rpcUrl: solanaRpcUrl?.substring(0, 50) + '...'
    })
    
    if (!walletPrivateKey) {
      console.error('❌ Wallet private key not configured')
      return NextResponse.json(
        { success: false, error: 'Wallet not configured' },
        { status: 500 }
      )
    }

    // Create connection and wallet with HTTP-only configuration  
    const connection = new Connection(solanaRpcUrl, {
      commitment: 'confirmed',
      wsEndpoint: undefined, // Disable WebSocket to avoid connection issues
      disableRetryOnRateLimit: false,
      confirmTransactionInitialTimeout: 60000,
    })
    const wallet = Keypair.fromSecretKey(bs58.decode(walletPrivateKey))

    // Get Jupiter quote first
    const slippageBps = Math.floor((slippage || 0.01) * 10000)
    const quoteUrl = `https://quote-api.jup.ag/v6/quote?inputMint=${tokenIn}&outputMint=${tokenOut}&amount=${amount}&slippageBps=${slippageBps}`
    
    console.log('📈 Getting Jupiter quote:', { tokenIn, tokenOut, amount, slippageBps })
    
    const quoteResponse = await fetch(quoteUrl)
    if (!quoteResponse.ok) {
      const errorText = await quoteResponse.text()
      console.error('❌ Quote failed:', errorText)
      throw new Error(`Failed to get Jupiter quote: ${errorText}`)
    }
    
    const quoteData = await quoteResponse.json()
    console.log('✅ Quote received:', {
      inAmount: quoteData.inAmount,
      outAmount: quoteData.outAmount,
      priceImpact: quoteData.priceImpactPct
    })
    
    // Get swap transaction from Jupiter
    const swapResponse = await fetch('https://quote-api.jup.ag/v6/swap', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        quoteResponse: quoteData,
        userPublicKey: wallet.publicKey.toString(),
        wrapAndUnwrapSol: true,
        // Add priority fee if needed
        prioritizationFeeLamports: 'auto'
      })
    })

    if (!swapResponse.ok) {
      throw new Error('Failed to get swap transaction')
    }

    const { swapTransaction } = await swapResponse.json()
    
    // Deserialize and sign transaction
    const transactionBuf = Buffer.from(swapTransaction, 'base64')
    const transaction = VersionedTransaction.deserialize(transactionBuf)
    
    // Sign transaction
    transaction.sign([wallet])
    
    // Send transaction
    console.log('📡 Sending transaction...')
    const signature = await connection.sendTransaction(transaction, {
      skipPreflight: false,
      preflightCommitment: 'confirmed'
    })
    
    console.log('⏳ Waiting for confirmation:', signature)
    
    // Wait for confirmation using polling method instead of WebSocket
    let confirmed = false
    let attempts = 0
    const maxAttempts = 30 // 30 attempts with 2-second intervals = 60 seconds max
    
    while (!confirmed && attempts < maxAttempts) {
      try {
        const status = await connection.getSignatureStatus(signature)
        if (status.value?.confirmationStatus === 'confirmed' || status.value?.confirmationStatus === 'finalized') {
          if (status.value.err) {
            console.error('❌ Transaction failed:', status.value.err)
            throw new Error(`Transaction failed: ${status.value.err}`)
          }
          confirmed = true
          break
        }
      } catch (error) {
        console.log(`⏳ Confirmation attempt ${attempts + 1}/${maxAttempts}`)
      }
      
      attempts++
      if (!confirmed && attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 2000)) // Wait 2 seconds
      }
    }
    
    if (!confirmed) {
      throw new Error('Transaction confirmation timeout')
    }
    
    console.log('🎉 Transaction confirmed!')

    return NextResponse.json({
      success: true,
      data: {
        transactionHash: signature,
        inputAmount: quoteData.inAmount,
        outputAmount: quoteData.outAmount,
        priceImpact: parseFloat(quoteData.priceImpactPct || '0'),
        strategyId: strategyId
      }
    })

  } catch (error) {
    console.error('Trade execution error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}