import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Default priority fee estimate based on network conditions
    // This is a simplified version - in production this would call Helius API
    const priorityFeeEstimate = {
      priorityFeeEstimate: 1000, // lamports (0.000001 SOL)
      priorityFeeLevels: {
        min: 500,
        low: 1000,
        medium: 5000,
        high: 10000,
        veryHigh: 50000,
        unsafeMax: 100000
      },
      networkConditions: {
        congestionLevel: 'medium',
        avgPriorityFee: 1000,
        recommendedLevel: 'medium'
      }
    }

    return NextResponse.json(priorityFeeEstimate)
  } catch (error) {
    console.error('Failed to get priority fee estimate:', error)
    return NextResponse.json(
      { 
        error: 'Failed to get priority fee estimate',
        priorityFeeEstimate: 1000, // fallback value
        priorityFeeLevels: {
          min: 500,
          low: 1000,
          medium: 5000,
          high: 10000,
          veryHigh: 50000,
          unsafeMax: 100000
        }
      },
      { status: 500 }
    )
  }
}