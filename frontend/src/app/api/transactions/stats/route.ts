import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  console.log('📈 Fetching transaction statistics')
  
  try {
    // Get auth token from request headers
    const authHeader = request.headers.get('authorization')
    const authToken = authHeader?.replace('Bearer ', '') || process.env.DEFAULT_AUTH_TOKEN || 'demo-token'
    
    // Parse query parameters for date filtering
    const { searchParams } = new URL(request.url)
    const dateFrom = searchParams.get('dateFrom')
    const dateTo = searchParams.get('dateTo')
    
    const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:3001'
    const queryParams = new URLSearchParams()
    if (dateFrom) queryParams.set('dateFrom', dateFrom)
    if (dateTo) queryParams.set('dateTo', dateTo)
    
    console.log('📊 Stats query params:', { dateFrom, dateTo })

    // Call backend transaction stats endpoint
    const response = await fetch(`${backendUrl}/api/transactions/stats?${queryParams.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
    })

    if (!response.ok) {
      console.error(`❌ Backend transaction stats fetch failed: ${response.status} ${response.statusText}`)
      // Return default stats instead of failing completely
      return NextResponse.json({
        success: true,
        data: {
          summary: {
            totalTransactions: 0,
            totalVolume: 0,
            totalVolumeUsd: 0,
            totalFees: 0,
            totalFeesUsd: 0,
            avgTransactionSize: 0,
            avgExecutionTime: 0,
            successRate: 0,
            profitableTrades: 0,
            unprofitableTrades: 0,
            totalPnl: 0,
            totalPnlUsd: 0,
            winRate: 0
          },
          byTimeframe: {},
          byToken: [],
          byStrategy: [],
          byPreset: [],
          recentActivity: []
        }
      })
    }

    const backendData = await response.json()
    console.log('✅ Transaction stats fetched successfully')

    // Transform and enhance the stats data
    const statsData = backendData.data || {}
    
    // Ensure all required fields exist with defaults
    const enhancedStats = {
      summary: {
        totalTransactions: statsData.summary?.totalTransactions || 0,
        totalVolume: statsData.summary?.totalVolume || 0,
        totalVolumeUsd: statsData.summary?.totalVolumeUsd || 0,
        totalFees: statsData.summary?.totalFees || 0,
        totalFeesUsd: statsData.summary?.totalFeesUsd || 0,
        avgTransactionSize: statsData.summary?.avgTransactionSize || 0,
        avgExecutionTime: statsData.summary?.avgExecutionTime || 0,
        successRate: statsData.summary?.successRate || 0,
        profitableTrades: statsData.summary?.profitableTrades || 0,
        unprofitableTrades: statsData.summary?.unprofitableTrades || 0,
        totalPnl: statsData.summary?.totalPnl || 0,
        totalPnlUsd: statsData.summary?.totalPnlUsd || 0,
        winRate: statsData.summary?.winRate || 0,
        mevProtectedCount: statsData.summary?.mevProtectedCount || 0,
        averageSlippage: statsData.summary?.averageSlippage || 0,
        bestTrade: statsData.summary?.bestTrade || null,
        worstTrade: statsData.summary?.worstTrade || null
      },
      byTimeframe: statsData.byTimeframe || {},
      byToken: (statsData.byToken || []).map((token: any) => ({
        tokenSymbol: token.tokenSymbol,
        transactionCount: token.transactionCount || 0,
        volume: token.volume || 0,
        volumeUsd: token.volumeUsd || 0,
        pnl: token.pnl || 0,
        pnlUsd: token.pnlUsd || 0,
        winRate: token.winRate || 0,
        avgTradeSize: token.avgTradeSize || 0
      })),
      byStrategy: (statsData.byStrategy || []).map((strategy: any) => ({
        strategyId: strategy.strategyId,
        strategyName: strategy.strategyName || 'Unknown',
        transactionCount: strategy.transactionCount || 0,
        successRate: strategy.successRate || 0,
        pnl: strategy.pnl || 0,
        pnlUsd: strategy.pnlUsd || 0,
        winRate: strategy.winRate || 0
      })),
      byPreset: (statsData.byPreset || []).map((preset: any) => ({
        presetName: preset.presetName,
        transactionCount: preset.transactionCount || 0,
        successRate: preset.successRate || 0,
        avgExecutionTime: preset.avgExecutionTime || 0,
        avgFees: preset.avgFees || 0,
        mevProtectedPercentage: preset.mevProtectedPercentage || 0
      })),
      recentActivity: (statsData.recentActivity || []).map((activity: any) => ({
        date: activity.date,
        transactionCount: activity.transactionCount || 0,
        volume: activity.volume || 0,
        pnl: activity.pnl || 0,
        fees: activity.fees || 0
      })),
      metadata: {
        dateFrom: dateFrom || null,
        dateTo: dateTo || null,
        generatedAt: new Date().toISOString(),
        source: 'backend'
      }
    }

    return NextResponse.json({
      success: true,
      data: enhancedStats
    })

  } catch (error) {
    console.error('❌ Error fetching transaction stats:', error)
    
    // Return default stats for development/demo mode
    return NextResponse.json({
      success: true,
      data: {
        summary: {
          totalTransactions: 0,
          totalVolume: 0,
          totalVolumeUsd: 0,
          totalFees: 0,
          totalFeesUsd: 0,
          avgTransactionSize: 0,
          avgExecutionTime: 0,
          successRate: 0,
          profitableTrades: 0,
          unprofitableTrades: 0,
          totalPnl: 0,
          totalPnlUsd: 0,
          winRate: 0
        },
        byTimeframe: {},
        byToken: [],
        byStrategy: [],
        byPreset: [],
        recentActivity: [],
        metadata: {
          dateFrom: null,
          dateTo: null,
          generatedAt: new Date().toISOString(),
          source: 'fallback'
        }
      }
    })
  }
}