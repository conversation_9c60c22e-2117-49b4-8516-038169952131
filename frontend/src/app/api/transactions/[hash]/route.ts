import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest, { params }: { params: { hash: string } }) {
  console.log(`🔍 Fetching transaction details for hash: ${params.hash}`)
  
  try {
    // Get auth token from request headers
    const authHeader = request.headers.get('authorization')
    const authToken = authHeader?.replace('Bearer ', '') || process.env.DEFAULT_AUTH_TOKEN || 'demo-token'
    
    // Validate transaction hash format (Solana transaction hashes are typically 88 characters)
    if (!params.hash || params.hash.length < 80) {
      return NextResponse.json({
        success: false,
        error: 'Invalid transaction hash format'
      }, { status: 400 })
    }

    const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:3001'
    
    // Call backend transaction detail endpoint
    const response = await fetch(`${backendUrl}/api/transactions/${params.hash}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bear<PERSON> ${authToken}`,
      },
    })

    if (!response.ok) {
      if (response.status === 404) {
        return NextResponse.json({
          success: false,
          error: 'Transaction not found'
        }, { status: 404 })
      }
      
      console.error(`❌ Backend transaction detail fetch failed: ${response.status} ${response.statusText}`)
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch transaction details'
      }, { status: response.status })
    }

    const backendData = await response.json()
    console.log(`✅ Transaction details fetched for ${params.hash}`)

    // Transform backend transaction data to frontend format with additional details
    const transaction = backendData.data
    const enhancedTransaction = {
      id: transaction.id,
      userId: transaction.userId,
      positionId: transaction.positionId,
      hash: transaction.hash || transaction.transactionHash,
      type: transaction.type,
      status: transaction.status || 'CONFIRMED',
      
      // Token and amounts
      tokenIn: transaction.tokenIn,
      tokenOut: transaction.tokenOut,
      amountIn: parseFloat(transaction.amountIn?.toString() || '0'),
      amountOut: parseFloat(transaction.amountOut?.toString() || '0'),
      price: parseFloat(transaction.price?.toString() || '0'),
      
      // Fees breakdown
      fees: {
        jupiterFee: parseFloat(transaction.fees?.jupiterFee?.toString() || '0'),
        priorityFee: parseFloat(transaction.fees?.priorityFee?.toString() || '0'),
        networkFee: parseFloat(transaction.fees?.networkFee?.toString() || '0.000005'),
        total: parseFloat(transaction.fees?.total?.toString() || '0')
      },
      
      // Strategy and execution details
      strategyId: transaction.strategyId,
      presetUsed: transaction.presetUsed || 'DEFAULT',
      mevProtected: transaction.mevProtected || false,
      slippage: transaction.slippage || 0,
      priceImpact: transaction.priceImpact || 0,
      
      // Timing information
      timestamp: new Date(transaction.timestamp || transaction.createdAt),
      confirmationTime: transaction.confirmationTime ? new Date(transaction.confirmationTime) : undefined,
      executionTime: transaction.executionTime || 0,
      
      // Blockchain details
      blockNumber: transaction.blockNumber,
      blockHash: transaction.blockHash,
      gasUsed: transaction.gasUsed,
      effectiveGasPrice: transaction.effectiveGasPrice,
      
      // Additional metadata
      metadata: {
        jupiterRoute: transaction.metadata?.jupiterRoute,
        mevDetected: transaction.metadata?.mevDetected || false,
        simulationResults: transaction.metadata?.simulationResults,
        retryCount: transaction.metadata?.retryCount || 0,
        errorHistory: transaction.metadata?.errorHistory || [],
        performanceMetrics: {
          quoteLatency: transaction.metadata?.performanceMetrics?.quoteLatency,
          executionLatency: transaction.metadata?.performanceMetrics?.executionLatency,
          confirmationLatency: transaction.metadata?.performanceMetrics?.confirmationLatency
        }
      },
      
      // Related data
      position: transaction.position ? {
        id: transaction.position.id,
        tokenSymbol: transaction.position.tokenSymbol,
        status: transaction.position.status,
        entryPrice: parseFloat(transaction.position.entryPrice?.toString() || '0'),
        quantity: parseFloat(transaction.position.quantity?.toString() || '0')
      } : undefined,
      
      strategy: transaction.strategy ? {
        id: transaction.strategy.id,
        type: transaction.strategy.type,
        name: transaction.strategy.customName || transaction.strategy.type,
        executionState: transaction.strategy.executionState
      } : undefined
    }

    return NextResponse.json({
      success: true,
      data: enhancedTransaction
    })

  } catch (error) {
    console.error(`❌ Error fetching transaction ${params.hash}:`, error)
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error while fetching transaction details'
    }, { status: 500 })
  }
}