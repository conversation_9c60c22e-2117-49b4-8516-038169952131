import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  console.log('📊 Fetching transaction history')
  
  try {
    // Get auth token from request headers
    const authHeader = request.headers.get('authorization')
    const authToken = authHeader?.replace('Bearer ', '') || process.env.DEFAULT_AUTH_TOKEN || 'demo-token'
    
    // Parse query parameters for filtering
    const { searchParams } = new URL(request.url)
    const params = Object.fromEntries(searchParams.entries())
    
    console.log('🔍 Transaction query params:', params)

    const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:3001'
    const queryString = new URLSearchParams(params).toString()
    
    // Call backend transaction history endpoint
    const response = await fetch(`${backendUrl}/api/transactions?${queryString}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
    })

    if (!response.ok) {
      console.error(`❌ Backend transaction fetch failed: ${response.status} ${response.statusText}`)
      // Return empty transactions instead of failing completely
      return NextResponse.json({
        success: true,
        data: {
          transactions: [],
          pagination: {
            total: 0,
            limit: parseInt(params.limit || '50'),
            offset: parseInt(params.offset || '0'),
            hasMore: false
          }
        }
      })
    }

    const backendData = await response.json()
    console.log(`✅ Fetched ${backendData.data?.transactions?.length || 0} transactions`)

    // Transform backend transaction data to frontend format
    const transactions = (backendData.data?.transactions || []).map((tx: any) => ({
      id: tx.id,
      userId: tx.userId,
      positionId: tx.positionId,
      hash: tx.hash || tx.transactionHash,
      type: tx.type,
      tokenIn: tx.tokenIn,
      tokenOut: tx.tokenOut,
      amountIn: parseFloat(tx.amountIn?.toString() || '0'),
      amountOut: parseFloat(tx.amountOut?.toString() || '0'),
      price: parseFloat(tx.price?.toString() || '0'),
      fees: {
        jupiterFee: parseFloat(tx.fees?.jupiterFee?.toString() || '0'),
        priorityFee: parseFloat(tx.fees?.priorityFee?.toString() || '0'),
        networkFee: parseFloat(tx.fees?.networkFee?.toString() || '0.000005'),
        total: parseFloat(tx.fees?.total?.toString() || '0')
      },
      strategyId: tx.strategyId,
      presetUsed: tx.presetUsed || 'DEFAULT',
      mevProtected: tx.mevProtected || false,
      timestamp: new Date(tx.timestamp || tx.createdAt),
      status: tx.status || 'CONFIRMED',
      confirmationTime: tx.confirmationTime ? new Date(tx.confirmationTime) : undefined,
      blockNumber: tx.blockNumber,
      gasUsed: tx.gasUsed,
      effectiveGasPrice: tx.effectiveGasPrice,
      slippage: tx.slippage || 0,
      priceImpact: tx.priceImpact || 0,
      executionTime: tx.executionTime || 0
    }))

    return NextResponse.json({
      success: true,
      data: {
        transactions,
        pagination: backendData.data?.pagination || {
          total: transactions.length,
          limit: parseInt(params.limit || '50'),
          offset: parseInt(params.offset || '0'),
          hasMore: false
        },
        filters: params,
        timestamp: Date.now()
      }
    })

  } catch (error) {
    console.error('❌ Error fetching transactions:', error)
    
    // Return empty transactions for development/demo mode
    return NextResponse.json({
      success: true,
      data: {
        transactions: [],
        pagination: {
          total: 0,
          limit: 50,
          offset: 0,
          hasMore: false
        }
      }
    })
  }
}