import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { walletAddress } = await request.json()
    
    // Create a simple test token (not actual JWT for now)
    const payload = {
      id: 'test-user-id',
      email: '<EMAIL>',
      role: 'user',
      walletAddress: walletAddress || process.env.TRADING_WALLET_ADDRESS
    }
    
    // Generate a simple token
    const token = `test-token-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    
    return NextResponse.json({
      success: true,
      data: {
        token,
        user: payload
      }
    })

  } catch (error) {
    console.error('Token generation error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  // Generate a default token
  try {
    const payload = {
      id: 'test-user-id',
      email: '<EMAIL>', 
      role: 'user',
      walletAddress: process.env.TRADING_WALLET_ADDRESS
    }
    
    // Generate a simple token
    const token = `test-token-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    
    return NextResponse.json({
      success: true,
      data: {
        token,
        user: payload
      }
    })

  } catch (error) {
    console.error('Token generation error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}