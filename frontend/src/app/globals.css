@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Base backgrounds with enhanced depth */
    --background: 210 45% 1.5%;
    --background-elevated: 210 40% 3%;
    --background-section: 210 38% 4%;
    --background-card: 210 35% 6%;
    --foreground: 0 0% 96%;

    /* Card system with better hierarchy */
    --card: 210 35% 6%;
    --card-elevated: 210 32% 8%;
    --card-interactive: 210 30% 10%;
    --card-foreground: 0 0% 95%;

    /* Enhanced section backgrounds */
    --section-primary: 210 40% 3.5%;
    --section-secondary: 210 38% 4.5%;
    --section-tertiary: 210 35% 5.5%;

    --popover: 210 35% 6%;
    --popover-foreground: 0 0% 95%;

    --primary: 160 100% 48%;
    --primary-foreground: 210 40% 2%;

    --secondary: 195 100% 50%;
    --secondary-foreground: 210 40% 2%;

    /* Enhanced muted colors */
    --muted: 210 35% 12%;
    --muted-elevated: 210 32% 15%;
    --muted-foreground: 0 0% 65%;

    --accent: 210 35% 12%;
    --accent-foreground: 0 0% 95%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 95%;

    /* Enhanced borders with better visibility */
    --border: 210 35% 15%;
    --border-elevated: 210 30% 18%;
    --border-interactive: 210 25% 22%;
    --input: 210 35% 15%;
    --ring: 160 100% 48%;

    /* Enhanced shadows */
    --shadow-light: 210 50% 3%;
    --shadow-medium: 210 55% 2%;
    --shadow-heavy: 210 60% 1%;

    --radius: 0.5rem;
  }

  .dark {
    /* Base backgrounds with enhanced depth */
    --background: 210 45% 1.5%;
    --background-elevated: 210 40% 3%;
    --background-section: 210 38% 4%;
    --background-card: 210 35% 6%;
    --foreground: 0 0% 96%;

    /* Card system with better hierarchy */
    --card: 210 35% 6%;
    --card-elevated: 210 32% 8%;
    --card-interactive: 210 30% 10%;
    --card-foreground: 0 0% 95%;

    /* Enhanced section backgrounds */
    --section-primary: 210 40% 3.5%;
    --section-secondary: 210 38% 4.5%;
    --section-tertiary: 210 35% 5.5%;

    --popover: 210 35% 6%;
    --popover-foreground: 0 0% 95%;

    --primary: 160 100% 48%;
    --primary-foreground: 210 40% 2%;

    --secondary: 195 100% 50%;
    --secondary-foreground: 210 40% 2%;

    --muted: 210 40% 8%;
    --muted-foreground: 0 0% 60%;

    --accent: 210 40% 8%;
    --accent-foreground: 0 0% 95%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 95%;

    --border: 210 40% 12%;
    --input: 210 40% 12%;
    --ring: 160 100% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* Enhanced Background Utilities */
  .bg-section-primary { @apply bg-[hsl(var(--section-primary))]; }
  .bg-section-secondary { @apply bg-[hsl(var(--section-secondary))]; }
  .bg-section-tertiary { @apply bg-[hsl(var(--section-tertiary))]; }
  
  .bg-elevated { @apply bg-[hsl(var(--background-elevated))]; }
  .bg-card-elevated { @apply bg-[hsl(var(--card-elevated))]; }
  .bg-card-interactive { @apply bg-[hsl(var(--card-interactive))]; }
  
  .border-elevated { @apply border-[hsl(var(--border-elevated))]; }
  .border-interactive { @apply border-[hsl(var(--border-interactive))]; }
  
  /* Section containers with enhanced backgrounds */
  .section-container {
    @apply bg-section-primary border border-elevated rounded-2xl p-6 shadow-lg;
    background: linear-gradient(135deg, 
      hsl(var(--section-primary)) 0%, 
      hsl(var(--section-secondary)) 100%);
  }
  
  .card-container {
    @apply bg-card-elevated border border-elevated rounded-xl p-6 shadow-md;
    background: linear-gradient(145deg, 
      hsl(var(--card-elevated)) 0%, 
      hsl(var(--card)) 100%);
  }
  
  .interactive-container {
    @apply bg-card-interactive border border-interactive rounded-lg p-4 shadow-sm;
    transition: all 0.2s ease-in-out;
  }
  
  .interactive-container:hover {
    @apply bg-[hsl(var(--card-elevated))] border-[hsl(var(--border-interactive))];
    transform: translateY(-1px);
    box-shadow: 0 8px 25px -5px hsl(var(--shadow-medium));
  }
  
  /* Status-based backgrounds */
  .bg-success-section {
    background: linear-gradient(135deg, 
      hsl(142 76% 8%) 0%, 
      hsl(142 70% 6%) 100%);
    border: 1px solid hsl(142 76% 15%);
  }
  
  .bg-warning-section {
    background: linear-gradient(135deg, 
      hsl(48 96% 8%) 0%, 
      hsl(48 90% 6%) 100%);
    border: 1px solid hsl(48 96% 15%);
  }
  
  .bg-danger-section {
    background: linear-gradient(135deg, 
      hsl(0 84% 8%) 0%, 
      hsl(0 80% 6%) 100%);
    border: 1px solid hsl(0 84% 15%);
  }
  
  .bg-info-section {
    background: linear-gradient(135deg, 
      hsl(217 91% 8%) 0%, 
      hsl(217 85% 6%) 100%);
    border: 1px solid hsl(217 91% 15%);
  }

  /* Trading specific styles */
  .trading-card {
    @apply bg-card border border-border rounded-lg p-6 shadow-lg;
  }

  .profit-text {
    @apply text-profit font-semibold;
  }

  .loss-text {
    @apply text-loss font-semibold;
  }

  .neutral-text {
    @apply text-muted-foreground;
  }

  /* Button variants */
  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-md font-medium transition-colors;
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/90 px-4 py-2 rounded-md font-medium transition-colors;
  }

  .btn-destructive {
    @apply bg-destructive text-destructive-foreground hover:bg-destructive/90 px-4 py-2 rounded-md font-medium transition-colors;
  }

  /* Input styles */
  .input-field {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  /* Preset tab styles */
  .preset-tab {
    @apply px-4 py-2 rounded-md font-medium transition-all duration-200 border border-border;
  }

  .preset-tab.active {
    @apply bg-primary text-primary-foreground border-primary;
  }

  .preset-tab.inactive {
    @apply bg-muted text-muted-foreground hover:bg-muted/80;
  }

  /* Status indicators */
  .status-active {
    @apply bg-profit/20 text-profit border border-profit/30 px-2 py-1 rounded-full text-xs font-medium;
  }

  .status-closed {
    @apply bg-muted/20 text-muted-foreground border border-muted/30 px-2 py-1 rounded-full text-xs font-medium;
  }

  .status-pending {
    @apply bg-warning/20 text-warning border border-warning/30 px-2 py-1 rounded-full text-xs font-medium;
  }

  /* Animation utilities */
  .animate-price-up {
    @apply animate-pulse-green;
  }

  .animate-price-down {
    @apply animate-pulse-red;
  }

  /* Scrollbar styles */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted)) hsl(var(--background));
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: hsl(var(--background));
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: hsl(var(--muted));
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground));
  }
}

/* Font imports */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

/* Global styles */
html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(210 40% 1%) 100%);
  min-height: 100vh;
}

/* Selection styles */
::selection {
  background: hsl(var(--primary) / 0.3);
  color: hsl(var(--foreground));
}

/* Focus styles */
:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Enhanced animations for token selector */
@keyframes slideDown {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.95); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Token selector specific styles */
.token-dropdown {
  animation: slideDown 0.2s ease-out;
  z-index: 999999 !important; /* Force maximum z-index */
  position: fixed !important; /* Ensure fixed positioning */
}

/* Shadcn popover z-index override */
[data-radix-popper-content-wrapper] {
  z-index: 999999 !important;
}

.z-\[999999\] {
  z-index: 999999 !important;
}

.token-dropdown-backdrop {
  z-index: 999998 !important; /* One level below dropdown */
  position: fixed !important;
}

.token-item:hover {
  transform: translateX(2px);
  transition: transform 0.15s ease-out;
}

/* Enhanced input focus styles */
.enhanced-input:focus {
  box-shadow: 0 0 0 2px hsl(var(--primary) / 0.2);
  border-color: hsl(var(--primary));
}

/* Validation states */
.input-error {
  border-color: hsl(var(--destructive));
  box-shadow: 0 0 0 2px hsl(var(--destructive) / 0.2);
}

.input-success {
  border-color: hsl(var(--primary));
  box-shadow: 0 0 0 2px hsl(var(--primary) / 0.2);
}

/* Swap interface enhancements */
.swap-button:hover {
  transform: scale(1.05);
  transition: transform 0.15s ease-out;
}

/* Loading spinner */
@keyframes spin {
  to { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Professional gradient animations */
@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

/* Pulse glow effect */
@keyframes pulseGlow {
  0%, 100% { box-shadow: 0 0 5px hsl(var(--primary) / 0.3); }
  50% { box-shadow: 0 0 20px hsl(var(--primary) / 0.6), 0 0 30px hsl(var(--primary) / 0.4); }
}

.animate-pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite;
}

/* Floating animation */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Professional card hover effects */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Enhanced button effects */
.btn-enhanced {
  position: relative;
  overflow: hidden;
}

.btn-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-enhanced:hover::before {
  left: 100%;
}

/* Toggle switch animations */
.toggle-switch {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.toggle-switch:focus {
  box-shadow: 0 0 0 3px hsl(var(--primary) / 0.2);
}

.toggle-switch-thumb {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Character counter animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.character-counter {
  animation: fadeInUp 0.2s ease-out;
}

/* Validation state animations */
.validation-success {
  animation: slideDown 0.3s ease-out;
}

.validation-error {
  animation: slideDown 0.3s ease-out;
}
