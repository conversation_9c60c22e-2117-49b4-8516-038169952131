'use client'

import { useEffect } from 'react'
import { Header } from '@/components/layout/Header'
import { Sidebar } from '@/components/layout/Sidebar'
import { TransactionIntelligenceCenter } from '@/components/transactions/TransactionIntelligenceCenter'
import { useTransactionStore } from '@/stores/transactionStore'
import { useAnalyticsStore } from '@/stores/analyticsStore'

export default function TransactionsPage() {
  const { 
    transactions, 
    filteredTransactions, 
    isLoading, 
    error, 
    lastFetch, 
    fetchTransactions 
  } = useTransactionStore()
  
  const { 
    performanceMetrics,
    strategyAnalytics,
    dailyPnlData,
    strategyPerformanceData,
    winLossData,
    isCalculating,
    calculateMetrics,
    cacheValid
  } = useAnalyticsStore()

  // Fetch transactions on mount if not already loaded or cache is stale
  useEffect(() => {
    // Handle case where lastFetch might be a string due to persistence
    const lastFetchTime = lastFetch ? (typeof lastFetch === 'string' ? new Date(lastFetch) : lastFetch) : null
    const shouldFetch = !lastFetchTime || 
      (Date.now() - lastFetchTime.getTime()) > 5 * 60 * 1000 || // 5 minutes cache
      transactions.length === 0

    if (shouldFetch && !isLoading) {
      fetchTransactions()
    }
  }, [lastFetch, transactions.length, isLoading, fetchTransactions])

  // Calculate analytics when transactions change or cache is invalid
  useEffect(() => {
    if (transactions.length > 0 && (!cacheValid || !performanceMetrics)) {
      calculateMetrics(transactions)
    }
  }, [transactions, cacheValid, performanceMetrics, calculateMetrics])

  if (error) {
    return (
      <div className="min-h-screen bg-background text-foreground">
        <Header />
        <div className="flex">
          <Sidebar />
          <main className="flex-1 p-6">
            <div className="max-w-7xl mx-auto">
              <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-6 text-center">
                <h2 className="text-xl font-semibold text-red-400 mb-2">Error Loading Transactions</h2>
                <p className="text-muted-foreground mb-4">{error}</p>
                <button
                  onClick={fetchTransactions}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                >
                  Retry
                </button>
              </div>
            </div>
          </main>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background text-foreground">
      <Header />
      
      <div className="flex">
        <Sidebar />
        
        <main className="flex-1 bg-elevated">
          <div className="min-h-screen bg-gradient-to-br from-background via-background-elevated to-background-section">
            <div className="max-w-7xl mx-auto">
            <TransactionIntelligenceCenter 
              transactions={filteredTransactions}
              performanceMetrics={performanceMetrics || undefined}
              strategyAnalytics={strategyAnalytics}
              dailyPnlData={dailyPnlData}
              strategyPerformanceData={strategyPerformanceData}
              winLossData={winLossData}
              isLoading={isLoading || isCalculating}
            />
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}