'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { LiveExposureMeter } from '@/components/trading/LiveExposureMeter'
import { OpenPositionCard } from '@/components/trading/OpenPositionCard'
import { PortfolioSummary } from '@/components/trading/PortfolioSummary'
import { ComprehensiveTradeFilters } from '@/components/trading/ComprehensiveTradeFilters'
import { Sidebar } from '@/components/layout/Sidebar'
import { Header } from '@/components/layout/Header'
import { Badge } from '@/components/ui/badge'
import { Position, ExposureData } from '@/types/trading'
import { formatCurrency } from '@/utils/trading'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useExitStrategyStore } from '@/stores/exitStrategyStore'
import { toast } from 'react-hot-toast'

// Real position data structure for backend integration
interface BackendPosition {
  id: string
  userId: string
  tokenAddress: string
  tokenSymbol: string
  entryPrice: number
  currentPrice: number
  quantity: number
  entryTimestamp: string
  strategyId?: string
  presetUsed: string
  riskLevel: string
  status: 'ACTIVE' | 'PARTIAL' | 'CLOSED'
  pnl: number
  pnlPercentage: number
  age: number
}

// API client for portfolio data
const apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api'

const portfolioAPI = {
  async getActivePositions(): Promise<BackendPosition[]> {
    const response = await fetch(`${apiBaseUrl}/portfolio/positions`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Add auth header when implemented
      },
    })
    
    if (!response.ok) {
      throw new Error(`Failed to fetch positions: ${response.statusText}`)
    }
    
    const data = await response.json()
    return data.data.positions || []
  },

  async getPortfolioSummary() {
    const response = await fetch(`${apiBaseUrl}/portfolio/summary`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })
    
    if (!response.ok) {
      throw new Error(`Failed to fetch portfolio summary: ${response.statusText}`)
    }
    
    const data = await response.json()
    return data.data
  },

  async closePosition(positionId: string): Promise<void> {
    const response = await fetch(`${apiBaseUrl}/portfolio/positions/${positionId}/close`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    })
    
    if (!response.ok) {
      throw new Error(`Failed to close position: ${response.statusText}`)
    }
  }
}

// Convert backend position to frontend format
function convertToFrontendPosition(backendPos: BackendPosition, strategy?: any): Position {
  return {
    id: backendPos.id,
    token: {
      symbol: backendPos.tokenSymbol,
      name: backendPos.tokenSymbol, // Would get from token metadata
      address: backendPos.tokenAddress
    },
    amount: backendPos.quantity,
    entryPrice: backendPos.entryPrice,
    currentPrice: backendPos.currentPrice,
    entryValue: backendPos.entryPrice * backendPos.quantity,
    currentValue: backendPos.currentPrice * backendPos.quantity,
    pnlPercentage: backendPos.pnlPercentage,
    pnlDollar: backendPos.pnl,
    trailingStop: strategy?.trailingStop?.currentStopPrice || 0,
    exitMilestones: strategy?.takeProfits?.map((tp: any) => tp.triggerPercent) || [],
    completedExits: strategy?.takeProfits?.filter((tp: any) => tp.executed).length || 0,
    timeOpened: new Date(backendPos.entryTimestamp),
    moonBag: strategy?.moonBag ? {
      percentage: strategy.moonBag.reservePercent,
      targetGain: strategy.moonBag.triggerConditions.minProfitPercent,
      currentProgress: backendPos.pnlPercentage
    } : undefined,
    status: determinePositionStatus(backendPos, strategy),
    progressToNextExit: {
      current: backendPos.pnlPercentage,
      next: getNextExitTarget(backendPos.pnlPercentage, strategy)
    }
  }
}

function determinePositionStatus(position: BackendPosition, strategy?: any): 'taking_profits' | 'approaching_target' | 'trailing' | 'moon_bag' {
  if (!strategy) return 'trailing'
  
  const completedTargets = strategy.takeProfits?.filter((tp: any) => tp.executed).length || 0
  if (completedTargets > 0) {
    if (strategy.moonBag?.enabled && position.pnlPercentage > 200) {
      return 'moon_bag'
    }
    return 'taking_profits'
  }
  
  const nextTarget = strategy.takeProfits?.[0]?.triggerPercent || 50
  if (position.pnlPercentage >= nextTarget * 0.8) {
    return 'approaching_target'
  }
  
  return 'trailing'
}

function getNextExitTarget(currentPnL: number, strategy?: any): number {
  if (!strategy?.takeProfits) return 50
  
  const unexecutedTargets = strategy.takeProfits.filter((tp: any) => !tp.executed)
  if (unexecutedTargets.length === 0) return 300 // Moon bag target
  
  return unexecutedTargets[0].triggerPercent
}

export default function ActiveTradesPage() {
  const queryClient = useQueryClient()
  const { strategies } = useExitStrategyStore()
  const [filteredOpenPositions, setFilteredOpenPositions] = useState<Position[]>([])
  
  // Fetch active positions from backend
  const {
    data: backendPositions = [],
    isLoading: positionsLoading,
    error: positionsError
  } = useQuery({
    queryKey: ['active-positions'],
    queryFn: portfolioAPI.getActivePositions,
    refetchInterval: 5000, // Refetch every 5 seconds for real-time updates
    staleTime: 2000,
  })
  
  // Fetch portfolio summary
  const {
    data: portfolioSummary,
    isLoading: summaryLoading
  } = useQuery({
    queryKey: ['portfolio-summary'],
    queryFn: portfolioAPI.getPortfolioSummary,
    refetchInterval: 10000,
    staleTime: 5000,
  })
  
  // Convert backend positions to frontend format with strategy integration
  const openPositions = useMemo(() => {
    return backendPositions.map(backendPos => {
      const strategy = strategies.find(s => s.id === backendPos.strategyId)
      return convertToFrontendPosition(backendPos, strategy)
    })
  }, [backendPositions, strategies])
  
  // Calculate exposure data from real positions
  const exposureData: ExposureData = useMemo(() => {
    const totalValue = openPositions.reduce((sum, pos) => sum + pos.currentValue, 0)
    const maxLimit = portfolioSummary?.maxExposureLimit || 50000
    
    return {
      currentExposure: totalValue,
      maximumLimit: maxLimit,
      capitalUsage: (totalValue / maxLimit) * 100,
      activePositions: openPositions.length,
      availableCapital: maxLimit - totalValue,
      positionsUnderLimit: openPositions.filter(p => p.currentValue < 10000).length
    }
  }, [openPositions, portfolioSummary])
  
  // Portfolio metrics
  const portfolioMetrics = useMemo(() => {
    const totalValue = openPositions.reduce((sum, pos) => sum + pos.currentValue, 0)
    const dailyPnL = openPositions.reduce((sum, pos) => sum + pos.pnlDollar, 0)
    
    return {
      totalValue,
      dailyPnL,
      activePositions: openPositions.length
    }
  }, [openPositions])

  // Initialize filtered positions when positions change
  useEffect(() => {
    setFilteredOpenPositions(openPositions)
  }, [openPositions])
  
  // Error handling
  useEffect(() => {
    if (positionsError) {
      toast.error('Failed to load positions. Please refresh the page.')
    }
  }, [positionsError])

  // Real-time updates are handled by React Query refetchInterval
  // Position Monitor Service in backend provides real-time price updates via WebSocket
  // No need for mock updates - data comes from blockchain and Jupiter API

  // Portfolio metrics are now calculated via useMemo above

  const handleOpenPositionsFilterChange = useCallback((filteredData: Position[]) => {
    setFilteredOpenPositions(filteredData)
  }, [])


  const handleClosePosition = async (positionId: string) => {
    try {
      await portfolioAPI.closePosition(positionId)
      queryClient.invalidateQueries({ queryKey: ['active-positions'] })
      queryClient.invalidateQueries({ queryKey: ['portfolio-summary'] })
      toast.success('Position closed successfully')
    } catch (error) {
      toast.error('Failed to close position')
      console.error('Error closing position:', error)
    }
  }

  const handleAddToPosition = (positionId: string) => {
    // This would open a modal to add more to the position
    // Could integrate with SwapInterface to pre-populate the trade
    toast.info('Add to position feature coming soon')
  }



  return (
    <div className="min-h-screen bg-background text-foreground">
      <Header />

      <div className="flex">
        <Sidebar />

        <main className="flex-1 p-6">
          <div className="max-w-7xl mx-auto space-y-6">
            {/* Portfolio Summary */}
            <div className="bg-gradient-to-br from-card/70 to-card/30 backdrop-blur-sm border border-border/40 rounded-2xl shadow-lg p-6">
              <PortfolioSummary positions={openPositions} />
            </div>

            {/* Live Exposure Meter */}
            <div className="bg-gradient-to-br from-card/70 to-card/30 backdrop-blur-sm border border-border/40 rounded-2xl shadow-lg p-6">
              <LiveExposureMeter data={exposureData} />
            </div>

            {/* Comprehensive Trade Filters */}
            <ComprehensiveTradeFilters 
              openPositions={openPositions}
              onOpenPositionsFilterChange={handleOpenPositionsFilterChange}
            />

            {/* Open Positions Section */}
            <div className="bg-gradient-to-br from-card/70 to-card/30 backdrop-blur-sm border border-border/40 rounded-2xl shadow-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-3xl font-bold text-foreground">Open Positions</h2>
                <div className="flex items-center gap-2">
                  <div className="flex items-center gap-2">
                    <Badge className="bg-green-500/20 text-green-400 border border-green-500/30 px-3 py-1">
                      {positionsLoading ? '...' : `${filteredOpenPositions.length} Open`}
                    </Badge>
                    {positionsLoading && (
                      <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                    )}
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {filteredOpenPositions.map((position) => (
                  <OpenPositionCard 
                    key={position.id} 
                    position={position}
                    onClose={handleClosePosition}
                    onAddToPosition={handleAddToPosition}
                  />
                ))}
              </div>

              {positionsLoading && (
                <div className="text-center py-12">
                  <div className="text-muted-foreground text-lg mb-2">Loading positions...</div>
                  <div className="text-sm text-muted-foreground">
                    Fetching your latest position data
                  </div>
                </div>
              )}
              
              {!positionsLoading && filteredOpenPositions.length === 0 && (
                <div className="text-center py-12">
                  <div className="text-muted-foreground text-lg mb-2">
                    {positionsError ? 'Failed to load positions' : 'No open positions found'}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {positionsError ? 'Please check your connection and try again' : 'Start a new trade to see positions here'}
                  </div>
                </div>
              )}
            </div>

          </div>
        </main>
      </div>
    </div>
  )
}