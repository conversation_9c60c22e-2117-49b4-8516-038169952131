'use client'

import { useState, useEffect, useCallback } from 'react'
import { LiveExposureMeter } from '@/components/trading/LiveExposureMeter'
import { OpenPositionCard } from '@/components/trading/OpenPositionCard'
import { PortfolioSummary } from '@/components/trading/PortfolioSummary'
import { ComprehensiveTradeFilters } from '@/components/trading/ComprehensiveTradeFilters'
import { Sidebar } from '@/components/layout/Sidebar'
import { Header } from '@/components/layout/Header'
import { Badge } from '@/components/ui/badge'
import { Position, ExposureData } from '@/types/trading'
import { formatCurrency } from '@/utils/trading'

export default function ActiveTradesPage() {
  const [openPositions, setOpenPositions] = useState<Position[]>([])
  const [filteredOpenPositions, setFilteredOpenPositions] = useState<Position[]>([])
  const [portfolioMetrics, setPortfolioMetrics] = useState({
    totalValue: 0,
    dailyPnL: 0,
    activePositions: 0
  })
  const [exposureData, setExposureData] = useState<ExposureData>({
    currentExposure: 37850,
    maximumLimit: 50000,
    capitalUsage: 75.7,
    activePositions: 8,
    availableCapital: 12150,
    positionsUnderLimit: 8
  })

  // Mock position data based on reference images
  useEffect(() => {
    const mockOpenPositions: Position[] = [
      {
        id: '1',
        token: {
          symbol: 'PEPE',
          name: 'Pepe',
          address: '0x6982508145454Ce325dDbE47a25d4ec3d2311933'
        },
        amount: 1000000,
        entryPrice: 0.00000118,
        currentPrice: 0.00000177,
        entryValue: 1180,
        currentValue: 1770,
        pnlPercentage: 50.0,
        pnlDollar: 590,
        trailingStop: 0.00000150,
        exitMilestones: [50, 100, 150, 200],
        completedExits: 1,
        timeOpened: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
        moonBag: {
          percentage: 25,
          targetGain: 500,
          currentProgress: 50.0
        },
        status: 'taking_profits',
        progressToNextExit: {
          current: 50.0,
          next: 100
        }
      },
      {
        id: '2',
        token: {
          symbol: 'BONK',
          name: 'Bonk',
          address: '0xdac17f958d2ee523a2206206994597c13d831ec7'
        },
        amount: 2000000,
        entryPrice: 0.00001980,
        currentPrice: 0.00002890,
        entryValue: 396,
        currentValue: 578,
        pnlPercentage: 46.0,
        pnlDollar: 182,
        trailingStop: 0.00002460,
        exitMilestones: [50, 100, 150, 200],
        completedExits: 0,
        timeOpened: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4 hours ago
        moonBag: {
          percentage: 25,
          targetGain: 500,
          currentProgress: 46.0
        },
        status: 'approaching_target',
        progressToNextExit: {
          current: 46.0,
          next: 50
        }
      },
      {
        id: '3',
        token: {
          symbol: 'DOGE',
          name: 'Dogecoin',
          address: '0x85f17Cf997934a597031b2E18a9aB6ebD4B9f6a4'
        },
        amount: 15000,
        entryPrice: 0.08450,
        currentPrice: 0.07890,
        entryValue: 1267.50,
        currentValue: 1183.50,
        pnlPercentage: -6.6,
        pnlDollar: -84,
        trailingStop: 0.07500,
        exitMilestones: [50, 100, 150, 200],
        completedExits: 0,
        timeOpened: new Date(Date.now() - 1000 * 60 * 60 * 8), // 8 hours ago
        moonBag: {
          percentage: 25,
          targetGain: 500,
          currentProgress: -6.6
        },
        status: 'trailing',
        progressToNextExit: {
          current: -6.6,
          next: 50
        }
      },
      {
        id: '4',
        token: {
          symbol: 'SHIB',
          name: 'Shiba Inu',
          address: '0x95aD61b0a150d79219dCF64E1E6Cc01f0B64C4cE'
        },
        amount: 50000000,
        entryPrice: 0.00000850,
        currentPrice: 0.00001245,
        entryValue: 425,
        currentValue: 622.50,
        pnlPercentage: 46.5,
        pnlDollar: 197.50,
        trailingStop: 0.00001100,
        exitMilestones: [50, 100, 150, 200],
        completedExits: 0,
        timeOpened: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 hours ago
        moonBag: {
          percentage: 25,
          targetGain: 500,
          currentProgress: 46.5
        },
        status: 'approaching_target',
        progressToNextExit: {
          current: 46.5,
          next: 50
        }
      },
      {
        id: '5',
        token: {
          symbol: 'WIF',
          name: 'dogwifhat',
          address: '0x2260FAC5E5542a773Aa44fBCfeDf7C193bc2C599'
        },
        amount: 8000,
        entryPrice: 0.245000,
        currentPrice: 0.389000,
        entryValue: 1960,
        currentValue: 3112,
        pnlPercentage: 58.8,
        pnlDollar: 1152,
        trailingStop: 0.350000,
        exitMilestones: [50, 100, 150, 200],
        completedExits: 1,
        timeOpened: new Date(Date.now() - 1000 * 60 * 60 * 12), // 12 hours ago
        moonBag: {
          percentage: 25,
          targetGain: 500,
          currentProgress: 58.8
        },
        status: 'taking_profits',
        progressToNextExit: {
          current: 58.8,
          next: 100
        }
      },
      {
        id: '6',
        token: {
          symbol: 'FLOKI',
          name: 'FLOKI',
          address: '0xcf0C122c6b73ff809C693DB761e7BaeBe62b6a2E'
        },
        amount: 25000000,
        entryPrice: 0.00012500,
        currentPrice: 0.00045600,
        entryValue: 3125,
        currentValue: 11400,
        pnlPercentage: 264.8,
        pnlDollar: 8275,
        trailingStop: 0.00040000,
        exitMilestones: [50, 100, 150, 200],
        completedExits: 3,
        timeOpened: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2), // 2 days ago
        moonBag: {
          percentage: 25,
          targetGain: 500,
          currentProgress: 264.8
        },
        status: 'moon_bag',
        progressToNextExit: {
          current: 264.8,
          next: 300
        }
      },
      {
        id: '7',
        token: {
          symbol: 'MYRO',
          name: 'Myro',
          address: '0x1f9840a85d5aF5bf1D1762F925BDADdC4201F984'
        },
        amount: 45000,
        entryPrice: 0.12340,
        currentPrice: 0.09850,
        entryValue: 5553,
        currentValue: 4432.50,
        pnlPercentage: -20.2,
        pnlDollar: -1120.50,
        trailingStop: 0.08500,
        exitMilestones: [50, 100, 150, 200],
        completedExits: 0,
        timeOpened: new Date(Date.now() - 1000 * 60 * 60 * 18), // 18 hours ago
        moonBag: {
          percentage: 25,
          targetGain: 500,
          currentProgress: -20.2
        },
        status: 'trailing',
        progressToNextExit: {
          current: -20.2,
          next: 50
        }
      },
      {
        id: '8',
        token: {
          symbol: 'POPCAT',
          name: 'Popcat',
          address: '0xA0b86991c431c8312b3b7d9d6e2f9b4b7b6b1b6b1'
        },
        amount: 12500,
        entryPrice: 0.78900,
        currentPrice: 1.15600,
        entryValue: 9862.50,
        currentValue: 14450,
        pnlPercentage: 46.5,
        pnlDollar: 4587.50,
        trailingStop: 1.05000,
        exitMilestones: [50, 100, 150, 200],
        completedExits: 0,
        timeOpened: new Date(Date.now() - 1000 * 60 * 60 * 3), // 3 hours ago
        moonBag: {
          percentage: 25,
          targetGain: 500,
          currentProgress: 46.5
        },
        status: 'approaching_target',
        progressToNextExit: {
          current: 46.5,
          next: 50
        }
      }
    ]

    setOpenPositions(mockOpenPositions)
    setFilteredOpenPositions(mockOpenPositions)
  }, [])

  // Mock real-time price updates
  useEffect(() => {
    const interval = setInterval(() => {
      // Update open positions
      setOpenPositions(prev => prev.map(position => {
        const priceChange = (Math.random() - 0.5) * 0.02 // ±1% change
        const newPrice = position.currentPrice * (1 + priceChange)
        const newValue = position.amount * newPrice
        const newPnlDollar = newValue - position.entryValue
        const newPnlPercentage = (newPnlDollar / position.entryValue) * 100

        return {
          ...position,
          currentPrice: newPrice,
          currentValue: newValue,
          pnlDollar: newPnlDollar,
          pnlPercentage: newPnlPercentage,
          progressToNextExit: {
            ...position.progressToNextExit,
            current: newPnlPercentage
          }
        }
      }))

    }, 3000) // Update every 3 seconds

    return () => clearInterval(interval)
  }, [])

  // Calculate portfolio metrics
  useEffect(() => {
    const totalValue = openPositions.reduce((sum, pos) => sum + pos.currentValue, 0)
    const dailyPnL = openPositions.reduce((sum, pos) => sum + pos.pnlDollar, 0)
    const activePositions = openPositions.length

    setPortfolioMetrics({
      totalValue,
      dailyPnL,
      activePositions
    })
  }, [openPositions])

  const handleOpenPositionsFilterChange = useCallback((filteredData: Position[]) => {
    setFilteredOpenPositions(filteredData)
  }, [])


  const handleClosePosition = (positionId: string) => {
    console.log('Closing position:', positionId)
    setOpenPositions(prev => prev.filter(p => p.id !== positionId))
    setFilteredOpenPositions(prev => prev.filter(p => p.id !== positionId))
  }

  const handleAddToPosition = (positionId: string) => {
    console.log('Adding to position:', positionId)
    // TODO: Implement add to position modal
  }



  return (
    <div className="min-h-screen bg-background text-foreground">
      <Header />

      <div className="flex">
        <Sidebar />

        <main className="flex-1 p-6">
          <div className="max-w-7xl mx-auto space-y-6">
            {/* Portfolio Summary */}
            <div className="bg-gradient-to-br from-card/70 to-card/30 backdrop-blur-sm border border-border/40 rounded-2xl shadow-lg p-6">
              <PortfolioSummary positions={openPositions} />
            </div>

            {/* Live Exposure Meter */}
            <div className="bg-gradient-to-br from-card/70 to-card/30 backdrop-blur-sm border border-border/40 rounded-2xl shadow-lg p-6">
              <LiveExposureMeter data={exposureData} />
            </div>

            {/* Comprehensive Trade Filters */}
            <ComprehensiveTradeFilters 
              openPositions={openPositions}
              onOpenPositionsFilterChange={handleOpenPositionsFilterChange}
            />

            {/* Open Positions Section */}
            <div className="bg-gradient-to-br from-card/70 to-card/30 backdrop-blur-sm border border-border/40 rounded-2xl shadow-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-3xl font-bold text-foreground">Open Positions</h2>
                <div className="flex items-center gap-2">
                  <Badge className="bg-green-500/20 text-green-400 border border-green-500/30 px-3 py-1">
                    {filteredOpenPositions.length} Open
                  </Badge>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {filteredOpenPositions.map((position) => (
                  <OpenPositionCard 
                    key={position.id} 
                    position={position}
                    onClose={handleClosePosition}
                    onAddToPosition={handleAddToPosition}
                  />
                ))}
              </div>

              {filteredOpenPositions.length === 0 && (
                <div className="text-center py-12">
                  <div className="text-muted-foreground text-lg mb-2">No open positions found</div>
                  <div className="text-sm text-muted-foreground">
                    Adjust your filters or start a new trade
                  </div>
                </div>
              )}
            </div>

          </div>
        </main>
      </div>
    </div>
  )
}