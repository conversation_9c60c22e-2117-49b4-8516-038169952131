'use client'

import { useState, useEffect, useCallback } from 'react'
import { LiveExposureMeter } from '@/components/trading/LiveExposureMeter'
import { OpenPositionCard } from '@/components/trading/OpenPositionCard'
import { PendingPositionCard } from '@/components/trading/PendingPositionCard'
import { PortfolioSummary } from '@/components/trading/PortfolioSummary'
import { ComprehensiveTradeFilters } from '@/components/trading/ComprehensiveTradeFilters'
import { Sidebar } from '@/components/layout/Sidebar'
import { Header } from '@/components/layout/Header'
import { Badge } from '@/components/ui/badge'
import { Position, PendingPosition, ExposureData } from '@/types/trading'
import { formatCurrency } from '@/utils/trading'

export default function ActiveTradesPage() {
  const [openPositions, setOpenPositions] = useState<Position[]>([])
  const [pendingPositions, setPendingPositions] = useState<PendingPosition[]>([])
  const [filteredOpenPositions, setFilteredOpenPositions] = useState<Position[]>([])
  const [filteredPendingPositions, setFilteredPendingPositions] = useState<PendingPosition[]>([])
  const [portfolioMetrics, setPortfolioMetrics] = useState({
    totalValue: 0,
    dailyPnL: 0,
    activePositions: 0
  })
  const [exposureData, setExposureData] = useState<ExposureData>({
    currentExposure: 1840,
    maximumLimit: 2000,
    capitalUsage: 92.0,
    activePositions: 4,
    availableCapital: 160,
    positionsUnderLimit: 4
  })

  // Mock position data based on reference images
  useEffect(() => {
    const mockOpenPositions: Position[] = [
      {
        id: '1',
        token: {
          symbol: 'PEPE',
          name: 'Pepe',
          address: '0x6982508145454Ce325dDbE47a25d4ec3d2311933'
        },
        amount: 1000000,
        entryPrice: 0.00000118,
        currentPrice: 0.00000177,
        entryValue: 1180,
        currentValue: 1770,
        pnlPercentage: 50.0,
        pnlDollar: 590,
        trailingStop: 0.00000150,
        exitMilestones: [50, 100, 150, 200],
        completedExits: 1,
        timeOpened: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
        moonBag: {
          percentage: 25,
          targetGain: 500,
          currentProgress: 50.0
        },
        status: 'taking_profits',
        progressToNextExit: {
          current: 50.0,
          next: 100
        }
      },
      {
        id: '2',
        token: {
          symbol: 'BONK',
          name: 'Bonk',
          address: '0xdac17f958d2ee523a2206206994597c13d831ec7'
        },
        amount: 2000000,
        entryPrice: 0.00001980,
        currentPrice: 0.00002890,
        entryValue: 396,
        currentValue: 578,
        pnlPercentage: 46.0,
        pnlDollar: 182,
        trailingStop: 0.00002460,
        exitMilestones: [50, 100, 150, 200],
        completedExits: 0,
        timeOpened: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4 hours ago
        moonBag: {
          percentage: 25,
          targetGain: 500,
          currentProgress: 46.0
        },
        status: 'approaching_target',
        progressToNextExit: {
          current: 46.0,
          next: 50
        }
      }
    ]

    const mockPendingPositions: PendingPosition[] = [
      {
        id: 'p1',
        token: {
          symbol: 'SHIB',
          name: 'Shiba Inu',
          address: '0x95aD61b0a150d79219dCF64E1E6Cc01f0B64C4cE'
        },
        orderType: 'buy_limit',
        quantity: 50000000,
        targetPrice: 0.00000850,
        totalValue: 425,
        currentPrice: 0.00000900,
        timeCreated: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
        status: 'pending'
      },
      {
        id: 'p2',
        token: {
          symbol: 'WIF',
          name: 'dogwifhat',
          address: '0x85f17Cf997934a597031b2E18a9aB6ebD4B9f6a4'
        },
        orderType: 'buy_limit',
        quantity: 10000,
        targetPrice: 0.00245000,
        totalValue: 245,
        currentPrice: 0.00278000,
        timeCreated: new Date(Date.now() - 1000 * 60 * 45), // 45 minutes ago
        status: 'pending'
      },
      {
        id: 'p3',
        token: {
          symbol: 'FLOKI',
          name: 'FLOKI',
          address: '0xcf0C122c6b73ff809C693DB761e7BaeBe62b6a2E'
        },
        orderType: 'sell_limit',
        quantity: 25000000,
        targetPrice: 0.00012500,
        totalValue: 312.5,
        currentPrice: 0.00011800,
        timeCreated: new Date(Date.now() - 1000 * 60 * 60 * 1), // 1 hour ago
        status: 'pending'
      }
    ]

    setOpenPositions(mockOpenPositions)
    setFilteredOpenPositions(mockOpenPositions)
    setPendingPositions(mockPendingPositions)
    setFilteredPendingPositions(mockPendingPositions)
  }, [])

  // Mock real-time price updates
  useEffect(() => {
    const interval = setInterval(() => {
      // Update open positions
      setOpenPositions(prev => prev.map(position => {
        const priceChange = (Math.random() - 0.5) * 0.02 // ±1% change
        const newPrice = position.currentPrice * (1 + priceChange)
        const newValue = position.amount * newPrice
        const newPnlDollar = newValue - position.entryValue
        const newPnlPercentage = (newPnlDollar / position.entryValue) * 100

        return {
          ...position,
          currentPrice: newPrice,
          currentValue: newValue,
          pnlDollar: newPnlDollar,
          pnlPercentage: newPnlPercentage,
          progressToNextExit: {
            ...position.progressToNextExit,
            current: newPnlPercentage
          }
        }
      }))

      // Update pending positions (just current price for comparison)
      setPendingPositions(prev => prev.map(position => {
        const priceChange = (Math.random() - 0.5) * 0.02 // ±1% change
        const newPrice = position.currentPrice * (1 + priceChange)

        return {
          ...position,
          currentPrice: newPrice
        }
      }))
    }, 3000) // Update every 3 seconds

    return () => clearInterval(interval)
  }, [])

  // Calculate portfolio metrics
  useEffect(() => {
    const totalValue = openPositions.reduce((sum, pos) => sum + pos.currentValue, 0)
    const dailyPnL = openPositions.reduce((sum, pos) => sum + pos.pnlDollar, 0)
    const activePositions = openPositions.length

    setPortfolioMetrics({
      totalValue,
      dailyPnL,
      activePositions
    })
  }, [openPositions])

  const handleOpenPositionsFilterChange = useCallback((filteredData: Position[]) => {
    setFilteredOpenPositions(filteredData)
  }, [])

  const handlePendingPositionsFilterChange = useCallback((filteredData: PendingPosition[]) => {
    setFilteredPendingPositions(filteredData)
  }, [])

  const handleClosePosition = (positionId: string) => {
    console.log('Closing position:', positionId)
    setOpenPositions(prev => prev.filter(p => p.id !== positionId))
    setFilteredOpenPositions(prev => prev.filter(p => p.id !== positionId))
  }

  const handleAddToPosition = (positionId: string) => {
    console.log('Adding to position:', positionId)
    // TODO: Implement add to position modal
  }

  const handleModifyPendingPosition = (positionId: string) => {
    console.log('Modifying pending position:', positionId)
    // TODO: Implement modify pending position modal
  }

  const handleCancelPendingPosition = (positionId: string) => {
    console.log('Canceling pending position:', positionId)
    setPendingPositions(prev => prev.filter(p => p.id !== positionId))
    setFilteredPendingPositions(prev => prev.filter(p => p.id !== positionId))
  }

  return (
    <div className="min-h-screen bg-background text-foreground">
      <Header />

      <div className="flex">
        <Sidebar />

        <main className="flex-1 p-6">
          <div className="max-w-7xl mx-auto space-y-6">
            {/* Portfolio Summary */}
            <div className="bg-gradient-to-br from-card/70 to-card/30 backdrop-blur-sm border border-border/40 rounded-2xl shadow-lg p-6">
              <PortfolioSummary positions={openPositions} />
            </div>

            {/* Live Exposure Meter */}
            <div className="bg-gradient-to-br from-card/70 to-card/30 backdrop-blur-sm border border-border/40 rounded-2xl shadow-lg p-6">
              <LiveExposureMeter data={exposureData} />
            </div>

            {/* Comprehensive Trade Filters */}
            <ComprehensiveTradeFilters 
              openPositions={openPositions}
              pendingPositions={pendingPositions}
              onOpenPositionsFilterChange={handleOpenPositionsFilterChange}
              onPendingPositionsFilterChange={handlePendingPositionsFilterChange}
            />

            {/* Open Positions Section */}
            <div className="bg-gradient-to-br from-card/70 to-card/30 backdrop-blur-sm border border-border/40 rounded-2xl shadow-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-3xl font-bold text-foreground">Open Positions</h2>
                <div className="flex items-center gap-2">
                  <Badge className="bg-green-500/20 text-green-400 border border-green-500/30 px-3 py-1">
                    {filteredOpenPositions.length} Open
                  </Badge>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {filteredOpenPositions.map((position) => (
                  <OpenPositionCard 
                    key={position.id} 
                    position={position}
                    onClose={handleClosePosition}
                    onAddToPosition={handleAddToPosition}
                  />
                ))}
              </div>

              {filteredOpenPositions.length === 0 && (
                <div className="text-center py-12">
                  <div className="text-muted-foreground text-lg mb-2">No open positions found</div>
                  <div className="text-sm text-muted-foreground">
                    Adjust your filters or start a new trade
                  </div>
                </div>
              )}
            </div>

            {/* Pending Positions Section */}
            <div className="bg-gradient-to-br from-card/70 to-card/30 backdrop-blur-sm border border-border/40 rounded-2xl shadow-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-3xl font-bold text-foreground">Pending Orders</h2>
                <div className="flex items-center gap-2">
                  <Badge className="bg-orange-500/20 text-orange-400 border border-orange-500/30 px-3 py-1">
                    {filteredPendingPositions.length} Pending
                  </Badge>
                  <Badge className="bg-blue-500/20 text-blue-400 border border-blue-500/30 px-3 py-1">
                    {formatCurrency(filteredPendingPositions.reduce((sum, pos) => sum + pos.totalValue, 0))} Total
                  </Badge>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {filteredPendingPositions.map((position) => (
                  <PendingPositionCard 
                    key={position.id} 
                    position={position}
                    onModify={handleModifyPendingPosition}
                    onCancel={handleCancelPendingPosition}
                  />
                ))}
              </div>

              {filteredPendingPositions.length === 0 && (
                <div className="text-center py-12">
                  <div className="text-muted-foreground text-lg mb-2">No pending orders found</div>
                  <div className="text-sm text-muted-foreground">
                    Adjust your filters or create a new order
                  </div>
                </div>
              )}
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}