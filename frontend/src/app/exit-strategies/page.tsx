'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Plus, Search, Settings, Lock, LockOpen, Star, Copy, Trash2, Edit } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { CreateStrategyModal } from '@/components/trading/CreateStrategyModal'
import { StrategyCard } from '@/components/trading/StrategyCard'
import { Sidebar } from '@/components/layout/Sidebar'
import { Header } from '@/components/layout/Header'
import { useExitStrategyStore } from '@/stores/exitStrategyStore'

// Mock data based on the reference images
const mockStrategies = [
  {
    id: '1',
    name: 'Conservative',
    description: 'Low risk, steady profits',
    usageCount: 12,
    winRate: 73,
    isDefault: true,
    locked: false,
    stopLoss: { percentage: 10 },
    profitTargets: [
      { target: 25, sellPercentage: 20 },
      { target: 50, sellPercentage: 30 },
      { target: 75, sellPercentage: 25 }
    ],
    trailingStop: { percentage: 15 },
    moonBag: null
  },
  {
    id: '2',
    name: 'Aggressive TP',
    description: 'Higher risk, bigger rewards',
    usageCount: 8,
    winRate: 65,
    isDefault: false,
    locked: false,
    stopLoss: { percentage: 15 },
    profitTargets: [
      { target: 50, sellPercentage: 15 },
      { target: 100, sellPercentage: 20 },
      { target: 150, sellPercentage: 25 },
      { target: 200, sellPercentage: 15 }
    ],
    trailingStop: { percentage: 15 },
    moonBag: { percentage: 25, targetGain: 500 }
  },
  {
    id: '3',
    name: 'Trailing Only',
    description: 'Pure trailing stop strategy',
    usageCount: 3,
    winRate: 80,
    isDefault: false,
    locked: true,
    stopLoss: null,
    profitTargets: [],
    trailingStop: { percentage: 15 },
    moonBag: null
  },
  {
    id: '4',
    name: 'Moon Bag',
    description: 'Hold majority for massive gains',
    usageCount: 12,
    winRate: 73,
    isDefault: false,
    locked: false,
    stopLoss: { percentage: 15 },
    profitTargets: [
      { target: 30, sellPercentage: 10 },
      { target: 75, sellPercentage: 15 },
      { target: 150, sellPercentage: 20 }
    ],
    trailingStop: { percentage: 15 },
    moonBag: { percentage: 35, targetGain: 500 }
  },
  {
    id: '5',
    name: 'Quick Scalp',
    description: 'Fast profits with tight stops',
    usageCount: 12,
    winRate: 73,
    isDefault: false,
    locked: false,
    stopLoss: { percentage: 5 },
    profitTargets: [
      { target: 15, sellPercentage: 50 },
      { target: 30, sellPercentage: 50 }
    ],
    trailingStop: { percentage: 8 },
    moonBag: null
  },
  {
    id: '6',
    name: 'Aggressive',
    description: 'Maximum risk, maximum rewards',
    usageCount: 12,
    winRate: 73,
    isDefault: false,
    locked: false,
    stopLoss: { percentage: 20 },
    profitTargets: [
      { target: 50, sellPercentage: 15 },
      { target: 100, sellPercentage: 25 },
      { target: 200, sellPercentage: 35 }
    ],
    trailingStop: { percentage: 20 },
    moonBag: { percentage: 25, targetGain: 500 }
  }
]

type Strategy = typeof mockStrategies[0]

export default function ExitStrategiesPage() {
  const {
    strategies,
    createStrategy,
    updateStrategy,
    deleteStrategy,
    pauseStrategy,
    resumeStrategy
  } = useExitStrategyStore()

  const [searchQuery, setSearchQuery] = useState('')
  const [selectedStrategy, setSelectedStrategy] = useState<any>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [deleteConfirmation, setDeleteConfirmation] = useState<{ isOpen: boolean; strategy: any }>({
    isOpen: false,
    strategy: null
  })

  // Filter strategies based on search
  const filteredStrategies = strategies.filter(strategy =>
    strategy.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    strategy.description.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const handleToggleLock = (strategyId: string) => {
    const strategy = strategies.find(s => s.id === strategyId)
    if (strategy) {
      // Toggle the locked status (assuming there's a locked field)
      updateStrategy(strategyId, {
        // Note: Backend schema may not have 'locked' field, this might need adjustment
        // executionMode: strategy.locked ? 'AUTOMATIC' : 'MANUAL_CONFIRM'
      })
    }
  }

  const handleSetDefault = (strategyId: string) => {
    // Check if strategy is locked (using executionMode as proxy for locked status)
    const strategy = strategies.find(s => s.id === strategyId)
    if (strategy?.executionMode === 'MANUAL_CONFIRM') {
      console.warn('Cannot set locked strategy as default')
      return
    }

    // Note: Backend schema may not have 'isDefault' field
    // This functionality might need to be implemented differently
    console.log('Setting strategy as default:', strategyId)
    // updateStrategy(strategyId, { /* isDefault field may not exist */ })
  }

  const handleDeleteStrategy = (strategyId: string) => {
    // Check if strategy is locked (using executionMode as proxy for locked status)
    const strategy = strategies.find(s => s.id === strategyId)
    if (strategy?.executionMode === 'MANUAL_CONFIRM') {
      console.warn('Cannot delete locked strategy')
      return
    }

    // Show confirmation dialog
    setDeleteConfirmation({ isOpen: true, strategy })
  }

  const confirmDeleteStrategy = () => {
    if (deleteConfirmation.strategy) {
      deleteStrategy(deleteConfirmation.strategy.id)
      setDeleteConfirmation({ isOpen: false, strategy: null })
    }
  }

  const cancelDeleteStrategy = () => {
    setDeleteConfirmation({ isOpen: false, strategy: null })
  }

  const handleDuplicateStrategy = (strategy: any) => {
    createStrategy({
      name: `${strategy.name} Copy`,
      type: strategy.type || 'COMPOSITE',
      executionMode: 'AUTOMATIC',
      slippageTolerance: 0.5,
      presetToUse: 'DEFAULT',
      mevProtection: false,
      status: 'ACTIVE',
      executionState: 'MONITORING',
      executionCount: 0,
      totalRealized: 0,
      userId: '', // Will be set by backend
      positionId: '', // Will be set when strategy is applied to position
      stopLoss: strategy.stopLoss,
      takeProfits: strategy.profitTargets,
      trailingStop: strategy.trailingStop,
      moonBag: strategy.moonBag
    })
  }

  const handleCreateStrategy = (newStrategy: any) => {
    // Convert CreateStrategyModal format to store format
    createStrategy({
      name: newStrategy.name,
      type: newStrategy.type || 'COMPOSITE',
      executionMode: 'AUTOMATIC',
      slippageTolerance: 0.5,
      presetToUse: 'DEFAULT',
      mevProtection: false,
      status: 'ACTIVE',
      executionState: 'MONITORING',
      executionCount: 0,
      totalRealized: 0,
      userId: '', // Will be set by backend
      positionId: '', // Will be set when strategy is applied to position
      stopLoss: newStrategy.stopLoss,
      takeProfits: newStrategy.profitTargets,
      trailingStop: newStrategy.trailingStop,
      moonBag: newStrategy.moonBag
    })
  }

  const handleEditStrategy = (updatedStrategy: any) => {
    if (!selectedStrategy) return

    updateStrategy(selectedStrategy.id, {
      name: updatedStrategy.name,
      // description: updatedStrategy.description || '',
      riskLevel: updatedStrategy.locked ? 'LOW' : 'MEDIUM',
      stopLoss: updatedStrategy.stopLoss,
      profitTargets: updatedStrategy.profitTargets,
      trailingStop: updatedStrategy.trailingStop,
      moonBag: updatedStrategy.moonBag,
      isDefault: updatedStrategy.isDefault || false,
      locked: updatedStrategy.locked || false
    })
  }

  const openCreateModal = () => {
    setSelectedStrategy(null)
    setIsModalOpen(true)
  }

  const openEditModal = (strategy: any) => {
    if (strategy.executionMode === 'MANUAL_CONFIRM') {
      console.warn('Cannot edit locked strategy')
      return
    }
    setSelectedStrategy(strategy)
    setIsModalOpen(true)
  }

  const closeModal = () => {
    setIsModalOpen(false)
    setSelectedStrategy(null)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20 text-foreground">
      <Header />

      <div className="flex">
        <Sidebar />

        <main className="flex-1 p-6">
          <div className="max-w-7xl mx-auto space-y-6">
            {/* Header Section */}
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-3xl font-bold tracking-tight">Exit Strategies</h2>
                <p className="text-muted-foreground mt-2">
                  Create and manage automated exit strategies for your trading positions
                </p>
              </div>
              <Button
                onClick={openCreateModal}
                className="flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                New Strategy
              </Button>
            </div>

            {/* Search and Filters */}
            <div className="flex items-center gap-4">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search strategies..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="flex items-center gap-1">
                  <Settings className="w-3 h-3" />
                  {strategies.length} Strategies
                </Badge>
              </div>
            </div>

            {/* Strategies Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
              {/* Strategy Cards */}
              {filteredStrategies.map((strategy) => (
                <StrategyCard
                  key={strategy.id}
                  strategy={strategy as Strategy}
                  onToggleLock={() => handleToggleLock(strategy.id)}
                  onToggleEnabled={() => strategy.status === 'ACTIVE' ? pauseStrategy(strategy.id) : resumeStrategy(strategy.id)}
                  onSetDefault={() => handleSetDefault(strategy.id)}
                  onDelete={() => handleDeleteStrategy(strategy.id)}
                  onDuplicate={() => handleDuplicateStrategy(strategy)}
                  onEdit={() => openEditModal(strategy)}
                />
              ))}
            </div>
          </div>
        </main>
      </div>

      {/* Create/Edit Strategy Modal */}
      <CreateStrategyModal
        isOpen={isModalOpen}
        onClose={closeModal}
        onCreateStrategy={(strategy) => {
          if (selectedStrategy) {
            handleEditStrategy(strategy as any)
          } else {
            handleCreateStrategy(strategy as any)
          }
        }}
        editStrategy={selectedStrategy}
        onEditComplete={closeModal}
      />

      {/* Delete Confirmation Dialog */}
      {deleteConfirmation.isOpen && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-card border border-border rounded-2xl shadow-2xl max-w-md w-full p-6">
            <div className="space-y-4">
              <div className="text-center">
                <h3 className="text-lg font-semibold text-foreground">Delete Strategy</h3>
                <p className="text-sm text-muted-foreground mt-2">
                  Are you sure you want to delete "<span className="font-medium">{deleteConfirmation.strategy?.name}</span>"?
                  This action cannot be undone.
                </p>
              </div>

              <div className="flex gap-3 justify-end">
                <Button
                  variant="outline"
                  onClick={cancelDeleteStrategy}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={confirmDeleteStrategy}
                  className="flex-1"
                >
                  Delete
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
