'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Plus, Search, Settings, Lock, LockOpen, Star, Copy, Trash2, Edit } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { CreateStrategyModal } from '@/components/trading/CreateStrategyModal'
import { StrategyCard } from '@/components/trading/StrategyCard'
import { Sidebar } from '@/components/layout/Sidebar'
import { Header } from '@/components/layout/Header'
import { useExitStrategyStore } from '@/stores/exitStrategyStore.tsx'

// Mock data based on the reference images
const mockStrategies = [
  {
    id: '1',
    name: 'Conservative',
    description: 'Low risk, steady profits',
    usageCount: 12,
    winRate: 73,
    isDefault: true,
    locked: false,
    stopLoss: { percentage: 10 },
    profitTargets: [
      { target: 25, sellPercentage: 20 },
      { target: 50, sellPercentage: 30 },
      { target: 75, sellPercentage: 25 }
    ],
    trailingStop: { percentage: 15 },
    moonBag: null
  },
  {
    id: '2',
    name: 'Aggressive TP',
    description: 'Higher risk, bigger rewards',
    usageCount: 8,
    winRate: 65,
    isDefault: false,
    locked: false,
    stopLoss: { percentage: 15 },
    profitTargets: [
      { target: 50, sellPercentage: 15 },
      { target: 100, sellPercentage: 20 },
      { target: 150, sellPercentage: 25 },
      { target: 200, sellPercentage: 15 }
    ],
    trailingStop: { percentage: 15 },
    moonBag: { percentage: 25, targetGain: 500 }
  },
  {
    id: '3',
    name: 'Trailing Only',
    description: 'Pure trailing stop strategy',
    usageCount: 3,
    winRate: 80,
    isDefault: false,
    locked: true,
    stopLoss: null,
    profitTargets: [],
    trailingStop: { percentage: 15 },
    moonBag: null
  },
  {
    id: '4',
    name: 'Moon Bag',
    description: 'Hold majority for massive gains',
    usageCount: 12,
    winRate: 73,
    isDefault: false,
    locked: false,
    stopLoss: { percentage: 15 },
    profitTargets: [
      { target: 30, sellPercentage: 10 },
      { target: 75, sellPercentage: 15 },
      { target: 150, sellPercentage: 20 }
    ],
    trailingStop: { percentage: 15 },
    moonBag: { percentage: 35, targetGain: 500 }
  },
  {
    id: '5',
    name: 'Quick Scalp',
    description: 'Fast profits with tight stops',
    usageCount: 12,
    winRate: 73,
    isDefault: false,
    locked: false,
    stopLoss: { percentage: 5 },
    profitTargets: [
      { target: 15, sellPercentage: 50 },
      { target: 30, sellPercentage: 50 }
    ],
    trailingStop: { percentage: 8 },
    moonBag: null
  },
  {
    id: '6',
    name: 'Aggressive',
    description: 'Maximum risk, maximum rewards',
    usageCount: 12,
    winRate: 73,
    isDefault: false,
    locked: false,
    stopLoss: { percentage: 20 },
    profitTargets: [
      { target: 50, sellPercentage: 15 },
      { target: 100, sellPercentage: 25 },
      { target: 200, sellPercentage: 35 }
    ],
    trailingStop: { percentage: 20 },
    moonBag: { percentage: 25, targetGain: 500 }
  }
]

type Strategy = typeof mockStrategies[0]

export default function ExitStrategiesPage() {
  const { 
    strategies, 
    addStrategy, 
    updateStrategy, 
    deleteStrategy,
    setDefaultStrategy
  } = useExitStrategyStore()
  
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedStrategy, setSelectedStrategy] = useState<any>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  // Filter strategies based on search
  const filteredStrategies = strategies.filter(strategy =>
    strategy.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    strategy.description.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const handleToggleLock = (strategyId: string) => {
    const strategy = strategies.find(s => s.id === strategyId)
    if (strategy) {
      updateStrategy(strategyId, { locked: !strategy.locked })
    }
  }

  const handleSetDefault = (strategyId: string) => {
    // Check if strategy is locked
    const strategy = strategies.find(s => s.id === strategyId)
    if (strategy?.locked) {
      console.warn('Cannot set locked strategy as default')
      return
    }

    // Use atomic operation to set new default (removes default from all others)
    setDefaultStrategy(strategyId)
  }

  const handleDeleteStrategy = (strategyId: string) => {
    // Check if strategy is locked
    const strategy = strategies.find(s => s.id === strategyId)
    if (strategy?.locked) {
      console.warn('Cannot delete locked strategy')
      return
    }

    deleteStrategy(strategyId)
  }

  const handleDuplicateStrategy = (strategy: any) => {
    addStrategy({
      name: `${strategy.name} Copy`,
      description: strategy.description,
      riskLevel: strategy.riskLevel,
      stopLoss: strategy.stopLoss,
      profitTargets: strategy.profitTargets,
      trailingStop: strategy.trailingStop,
      moonBag: strategy.moonBag,
      isDefault: false,
      locked: false
    })
  }

  const handleCreateStrategy = (newStrategy: any) => {
    // Convert CreateStrategyModal format to store format
    addStrategy({
      name: newStrategy.name,
      description: newStrategy.description || '',
      riskLevel: newStrategy.locked ? 'LOW' : 'MEDIUM',
      stopLoss: newStrategy.stopLoss,
      profitTargets: newStrategy.profitTargets,
      trailingStop: newStrategy.trailingStop,
      moonBag: newStrategy.moonBag,
      isDefault: newStrategy.isDefault || false,
      locked: newStrategy.locked || false
    })
  }

  const handleEditStrategy = (updatedStrategy: any) => {
    if (!selectedStrategy) return
    
    updateStrategy(selectedStrategy.id, {
      name: updatedStrategy.name,
      description: updatedStrategy.description || '',
      riskLevel: updatedStrategy.locked ? 'LOW' : 'MEDIUM',
      stopLoss: updatedStrategy.stopLoss,
      profitTargets: updatedStrategy.profitTargets,
      trailingStop: updatedStrategy.trailingStop,
      moonBag: updatedStrategy.moonBag,
      isDefault: updatedStrategy.isDefault || false,
      locked: updatedStrategy.locked || false
    })
  }

  const openCreateModal = () => {
    setSelectedStrategy(null)
    setIsModalOpen(true)
  }

  const openEditModal = (strategy: Strategy) => {
    if (strategy.locked) {
      console.warn('Cannot edit locked strategy')
      return
    }
    setSelectedStrategy(strategy)
    setIsModalOpen(true)
  }

  const closeModal = () => {
    setIsModalOpen(false)
    setSelectedStrategy(null)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20 text-foreground">
      <Header />

      <div className="flex">
        <Sidebar />

        <main className="flex-1 p-6">
          <div className="max-w-7xl mx-auto space-y-6">
            {/* Header Section */}
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-3xl font-bold tracking-tight">Exit Strategies</h2>
                <p className="text-muted-foreground mt-2">
                  Create and manage automated exit strategies for your trading positions
                </p>
              </div>
              <Button
                onClick={openCreateModal}
                className="flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                New Strategy
              </Button>
            </div>

            {/* Search and Filters */}
            <div className="flex items-center gap-4">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search strategies..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="flex items-center gap-1">
                  <Settings className="w-3 h-3" />
                  {strategies.length} Strategies
                </Badge>
              </div>
            </div>

            {/* Strategies Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
              {/* Strategy Cards */}
              {filteredStrategies.map((strategy) => (
                <StrategyCard
                  key={strategy.id}
                  strategy={strategy}
                  onToggleLock={() => handleToggleLock(strategy.id)}
                  onSetDefault={() => handleSetDefault(strategy.id)}
                  onDelete={() => handleDeleteStrategy(strategy.id)}
                  onDuplicate={() => handleDuplicateStrategy(strategy)}
                  onEdit={() => openEditModal(strategy)}
                />
              ))}
            </div>
          </div>
        </main>
      </div>

      {/* Create/Edit Strategy Modal */}
      <CreateStrategyModal
        isOpen={isModalOpen}
        onClose={closeModal}
        onCreateStrategy={(strategy) => {
          if (selectedStrategy) {
            handleEditStrategy(strategy as any)
          } else {
            handleCreateStrategy(strategy as any)
          }
        }}
        editStrategy={selectedStrategy}
        onEditComplete={closeModal}
      />
    </div>
  )
}