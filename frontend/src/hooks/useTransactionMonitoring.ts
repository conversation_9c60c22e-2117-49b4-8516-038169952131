'use client'

import React from 'react'
import { useTransactionStore } from '@/stores/transactionStore'
import { useError<PERSON>andler } from '@/stores/errorStore'
import { useWebSocketStore } from '@/stores/websocketStore'

interface TransactionMonitoringConfig {
  autoRefresh: boolean
  refreshInterval: number
  enableRealTimeUpdates: boolean
  enableErrorTracking: boolean
  maxRetries: number
}

interface TransactionUpdate {
  hash: string
  status: 'PENDING' | 'CONFIRMED' | 'FAILED'
  confirmationTime?: number
  blockNumber?: number
  gasUsed?: number
  error?: string
}

interface TransactionMonitoringHook {
  // State
  isMonitoring: boolean
  pendingTransactions: string[]
  failedTransactions: string[]
  lastUpdate: Date | null
  
  // Actions
  startMonitoring: (config?: Partial<TransactionMonitoringConfig>) => void
  stopMonitoring: () => void
  trackTransaction: (hash: string) => void
  untrackTransaction: (hash: string) => void
  
  // Real-time updates
  subscribeToUpdates: () => void
  unsubscribeFromUpdates: () => void
  
  // Error handling
  retryFailedTransaction: (hash: string) => Promise<boolean>
  clearFailedTransactions: () => void
  
  // Statistics
  getMonitoringStats: () => {
    totalTracked: number
    pending: number
    confirmed: number
    failed: number
    successRate: number
  }
}

const DEFAULT_CONFIG: TransactionMonitoringConfig = {
  autoRefresh: true,
  refreshInterval: 30000, // 30 seconds
  enableRealTimeUpdates: true,
  enableErrorTracking: true,
  maxRetries: 3
}

export const useTransactionMonitoring = (): TransactionMonitoringHook => {
  const transactionStore = useTransactionStore()
  const { handleError, retryWithBackoff } = useErrorHandler()
  const wsStore = useWebSocketStore()
  
  const [isMonitoring, setIsMonitoring] = React.useState(false)
  const [config, setConfig] = React.useState<TransactionMonitoringConfig>(DEFAULT_CONFIG)
  const [pendingTransactions, setPendingTransactions] = React.useState<string[]>([])
  const [failedTransactions, setFailedTransactions] = React.useState<string[]>([])
  const [trackedTransactions, setTrackedTransactions] = React.useState<Set<string>>(new Set())
  const [lastUpdate, setLastUpdate] = React.useState<Date | null>(null)
  
  // Refs for cleanup
  const refreshIntervalRef = React.useRef<NodeJS.Timeout | null>(null)
  const wsUnsubscribeRef = React.useRef<(() => void) | null>(null)

  // Start monitoring with configuration
  const startMonitoring = React.useCallback((customConfig?: Partial<TransactionMonitoringConfig>) => {
    const finalConfig = { ...DEFAULT_CONFIG, ...customConfig }
    setConfig(finalConfig)
    setIsMonitoring(true)

    console.log('🔍 Starting transaction monitoring:', finalConfig)

    // Start auto-refresh if enabled
    if (finalConfig.autoRefresh) {
      refreshIntervalRef.current = setInterval(() => {
        transactionStore.refreshTransactions()
        setLastUpdate(new Date())
      }, finalConfig.refreshInterval)
    }

    // Subscribe to real-time updates if enabled
    if (finalConfig.enableRealTimeUpdates) {
      subscribeToUpdates()
    }

    // Initial load
    if (transactionStore.transactions.length === 0) {
      transactionStore.fetchTransactions()
    }
  }, [transactionStore])

  // Stop monitoring
  const stopMonitoring = React.useCallback(() => {
    setIsMonitoring(false)
    
    // Clear auto-refresh
    if (refreshIntervalRef.current) {
      clearInterval(refreshIntervalRef.current)
      refreshIntervalRef.current = null
    }

    // Unsubscribe from real-time updates
    unsubscribeFromUpdates()

    console.log('⏹️ Transaction monitoring stopped')
  }, [])

  // Track specific transaction
  const trackTransaction = React.useCallback((hash: string) => {
    setTrackedTransactions(prev => new Set(prev).add(hash))
    setPendingTransactions(prev => [...prev, hash])
    
    console.log(`🎯 Tracking transaction: ${hash}`)

    // Subscribe to updates for this specific transaction
    if (config.enableRealTimeUpdates) {
      wsStore.subscribeToTransactions([hash])
    }
  }, [config.enableRealTimeUpdates, wsStore])

  // Untrack transaction
  const untrackTransaction = React.useCallback((hash: string) => {
    setTrackedTransactions(prev => {
      const newSet = new Set(prev)
      newSet.delete(hash)
      return newSet
    })
    setPendingTransactions(prev => prev.filter(h => h !== hash))
    setFailedTransactions(prev => prev.filter(h => h !== hash))
    
    console.log(`🚫 Untracking transaction: ${hash}`)
  }, [])

  // Subscribe to real-time transaction updates
  const subscribeToUpdates = React.useCallback(() => {
    if (wsUnsubscribeRef.current) return // Already subscribed

    wsUnsubscribeRef.current = wsStore.onTransactionUpdate((update: TransactionUpdate) => {
      const { hash, status, confirmationTime, blockNumber, gasUsed, error } = update
      
      console.log(`📡 Transaction update received: ${hash} -> ${status}`)

      // Update transaction in store
      const existingTransaction = transactionStore.transactions.find(tx => tx.hash === hash)
      if (existingTransaction) {
        transactionStore.updateTransaction(existingTransaction.id, {
          status: status === 'CONFIRMED' ? 'CONFIRMED' : status === 'FAILED' ? 'FAILED' : 'PENDING',
          confirmationTime: confirmationTime ? new Date(confirmationTime) : undefined,
          blockNumber,
          gasUsed
        })
      }

      // Update tracking state
      if (status === 'CONFIRMED') {
        setPendingTransactions(prev => prev.filter(h => h !== hash))
        setFailedTransactions(prev => prev.filter(h => h !== hash))
      } else if (status === 'FAILED') {
        setPendingTransactions(prev => prev.filter(h => h !== hash))
        setFailedTransactions(prev => [...prev.filter(h => h !== hash), hash])
        
        // Handle error if error tracking is enabled
        if (config.enableErrorTracking && error) {
          handleError({
            code: 'TRANSACTION_FAILED',
            message: `Transaction ${hash.slice(0, 8)}... failed: ${error}`,
            category: 'TRANSACTION',
            severity: 'HIGH',
            isRetryable: true,
            recommendations: [
              'Check transaction details',
              'Verify network conditions',
              'Try again with higher priority fee'
            ],
            context: { transactionHash: hash }
          })
        }
      }

      setLastUpdate(new Date())
    })

    console.log('📡 Subscribed to real-time transaction updates')
  }, [config.enableErrorTracking, handleError, transactionStore, wsStore])

  // Unsubscribe from real-time updates
  const unsubscribeFromUpdates = React.useCallback(() => {
    if (wsUnsubscribeRef.current) {
      wsUnsubscribeRef.current()
      wsUnsubscribeRef.current = null
      console.log('📡 Unsubscribed from transaction updates')
    }
  }, [])

  // Retry failed transaction
  const retryFailedTransaction = React.useCallback(async (hash: string): Promise<boolean> => {
    console.log(`🔄 Retrying failed transaction: ${hash}`)
    
    try {
      // In a real implementation, this would involve:
      // 1. Getting the original transaction parameters
      // 2. Re-executing the transaction with potentially adjusted parameters
      // 3. Tracking the new transaction hash
      
      const success = await retryWithBackoff(async () => {
        // Fetch transaction details to understand failure
        const response = await fetch(`/api/transactions/${hash}`)
        if (!response.ok) {
          throw new Error('Failed to fetch transaction details for retry')
        }
        
        // For now, just mark as attempting retry
        console.log(`🔄 Transaction retry attempted for ${hash}`)
      }, 'TRANSACTION')

      if (success) {
        setFailedTransactions(prev => prev.filter(h => h !== hash))
        console.log(`✅ Transaction retry succeeded for ${hash}`)
      }

      return success
    } catch (error) {
      console.error(`❌ Transaction retry failed for ${hash}:`, error)
      return false
    }
  }, [retryWithBackoff])

  // Clear failed transactions
  const clearFailedTransactions = React.useCallback(() => {
    setFailedTransactions([])
    console.log('🧹 Cleared failed transactions')
  }, [])

  // Get monitoring statistics
  const getMonitoringStats = React.useCallback(() => {
    const totalTracked = trackedTransactions.size
    const pending = pendingTransactions.length
    const failed = failedTransactions.length
    const confirmed = totalTracked - pending - failed
    const successRate = totalTracked > 0 ? (confirmed / totalTracked) * 100 : 0

    return {
      totalTracked,
      pending,
      confirmed,
      failed,
      successRate
    }
  }, [trackedTransactions.size, pendingTransactions.length, failedTransactions.length])

  // Auto-track new transactions from store
  React.useEffect(() => {
    if (!isMonitoring) return

    const recentTransactions = transactionStore.transactions
      .filter(tx => tx.timestamp > new Date(Date.now() - 60000)) // Last minute
      .filter(tx => !trackedTransactions.has(tx.hash))

    recentTransactions.forEach(tx => {
      if (tx.hash && !trackedTransactions.has(tx.hash)) {
        trackTransaction(tx.hash)
      }
    })
  }, [transactionStore.transactions, isMonitoring, trackedTransactions, trackTransaction])

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      stopMonitoring()
    }
  }, [stopMonitoring])

  return {
    isMonitoring,
    pendingTransactions,
    failedTransactions,
    lastUpdate,
    startMonitoring,
    stopMonitoring,
    trackTransaction,
    untrackTransaction,
    subscribeToUpdates,
    unsubscribeFromUpdates,
    retryFailedTransaction,
    clearFailedTransactions,
    getMonitoringStats
  }
}

// Hook for monitoring specific transaction
export const useTransactionTracker = (hash: string) => {
  const monitoring = useTransactionMonitoring()
  const [status, setStatus] = React.useState<'PENDING' | 'CONFIRMED' | 'FAILED' | 'UNKNOWN'>('UNKNOWN')
  const [details, setDetails] = React.useState<any>(null)

  React.useEffect(() => {
    if (hash) {
      monitoring.trackTransaction(hash)
      
      // Fetch initial status
      fetch(`/api/transactions/${hash}`)
        .then(res => res.json())
        .then(data => {
          if (data.success) {
            setDetails(data.data)
            setStatus(data.data.status || 'PENDING')
          }
        })
        .catch(err => {
          console.error('Failed to fetch transaction details:', err)
          setStatus('UNKNOWN')
        })
    }

    return () => {
      if (hash) monitoring.untrackTransaction(hash)
    }
  }, [hash, monitoring])

  return {
    status,
    details,
    isPending: status === 'PENDING',
    isConfirmed: status === 'CONFIRMED',
    isFailed: status === 'FAILED',
    retry: () => monitoring.retryFailedTransaction(hash)
  }
}