'use client'

import { useEffect, useRef, useState, useCallback } from 'react'
import { useAlertStore } from '@/stores/alertStore'
import { Alert } from 'shared/src/types/alerts'

interface WebSocketMessage {
  type: 'alert' | 'ping' | 'error' | 'connected' | 'disconnected'
  data?: any
  timestamp?: string
}

interface UseWebSocketOptions {
  url?: string
  autoConnect?: boolean
  reconnectAttempts?: number
  reconnectInterval?: number
  pingInterval?: number
}

interface WebSocketState {
  isConnected: boolean
  isConnecting: boolean
  error: string | null
  lastMessage: WebSocketMessage | null
  connectionAttempts: number
}

export function useWebSocket(options: UseWebSocketOptions = {}) {
  const {
    url = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8080/ws/alerts',
    autoConnect = true,
    reconnectAttempts = 5,
    reconnectInterval = 3000,
    pingInterval = 30000
  } = options
  
  const wsRef = useRef<WebSocket | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const pingIntervalRef = useRef<NodeJS.Interval | null>(null)
  const { addAlert } = useAlertStore()
  
  const [state, setState] = useState<WebSocketState>({
    isConnected: false,
    isConnecting: false,
    error: null,
    lastMessage: null,
    connectionAttempts: 0
  })
  
  const clearTimeouts = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }
    if (pingIntervalRef.current) {
      clearInterval(pingIntervalRef.current)
      pingIntervalRef.current = null
    }
  }, [])
  
  const handleMessage = useCallback((event: MessageEvent) => {
    try {
      const message: WebSocketMessage = JSON.parse(event.data)
      
      setState(prev => ({ ...prev, lastMessage: message, error: null }))
      
      switch (message.type) {
        case 'alert':
          if (message.data) {
            // Add the new alert to the store
            addAlert(message.data)
          }
          break
        case 'ping':
          // Respond to ping with pong
          if (wsRef.current?.readyState === WebSocket.OPEN) {
            wsRef.current.send(JSON.stringify({ type: 'pong' }))
          }
          break
        case 'error':
          setState(prev => ({ ...prev, error: message.data?.message || 'WebSocket error' }))
          break
        default:
          console.log('Unhandled WebSocket message type:', message.type)
      }
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error)
      setState(prev => ({ ...prev, error: 'Invalid message format' }))
    }
  }, [addAlert])
  
  const startPing = useCallback(() => {
    if (pingIntervalRef.current) {
      clearInterval(pingIntervalRef.current)
    }
    
    pingIntervalRef.current = setInterval(() => {
      if (wsRef.current?.readyState === WebSocket.OPEN) {
        wsRef.current.send(JSON.stringify({ type: 'ping' }))
      }
    }, pingInterval)
  }, [pingInterval])
  
  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return // Already connected
    }
    
    setState(prev => ({ 
      ...prev, 
      isConnecting: true, 
      error: null,
      connectionAttempts: prev.connectionAttempts + 1
    }))
    
    try {
      const ws = new WebSocket(url)
      wsRef.current = ws
      
      ws.onopen = () => {
        setState(prev => ({ 
          ...prev, 
          isConnected: true, 
          isConnecting: false, 
          error: null,
          connectionAttempts: 0
        }))
        startPing()
        console.log('WebSocket connected')
      }
      
      ws.onmessage = handleMessage
      
      ws.onclose = (event) => {
        setState(prev => ({ 
          ...prev, 
          isConnected: false, 
          isConnecting: false,
          error: event.reason || 'Connection closed'
        }))
        clearTimeouts()
        
        // Attempt to reconnect if not intentionally closed
        if (event.code !== 1000 && state.connectionAttempts < reconnectAttempts) {
          console.log(`WebSocket closed, attempting to reconnect... (${state.connectionAttempts}/${reconnectAttempts})`)
          reconnectTimeoutRef.current = setTimeout(connect, reconnectInterval)
        }
      }
      
      ws.onerror = (error) => {
        console.error('WebSocket error:', error)
        setState(prev => ({ 
          ...prev, 
          error: 'Connection error',
          isConnecting: false
        }))
      }
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: 'Failed to create WebSocket connection',
        isConnecting: false
      }))
    }
  }, [url, handleMessage, startPing, reconnectAttempts, reconnectInterval, state.connectionAttempts])
  
  const disconnect = useCallback(() => {
    clearTimeouts()
    
    if (wsRef.current) {
      wsRef.current.close(1000, 'Intentional disconnect')
      wsRef.current = null
    }
    
    setState(prev => ({ 
      ...prev, 
      isConnected: false, 
      isConnecting: false,
      connectionAttempts: 0
    }))
  }, [clearTimeouts])
  
  const sendMessage = useCallback((message: Omit<WebSocketMessage, 'timestamp'>) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      const messageWithTimestamp = {
        ...message,
        timestamp: new Date().toISOString()
      }
      wsRef.current.send(JSON.stringify(messageWithTimestamp))
      return true
    }
    return false
  }, [])
  
  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect) {
      connect()
    }
    
    return () => {
      disconnect()
    }
  }, [autoConnect, connect, disconnect])
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearTimeouts()
      if (wsRef.current) {
        wsRef.current.close()
      }
    }
  }, [clearTimeouts])
  
  return {
    ...state,
    connect,
    disconnect,
    sendMessage,
    reconnect: connect
  }
}

// Hook specifically for alert notifications
export function useAlertWebSocket() {
  const webSocket = useWebSocket({
    autoConnect: true,
    reconnectAttempts: 5,
    reconnectInterval: 3000,
    pingInterval: 30000
  })
  
  const subscribeToAlerts = useCallback((filters?: {
    types?: string[]
    priorities?: string[]
    tokens?: string[]
  }) => {
    return webSocket.sendMessage({
      type: 'alert',
      data: {
        action: 'subscribe',
        filters
      }
    })
  }, [webSocket])
  
  const unsubscribeFromAlerts = useCallback(() => {
    return webSocket.sendMessage({
      type: 'alert',
      data: {
        action: 'unsubscribe'
      }
    })
  }, [webSocket])
  
  return {
    ...webSocket,
    subscribeToAlerts,
    unsubscribeFromAlerts
  }
}

// Mock WebSocket server simulation for development
export function useMockAlertStream(enabled = process.env.NODE_ENV === 'development') {
  const { addAlert } = useAlertStore()
  const intervalRef = useRef<NodeJS.Interval | null>(null)
  
  useEffect(() => {
    if (!enabled) return
    
    // Simulate random alerts every 15-45 seconds
    const startMockStream = () => {
      const generateRandomAlert = () => {
        const types = ['TRADE', 'EXIT', 'ERROR', 'SYSTEM', 'PRICE', 'STRATEGY']
        const priorities = ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']
        const tokens = ['PEPE', 'BONK', 'DOGE', 'SHIB', 'WIF', 'FLOKI']
        
        const mockAlert = {
          userId: 'user-123',
          type: types[Math.floor(Math.random() * types.length)] as any,
          priority: priorities[Math.floor(Math.random() * priorities.length)] as any,
          title: 'Live Alert Simulation',
          message: `Simulated ${types[Math.floor(Math.random() * types.length)].toLowerCase()} alert for testing`,
          metadata: {
            token: { symbol: tokens[Math.floor(Math.random() * tokens.length)] },
            simulation: true
          },
          read: false,
          actionable: Math.random() > 0.7
        }
        
        addAlert(mockAlert)
      }
      
      intervalRef.current = setInterval(() => {
        generateRandomAlert()
      }, 15000 + Math.random() * 30000) // 15-45 seconds
    }
    
    // Start after a short delay
    const timeout = setTimeout(startMockStream, 5000)
    
    return () => {
      clearTimeout(timeout)
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [enabled, addAlert])
  
  return {
    isSimulating: enabled && intervalRef.current !== null
  }
}