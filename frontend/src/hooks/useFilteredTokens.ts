'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { useWalletBalance } from './useWalletBalance'

interface Token {
  address: string
  symbol: string
  name: string
  logoURI?: string
  decimals: number
  verified?: boolean
  balance?: number
  valueUSD?: number
}

interface UseFilteredTokensResult {
  availableTokens: Token[]
  loading: boolean
  error: string | null
  refresh: () => Promise<void>
  getTokenBalance: (tokenAddress: string) => number
  getTokenValueUSD: (tokenAddress: string) => number
}

/**
 * Hook to get user's available tokens filtered by minimum USD value
 */
export const useFilteredTokens = (
  minValueUSD: number = 0.50,
  includeZeroBalance: boolean = true
): UseFilteredTokensResult => {
  const [popularTokens, setPopularTokens] = useState<Token[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const { 
    balance: walletBalance, 
    solBalance, 
    tokenBalances, 
    loading: balanceLoading,
    error: balanceError,
    refresh: refreshBalance,
    getTokenBalance,
  } = useWalletBalance(minValueUSD, true)

  // Fetch popular tokens from Jupiter
  const fetchPopularTokens = useCallback(async () => {
    try {
      const response = await fetch('https://token.jup.ag/popular', { timeout: 10000 })
      if (!response.ok) {
        throw new Error('Failed to fetch popular tokens')
      }
      const tokens = await response.json()
      
      // Transform to our token format
      const formattedTokens: Token[] = tokens.slice(0, 50).map((token: any) => ({
        address: token.address,
        symbol: token.symbol,
        name: token.name,
        logoURI: token.logoURI,
        decimals: token.decimals,
        verified: true,
        balance: 0,
        valueUSD: 0
      }))
      
      setPopularTokens(formattedTokens)
    } catch (err) {
      console.error('Failed to fetch popular tokens:', err)
      // Set default popular tokens if fetch fails
      setPopularTokens([
        {
          address: 'So11111111111111111111111111111111111111112',
          symbol: 'SOL',
          name: 'Solana',
          decimals: 9,
          verified: true,
          balance: 0,
          valueUSD: 0
        },
        {
          address: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
          symbol: 'USDC',
          name: 'USD Coin',
          decimals: 6,
          verified: true,
          balance: 0,
          valueUSD: 0
        }
      ])
    }
  }, [])

  // Initialize
  useEffect(() => {
    const initialize = async () => {
      setLoading(true)
      setError(null)
      
      try {
        await fetchPopularTokens()
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to initialize')
      } finally {
        setLoading(false)
      }
    }
    
    initialize()
  }, [fetchPopularTokens])

  // Get token value in USD
  const getTokenValueUSD = useCallback((tokenAddress: string): number => {
    if (!walletBalance) return 0
    
    if (tokenAddress === 'So11111111111111111111111111111111111111112') {
      // SOL token - estimate USD value from balance
      return solBalance * 100 // Rough estimate, should use real price
    }
    
    const token = tokenBalances.find(t => t.address === tokenAddress)
    return token ? token.valueUSD : 0
  }, [walletBalance, solBalance, tokenBalances])

  // Merge user tokens with popular tokens and apply filtering
  const availableTokens = useMemo(() => {
    const tokensMap = new Map<string, Token>()
    
    // Add popular tokens
    popularTokens.forEach(token => {
      const balance = getTokenBalance(token.address)
      const valueUSD = getTokenValueUSD(token.address)
      
      tokensMap.set(token.address, {
        ...token,
        balance,
        valueUSD
      })
    })
    
    // Add user's tokens with significant balances
    if (walletBalance) {
      // Add SOL
      tokensMap.set('So11111111111111111111111111111111111111112', {
        address: 'So11111111111111111111111111111111111111112',
        symbol: 'SOL',
        name: 'Solana',
        decimals: 9,
        verified: true,
        balance: solBalance,
        valueUSD: solBalance * 100 // Rough estimate
      })
      
      // Add user's token balances
      tokenBalances.forEach(token => {
        tokensMap.set(token.address, {
          address: token.address,
          symbol: token.symbol,
          name: token.name,
          logoURI: token.logoURI,
          decimals: token.decimals,
          verified: false,
          balance: token.uiAmount,
          valueUSD: token.valueUSD
        })
      })
    }
    
    // Convert to array and apply filters
    let tokens = Array.from(tokensMap.values())
    
    if (!includeZeroBalance) {
      // Only show tokens with balance > 0 OR popular tokens
      tokens = tokens.filter(token => 
        token.balance > 0 || 
        token.verified ||
        token.address === 'So11111111111111111111111111111111111111112'
      )
    }
    
    // Apply minimum USD value filter (but keep verified popular tokens)
    tokens = tokens.filter(token => 
      token.valueUSD >= minValueUSD || 
      token.verified ||
      token.address === 'So11111111111111111111111111111111111111112'
    )
    
    // Sort by value USD descending, then by balance
    return tokens.sort((a, b) => {
      if (b.valueUSD !== a.valueUSD) {
        return b.valueUSD - a.valueUSD
      }
      return (b.balance || 0) - (a.balance || 0)
    })
  }, [popularTokens, walletBalance, solBalance, tokenBalances, getTokenBalance, getTokenValueUSD, minValueUSD, includeZeroBalance])

  const refresh = useCallback(async () => {
    await Promise.all([
      fetchPopularTokens(),
      refreshBalance()
    ])
  }, [fetchPopularTokens, refreshBalance])

  return {
    availableTokens,
    loading: loading || balanceLoading,
    error: error || balanceError,
    refresh,
    getTokenBalance,
    getTokenValueUSD
  }
}