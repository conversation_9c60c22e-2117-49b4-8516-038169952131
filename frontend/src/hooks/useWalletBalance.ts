'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { useWebSocketStore } from '@/stores/websocketStore'

interface TokenBalance {
  address: string
  symbol: string
  name: string
  decimals: number
  balance: number
  uiAmount: number
  valueUSD: number
  logoURI?: string
}

interface WalletBalance {
  solBalance: number
  tokenBalances: TokenBalance[]
  totalValueUSD: number
  lastUpdated: number
}

interface UseWalletBalanceResult {
  balance: WalletBalance | null
  solBalance: number
  tokenBalances: TokenBalance[]
  totalValueUSD: number
  loading: boolean
  error: string | null
  refresh: () => Promise<void>
  getTokenBalance: (tokenAddress: string) => number
}

export const useWalletBalance = (
  minValueUSD: number = 0.5,
  autoRefresh: boolean = true,
  refreshInterval: number = 30000 // 30 seconds
): UseWalletBalanceResult => {
  const [balance, setBalance] = useState<WalletBalance | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchBalance = useCallback(async () => {
    try {
      setError(null)
      const response = await fetch(`/api/wallet/balance?minValue=${minValueUSD}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch wallet balance: ${response.statusText}`)
      }

      const data = await response.json()
      if (data.success) {
        setBalance(data.data)
      } else {
        throw new Error(data.message || 'Failed to fetch wallet balance')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      console.error('Error fetching wallet balance:', err)
    } finally {
      setLoading(false)
    }
  }, [minValueUSD])

  const refresh = useCallback(async () => {
    setLoading(true)
    await fetchBalance()
  }, [fetchBalance])

  const getTokenBalance = useCallback((tokenAddress: string): number => {
    if (!balance) return 0
    
    // Check if it's SOL
    if (tokenAddress === 'So11111111111111111111111111111111111111112') {
      return balance.solBalance
    }
    
    // Find token in balance list
    const token = balance.tokenBalances.find(t => t.address === tokenAddress)
    return token ? token.uiAmount : 0
  }, [balance])

  // Initial fetch
  useEffect(() => {
    fetchBalance()
  }, [fetchBalance]) // Keep fetchBalance but it's properly memoized

  // Auto-refresh interval
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(() => {
      fetchBalance()
    }, refreshInterval)

    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval, fetchBalance]) // Remove connected dependency

  // Balance refreshes are handled by the interval above
  // Real-time price updates will be reflected in subsequent API calls

  return useMemo(() => ({
    balance,
    solBalance: balance?.solBalance || 0,
    tokenBalances: balance?.tokenBalances || [],
    totalValueUSD: balance?.totalValueUSD || 0,
    loading,
    error,
    refresh,
    getTokenBalance
  }), [balance, loading, error, refresh, getTokenBalance])
}

// Hook for getting SOL balance only (lighter weight)
export const useSOLBalance = () => {
  const [solBalance, setSolBalance] = useState<number>(0)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchSOLBalance = useCallback(async () => {
    try {
      setError(null)
      const response = await fetch('/api/wallet/balance/sol', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch SOL balance: ${response.statusText}`)
      }

      const data = await response.json()
      if (data.success) {
        setSolBalance(data.data.balance)
      } else {
        throw new Error(data.message || 'Failed to fetch SOL balance')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      console.error('Error fetching SOL balance:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  const refresh = useCallback(async () => {
    setLoading(true)
    await fetchSOLBalance()
  }, [fetchSOLBalance])

  useEffect(() => {
    fetchSOLBalance()
  }, [fetchSOLBalance])

  return {
    solBalance,
    loading,
    error,
    refresh
  }
}

// Hook for getting specific token balance
export const useTokenBalance = (tokenAddress: string) => {
  const [balance, setBalance] = useState<number>(0)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchTokenBalance = useCallback(async () => {
    if (!tokenAddress) return

    try {
      setError(null)
      const response = await fetch(`/api/wallet/balance/token/${tokenAddress}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch token balance: ${response.statusText}`)
      }

      const data = await response.json()
      if (data.success) {
        setBalance(data.data.balance)
      } else {
        throw new Error(data.message || 'Failed to fetch token balance')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      console.error('Error fetching token balance:', err)
    } finally {
      setLoading(false)
    }
  }, [tokenAddress])

  const refresh = useCallback(async () => {
    setLoading(true)
    await fetchTokenBalance()
  }, [fetchTokenBalance])

  useEffect(() => {
    fetchTokenBalance()
  }, [fetchTokenBalance])

  return {
    balance,
    loading,
    error,
    refresh
  }
}