'use client'

import { useState, useCallback, useEffect } from 'react'
import { formatSOL, formatSOLInput, parseSOLAmount } from '@/lib/utils'

export interface UseSOLAmountOptions {
  initialValue?: string | number
  maxDecimals?: number
  onValueChange?: (rawValue: number, formattedValue: string) => void
}

export interface UseSOLAmountReturn {
  // Display value for input fields (formatted for user)
  displayValue: string
  // Raw numeric value for calculations
  rawValue: number
  // Formatted value for display (removes trailing zeros)
  formattedValue: string
  // Input change handler
  handleInputChange: (value: string) => void
  // Set value programmatically 
  setValue: (value: number | string) => void
  // Clear the value
  clearValue: () => void
  // Validate if the current value is valid
  isValid: boolean
}

/**
 * Custom hook for managing SOL amounts with smart formatting
 * Separates display values from calculation values for better UX
 * 
 * @param options Configuration options
 * @returns Object with display/raw values and handlers
 */
export function useSOLAmount(options: UseSOLAmountOptions = {}): UseSOLAmountReturn {
  const {
    initialValue = '',
    maxDecimals = 9,
    onValueChange
  } = options

  // Display value - what the user sees in input fields
  const [displayValue, setDisplayValue] = useState<string>(() => {
    if (typeof initialValue === 'number') {
      return formatSOL(initialValue, maxDecimals)
    }
    return initialValue.toString()
  })

  // Raw numeric value - used for calculations
  const [rawValue, setRawValue] = useState<number>(() => {
    return parseSOLAmount(displayValue)
  })

  // Formatted value - clean display format
  const formattedValue = formatSOL(rawValue, maxDecimals)

  // Validate current value
  const isValid = !isNaN(rawValue) && rawValue >= 0

  // Handle input changes from user typing
  const handleInputChange = useCallback((value: string) => {
    // Allow empty input
    if (!value || value === '') {
      setDisplayValue('')
      setRawValue(0)
      return
    }

    // Allow partial input during typing (like "0.", ".", etc.)
    const partialNumberRegex = /^(\d*\.?\d*)$/
    if (partialNumberRegex.test(value)) {
      setDisplayValue(value)
      const parsed = parseSOLAmount(value)
      setRawValue(parsed)
      return
    }

    // If it's not a valid partial number, try to parse and format
    const parsed = parseSOLAmount(value)
    if (!isNaN(parsed)) {
      const formatted = formatSOLInput(value)
      setDisplayValue(formatted)
      setRawValue(parsed)
    }
  }, [])

  // Set value programmatically (from external sources)
  const setValue = useCallback((value: number | string) => {
    if (typeof value === 'number') {
      const formatted = formatSOL(value, maxDecimals)
      setDisplayValue(formatted)
      setRawValue(value)
    } else {
      const parsed = parseSOLAmount(value)
      const formatted = formatSOL(parsed, maxDecimals)
      setDisplayValue(formatted)
      setRawValue(parsed)
    }
  }, [maxDecimals])

  // Clear the value
  const clearValue = useCallback(() => {
    setDisplayValue('')
    setRawValue(0)
  }, [])

  // Notify parent of value changes
  useEffect(() => {
    if (onValueChange) {
      onValueChange(rawValue, formattedValue)
    }
  }, [rawValue, formattedValue, onValueChange])

  return {
    displayValue,
    rawValue,
    formattedValue,
    handleInputChange,
    setValue,
    clearValue,
    isValid
  }
}

/**
 * Specialized hook for balance display - formats balances nicely
 */
export function useSOLBalance(initialBalance: number = 0) {
  const [balance, setBalance] = useState(initialBalance)
  
  const formattedBalance = formatSOL(balance)
  
  const updateBalance = useCallback((newBalance: number) => {
    setBalance(newBalance)
  }, [])
  
  return {
    balance,
    formattedBalance,
    updateBalance
  }
}