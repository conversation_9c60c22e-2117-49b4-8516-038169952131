/**
 * Test file to demonstrate SOL formatting functions work correctly
 * This would typically be run with a test framework like Jest
 */

import { formatSOL, formatSOLBalance, parseSOLAmount, formatSOLWithUnit } from '@/lib/utils'

// Test cases demonstrating the expected behavior
console.log('=== SOL Formatting Tests ===')

// Test formatSOL function
console.log('\n--- formatSOL Tests ---')
console.log('formatSOL(5):', formatSOL(5)) // Expected: "5"
console.log('formatSOL(5.5):', formatSOL(5.5)) // Expected: "5.5"
console.log('formatSOL(5.500000):', formatSOL(5.500000)) // Expected: "5.5"
console.log('formatSOL(0.001234):', formatSOL(0.001234)) // Expected: "0.001234"
console.log('formatSOL(0.100000):', formatSOL(0.100000)) // Expected: "0.1"
console.log('formatSOL(1000.000000):', formatSOL(1000.000000)) // Expected: "1000"
console.log('formatSOL(0):', formatSOL(0)) // Expected: "0"
console.log('formatSOL(""):', formatSOL("")) // Expected: "0"

// Test parseSOLAmount function
console.log('\n--- parseSOLAmount Tests ---')
console.log('parseSOLAmount("5"):', parseSOLAmount("5")) // Expected: 5
console.log('parseSOLAmount("5.5"):', parseSOLAmount("5.5")) // Expected: 5.5
console.log('parseSOLAmount("0.001234"):', parseSOLAmount("0.001234")) // Expected: 0.001234
console.log('parseSOLAmount(""):', parseSOLAmount("")) // Expected: 0

// Test formatSOLWithUnit function
console.log('\n--- formatSOLWithUnit Tests ---')
console.log('formatSOLWithUnit(5):', formatSOLWithUnit(5)) // Expected: "5 SOL"
console.log('formatSOLWithUnit(5.5, false):', formatSOLWithUnit(5.5, false)) // Expected: "5.5"

// Test formatSOLBalance function
console.log('\n--- formatSOLBalance Tests ---')
console.log('formatSOLBalance(2.5):', formatSOLBalance(2.5)) // Expected: "2.5"
console.log('formatSOLBalance(0.001234):', formatSOLBalance(0.001234)) // Expected: "0.001234"
console.log('formatSOLBalance(1000):', formatSOLBalance(1000)) // Expected: "1000"

// Test round-trip (format -> parse -> format)
console.log('\n--- Round-trip Tests ---')
const testValues = [5, 5.5, 0.001234, 0.1, 1000, 0.000001]
testValues.forEach(value => {
  const formatted = formatSOL(value)
  const parsed = parseSOLAmount(formatted)
  const reformatted = formatSOL(parsed)
  console.log(`${value} -> "${formatted}" -> ${parsed} -> "${reformatted}"`)
})

console.log('\n=== All tests completed ===')

export {} // Make this a module