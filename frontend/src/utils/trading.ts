export const formatCurrency = (amount: number, decimals: number = 0) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(amount)
}

export const formatPrice = (price: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 8,
    maximumFractionDigits: 8,
  }).format(price)
}

export const formatTokenAmount = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount)
}

export const formatTimeAgo = (date: Date) => {
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
  
  if (diffInMinutes < 1) return 'Just now'
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`
  
  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) return `${diffInHours}h ago`
  
  const diffInDays = Math.floor(diffInHours / 24)
  return `${diffInDays}d ago`
}

export const getTokenColor = (symbol: string) => {
  const colors = {
    'PEPE': 'bg-green-500',
    'BONK': 'bg-orange-500', 
    'DOGE': 'bg-purple-500',
    'SHIB': 'bg-red-500',
    'WIF': 'bg-blue-500',
    'FLOKI': 'bg-yellow-500'
  }
  return colors[symbol as keyof typeof colors] || 'bg-blue-500'
}

export const getStatusColor = (status: string) => {
  const colors = {
    'taking_profits': 'bg-green-500/20 text-green-400 border-green-500/30',
    'approaching_target': 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
    'moon_bag': 'bg-purple-500/20 text-purple-400 border-purple-500/30',
    'trailing': 'bg-blue-500/20 text-blue-400 border-blue-500/30',
    'pending': 'bg-orange-500/20 text-orange-400 border-orange-500/30'
  }
  return colors[status as keyof typeof colors] || 'bg-gray-500/20 text-gray-400 border-gray-500/30'
}

export const getStatusLabel = (status: string) => {
  const labels = {
    'taking_profits': 'Taking Profits',
    'approaching_target': 'Approaching Target',
    'moon_bag': 'Moon Bag',
    'trailing': 'Trailing',
    'pending': 'Pending'
  }
  return labels[status as keyof typeof labels] || status
}

export const getOrderTypeLabel = (orderType: string) => {
  const labels = {
    'buy_limit': 'Buy Limit',
    'sell_limit': 'Sell Limit'
  }
  return labels[orderType as keyof typeof labels] || orderType
}