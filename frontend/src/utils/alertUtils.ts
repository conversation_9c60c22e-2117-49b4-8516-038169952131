import { Alert, AlertConfig } from 'shared/src/types/alerts'
import { AlertType, AlertPriority } from 'shared/src/types/enums'

export interface AlertFilters {
  categories: AlertType[]
  priorities: AlertPriority[]
  readStatus: 'all' | 'read' | 'unread'
  timeRange: 'today' | '24h' | '7d' | '30d' | 'custom'
  customStartDate?: Date
  customEndDate?: Date
  tokens: string[]
  searchTerm: string
}

export const getAlertPriorityColor = (priority: AlertPriority) => {
  const colors = {
    [AlertPriority.CRITICAL]: 'text-red-500 bg-red-500/10 border-red-500/30',
    [AlertPriority.HIGH]: 'text-orange-500 bg-orange-500/10 border-orange-500/30',
    [AlertPriority.MEDIUM]: 'text-yellow-500 bg-yellow-500/10 border-yellow-500/30',
    [AlertPriority.LOW]: 'text-blue-500 bg-blue-500/10 border-blue-500/30'
  }
  return colors[priority] || colors[AlertPriority.LOW]
}

export const getAlertTypeColor = (type: AlertType) => {
  const colors = {
    [AlertType.TRADE]: 'text-green-500',
    [AlertType.EXIT]: 'text-blue-500',
    [AlertType.ERROR]: 'text-red-500',
    [AlertType.SYSTEM]: 'text-purple-500',
    [AlertType.PRICE]: 'text-yellow-500',
    [AlertType.STRATEGY]: 'text-indigo-500'
  }
  return colors[type] || colors[AlertType.SYSTEM]
}

export const getAlertTypeIcon = (type: AlertType) => {
  const icons = {
    [AlertType.TRADE]: '💰',
    [AlertType.EXIT]: '🚪',
    [AlertType.ERROR]: '⚠️',
    [AlertType.SYSTEM]: '⚙️',
    [AlertType.PRICE]: '📈',
    [AlertType.STRATEGY]: '🎯'
  }
  return icons[type] || icons[AlertType.SYSTEM]
}

export const formatAlertTimestamp = (timestamp: Date) => {
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60))
  
  if (diffInMinutes < 1) return 'just now'
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`
  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
  if (diffInMinutes < 10080) return `${Math.floor(diffInMinutes / 1440)}d ago`
  
  return timestamp.toLocaleDateString()
}

export const filterAlerts = (alerts: Alert[], filters: AlertFilters): Alert[] => {
  return alerts.filter(alert => {
    // Category filter
    if (filters.categories.length > 0 && !filters.categories.includes(alert.type)) {
      return false
    }

    // Priority filter
    if (filters.priorities.length > 0 && !filters.priorities.includes(alert.priority)) {
      return false
    }

    // Read status filter
    if (filters.readStatus === 'read' && !alert.read) return false
    if (filters.readStatus === 'unread' && alert.read) return false

    // Time range filter
    const alertDate = new Date(alert.timestamp)
    const now = new Date()
    
    switch (filters.timeRange) {
      case 'today':
        const today = new Date()
        today.setHours(0, 0, 0, 0)
        if (alertDate < today) return false
        break
      case '24h':
        const twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)
        if (alertDate < twentyFourHoursAgo) return false
        break
      case '7d':
        const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        if (alertDate < sevenDaysAgo) return false
        break
      case '30d':
        const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        if (alertDate < thirtyDaysAgo) return false
        break
      case 'custom':
        if (filters.customStartDate && alertDate < filters.customStartDate) return false
        if (filters.customEndDate && alertDate > filters.customEndDate) return false
        break
    }

    // Token filter
    if (filters.tokens.length > 0) {
      const alertToken = alert.metadata?.token?.symbol || alert.metadata?.tokenSymbol
      if (!alertToken || !filters.tokens.includes(alertToken)) return false
    }

    // Search term filter
    if (filters.searchTerm.trim()) {
      const searchLower = filters.searchTerm.toLowerCase()
      const matchesTitle = alert.title.toLowerCase().includes(searchLower)
      const matchesMessage = alert.message.toLowerCase().includes(searchLower)
      const matchesToken = alert.metadata?.token?.symbol?.toLowerCase().includes(searchLower)
      
      if (!matchesTitle && !matchesMessage && !matchesToken) return false
    }

    return true
  })
}

export const sortAlertsByTimestamp = (alerts: Alert[], descending = true): Alert[] => {
  return [...alerts].sort((a, b) => {
    const timeA = new Date(a.timestamp).getTime()
    const timeB = new Date(b.timestamp).getTime()
    return descending ? timeB - timeA : timeA - timeB
  })
}

export const getAlertStatistics = (alerts: Alert[]) => {
  const now = new Date()
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  const todayAlerts = alerts.filter(alert => new Date(alert.timestamp) >= today)
  const unreadCount = alerts.filter(alert => !alert.read).length

  const byCategory = {
    [AlertType.TRADE]: todayAlerts.filter(a => a.type === AlertType.TRADE).length,
    [AlertType.EXIT]: todayAlerts.filter(a => a.type === AlertType.EXIT).length,
    [AlertType.ERROR]: todayAlerts.filter(a => a.type === AlertType.ERROR).length,
    [AlertType.SYSTEM]: todayAlerts.filter(a => a.type === AlertType.SYSTEM).length,
    [AlertType.PRICE]: todayAlerts.filter(a => a.type === AlertType.PRICE).length,
    [AlertType.STRATEGY]: todayAlerts.filter(a => a.type === AlertType.STRATEGY).length
  }

  const byPriority = {
    [AlertPriority.CRITICAL]: todayAlerts.filter(a => a.priority === AlertPriority.CRITICAL).length,
    [AlertPriority.HIGH]: todayAlerts.filter(a => a.priority === AlertPriority.HIGH).length,
    [AlertPriority.MEDIUM]: todayAlerts.filter(a => a.priority === AlertPriority.MEDIUM).length,
    [AlertPriority.LOW]: todayAlerts.filter(a => a.priority === AlertPriority.LOW).length
  }

  const autoExits = todayAlerts.filter(alert => 
    alert.type === AlertType.EXIT && 
    (alert.metadata?.trigger === 'auto' || alert.metadata?.automated === true)
  ).length

  return {
    total: alerts.length,
    today: todayAlerts.length,
    unread: unreadCount,
    byCategory,
    byPriority,
    autoExits
  }
}

export const isInQuietHours = (alertConfig: AlertConfig, timestamp: Date = new Date()): boolean => {
  if (!alertConfig.quietHours.enabled) return false

  const hour = timestamp.getHours()
  const startHour = parseInt(alertConfig.quietHours.start.split(':')[0])
  const endHour = parseInt(alertConfig.quietHours.end.split(':')[0])

  // Handle overnight quiet hours (e.g., 23:00 to 07:00)
  if (startHour > endHour) {
    return hour >= startHour || hour < endHour
  }
  
  // Handle same-day quiet hours (e.g., 01:00 to 06:00)
  return hour >= startHour && hour < endHour
}

export const shouldShowAlert = (alert: Alert, config: AlertConfig): boolean => {
  // Check if alerts are enabled
  if (!config.enabled) return false

  // Check minimum priority
  const priorityOrder = [AlertPriority.LOW, AlertPriority.MEDIUM, AlertPriority.HIGH, AlertPriority.CRITICAL]
  const alertPriorityIndex = priorityOrder.indexOf(alert.priority)
  const minPriorityIndex = priorityOrder.indexOf(config.filters.minPriority)
  
  if (alertPriorityIndex < minPriorityIndex) return false

  // Check category filter
  if (config.filters.categories.length > 0 && !config.filters.categories.includes(alert.type)) {
    return false
  }

  // Check token filter
  if (config.filters.tokens.length > 0) {
    const alertToken = alert.metadata?.token?.symbol || alert.metadata?.tokenSymbol
    if (!alertToken || !config.filters.tokens.includes(alertToken)) return false
  }

  // Check quiet hours
  if (isInQuietHours(config, new Date(alert.timestamp))) {
    // Allow high priority alerts to override quiet hours if configured
    if (config.quietHours.highPriorityOverride && alert.priority === AlertPriority.HIGH) {
      return true
    }
    // Always allow critical alerts
    if (alert.priority === AlertPriority.CRITICAL) {
      return true
    }
    return false
  }

  return true
}

export const getDefaultAlertConfig = (): AlertConfig => ({
  enabled: true,
  channels: [
    {
      type: 'desktop',
      enabled: true,
      config: {}
    },
    {
      type: 'sound',
      enabled: true,
      config: { volume: 0.7 }
    },
    {
      type: 'email',
      enabled: false,
      config: {}
    },
    {
      type: 'webhook',
      enabled: false,
      config: {}
    }
  ],
  quietHours: {
    enabled: true,
    start: '23:00',
    end: '07:00',
    highPriorityOverride: true
  },
  filters: {
    minPriority: AlertPriority.MEDIUM,
    categories: Object.values(AlertType),
    tokens: []
  }
})

export const validateAlertConfig = (config: Partial<AlertConfig>): string[] => {
  const errors: string[] = []

  if (config.quietHours) {
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
    
    if (!timeRegex.test(config.quietHours.start || '')) {
      errors.push('Invalid quiet hours start time format')
    }
    
    if (!timeRegex.test(config.quietHours.end || '')) {
      errors.push('Invalid quiet hours end time format')
    }
  }

  if (config.channels) {
    config.channels.forEach((channel, index) => {
      if (!['sound', 'email', 'desktop', 'webhook'].includes(channel.type)) {
        errors.push(`Invalid channel type at index ${index}`)
      }
    })
  }

  return errors
}