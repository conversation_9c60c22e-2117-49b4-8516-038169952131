'use client'

import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { useWebSocketStore } from './websocketStore'

interface ErrorDetails {
  code: string
  message: string
  category: string
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  isRetryable: boolean
  recommendations: string[]
  requestId: string
  timestamp: number
  context?: {
    endpoint?: string
    method?: string
    userId?: string
  }
  metadata?: any
  recoveryStrategy?: string
  estimatedRecoveryTime?: number
}

interface ErrorAnalysis {
  totalErrors: number
  errorsByCategory: Record<string, number>
  errorsBySeverity: Record<string, number>
  recentErrors: ErrorDetails[]
  patterns: {
    category: string
    count: number
    lastOccurrence: number
    avgRecoveryTime: number
  }[]
  circuitBreakers: string[]
  healthScore: number
}

interface RetryConfig {
  maxRetries: number
  baseDelay: number
  maxDelay: number
  exponentialBackoff: boolean
}

interface ErrorStore {
  // State
  errors: Map<string, ErrorDetails>
  errorHistory: ErrorDetails[]
  isRetrying: Map<string, boolean>
  retryConfigs: Map<string, RetryConfig>
  analysis: ErrorAnalysis | null
  lastAnalysisUpdate: number
  
  // Settings
  maxHistorySize: number
  autoRetryEnabled: boolean
  notificationsEnabled: boolean
  
  // Actions
  addError: (error: ErrorDetails) => void
  clearError: (requestId: string) => void
  clearAllErrors: () => void
  retryOperation: (requestId: string, operation: () => Promise<void>) => Promise<boolean>
  setRetryConfig: (category: string, config: RetryConfig) => void
  
  // Analysis
  analyzeErrors: () => void
  getErrorsByCategory: (category: string) => ErrorDetails[]
  getErrorsBySeverity: (severity: string) => ErrorDetails[]
  getHealthScore: () => number
  
  // Real-time updates
  subscribeToErrorEvents: () => void
  unsubscribeFromErrorEvents: () => void
  
  // Settings
  setAutoRetry: (enabled: boolean) => void
  setNotifications: (enabled: boolean) => void
  setMaxHistorySize: (size: number) => void
  
  // Utilities
  getRetryDelay: (category: string, attempt: number) => number
  shouldRetry: (error: ErrorDetails, attempt: number) => boolean
  formatErrorForUser: (error: ErrorDetails) => string
  exportErrorReport: () => string
}

const DEFAULT_RETRY_CONFIGS: Record<string, RetryConfig> = {
  NETWORK: { maxRetries: 3, baseDelay: 1000, maxDelay: 8000, exponentialBackoff: true },
  RPC: { maxRetries: 2, baseDelay: 2000, maxDelay: 10000, exponentialBackoff: true },
  JUPITER: { maxRetries: 2, baseDelay: 1500, maxDelay: 6000, exponentialBackoff: true },
  TRANSACTION: { maxRetries: 1, baseDelay: 3000, maxDelay: 3000, exponentialBackoff: false },
  RATE_LIMIT: { maxRetries: 3, baseDelay: 5000, maxDelay: 30000, exponentialBackoff: true },
  DATABASE: { maxRetries: 1, baseDelay: 2000, maxDelay: 2000, exponentialBackoff: false },
  DEFAULT: { maxRetries: 2, baseDelay: 1000, maxDelay: 5000, exponentialBackoff: true }
}

export const useErrorStore = create<ErrorStore>()(
  subscribeWithSelector((set, get) => {
    let errorEventUnsubscribe: (() => void) | null = null

    return {
      // Initial state
      errors: new Map(),
      errorHistory: [],
      isRetrying: new Map(),
      retryConfigs: new Map(Object.entries(DEFAULT_RETRY_CONFIGS)),
      analysis: null,
      lastAnalysisUpdate: 0,
      maxHistorySize: 100,
      autoRetryEnabled: true,
      notificationsEnabled: true,

      // Add error to store
      addError: (error) => {
        set(state => {
          const newErrors = new Map(state.errors)
          newErrors.set(error.requestId, error)
          
          const newHistory = [error, ...state.errorHistory]
          if (newHistory.length > state.maxHistorySize) {
            newHistory.splice(state.maxHistorySize)
          }
          
          return {
            errors: newErrors,
            errorHistory: newHistory
          }
        })

        // Trigger analysis update
        get().analyzeErrors()

        // Show notification if enabled
        if (get().notificationsEnabled && error.severity === 'HIGH' || error.severity === 'CRITICAL') {
          console.error(`🚨 ${error.severity} Error:`, error.message)
        }

        // Auto-retry if enabled and error is retryable
        if (get().autoRetryEnabled && error.isRetryable && error.recoveryStrategy?.includes('RETRY')) {
          console.log(`🔄 Auto-retry available for error: ${error.code}`)
        }
      },

      clearError: (requestId) => {
        set(state => {
          const newErrors = new Map(state.errors)
          newErrors.delete(requestId)
          return { errors: newErrors }
        })
      },

      clearAllErrors: () => {
        set({
          errors: new Map(),
          isRetrying: new Map()
        })
      },

      // Retry operation with intelligent backoff
      retryOperation: async (requestId, operation) => {
        const { errors, isRetrying, getRetryDelay, shouldRetry } = get()
        const error = errors.get(requestId)
        
        if (!error || !shouldRetry(error, 1)) {
          return false
        }

        // Mark as retrying
        set(state => ({
          isRetrying: new Map(state.isRetrying).set(requestId, true)
        }))

        try {
          const delay = getRetryDelay(error.category, 1)
          console.log(`🔄 Retrying operation for ${requestId} after ${delay}ms delay`)
          
          await new Promise(resolve => setTimeout(resolve, delay))
          await operation()
          
          // Success - clear error and retry state
          get().clearError(requestId)
          set(state => {
            const newIsRetrying = new Map(state.isRetrying)
            newIsRetrying.delete(requestId)
            return { isRetrying: newIsRetrying }
          })
          
          console.log(`✅ Retry successful for ${requestId}`)
          return true
          
        } catch (retryError) {
          console.error(`❌ Retry failed for ${requestId}:`, retryError)
          
          // Update error with retry information
          if (error) {
            const updatedError = {
              ...error,
              metadata: {
                ...error.metadata,
                retryAttempts: (error.metadata?.retryAttempts || 0) + 1,
                lastRetryError: retryError instanceof Error ? retryError.message : retryError
              }
            }
            get().addError(updatedError)
          }
          
          set(state => {
            const newIsRetrying = new Map(state.isRetrying)
            newIsRetrying.delete(requestId)
            return { isRetrying: newIsRetrying }
          })
          
          return false
        }
      },

      setRetryConfig: (category, config) => {
        set(state => ({
          retryConfigs: new Map(state.retryConfigs).set(category, config)
        }))
      },

      // Analyze error patterns and trends
      analyzeErrors: () => {
        const { errorHistory } = get()
        const now = Date.now()
        const oneHourAgo = now - 3600000 // 1 hour

        // Filter recent errors
        const recentErrors = errorHistory.filter(error => error.timestamp > oneHourAgo)
        
        // Count by category
        const errorsByCategory: Record<string, number> = {}
        const errorsBySeverity: Record<string, number> = {}
        
        recentErrors.forEach(error => {
          errorsByCategory[error.category] = (errorsByCategory[error.category] || 0) + 1
          errorsBySeverity[error.severity] = (errorsBySeverity[error.severity] || 0) + 1
        })

        // Identify patterns
        const categoryPatterns = Object.entries(errorsByCategory).map(([category, count]) => {
          const categoryErrors = recentErrors.filter(e => e.category === category)
          const avgRecoveryTime = categoryErrors.reduce((sum, error) => {
            return sum + (error.estimatedRecoveryTime || 0)
          }, 0) / categoryErrors.length || 0
          
          return {
            category,
            count,
            lastOccurrence: Math.max(...categoryErrors.map(e => e.timestamp)),
            avgRecoveryTime
          }
        })

        // Calculate health score (0-100)
        const criticalErrors = errorsBySeverity['CRITICAL'] || 0
        const highErrors = errorsBySeverity['HIGH'] || 0
        const totalErrors = recentErrors.length
        
        let healthScore = 100
        healthScore -= criticalErrors * 20
        healthScore -= highErrors * 10
        healthScore -= Math.min(totalErrors * 2, 40) // General error penalty
        healthScore = Math.max(0, Math.min(100, healthScore))

        // Identify circuit breakers (categories with high error rates)
        const circuitBreakers = categoryPatterns
          .filter(pattern => pattern.count >= 5)
          .map(pattern => pattern.category)

        const analysis: ErrorAnalysis = {
          totalErrors: totalErrors,
          errorsByCategory,
          errorsBySeverity,
          recentErrors: recentErrors.slice(0, 20), // Last 20 errors
          patterns: categoryPatterns,
          circuitBreakers,
          healthScore
        }

        set({
          analysis,
          lastAnalysisUpdate: now
        })
      },

      // Get errors by category
      getErrorsByCategory: (category) => {
        return get().errorHistory.filter(error => error.category === category)
      },

      // Get errors by severity
      getErrorsBySeverity: (severity) => {
        return get().errorHistory.filter(error => error.severity === severity)
      },

      // Get current health score
      getHealthScore: () => {
        return get().analysis?.healthScore || 100
      },

      // Subscribe to real-time error events
      subscribeToErrorEvents: () => {
        if (errorEventUnsubscribe) return // Already subscribed

        const wsStore = useWebSocketStore.getState()
        errorEventUnsubscribe = wsStore.onAlert((data) => {
          if (data.type === 'error' || data.category) {
            // Transform WebSocket error event to ErrorDetails format
            const error: ErrorDetails = {
              code: data.code || 'WS_ERROR',
              message: data.message || 'WebSocket error occurred',
              category: data.category || 'WEBSOCKET',
              severity: data.severity || 'MEDIUM',
              isRetryable: data.isRetryable || false,
              recommendations: data.recommendations || [],
              requestId: data.requestId || `ws_${Date.now()}`,
              timestamp: Date.now(),
              context: {
                endpoint: 'websocket',
                userId: data.userId
              },
              metadata: data
            }
            
            get().addError(error)
          }
        })

        console.log('📡 Subscribed to real-time error events')
      },

      unsubscribeFromErrorEvents: () => {
        if (errorEventUnsubscribe) {
          errorEventUnsubscribe()
          errorEventUnsubscribe = null
          console.log('📡 Unsubscribed from error events')
        }
      },

      // Settings
      setAutoRetry: (enabled) => set({ autoRetryEnabled: enabled }),
      setNotifications: (enabled) => set({ notificationsEnabled: enabled }),
      setMaxHistorySize: (size) => set({ maxHistorySize: size }),

      // Utility functions
      getRetryDelay: (category, attempt) => {
        const config = get().retryConfigs.get(category) || get().retryConfigs.get('DEFAULT')!
        
        if (config.exponentialBackoff) {
          const delay = Math.min(
            config.baseDelay * Math.pow(2, attempt - 1),
            config.maxDelay
          )
          // Add jitter to prevent thundering herd
          return delay + Math.random() * 1000
        }
        
        return config.baseDelay
      },

      shouldRetry: (error, attempt) => {
        if (!error.isRetryable) return false
        
        const config = get().retryConfigs.get(error.category) || get().retryConfigs.get('DEFAULT')!
        return attempt <= config.maxRetries
      },

      formatErrorForUser: (error) => {
        const severityEmoji = {
          LOW: '🟡',
          MEDIUM: '🟠', 
          HIGH: '🔴',
          CRITICAL: '🚨'
        }

        return `${severityEmoji[error.severity]} ${error.message}${
          error.recommendations.length > 0 
            ? `\n\nSuggestions:\n${error.recommendations.map(r => `• ${r}`).join('\n')}`
            : ''
        }`
      },

      exportErrorReport: () => {
        const { errorHistory, analysis } = get()
        
        const report = {
          exportedAt: new Date().toISOString(),
          healthScore: analysis?.healthScore || 0,
          summary: {
            totalErrors: errorHistory.length,
            byCategory: analysis?.errorsByCategory || {},
            bySeverity: analysis?.errorsBySeverity || {}
          },
          recentErrors: errorHistory.slice(0, 50).map(error => ({
            timestamp: new Date(error.timestamp).toISOString(),
            code: error.code,
            category: error.category,
            severity: error.severity,
            message: error.message,
            isRetryable: error.isRetryable,
            context: error.context
          })),
          patterns: analysis?.patterns || [],
          circuitBreakers: analysis?.circuitBreakers || []
        }
        
        return JSON.stringify(report, null, 2)
      }
    }
  })
)

// Auto-setup error event subscription
if (typeof window !== 'undefined') {
  const store = useErrorStore.getState()
  store.subscribeToErrorEvents()
  
  // Set up periodic analysis
  setInterval(() => {
    store.analyzeErrors()
  }, 60000) // Every minute
}

// Hook for easy error handling
export const useErrorHandler = () => {
  const store = useErrorStore()
  
  const handleError = (error: any, context?: any) => {
    // Transform error to ErrorDetails format
    const errorDetails: ErrorDetails = {
      code: error.code || 'UNKNOWN_ERROR',
      message: error.message || 'An unknown error occurred',
      category: error.category || 'UNKNOWN',
      severity: error.severity || 'MEDIUM',
      isRetryable: error.isRetryable || false,
      recommendations: error.recommendations || ['Try again later'],
      requestId: error.requestId || `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      context,
      metadata: error.metadata || {}
    }
    
    store.addError(errorDetails)
    return errorDetails
  }

  const retryWithBackoff = async (operation: () => Promise<void>, category = 'DEFAULT') => {
    let attempt = 1
    const maxRetries = store.retryConfigs.get(category)?.maxRetries || 2
    
    while (attempt <= maxRetries) {
      try {
        await operation()
        return true
      } catch (error) {
        if (attempt === maxRetries) throw error
        
        const delay = store.getRetryDelay(category, attempt)
        console.log(`🔄 Retry attempt ${attempt}/${maxRetries} after ${delay}ms`)
        await new Promise(resolve => setTimeout(resolve, delay))
        attempt++
      }
    }
    
    return false
  }

  return {
    handleError,
    retryWithBackoff,
    clearError: store.clearError,
    clearAllErrors: store.clearAllErrors,
    getHealthScore: store.getHealthScore,
    errors: Array.from(store.errors.values()),
    recentErrors: store.analysis?.recentErrors || [],
    isRetrying: (requestId: string) => store.isRetrying.get(requestId) || false
  }
}

import React