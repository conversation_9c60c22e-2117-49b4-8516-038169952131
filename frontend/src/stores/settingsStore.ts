import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { 
  AppSettings, 
  TradingSettings, 
  AlertSettings, 
  PortfolioSettings, 
  SecuritySettings, 
  UISettings, 
  PerformanceSettings,
  SettingsPreset,
  QuickAmountConfig
} from 'shared/src/types/settings'
import { 
  AlertType, 
  AlertPriority, 
  MEVProtectionLevel, 
  RiskLevel 
} from 'shared/src/types/enums'
import { validateSettings, validateSettingsPartial, DEFAULT_TRADING_SETTINGS } from 'shared/src/validation/settingsSchemas'

interface SettingsState {
  settings: AppSettings
  lockedSettings: Record<string, boolean>
  isLoading: boolean
  error: string | null
  hasUnsavedChanges: boolean
  validationErrors: string[]
  
  // Actions
  updateSettings: <K extends keyof AppSettings>(category: K, updates: Partial<AppSettings[K]>) => void
  resetCategory: (category: keyof AppSettings) => void
  resetAllSettings: () => void
  saveSettings: () => Promise<void>
  loadSettings: () => Promise<void>
  
  // Validation
  validateSettings: () => string[]
  
  // Presets
  applyPreset: (preset: 'conservative' | 'balanced' | 'aggressive') => void
  saveAsPreset: (name: string, description: string) => void
  loadPreset: (preset: SettingsPreset) => void
  
  // Import/Export
  exportSettings: () => string
  importSettings: (jsonString: string) => boolean
  
  // Utils
  setHasUnsavedChanges: (value: boolean) => void
  getDefaultSettings: () => AppSettings
  resetToDefaults: () => void
  
  // Lock functionality
  isSettingLocked: (category: string, key: string) => boolean
  toggleSettingLock: (category: string, key: string) => void
  unlockAllSettings: () => void
  
}

// Default settings factory
function getDefaultSettings(): AppSettings {
  return {
    trading: {
      defaultSlippage: DEFAULT_TRADING_SETTINGS.defaultSlippage,
      priorityFee: DEFAULT_TRADING_SETTINGS.priorityFee,
      mevProtectionLevel: MEVProtectionLevel.BASIC,
      tradingPresets: [],
      autoApproveBelow: 0.1,
      simulateFirst: true,
      defaultPositionSize: 10,
      quickAmounts: [
        // Percentage-based buttons
        { label: 'LITE', type: 'percentage', value: 10 },
        { label: 'HALF', type: 'percentage', value: 50 },
        { label: 'PUMP', type: 'percentage', value: 75, isDefault: true },
        { label: 'MAX', type: 'percentage', value: 100 },
        // Fixed SOL amount buttons
        { label: '0.1 SOL', type: 'sol', value: 0.1 },
        { label: '0.25 SOL', type: 'sol', value: 0.25 },
        { label: '0.5 SOL', type: 'sol', value: 0.5 },
        { label: '1 SOL', type: 'sol', value: 1 },
        { label: '5 SOL', type: 'sol', value: 5 }
      ]
    },
    alerts: {
      enabled: true,
      channels: [
        { type: 'desktop', enabled: true, config: {} },
        { type: 'sound', enabled: true, config: { volume: 0.7 } },
        { type: 'email', enabled: false, config: {} },
        { type: 'webhook', enabled: false, config: {} }
      ],
      filters: {
        minPriority: AlertPriority.MEDIUM,
        categories: Object.values(AlertType),
        tokens: []
      },
      quietHours: {
        enabled: false,
        start: '22:00',
        end: '08:00',
        highPriorityOverride: true
      },
      notificationChannels: [
        { type: 'desktop', enabled: true, config: {} },
        { type: 'sound', enabled: true, config: { volume: 0.7 } }
      ],
      alertCategories: Object.values(AlertType).map(type => ({
        type,
        enabled: true,
        minPriority: AlertPriority.LOW
      }))
    },
    portfolio: {
      riskTolerance: RiskLevel.MODERATE,
      defaultPositionSize: 10,
      maxExposure: 80,
      correlationLimit: 0.7,
      rebalanceThreshold: 15,
      stopLossDefault: 10,
      takeProfitDefaults: {
        targets: [10, 25, 50, 100],
        exitPercentages: [20, 30, 30, 10],
        moonBagPercentage: 10
      },
      diversificationRules: [
        { type: 'sector', maxAllocation: 40, enabled: true },
        { type: 'correlation', maxAllocation: 30, enabled: true }
      ]
    },
    security: {
      autoApproveTransactions: false,
      sessionTimeout: 30,
      walletConnectionTimeout: 30,
      emergencyStopEnabled: true,
      requireConfirmationAbove: 1,
      whitelistedContracts: [],
      blacklistedContracts: [],
      maxDailyTransactions: 100,
      maxDailyVolume: 1000,
      twoFactorEnabled: false
    },
    ui: {
      theme: 'dark',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      language: 'en',
      dateFormat: 'MM/DD/YYYY',
      numberFormat: 'comma',
      compactMode: false,
      dashboardLayout: {
        type: 'default',
        widgets: [],
        columns: 3
      },
      animations: true,
      chartSettings: {
        defaultInterval: '15m',
        showVolume: true,
        indicators: ['MA', 'RSI'],
        colorScheme: 'default'
      },
      keyboardShortcuts: true
    },
    performance: {
      websocketHeartbeat: DEFAULT_TRADING_SETTINGS.websocketHeartbeat,
      priceUpdateFrequency: 1000,
      cacheEnabled: true,
      cacheDuration: 300,
      rateLimitBuffer: 20,
      maxConcurrentRequests: 10,
      dataRetentionDays: 30,
      autoRefreshEnabled: true,
      autoRefreshInterval: 60,
      lowBandwidthMode: false
    },
    version: '1.0.0',
    lastUpdated: new Date().toISOString()
  }
}

// Preset configurations
const PRESET_CONFIGS = {
  conservative: {
    trading: {
      defaultSlippage: 1,
      simulateFirst: true,
      autoApproveBelow: 0.05,
      defaultPositionSize: 5
    },
    portfolio: {
      riskTolerance: RiskLevel.CONSERVATIVE,
      defaultPositionSize: 5,
      maxExposure: 50,
      stopLossDefault: 5
    },
    security: {
      autoApproveTransactions: false,
      requireConfirmationAbove: 0.1,
      emergencyStopEnabled: true
    }
  },
  balanced: {
    trading: {
      defaultSlippage: 3,
      simulateFirst: true,
      autoApproveBelow: 0.1,
      defaultPositionSize: 10
    },
    portfolio: {
      riskTolerance: RiskLevel.MODERATE,
      defaultPositionSize: 10,
      maxExposure: 70,
      stopLossDefault: 10
    },
    security: {
      autoApproveTransactions: false,
      requireConfirmationAbove: 0.5,
      emergencyStopEnabled: true
    }
  },
  aggressive: {
    trading: {
      defaultSlippage: 5,
      simulateFirst: false,
      autoApproveBelow: 0.5,
      defaultPositionSize: 25
    },
    portfolio: {
      riskTolerance: RiskLevel.AGGRESSIVE,
      defaultPositionSize: 25,
      maxExposure: 90,
      stopLossDefault: 15
    },
    security: {
      autoApproveTransactions: true,
      requireConfirmationAbove: 2,
      emergencyStopEnabled: false
    }
  }
}

// Migration helper for old quick amount format
function migrateQuickAmounts(quickAmounts: any[]): QuickAmountConfig[] {
  if (!quickAmounts || !Array.isArray(quickAmounts)) {
    return getDefaultSettings().trading.quickAmounts
  }
  
  // If we have less than 9 buttons, use defaults to ensure we have all required buttons
  if (quickAmounts.length < 9) {
    return getDefaultSettings().trading.quickAmounts
  }
  
  const migrated = quickAmounts.map(amount => {
    // Handle old format with 'percentage' field
    if (amount.percentage !== undefined && amount.type === undefined) {
      return {
        label: amount.label,
        type: 'percentage' as const,
        value: amount.percentage,
        isDefault: amount.isDefault
      }
    }
    // Return new format as-is
    return amount
  })
  
  return migrated
}

export const useSettingsStore = create<SettingsState>()(
  devtools(
    persist(
      (set, get) => ({
        settings: getDefaultSettings(),
        lockedSettings: {},
        isLoading: false,
        error: null,
        hasUnsavedChanges: false,
        validationErrors: [],
        
        updateSettings: (category, updates) => {
          set((state) => {
            // Filter out locked settings from updates
            const filteredUpdates = Object.entries(updates).reduce((acc, [key, value]) => {
              const settingKey = `${category}.${key}`
              if (!state.lockedSettings[settingKey]) {
                acc[key] = value
              }
              return acc
            }, {} as any)
            
            // Only proceed if there are non-locked settings to update
            if (Object.keys(filteredUpdates).length === 0) {
              return state
            }
            
            const newSettings = {
              ...state.settings,
              [category]: {
                ...(state.settings[category] as any),
                ...filteredUpdates
              },
              lastUpdated: new Date().toISOString()
            }
            
            // Validate the updated settings
            const validation = validateSettingsPartial(newSettings)
            
            return {
              settings: newSettings,
              hasUnsavedChanges: true,
              validationErrors: validation.errors
            }
          })
        },
        
        resetCategory: (category) => {
          const defaultSettings = getDefaultSettings()
          set((state) => ({
            settings: {
              ...state.settings,
              [category]: defaultSettings[category],
              lastUpdated: new Date().toISOString()
            },
            hasUnsavedChanges: true
          }))
        },
        
        resetAllSettings: () => {
          set({
            settings: getDefaultSettings(),
            hasUnsavedChanges: true,
            validationErrors: []
          })
        },
        
        saveSettings: async () => {
          const { settings, validateSettings } = get()
          const errors = validateSettings()
          
          if (errors.length > 0) {
            set({ error: 'Settings validation failed', validationErrors: errors })
            throw new Error('Settings validation failed')
          }
          
          set({ isLoading: true, error: null })
          
          try {
            // In a real app, this would save to a backend
            // For now, we just update the timestamp
            set((state) => ({
              settings: {
                ...state.settings,
                lastUpdated: new Date().toISOString()
              },
              hasUnsavedChanges: false,
              isLoading: false
            }))
          } catch (error) {
            set({ 
              error: error instanceof Error ? error.message : 'Failed to save settings',
              isLoading: false 
            })
            throw error
          }
        },
        
        loadSettings: async () => {
          set({ isLoading: true, error: null })
          
          try {
            // In a real app, this would load from a backend
            // For now, we just validate and migrate current settings
            const { settings } = get()
            
            // Migrate quick amounts if needed
            if (settings.trading.quickAmounts) {
              const migratedQuickAmounts = migrateQuickAmounts(settings.trading.quickAmounts as any[])
              
              set(state => ({
                settings: {
                  ...state.settings,
                  trading: {
                    ...state.settings.trading,
                    quickAmounts: migratedQuickAmounts
                  }
                }
              }))
            }
            
            set({ isLoading: false })
          } catch (error) {
            console.error('Settings load error:', error)
            // Reset to defaults on error
            set({ 
              settings: getDefaultSettings(),
              error: error instanceof Error ? error.message : 'Failed to load settings',
              isLoading: false 
            })
          }
        },
        
        validateSettings: () => {
          const { settings } = get()
          const validation = validateSettings(settings)
          set({ validationErrors: validation.errors })
          return validation.errors
        },
        
        applyPreset: (preset) => {
          const presetConfig = PRESET_CONFIGS[preset]
          set((state) => ({
            settings: {
              ...state.settings,
              ...Object.entries(presetConfig).reduce((acc, [key, value]) => ({
                ...acc,
                [key]: {
                  ...(state.settings[key as keyof AppSettings] as any),
                  ...value
                }
              }), {} as Partial<AppSettings>),
              lastUpdated: new Date().toISOString()
            },
            hasUnsavedChanges: true
          }))
        },
        
        saveAsPreset: (name, description) => {
          const { settings } = get()
          const preset: SettingsPreset = {
            id: `custom-${Date.now()}`,
            name,
            description,
            category: 'custom',
            settings: settings
          }
          
          // In a real app, this would save to storage
          console.log('Saving preset:', preset)
        },
        
        loadPreset: (preset) => {
          set((state) => ({
            settings: {
              ...state.settings,
              ...preset.settings,
              lastUpdated: new Date().toISOString()
            },
            hasUnsavedChanges: true
          }))
        },
        
        exportSettings: () => {
          const { settings } = get()
          return JSON.stringify({
            version: settings.version,
            exportDate: new Date().toISOString(),
            settings: settings
          }, null, 2)
        },
        
        importSettings: (jsonString) => {
          try {
            const data = JSON.parse(jsonString)
            const validation = validateSettings(data.settings)
            
            if (validation.errors.length > 0) {
              set({ error: 'Invalid settings format', validationErrors: validation.errors })
              return false
            }
            
            set({
              settings: data.settings,
              hasUnsavedChanges: true,
              validationErrors: []
            })
            return true
          } catch (error) {
            set({ error: 'Failed to parse settings' })
            return false
          }
        },
        
        setHasUnsavedChanges: (value) => set({ hasUnsavedChanges: value }),
        getDefaultSettings,
        
        // Utility function to reset settings (for debugging/recovery)
        resetToDefaults: () => {
          set({
            settings: getDefaultSettings(),
            lockedSettings: {},
            hasUnsavedChanges: false,
            validationErrors: [],
            error: null
          })
        },
        
        // Lock functionality
        isSettingLocked: (category, key) => {
          const { lockedSettings } = get()
          const settingKey = `${category}.${key}`
          return lockedSettings[settingKey] || false
        },
        
        toggleSettingLock: (category, key) => {
          const settingKey = `${category}.${key}`
          set((state) => ({
            lockedSettings: {
              ...state.lockedSettings,
              [settingKey]: !state.lockedSettings[settingKey]
            }
          }))
        },
        
        unlockAllSettings: () => {
          set({ lockedSettings: {} })
        }
      }),
      {
        name: 'app-settings',
        version: 1,
        partialize: (state) => ({ settings: state.settings, lockedSettings: state.lockedSettings }),
        migrate: (persistedState: any, version: number) => {
          try {
            if (persistedState?.settings?.trading?.quickAmounts) {
              persistedState.settings.trading.quickAmounts = migrateQuickAmounts(
                persistedState.settings.trading.quickAmounts
              )
            } else {
              // No quickAmounts found in persisted state, use defaults
              if (!persistedState.settings) persistedState.settings = {}
              if (!persistedState.settings.trading) persistedState.settings.trading = {}
              persistedState.settings.trading.quickAmounts = getDefaultSettings().trading.quickAmounts
            }
            
            return persistedState
          } catch (error) {
            console.error('Settings migration error:', error)
            // Return default settings on migration error
            return { settings: getDefaultSettings() }
          }
        }
      }
    )
  )
)