'use client'

import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { Transaction, TransactionFilters, ExportData } from 'shared/src'

interface PaginationInfo {
  currentPage: number
  itemsPerPage: number
  totalItems: number
  totalPages: number
}

interface TransactionState {
  // State
  transactions: Transaction[]
  filteredTransactions: Transaction[]
  filters: TransactionFilters
  isLoading: boolean
  error: string | null
  pagination: PaginationInfo
  selectedTransactionIds: string[]
  lastFetch: Date | null
  
  // Actions
  setTransactions: (transactions: Transaction[]) => void
  addTransaction: (transaction: Transaction) => void
  updateTransaction: (id: string, updates: Partial<Transaction>) => void
  setFilters: (filters: Partial<TransactionFilters>) => void
  clearFilters: () => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  setPagination: (pagination: Partial<PaginationInfo>) => void
  setSelectedTransactions: (ids: string[]) => void
  toggleTransactionSelection: (id: string) => void
  selectAllTransactions: (select: boolean) => void
  fetchTransactions: () => Promise<void>
  refreshTransactions: () => Promise<void>
  exportTransactions: (format: 'csv' | 'json') => Promise<void>
  applyFilters: () => void
}

const defaultFilters: TransactionFilters = {
  dateRange: { start: null, end: null },
  strategies: [],
  transactionTypes: [],
  tokenSearch: '',
  pnlRange: { min: null, max: null },
  profitableOnly: false,
  lossesOnly: false
}

const defaultPagination: PaginationInfo = {
  currentPage: 1,
  itemsPerPage: 50,
  totalItems: 0,
  totalPages: 0
}

// Mock function to simulate API call
const mockFetchTransactions = async (): Promise<Transaction[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  // Generate mock transactions
  return Array.from({ length: 250 }, (_, i) => ({
    id: `tx-${i + 1}`,
    userId: 'user-1',
    positionId: i % 10 === 0 ? undefined : `pos-${Math.floor(i / 3) + 1}`,
    hash: `${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`,
    type: (['BUY', 'SELL', 'PARTIAL_SELL'] as const)[Math.floor(Math.random() * 3)],
    tokenIn: i % 2 === 0 ? 'SOL' : ['PEPE', 'BONK', 'DOGE', 'SHIB'][Math.floor(Math.random() * 4)],
    tokenOut: i % 2 === 0 ? ['PEPE', 'BONK', 'DOGE', 'SHIB'][Math.floor(Math.random() * 4)] : 'SOL',
    amountIn: Math.random() * 1000 + 50,
    amountOut: Math.random() * 50000 + 1000,
    price: Math.random() * 0.001 + 0.00001,
    fees: {
      jupiterFee: Math.random() * 0.01 + 0.001,
      priorityFee: Math.random() * 0.05 + 0.01,
      networkFee: 0.000005,
      total: Math.random() * 0.06 + 0.011005
    },
    strategyId: Math.random() > 0.2 ? `${Math.floor(Math.random() * 6) + 1}` : undefined,
    presetUsed: (['DEFAULT', 'VOL', 'DEAD', 'NUN', 'P5'] as const)[Math.floor(Math.random() * 5)],
    mevProtected: Math.random() > 0.5,
    timestamp: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000) // Last 30 days
  }))
}

const filterTransactions = (transactions: Transaction[], filters: TransactionFilters): Transaction[] => {
  return transactions.filter(transaction => {
    // Date range filter
    if (filters.dateRange.start && transaction.timestamp < filters.dateRange.start) return false
    if (filters.dateRange.end && transaction.timestamp > filters.dateRange.end) return false
    
    // Strategy filter
    if (filters.strategies.length > 0 && !filters.strategies.includes(transaction.presetUsed)) return false
    
    // Transaction type filter
    if (filters.transactionTypes.length > 0 && !filters.transactionTypes.includes(transaction.type)) return false
    
    // Token search filter
    if (filters.tokenSearch.trim()) {
      const searchTerm = filters.tokenSearch.toLowerCase()
      const tokenMatch = transaction.tokenIn.toLowerCase().includes(searchTerm) || 
                         transaction.tokenOut.toLowerCase().includes(searchTerm)
      if (!tokenMatch) return false
    }
    
    // P&L range filter (mock calculation)
    const mockPnL = (Math.random() - 0.5) * transaction.amountIn * 0.3
    if (filters.pnlRange.min !== null && mockPnL < filters.pnlRange.min) return false
    if (filters.pnlRange.max !== null && mockPnL > filters.pnlRange.max) return false
    
    // Profitable/losses only filters
    if (filters.profitableOnly && mockPnL <= 0) return false
    if (filters.lossesOnly && mockPnL >= 0) return false
    
    return true
  })
}

const generateExportData = (transactions: Transaction[]): ExportData[] => {
  return transactions.map(transaction => {
    const mockPnL = (Math.random() - 0.5) * transaction.amountIn * 0.3
    const mockPnLUsd = mockPnL * 200 // Mock SOL price
    
    return {
      date: transaction.timestamp.toISOString().split('T')[0],
      tokenInSymbol: transaction.tokenIn,
      tokenOutSymbol: transaction.tokenOut,
      amountIn: transaction.amountIn,
      amountOut: transaction.amountOut,
      price: transaction.price,
      totalFeesSol: transaction.fees.total,
      strategyUsed: transaction.presetUsed,
      pnlSol: mockPnL,
      pnlUsd: mockPnLUsd,
      transactionHash: transaction.hash
    }
  })
}

const downloadCSV = (data: ExportData[], filename: string) => {
  const headers = [
    'Date', 'Token In', 'Token Out', 'Amount In', 'Amount Out', 
    'Price', 'Total Fees (SOL)', 'Strategy', 'P&L (SOL)', 'P&L (USD)', 'Transaction Hash'
  ]
  
  const csvContent = [
    headers.join(','),
    ...data.map(row => [
      row.date,
      row.tokenInSymbol,
      row.tokenOutSymbol,
      row.amountIn.toFixed(6),
      row.amountOut.toFixed(6),
      row.price.toFixed(8),
      row.totalFeesSol.toFixed(6),
      row.strategyUsed,
      row.pnlSol.toFixed(6),
      row.pnlUsd.toFixed(2),
      row.transactionHash
    ].join(','))
  ].join('\n')
  
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', filename)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

const downloadJSON = (data: ExportData[], filename: string) => {
  const jsonContent = JSON.stringify(data, null, 2)
  const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', filename)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

export const useTransactionStore = create<TransactionState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        transactions: [],
        filteredTransactions: [],
        filters: defaultFilters,
        isLoading: false,
        error: null,
        pagination: defaultPagination,
        selectedTransactionIds: [],
        lastFetch: null,

        // Actions
        setTransactions: (transactions) => {
          set({ transactions, lastFetch: new Date() })
          get().applyFilters()
        },

        addTransaction: (transaction) => {
          const { transactions } = get()
          const newTransactions = [transaction, ...transactions]
          set({ transactions: newTransactions })
          get().applyFilters()
        },

        updateTransaction: (id, updates) => {
          const { transactions } = get()
          const newTransactions = transactions.map(t => 
            t.id === id ? { ...t, ...updates } : t
          )
          set({ transactions: newTransactions })
          get().applyFilters()
        },

        setFilters: (newFilters) => {
          const { filters } = get()
          const updatedFilters = { ...filters, ...newFilters }
          set({ filters: updatedFilters, pagination: { ...get().pagination, currentPage: 1 } })
          get().applyFilters()
        },

        clearFilters: () => {
          set({ filters: defaultFilters, pagination: { ...get().pagination, currentPage: 1 } })
          get().applyFilters()
        },

        setLoading: (isLoading) => set({ isLoading }),

        setError: (error) => set({ error }),

        setPagination: (updates) => {
          const { pagination } = get()
          set({ pagination: { ...pagination, ...updates } })
        },

        setSelectedTransactions: (ids) => set({ selectedTransactionIds: ids }),

        toggleTransactionSelection: (id) => {
          const { selectedTransactionIds } = get()
          const newSelected = selectedTransactionIds.includes(id)
            ? selectedTransactionIds.filter(selectedId => selectedId !== id)
            : [...selectedTransactionIds, id]
          set({ selectedTransactionIds: newSelected })
        },

        selectAllTransactions: (select) => {
          const { filteredTransactions } = get()
          const newSelected = select ? filteredTransactions.map(t => t.id) : []
          set({ selectedTransactionIds: newSelected })
        },

        fetchTransactions: async () => {
          const { setLoading, setError, setTransactions } = get()
          
          setLoading(true)
          setError(null)
          
          try {
            const transactions = await mockFetchTransactions()
            setTransactions(transactions)
          } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to fetch transactions')
          } finally {
            setLoading(false)
          }
        },

        refreshTransactions: async () => {
          const { fetchTransactions } = get()
          await fetchTransactions()
        },

        exportTransactions: async (format) => {
          const { filteredTransactions, setLoading, setError } = get()
          
          setLoading(true)
          setError(null)
          
          try {
            const exportData = generateExportData(filteredTransactions)
            const timestamp = new Date().toISOString().split('T')[0]
            const filename = `transactions_${timestamp}.${format}`
            
            if (format === 'csv') {
              downloadCSV(exportData, filename)
            } else {
              downloadJSON(exportData, filename)
            }
          } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to export transactions')
          } finally {
            setLoading(false)
          }
        },

        applyFilters: () => {
          const { transactions, filters, pagination } = get()
          const filtered = filterTransactions(transactions, filters)
          const totalPages = Math.ceil(filtered.length / pagination.itemsPerPage)
          
          set({
            filteredTransactions: filtered,
            pagination: {
              ...pagination,
              totalItems: filtered.length,
              totalPages,
              currentPage: Math.min(pagination.currentPage, Math.max(1, totalPages))
            }
          })
        }
      }),
      {
        name: 'transaction-store',
        partialize: (state) => ({
          filters: state.filters,
          pagination: state.pagination,
          lastFetch: state.lastFetch
        }),
        onRehydrateStorage: () => (state) => {
          if (state?.lastFetch && typeof state.lastFetch === 'string') {
            state.lastFetch = new Date(state.lastFetch)
          }
        }
      }
    ),
    { name: 'TransactionStore' }
  )
)