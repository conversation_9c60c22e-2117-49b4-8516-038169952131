'use client'

import { create } from 'zustand'
import { devtools, subscribeWithSelector, persist } from 'zustand/middleware'
import { <PERSON>ert, AlertConfig } from 'shared/src/types/alerts'
import { AlertType, AlertPriority } from 'shared/src/types/enums'
import { AlertFilters, getDefaultAlertConfig, filterAlerts, getAlertStatistics } from '@/utils/alertUtils'

interface AlertState {
  // Core Data
  alerts: Alert[]
  config: AlertConfig
  
  // UI State
  filters: AlertFilters
  selectedAlerts: Set<string>
  
  // Statistics
  statistics: ReturnType<typeof getAlertStatistics>
  
  // Loading States
  isLoading: boolean
  error: string | null
  
  // Actions
  addAlert: (alert: Omit<Alert, 'id' | 'timestamp'>) => void
  markAsRead: (alertId: string) => void
  markAllAsRead: () => void
  deleteAlert: (alertId: string) => void
  deleteSelectedAlerts: () => void
  toggleAlertSelection: (alertId: string) => void
  clearSelection: () => void
  
  // Configuration Actions
  updateConfig: (config: Partial<AlertConfig>) => void
  setQuickPreset: (preset: 'minimal' | 'balanced' | 'everything') => void
  
  // Filter Actions
  updateFilters: (filters: Partial<AlertFilters>) => void
  clearFilters: () => void
  
  // Computed Data
  getFilteredAlerts: () => Alert[]
  getUnreadCount: () => number
  
  // Utility Actions
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  refreshStatistics: () => void
  
  // Mock Data Action (for development)
  generateMockAlerts: () => void
}

// Mock data generator for development
const generateMockAlert = (overrides: Partial<Alert> = {}): Alert => {
  const types = Object.values(AlertType)
  const priorities = Object.values(AlertPriority)
  const tokens = ['PEPE', 'BONK', 'DOGE', 'SHIB', 'WIF', 'FLOKI', 'MYRO', 'POPCAT']
  
  const baseAlerts = [
    {
      type: AlertType.TRADE,
      title: 'Trade Executed',
      message: 'Successfully bought 1,000,000 PEPE tokens',
      metadata: { token: { symbol: 'PEPE' }, amount: 1000000, price: 0.00000177 }
    },
    {
      type: AlertType.EXIT,
      title: 'Profit Target Hit',
      message: 'Sold 25% of BONK position at 50% profit target',
      metadata: { token: { symbol: 'BONK' }, percentage: 25, profit: 50 }
    },
    {
      type: AlertType.ERROR,
      title: 'Transaction Failed',
      message: 'Insufficient SOL for transaction fees',
      metadata: { error: 'INSUFFICIENT_FUNDS' }
    },
    {
      type: AlertType.PRICE,
      title: 'Price Alert',
      message: 'DOGE reached target price of $0.08',
      metadata: { token: { symbol: 'DOGE' }, targetPrice: 0.08, currentPrice: 0.08 }
    },
    {
      type: AlertType.STRATEGY,
      title: 'Trailing Stop Activated',
      message: 'Trailing stop activated for WIF position',
      metadata: { token: { symbol: 'WIF' }, trailingPercentage: 10 }
    }
  ]
  
  const template = baseAlerts[Math.floor(Math.random() * baseAlerts.length)]
  
  return {
    id: Math.random().toString(36).substr(2, 9),
    userId: 'user-123',
    type: template.type,
    priority: priorities[Math.floor(Math.random() * priorities.length)],
    title: template.title,
    message: template.message,
    metadata: template.metadata,
    read: Math.random() > 0.6, // 40% unread
    actionable: Math.random() > 0.7, // 30% actionable
    timestamp: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000), // Within last 7 days
    ...overrides
  }
}

const getDefaultFilters = (): AlertFilters => ({
  categories: [],
  priorities: [],
  readStatus: 'all',
  timeRange: '7d',
  tokens: [],
  searchTerm: ''
})

export const useAlertStore = create<AlertState>()(
  devtools(
    subscribeWithSelector(
      persist(
        (set, get) => ({
          // Initial state
          alerts: [],
          config: getDefaultAlertConfig(),
          filters: getDefaultFilters(),
          selectedAlerts: new Set(),
          statistics: {
            total: 0,
            today: 0,
            unread: 0,
            byCategory: {
              [AlertType.TRADE]: 0,
              [AlertType.EXIT]: 0,
              [AlertType.ERROR]: 0,
              [AlertType.SYSTEM]: 0,
              [AlertType.PRICE]: 0,
              [AlertType.STRATEGY]: 0
            },
            byPriority: {
              [AlertPriority.CRITICAL]: 0,
              [AlertPriority.HIGH]: 0,
              [AlertPriority.MEDIUM]: 0,
              [AlertPriority.LOW]: 0
            },
            autoExits: 0
          },
          isLoading: false,
          error: null,

          // Actions
          addAlert: (alertData) => {
            const alert: Alert = {
              ...alertData,
              id: Math.random().toString(36).substr(2, 9),
              timestamp: new Date()
            }
            
            set(state => ({
              alerts: [alert, ...state.alerts]
            }))
            
            get().refreshStatistics()
          },

          markAsRead: (alertId) => {
            set(state => ({
              alerts: state.alerts.map(alert =>
                alert.id === alertId ? { ...alert, read: true } : alert
              )
            }))
            get().refreshStatistics()
          },

          markAllAsRead: () => {
            set(state => ({
              alerts: state.alerts.map(alert => ({ ...alert, read: true }))
            }))
            get().refreshStatistics()
          },

          deleteAlert: (alertId) => {
            set(state => ({
              alerts: state.alerts.filter(alert => alert.id !== alertId),
              selectedAlerts: new Set([...state.selectedAlerts].filter(id => id !== alertId))
            }))
            get().refreshStatistics()
          },

          deleteSelectedAlerts: () => {
            const { selectedAlerts } = get()
            set(state => ({
              alerts: state.alerts.filter(alert => !selectedAlerts.has(alert.id)),
              selectedAlerts: new Set()
            }))
            get().refreshStatistics()
          },

          toggleAlertSelection: (alertId) => {
            set(state => {
              const newSelection = new Set(state.selectedAlerts)
              if (newSelection.has(alertId)) {
                newSelection.delete(alertId)
              } else {
                newSelection.add(alertId)
              }
              return { selectedAlerts: newSelection }
            })
          },

          clearSelection: () => {
            set({ selectedAlerts: new Set() })
          },

          // Configuration Actions
          updateConfig: (configUpdate) => {
            set(state => ({
              config: { ...state.config, ...configUpdate }
            }))
          },

          setQuickPreset: (preset) => {
            const presets = {
              minimal: {
                enabled: true,
                filters: {
                  minPriority: AlertPriority.HIGH,
                  categories: [AlertType.TRADE, AlertType.EXIT, AlertType.ERROR],
                  tokens: []
                }
              },
              balanced: {
                enabled: true,
                filters: {
                  minPriority: AlertPriority.MEDIUM,
                  categories: [AlertType.TRADE, AlertType.EXIT, AlertType.ERROR, AlertType.STRATEGY],
                  tokens: []
                }
              },
              everything: {
                enabled: true,
                filters: {
                  minPriority: AlertPriority.LOW,
                  categories: Object.values(AlertType),
                  tokens: []
                }
              }
            }

            get().updateConfig(presets[preset])
          },

          // Filter Actions
          updateFilters: (filterUpdate) => {
            set(state => ({
              filters: { ...state.filters, ...filterUpdate }
            }))
          },

          clearFilters: () => {
            set({ filters: getDefaultFilters() })
          },

          // Computed Data
          getFilteredAlerts: () => {
            const { alerts, filters } = get()
            return filterAlerts(alerts, filters)
          },

          getUnreadCount: () => {
            const { alerts } = get()
            return alerts.filter(alert => !alert.read).length
          },

          // Utility Actions
          setLoading: (loading) => {
            set({ isLoading: loading })
          },

          setError: (error) => {
            set({ error })
          },

          refreshStatistics: () => {
            const { alerts } = get()
            const statistics = getAlertStatistics(alerts)
            set({ statistics })
          },

          // Mock Data Generator
          generateMockAlerts: () => {
            const mockAlerts = Array.from({ length: 50 }, () => generateMockAlert())
            set({ alerts: mockAlerts })
            get().refreshStatistics()
          }
        }),
        {
          name: 'alert-store',
          partialize: (state) => ({
            config: state.config,
            filters: state.filters
          })
        }
      )
    ),
    {
      name: 'AlertStore'
    }
  )
)

// Initialize with mock data in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  const store = useAlertStore.getState()
  if (store.alerts.length === 0) {
    store.generateMockAlerts()
  }
}