'use client'

import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { 
  PerformanceMetrics, 
  StrategyAnalytics, 
  DailyPnlData, 
  StrategyPerformanceData, 
  WinLossDistribution,
  Transaction
} from 'shared/src'

// Exit strategy mapping based on exitStrategyStore.tsx
const EXIT_STRATEGY_MAP: Record<string, string> = {
  '1': 'Conservative',
  '2': 'Aggressive TP', 
  '3': 'Trailing Only',
  '4': 'Moon Bag',
  '5': 'Quick Scalp',
  '6': 'Aggressive'
}

interface AnalyticsState {
  // State
  performanceMetrics: PerformanceMetrics | null
  strategyAnalytics: StrategyAnalytics[]
  dailyPnlData: DailyPnlData[]
  strategyPerformanceData: StrategyPerformanceData[]
  winLossData: WinLossDistribution[]
  isCalculating: boolean
  lastCalculated: Date | null
  error: string | null
  cacheValid: boolean

  // Actions
  calculateMetrics: (transactions: Transaction[]) => void
  refreshAnalytics: (transactions: Transaction[]) => void
  setCalculating: (calculating: boolean) => void
  setError: (error: string | null) => void
  invalidateCache: () => void
}

// Helper functions for calculations
const calculatePnL = (transaction: Transaction): number => {
  // Mock P&L calculation - in real app this would use actual entry/exit prices
  const isBuy = transaction.type === 'BUY'
  const baseAmount = isBuy ? transaction.amountIn : transaction.amountOut
  return (Math.random() - 0.5) * baseAmount * 0.3
}

const calculatePerformanceMetrics = (transactions: Transaction[]): PerformanceMetrics => {
  if (transactions.length === 0) {
    return {
      totalTrades: 0,
      successfulTrades: 0,
      winRate: 0,
      totalPnlSol: 0,
      totalPnlUsd: 0,
      totalVolume: 0,
      profitFactor: 0,
      sharpeRatio: 0,
      maxDrawdown: 0,
      averageHoldTime: 0,
      averageDailyReturn: 0,
      standardDeviation: 0
    }
  }

  const totalTrades = transactions.length
  const pnlData = transactions.map(t => calculatePnL(t))
  const successfulTrades = pnlData.filter(pnl => pnl > 0).length
  const winRate = (successfulTrades / totalTrades) * 100
  const totalPnlSol = pnlData.reduce((sum, pnl) => sum + pnl, 0)
  const totalPnlUsd = totalPnlSol * 200 // Mock SOL price
  const totalVolume = transactions.reduce((sum, t) => sum + t.amountIn, 0)

  // Calculate profit factor
  const wins = pnlData.filter(pnl => pnl > 0)
  const losses = pnlData.filter(pnl => pnl < 0)
  const totalWins = wins.reduce((sum, pnl) => sum + pnl, 0)
  const totalLosses = Math.abs(losses.reduce((sum, pnl) => sum + pnl, 0))
  const profitFactor = totalLosses > 0 ? totalWins / totalLosses : totalWins > 0 ? 10 : 0

  // Calculate Sharpe ratio (simplified)
  const averageReturn = totalPnlSol / totalTrades
  const variance = pnlData.reduce((sum, pnl) => sum + Math.pow(pnl - averageReturn, 2), 0) / totalTrades
  const standardDeviation = Math.sqrt(variance)
  const sharpeRatio = standardDeviation > 0 ? (averageReturn / standardDeviation) * Math.sqrt(365) : 0

  // Calculate max drawdown (simplified)
  let maxDrawdown = 0
  let peak = 0
  let cumulativePnl = 0
  for (const pnl of pnlData) {
    cumulativePnl += pnl
    if (cumulativePnl > peak) {
      peak = cumulativePnl
    }
    const drawdown = (peak - cumulativePnl) / Math.max(peak, 1)
    if (drawdown > maxDrawdown) {
      maxDrawdown = drawdown
    }
  }

  // Calculate average hold time (mock - in hours)
  const averageHoldTime = 4.5 + Math.random() * 6

  // Calculate daily return
  const daysDiff = Math.max(1, (new Date().getTime() - Math.min(...transactions.map(t => t.timestamp.getTime()))) / (1000 * 60 * 60 * 24))
  const averageDailyReturn = (totalPnlSol / daysDiff) * 100

  return {
    totalTrades,
    successfulTrades,
    winRate,
    totalPnlSol,
    totalPnlUsd,
    totalVolume,
    profitFactor,
    sharpeRatio,
    maxDrawdown: maxDrawdown * 100,
    averageHoldTime,
    averageDailyReturn,
    standardDeviation
  }
}

const calculateStrategyAnalytics = (transactions: Transaction[]): StrategyAnalytics[] => {
  const strategiesMap = new Map<string, Transaction[]>()
  
  // Group transactions by exit strategy ID
  transactions.forEach(transaction => {
    // Only include transactions that have an exit strategy assigned
    if (transaction.strategyId) {
      const strategyId = transaction.strategyId
      if (!strategiesMap.has(strategyId)) {
        strategiesMap.set(strategyId, [])
      }
      strategiesMap.get(strategyId)!.push(transaction)
    }
  })

  // Calculate analytics for each strategy
  return Array.from(strategiesMap.entries()).map(([strategyId, strategyTransactions]) => {
    const pnlData = strategyTransactions.map(t => calculatePnL(t))
    const successfulTrades = pnlData.filter(pnl => pnl > 0).length
    const winRate = (successfulTrades / strategyTransactions.length) * 100
    const totalPnl = pnlData.reduce((sum, pnl) => sum + pnl, 0)
    const averagePnl = totalPnl / strategyTransactions.length
    const bestTrade = Math.max(...pnlData)
    const worstTrade = Math.min(...pnlData)
    const averageHoldTime = 3 + Math.random() * 8 // Mock average hold time
    const totalVolume = strategyTransactions.reduce((sum, t) => sum + t.amountIn, 0)

    return {
      strategyId,
      strategyName: EXIT_STRATEGY_MAP[strategyId] || `Strategy ${strategyId}`,
      totalTrades: strategyTransactions.length,
      winRate,
      averagePnl,
      totalPnl,
      bestTrade,
      worstTrade,
      averageHoldTime,
      totalVolume
    }
  })
}

const calculateDailyPnlData = (transactions: Transaction[]): DailyPnlData[] => {
  const dailyData = new Map<string, { pnl: number; trades: number; cumulativePnl: number }>()
  
  // Sort transactions by date
  const sortedTransactions = [...transactions].sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime())
  
  let cumulativePnl = 0
  
  sortedTransactions.forEach(transaction => {
    const dateKey = transaction.timestamp.toISOString().split('T')[0]
    const pnl = calculatePnL(transaction)
    cumulativePnl += pnl
    
    if (!dailyData.has(dateKey)) {
      dailyData.set(dateKey, { pnl: 0, trades: 0, cumulativePnl })
    }
    
    const dayData = dailyData.get(dateKey)!
    dayData.pnl += pnl
    dayData.trades += 1
    dayData.cumulativePnl = cumulativePnl
  })

  return Array.from(dailyData.entries())
    .map(([date, data]) => ({
      date,
      value: data.pnl,
      cumulativePnl: data.cumulativePnl,
      trades: data.trades
    }))
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
}

const calculateStrategyPerformanceData = (strategyAnalytics: StrategyAnalytics[]): StrategyPerformanceData[] => {
  return strategyAnalytics.map(analytics => ({
    strategyId: analytics.strategyId,
    strategyName: analytics.strategyName,
    pnl: analytics.totalPnl,
    trades: analytics.totalTrades,
    winRate: analytics.winRate
  }))
}

const calculateWinLossData = (transactions: Transaction[]): WinLossDistribution[] => {
  const pnlData = transactions.map(t => calculatePnL(t))
  const wins = pnlData.filter(pnl => pnl > 0).length
  const losses = pnlData.filter(pnl => pnl <= 0).length
  const total = wins + losses

  if (total === 0) {
    return [
      { name: 'Wins', value: 0, percentage: 0, color: '#22c55e' },
      { name: 'Losses', value: 0, percentage: 0, color: '#ef4444' }
    ]
  }

  return [
    { 
      name: 'Wins', 
      value: wins, 
      percentage: (wins / total) * 100, 
      color: '#22c55e' 
    },
    { 
      name: 'Losses', 
      value: losses, 
      percentage: (losses / total) * 100, 
      color: '#ef4444' 
    }
  ]
}

export const useAnalyticsStore = create<AnalyticsState>()(
  devtools(
    (set, get) => ({
      // Initial state
      performanceMetrics: null,
      strategyAnalytics: [],
      dailyPnlData: [],
      strategyPerformanceData: [],
      winLossData: [],
      isCalculating: false,
      lastCalculated: null,
      error: null,
      cacheValid: false,

      // Actions
      calculateMetrics: (transactions) => {
        const { setCalculating, setError } = get()
        
        setCalculating(true)
        setError(null)

        try {
          // Calculate all metrics
          const performanceMetrics = calculatePerformanceMetrics(transactions)
          const strategyAnalytics = calculateStrategyAnalytics(transactions)
          const dailyPnlData = calculateDailyPnlData(transactions)
          const strategyPerformanceData = calculateStrategyPerformanceData(strategyAnalytics)
          const winLossData = calculateWinLossData(transactions)

          set({
            performanceMetrics,
            strategyAnalytics,
            dailyPnlData,
            strategyPerformanceData,
            winLossData,
            lastCalculated: new Date(),
            cacheValid: true,
            error: null
          })
        } catch (error) {
          setError(error instanceof Error ? error.message : 'Failed to calculate metrics')
        } finally {
          setCalculating(false)
        }
      },

      refreshAnalytics: (transactions) => {
        const { calculateMetrics, invalidateCache } = get()
        invalidateCache()
        calculateMetrics(transactions)
      },

      setCalculating: (isCalculating) => set({ isCalculating }),

      setError: (error) => set({ error }),

      invalidateCache: () => set({ cacheValid: false })
    }),
    { name: 'AnalyticsStore' }
  )
)