'use client'

import { createContext, useContext, useState, useEffect, ReactNode, useMemo } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'

// Backend API types matching the server implementation
interface BackendExitStrategy {
  id: string
  userId: string
  positionId: string
  name: string
  type: 'STOP_LOSS' | 'TAKE_PROFIT' | 'TRAILING_STOP' | 'MOON_BAG' | 'LADDER' | 'TIME_BASED' | 'COMPOSITE'
  
  // Strategy parameters
  stopLoss?: StopLossConfig
  takeProfits?: TakeProfitConfig[]
  trailingStop?: TrailingStopConfig
  moonBag?: MoonBagConfig
  ladder?: LadderConfig
  timeBased?: TimeBasedConfig
  
  // Execution settings
  executionMode: 'AUTOMATIC' | 'ALERT_ONLY' | 'MANUAL_CONFIRM'
  slippageTolerance: number
  presetToUse: 'DEFAULT' | 'AGGRESSIVE' | 'CONSERVATIVE' | 'MEV_PROTECTED'
  mevProtection: boolean
  
  // Risk management
  maxLossPercent?: number
  minProfitPercent?: number
  cooldownPeriod?: number
  maxExecutionsPerDay?: number
  
  // Status and tracking
  status: 'ACTIVE' | 'PAUSED' | 'COMPLETED' | 'CANCELLED' | 'ERROR'
  executionState: ExitStrategyState
  
  // Metadata
  createdAt: string
  updatedAt: string
  lastExecuted?: string
  executionCount: number
  totalRealized: number
}

interface StopLossConfig {
  enabled: boolean
  type: 'FIXED_PRICE' | 'PERCENTAGE' | 'ATR_BASED' | 'SUPPORT_LEVEL'
  triggerPrice?: number
  triggerPercent?: number
  atrMultiplier?: number
  supportLevel?: number
  timeDelay?: number
  volumeConfirmation?: boolean
  partialExecution?: boolean
  partialPercent?: number
}

interface TakeProfitConfig {
  id: string
  enabled: boolean
  type: 'FIXED_PRICE' | 'PERCENTAGE' | 'RISK_REWARD_RATIO' | 'RESISTANCE_LEVEL'
  triggerPrice?: number
  triggerPercent?: number
  riskRewardRatio?: number
  resistanceLevel?: number
  sellPercent: number
  executed: boolean
  executedAt?: string
  executedPrice?: number
  timeWindow?: { start: number, end: number }
  volumeThreshold?: number
  priceMovementConfirmation?: boolean
}

interface TrailingStopConfig {
  enabled: boolean
  type: 'PERCENTAGE' | 'ATR_BASED' | 'FIXED_AMOUNT'
  trailPercent?: number
  trailAmount?: number
  atrMultiplier?: number
  highWaterMark: number
  currentStopPrice: number
  activationPrice?: number
  minProfit?: number
  accelerated?: boolean
}

interface MoonBagConfig {
  enabled: boolean
  reservePercent: number
  triggerConditions: {
    minProfitPercent: number
    priceMultiple?: number
    timeHolding?: number
    volumeSpike?: boolean
  }
  neverSell: boolean
  sellRules?: {
    priceTargets: number[]
    sellPercentages: number[]
  }
}

interface LadderConfig {
  enabled: boolean
  type: 'PROFIT_LADDER' | 'LOSS_LADDER'
  steps: LadderStep[]
  resetOnReversal: boolean
}

interface LadderStep {
  triggerPercent: number
  sellPercent: number
  executed: boolean
  executedAt?: string
  executedPrice?: number
}

interface TimeBasedConfig {
  enabled: boolean
  type: 'SCHEDULED_EXIT' | 'HOLDING_PERIOD' | 'MARKET_HOURS'
  scheduledTime?: string
  holdingPeriod?: number
  marketHours?: {
    exitBeforeClose: boolean
    minutesBeforeClose: number
    weekendsOnly: boolean
  }
}

interface ExitStrategyState {
  currentPrice: number
  entryPrice: number
  currentPnL: number
  currentPnLPercent: number
  highWaterMark: number
  lowWaterMark: number
  lastCheckTime: string
  nextCheckTime: string
  executionHistory: StrategyExecution[]
  pendingAlerts: StrategyAlert[]
  alertHistory: StrategyAlert[]
  riskScore: number
  volatility: number
  correlation: number
}

interface StrategyExecution {
  id: string
  timestamp: string
  type: 'STOP_LOSS' | 'TAKE_PROFIT' | 'TRAILING_STOP' | 'MOON_BAG' | 'LADDER' | 'TIME_BASED'
  trigger: string
  price: number
  quantity: number
  percentage: number
  transactionHash?: string
  success: boolean
  error?: string
  realizedPnL: number
  remainingQuantity: number
  marketConditions: {
    volume: number
    volatility: number
    spread: number
    liquidity: number
  }
}

interface StrategyAlert {
  id: string
  timestamp: string
  type: 'EXECUTION_TRIGGERED' | 'RISK_WARNING' | 'MARKET_CONDITION' | 'SYSTEM_ERROR'
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  title: string
  message: string
  data: any
  acknowledged: boolean
}

// Frontend display interface (converted from backend data)
interface ExitStrategy {
  id: string
  name: string
  description: string
  usageCount: number
  winRate: number
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
  stopLoss: { percentage: number } | null
  profitTargets: { target: number; sellPercentage: number }[]
  trailingStop: { percentage: number } | null
  moonBag: { percentage: number; targetGain: number } | null
  isDefault?: boolean
  locked?: boolean
  enabled?: boolean
  createdAt?: Date
  updatedAt?: Date
  status?: 'ACTIVE' | 'PAUSED' | 'COMPLETED' | 'CANCELLED' | 'ERROR'
  executionState?: ExitStrategyState
  positionId?: string
  userId?: string
  type?: string
  executionMode?: string
  slippageTolerance?: number
  mevProtection?: boolean
  totalRealized?: number
}

interface ExitStrategyContextType {
  // Data from React Query
  strategies: ExitStrategy[]
  selectedStrategy: ExitStrategy | null
  isLoading: boolean
  error: Error | null
  
  // Actions
  setSelectedStrategy: (strategy: ExitStrategy | null) => void
  getStrategyById: (id: string) => ExitStrategy | undefined
  
  // Mutations
  createStrategy: (strategy: Partial<BackendExitStrategy>) => Promise<void>
  updateStrategy: (id: string, updates: Partial<BackendExitStrategy>) => Promise<void>
  deleteStrategy: (id: string) => Promise<void>
  pauseStrategy: (id: string) => Promise<void>
  resumeStrategy: (id: string) => Promise<void>
  
  // Real-time updates
  subscribeToAlerts: () => void
  unsubscribeFromAlerts: () => void
  acknowledgeAlert: (strategyId: string, alertId: string) => Promise<void>
}

// API client functions
const apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api'

const apiClient = {
  async getStrategies(): Promise<BackendExitStrategy[]> {
    const response = await fetch(`${apiBaseUrl}/exit-strategies`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Add auth header when implemented
        // 'Authorization': `Bearer ${getAuthToken()}`
      },
    })
    
    if (!response.ok) {
      throw new Error(`Failed to fetch strategies: ${response.statusText}`)
    }
    
    const data = await response.json()
    return data.data.strategies || []
  },

  async createStrategy(strategy: Partial<BackendExitStrategy>): Promise<BackendExitStrategy> {
    const response = await fetch(`${apiBaseUrl}/exit-strategies`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Add auth header when implemented
      },
      body: JSON.stringify(strategy),
    })
    
    if (!response.ok) {
      throw new Error(`Failed to create strategy: ${response.statusText}`)
    }
    
    const data = await response.json()
    return data.data
  },

  async updateStrategy(id: string, updates: Partial<BackendExitStrategy>): Promise<BackendExitStrategy> {
    const response = await fetch(`${apiBaseUrl}/exit-strategies/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    })
    
    if (!response.ok) {
      throw new Error(`Failed to update strategy: ${response.statusText}`)
    }
    
    const data = await response.json()
    return data.data
  },

  async deleteStrategy(id: string): Promise<void> {
    const response = await fetch(`${apiBaseUrl}/exit-strategies/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    })
    
    if (!response.ok) {
      throw new Error(`Failed to delete strategy: ${response.statusText}`)
    }
  },

  async pauseStrategy(id: string): Promise<BackendExitStrategy> {
    const response = await fetch(`${apiBaseUrl}/exit-strategies/${id}/pause`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    })
    
    if (!response.ok) {
      throw new Error(`Failed to pause strategy: ${response.statusText}`)
    }
    
    const data = await response.json()
    return data.data
  },

  async resumeStrategy(id: string): Promise<BackendExitStrategy> {
    const response = await fetch(`${apiBaseUrl}/exit-strategies/${id}/resume`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    })
    
    if (!response.ok) {
      throw new Error(`Failed to resume strategy: ${response.statusText}`)
    }
    
    const data = await response.json()
    return data.data
  }
}

// Convert backend strategy to frontend format
function convertBackendToFrontend(backendStrategy: BackendExitStrategy): ExitStrategy {
  // Safe type handling with fallbacks
  const strategyType = backendStrategy.type || 'COMPOSITE'
  const strategyName = backendStrategy.name || 'Custom Strategy'
  const typeDescription = typeof strategyType === 'string' 
    ? strategyType.replace(/_/g, ' ').toLowerCase() 
    : 'custom'
  
  return {
    id: backendStrategy.id || `strategy_${Date.now()}`,
    name: strategyName,
    description: `${typeDescription} strategy`,
    usageCount: backendStrategy.executionCount || 0,
    winRate: calculateWinRate(backendStrategy.executionState?.executionHistory || []),
    riskLevel: calculateRiskLevel(backendStrategy),
    stopLoss: backendStrategy.stopLoss?.enabled ? 
      { percentage: backendStrategy.stopLoss.triggerPercent || 0 } : null,
    profitTargets: (backendStrategy.takeProfits || []).map(tp => ({
      target: tp?.triggerPercent || 0,
      sellPercentage: tp?.sellPercent || 0
    })),
    trailingStop: backendStrategy.trailingStop?.enabled ? 
      { percentage: backendStrategy.trailingStop.trailPercent || 0 } : null,
    moonBag: backendStrategy.moonBag?.enabled ? {
      percentage: backendStrategy.moonBag.reservePercent || 0,
      targetGain: backendStrategy.moonBag.triggerConditions?.minProfitPercent || 0
    } : null,
    isDefault: false, // Will be determined by user preferences
    locked: false, // Strategies from backend are user-created, not locked
    enabled: backendStrategy.status === 'ACTIVE',
    createdAt: backendStrategy.createdAt ? new Date(backendStrategy.createdAt) : new Date(),
    updatedAt: backendStrategy.updatedAt ? new Date(backendStrategy.updatedAt) : new Date(),
    status: backendStrategy.status || 'ACTIVE',
    executionState: backendStrategy.executionState || undefined,
    positionId: backendStrategy.positionId || undefined,
    userId: backendStrategy.userId || undefined,
    type: strategyType,
    executionMode: backendStrategy.executionMode || undefined,
    slippageTolerance: backendStrategy.slippageTolerance || undefined,
    mevProtection: backendStrategy.mevProtection || undefined,
    totalRealized: backendStrategy.totalRealized || undefined
  }
}

function calculateWinRate(executions: StrategyExecution[]): number {
  if (executions.length === 0) return 0
  const wins = executions.filter(exec => exec.success && exec.realizedPnL > 0).length
  return Math.round((wins / executions.length) * 100)
}

function calculateRiskLevel(strategy: BackendExitStrategy): 'LOW' | 'MEDIUM' | 'HIGH' {
  // Calculate based on stop loss percentage and risk settings with safe fallbacks
  if (!strategy) return 'MEDIUM'
  
  const stopLossPercent = strategy.stopLoss?.triggerPercent ?? 100
  const maxLossPercent = strategy.maxLossPercent ?? 100
  
  if (stopLossPercent <= 5 || maxLossPercent <= 10) return 'LOW'
  if (stopLossPercent <= 15 || maxLossPercent <= 25) return 'MEDIUM'
  return 'HIGH'
}

// Default strategies for initial display (will be replaced by backend data)
const getDefaultStrategies = (): ExitStrategy[] => [
  {
    id: 'strategy_1',
    name: 'Conservative Take Profit',
    description: 'Safe strategy with modest profits and tight stop loss',
    usageCount: 45,
    winRate: 72,
    riskLevel: 'LOW',
    isDefault: true,
    enabled: true,
    stopLoss: { percentage: 8 },
    profitTargets: [
      { target: 15, sellPercentage: 30 },
      { target: 25, sellPercentage: 40 },
      { target: 40, sellPercentage: 30 }
    ],
    trailingStop: { percentage: 5 },
    moonBag: null,
    createdAt: new Date('2024-01-15'),
    status: 'ACTIVE'
  },
  {
    id: 'strategy_2', 
    name: 'Aggressive Scalping',
    description: 'Quick profits with tight targets for experienced traders',
    usageCount: 128,
    winRate: 68,
    riskLevel: 'HIGH',
    isDefault: true,
    enabled: true,
    stopLoss: { percentage: 5 },
    profitTargets: [
      { target: 8, sellPercentage: 50 },
      { target: 12, sellPercentage: 30 },
      { target: 18, sellPercentage: 20 }
    ],
    trailingStop: { percentage: 3 },
    moonBag: null,
    createdAt: new Date('2024-01-10'),
    status: 'ACTIVE'
  },
  {
    id: 'strategy_3',
    name: 'HODLer\'s Dream',
    description: 'Long-term holding with minimal exits and moon bag reserve',
    usageCount: 67,
    winRate: 85,
    riskLevel: 'LOW',
    isDefault: true,
    enabled: true,
    stopLoss: { percentage: 20 },
    profitTargets: [
      { target: 100, sellPercentage: 20 },
      { target: 300, sellPercentage: 30 }
    ],
    trailingStop: null,
    moonBag: { percentage: 50, targetGain: 500 },
    createdAt: new Date('2024-01-08'),
    status: 'ACTIVE'
  },
  {
    id: 'strategy_4',
    name: 'Risk Management Pro',
    description: 'Tight controls with multiple safety mechanisms',
    usageCount: 89,
    winRate: 78,
    riskLevel: 'MEDIUM',
    isDefault: true,
    enabled: true,
    stopLoss: { percentage: 6 },
    profitTargets: [
      { target: 10, sellPercentage: 25 },
      { target: 20, sellPercentage: 35 },
      { target: 35, sellPercentage: 25 },
      { target: 50, sellPercentage: 15 }
    ],
    trailingStop: { percentage: 4 },
    moonBag: null,
    createdAt: new Date('2024-01-12'),
    status: 'ACTIVE'
  },
  {
    id: 'strategy_5',
    name: 'Moon Bag Master',
    description: 'Keep majority position while taking strategic profits',
    usageCount: 34,
    winRate: 91,
    riskLevel: 'MEDIUM',
    isDefault: true,
    enabled: true,
    stopLoss: { percentage: 12 },
    profitTargets: [
      { target: 50, sellPercentage: 15 },
      { target: 150, sellPercentage: 20 }
    ],
    trailingStop: { percentage: 8 },
    moonBag: { percentage: 65, targetGain: 1000 },
    createdAt: new Date('2024-01-20'),
    status: 'ACTIVE'
  },
  {
    id: 'strategy_6',
    name: 'Ladder Exit Pro',
    description: 'Systematic profit taking at regular intervals',
    usageCount: 76,
    winRate: 74,
    riskLevel: 'MEDIUM',
    isDefault: true,
    enabled: true,
    stopLoss: { percentage: 10 },
    profitTargets: [
      { target: 20, sellPercentage: 20 },
      { target: 40, sellPercentage: 20 },
      { target: 60, sellPercentage: 20 },
      { target: 80, sellPercentage: 20 },
      { target: 100, sellPercentage: 20 }
    ],
    trailingStop: { percentage: 6 },
    moonBag: null,
    createdAt: new Date('2024-01-18'),
    status: 'ACTIVE'
  }
]

// WebSocket connection for real-time alerts
let alertsWebSocket: WebSocket | null = null
const wsUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3001'

function connectToAlerts(onAlert: (alert: StrategyAlert) => void) {
  if (alertsWebSocket?.readyState === WebSocket.OPEN) {
    return
  }

  try {
    alertsWebSocket = new WebSocket(`${wsUrl}/strategy-alerts`)
    
    alertsWebSocket.onopen = () => {
      console.log('✅ Connected to strategy alerts WebSocket')
    }
    
    alertsWebSocket.onmessage = (event) => {
      try {
        const alert = JSON.parse(event.data)
        onAlert(alert)
      } catch (error) {
        console.error('Failed to parse alert message:', error)
      }
    }
    
    alertsWebSocket.onclose = (event) => {
      if (event.code === 1006) {
        console.warn('⚠️ Strategy alerts WebSocket connection failed - backend unavailable')
        // Don't attempt to reconnect if backend is unavailable
        return
      }
      
      console.log('Strategy alerts WebSocket disconnected')
      // Attempt to reconnect after 10 seconds for other close codes
      setTimeout(() => connectToAlerts(onAlert), 10000)
    }
    
    alertsWebSocket.onerror = (error) => {
      console.error('❌ Strategy alerts WebSocket error:', error)
    }
  } catch (error) {
    console.error('❌ Failed to create WebSocket connection:', error)
  }
}

function disconnectFromAlerts() {
  if (alertsWebSocket) {
    alertsWebSocket.close()
    alertsWebSocket = null
  }
}

// Context
const ExitStrategyContext = createContext<ExitStrategyContextType | null>(null)

// Provider component
export const ExitStrategyProvider = ({ children }: { children: ReactNode }) => {
  const queryClient = useQueryClient()
  const [selectedStrategy, setSelectedStrategy] = useState<ExitStrategy | null>(null)
  const [alerts, setAlerts] = useState<StrategyAlert[]>([])
  
  // Fetch strategies from backend with fallback to defaults
  const {
    data: backendStrategies = getDefaultStrategies(),
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['exit-strategies'],
    queryFn: async () => {
      try {
        const strategies = await apiClient.getStrategies()
        // Ensure we have valid data before returning
        if (!Array.isArray(strategies)) {
          throw new Error('Invalid strategies data format')
        }
        return strategies.length > 0 ? strategies : []
      } catch (error) {
        console.warn('Failed to fetch strategies from backend, using defaults:', error)
        return []
      }
    },
    refetchInterval: 30000, // Refetch every 30 seconds
    staleTime: 10000, // Consider data stale after 10 seconds
  })
  
  // Convert backend strategies to frontend format with safety checks
  const strategies = useMemo(() => {
    if (!Array.isArray(backendStrategies) || backendStrategies.length === 0) {
      console.log('Using default strategies')
      return getDefaultStrategies()
    }
    
    return backendStrategies
      .filter(strategy => strategy && typeof strategy === 'object')
      .map(strategy => {
        try {
          return convertBackendToFrontend(strategy)
        } catch (error) {
          console.error('Error converting strategy:', error, strategy)
          // Return a fallback strategy
          return {
            id: strategy?.id || `fallback_${Date.now()}`,
            name: strategy?.name || 'Error Strategy',
            description: 'Strategy conversion failed',
            usageCount: 0,
            winRate: 0,
            riskLevel: 'MEDIUM' as const,
            stopLoss: null,
            profitTargets: [],
            trailingStop: null,
            moonBag: null,
            isDefault: false,
            enabled: false,
            status: 'PAUSED' as const
          }
        }
      })
  }, [backendStrategies])
  
  // Mutations for strategy operations
  const createMutation = useMutation({
    mutationFn: apiClient.createStrategy,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['exit-strategies'] })
      toast.success('Strategy created successfully')
    },
    onError: (error: Error) => {
      toast.error(`Failed to create strategy: ${error.message}`)
    }
  })
  
  const updateMutation = useMutation({
    mutationFn: ({ id, updates }: { id: string, updates: Partial<BackendExitStrategy> }) => 
      apiClient.updateStrategy(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['exit-strategies'] })
      toast.success('Strategy updated successfully')
    },
    onError: (error: Error) => {
      toast.error(`Failed to update strategy: ${error.message}`)
    }
  })
  
  const deleteMutation = useMutation({
    mutationFn: apiClient.deleteStrategy,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['exit-strategies'] })
      toast.success('Strategy deleted successfully')
      // Clear selected strategy if it was deleted
      if (selectedStrategy && strategies.find(s => s.id === selectedStrategy.id)) {
        setSelectedStrategy(null)
      }
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete strategy: ${error.message}`)
    }
  })
  
  const pauseMutation = useMutation({
    mutationFn: apiClient.pauseStrategy,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['exit-strategies'] })
      toast.success('Strategy paused')
    },
    onError: (error: Error) => {
      toast.error(`Failed to pause strategy: ${error.message}`)
    }
  })
  
  const resumeMutation = useMutation({
    mutationFn: apiClient.resumeStrategy,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['exit-strategies'] })
      toast.success('Strategy resumed')
    },
    onError: (error: Error) => {
      toast.error(`Failed to resume strategy: ${error.message}`)
    }
  })
  
  // WebSocket connection for real-time alerts
  useEffect(() => {
    const handleAlert = (alert: StrategyAlert) => {
      setAlerts(prev => [alert, ...prev])
      
      // Show toast notification for high priority alerts
      if (alert.severity === 'HIGH' || alert.severity === 'CRITICAL') {
        toast.error(alert.title, {
          duration: 5000,
          icon: '⚠️',
        })
      } else if (alert.type === 'EXECUTION_TRIGGERED') {
        toast.success(alert.title, {
          duration: 3000,
          icon: '✅',
        })
      }
    }
    
    connectToAlerts(handleAlert)
    
    return () => {
      disconnectFromAlerts()
    }
  }, [])

  // Helper functions
  const getStrategyById = (id: string) => {
    return strategies.find(strategy => strategy.id === id)
  }
  
  const acknowledgeAlert = async (strategyId: string, alertId: string) => {
    try {
      const response = await fetch(`${apiBaseUrl}/exit-strategies/${strategyId}/alerts/${alertId}/acknowledge`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      if (!response.ok) {
        throw new Error('Failed to acknowledge alert')
      }
      
      // Remove from local alerts
      setAlerts(prev => prev.filter(alert => alert.id !== alertId))
      toast.success('Alert acknowledged')
    } catch (error) {
      toast.error('Failed to acknowledge alert')
    }
  }
  
  const subscribeToAlerts = () => {
    // Already handled in useEffect above
  }
  
  const unsubscribeFromAlerts = () => {
    disconnectFromAlerts()
  }

  const value: ExitStrategyContextType = {
    strategies,
    selectedStrategy,
    isLoading,
    error: error as Error | null,
    setSelectedStrategy,
    getStrategyById,
    createStrategy: async (strategy) => createMutation.mutateAsync(strategy),
    updateStrategy: async (id, updates) => updateMutation.mutateAsync({ id, updates }),
    deleteStrategy: async (id) => deleteMutation.mutateAsync(id),
    pauseStrategy: async (id) => pauseMutation.mutateAsync(id),
    resumeStrategy: async (id) => resumeMutation.mutateAsync(id),
    subscribeToAlerts,
    unsubscribeFromAlerts,
    acknowledgeAlert
  }

  return (
    <ExitStrategyContext.Provider value={value}>
      {children}
    </ExitStrategyContext.Provider>
  )
}

// Hook to use the context
export const useExitStrategyStore = () => {
  const context = useContext(ExitStrategyContext)
  if (!context) {
    throw new Error('useExitStrategyStore must be used within an ExitStrategyProvider')
  }
  return context
}