'use client'

import { createContext, useContext, useReducer, useEffect, ReactNode, useRef } from 'react'

interface ExitStrategy {
  id: string
  name: string
  description: string
  usageCount: number
  winRate: number
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
  stopLoss: { percentage: number } | null
  profitTargets: { target: number; sellPercentage: number }[]
  trailingStop: { percentage: number } | null
  moonBag: { percentage: number; targetGain: number } | null
  isDefault?: boolean
  locked?: boolean
  createdAt?: Date
  updatedAt?: Date
}

interface ExitStrategyState {
  strategies: ExitStrategy[]
  selectedStrategy: ExitStrategy | null
}

type ExitStrategyAction = 
  | { type: 'ADD_STRATEGY'; payload: Omit<ExitStrategy, 'id' | 'usageCount' | 'winRate' | 'createdAt' | 'updatedAt'> }
  | { type: 'UPDATE_STRATEGY'; payload: { id: string; updates: Partial<ExitStrategy> } }
  | { type: 'DELETE_STRATEGY'; payload: string }
  | { type: 'SET_SELECTED_STRATEGY'; payload: ExitStrategy | null }
  | { type: 'INCREMENT_USAGE'; payload: string }
  | { type: 'UPDATE_WIN_RATE'; payload: { id: string; won: boolean } }
  | { type: 'SET_DEFAULT_STRATEGY'; payload: string }
  | { type: 'LOAD_STRATEGIES'; payload: ExitStrategy[] }

interface ExitStrategyContextType extends ExitStrategyState {
  addStrategy: (strategy: Omit<ExitStrategy, 'id' | 'usageCount' | 'winRate' | 'createdAt' | 'updatedAt'>) => void
  updateStrategy: (id: string, updates: Partial<ExitStrategy>) => void
  deleteStrategy: (id: string) => void
  setSelectedStrategy: (strategy: ExitStrategy | null) => void
  setDefaultStrategy: (id: string) => void
  getStrategyById: (id: string) => ExitStrategy | undefined
  incrementUsage: (id: string) => void
  updateWinRate: (id: string, won: boolean) => void
}

// Initial strategies data
const initialStrategies: ExitStrategy[] = [
  {
    id: '1',
    name: 'Conservative',
    description: 'Low risk, steady profits',
    usageCount: 12,
    winRate: 73,
    riskLevel: 'LOW',
    isDefault: true,
    locked: false,
    stopLoss: { percentage: 10 },
    profitTargets: [
      { target: 25, sellPercentage: 20 },
      { target: 50, sellPercentage: 30 },
      { target: 75, sellPercentage: 25 }
    ],
    trailingStop: { percentage: 15 },
    moonBag: null,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: '2',
    name: 'Aggressive TP',
    description: 'Higher risk, bigger rewards',
    usageCount: 8,
    winRate: 65,
    riskLevel: 'HIGH',
    isDefault: false,
    locked: false,
    stopLoss: { percentage: 15 },
    profitTargets: [
      { target: 50, sellPercentage: 15 },
      { target: 100, sellPercentage: 20 },
      { target: 150, sellPercentage: 25 },
      { target: 200, sellPercentage: 15 }
    ],
    trailingStop: { percentage: 15 },
    moonBag: { percentage: 25, targetGain: 500 },
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: '3',
    name: 'Trailing Only',
    description: 'Pure trailing stop strategy',
    usageCount: 3,
    winRate: 80,
    riskLevel: 'LOW',
    isDefault: false,
    locked: true,
    stopLoss: null,
    profitTargets: [],
    trailingStop: { percentage: 15 },
    moonBag: null,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: '4',
    name: 'Moon Bag',
    description: 'Hold majority for massive gains',
    usageCount: 12,
    winRate: 73,
    riskLevel: 'MEDIUM',
    isDefault: false,
    locked: false,
    stopLoss: { percentage: 15 },
    profitTargets: [
      { target: 30, sellPercentage: 10 },
      { target: 75, sellPercentage: 15 },
      { target: 150, sellPercentage: 20 }
    ],
    trailingStop: { percentage: 15 },
    moonBag: { percentage: 35, targetGain: 500 },
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: '5',
    name: 'Quick Scalp',
    description: 'Fast profits with tight stops',
    usageCount: 12,
    winRate: 73,
    riskLevel: 'MEDIUM',
    isDefault: false,
    locked: false,
    stopLoss: { percentage: 5 },
    profitTargets: [
      { target: 15, sellPercentage: 50 },
      { target: 30, sellPercentage: 50 }
    ],
    trailingStop: { percentage: 8 },
    moonBag: null,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: '6',
    name: 'Aggressive',
    description: 'Maximum risk, maximum rewards',
    usageCount: 12,
    winRate: 73,
    riskLevel: 'HIGH',
    isDefault: false,
    locked: false,
    stopLoss: { percentage: 20 },
    profitTargets: [
      { target: 50, sellPercentage: 15 },
      { target: 100, sellPercentage: 25 },
      { target: 200, sellPercentage: 35 }
    ],
    trailingStop: { percentage: 20 },
    moonBag: { percentage: 25, targetGain: 500 },
    createdAt: new Date(),
    updatedAt: new Date()
  }
]

// Reducer function
const exitStrategyReducer = (state: ExitStrategyState, action: ExitStrategyAction): ExitStrategyState => {
  switch (action.type) {
    case 'ADD_STRATEGY': {
      const newStrategy: ExitStrategy = {
        ...action.payload,
        id: Date.now().toString(),
        usageCount: 0,
        winRate: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      }
      return {
        ...state,
        strategies: [...state.strategies, newStrategy]
      }
    }
    case 'UPDATE_STRATEGY':
      return {
        ...state,
        strategies: state.strategies.map(strategy =>
          strategy.id === action.payload.id 
            ? { ...strategy, ...action.payload.updates, updatedAt: new Date() }
            : strategy
        )
      }
    case 'DELETE_STRATEGY':
      return {
        ...state,
        strategies: state.strategies.filter(strategy => strategy.id !== action.payload),
        selectedStrategy: state.selectedStrategy?.id === action.payload ? null : state.selectedStrategy
      }
    case 'SET_SELECTED_STRATEGY':
      return {
        ...state,
        selectedStrategy: action.payload
      }
    case 'INCREMENT_USAGE':
      return {
        ...state,
        strategies: state.strategies.map(strategy =>
          strategy.id === action.payload 
            ? { ...strategy, usageCount: strategy.usageCount + 1, updatedAt: new Date() }
            : strategy
        )
      }
    case 'UPDATE_WIN_RATE': {
      const strategy = state.strategies.find(s => s.id === action.payload.id)
      if (strategy) {
        const totalTrades = strategy.usageCount
        const currentWins = Math.round((strategy.winRate / 100) * totalTrades)
        const newWins = action.payload.won ? currentWins + 1 : currentWins
        const newWinRate = totalTrades > 0 ? Math.round((newWins / totalTrades) * 100) : 0
        
        return {
          ...state,
          strategies: state.strategies.map(s =>
            s.id === action.payload.id 
              ? { ...s, winRate: newWinRate, updatedAt: new Date() }
              : s
          )
        }
      }
      return state
    }
    case 'SET_DEFAULT_STRATEGY':
      return {
        ...state,
        strategies: state.strategies.map(strategy => ({
          ...strategy,
          isDefault: strategy.id === action.payload,
          updatedAt: new Date()
        }))
      }
    case 'LOAD_STRATEGIES':
      // Ensure Date objects are properly restored
      const strategies = action.payload.map(strategy => ({
        ...strategy,
        createdAt: strategy.createdAt ? new Date(strategy.createdAt) : new Date(),
        updatedAt: strategy.updatedAt ? new Date(strategy.updatedAt) : new Date()
      }))
      return {
        ...state,
        strategies
      }
    default:
      return state
  }
}

// Context
const ExitStrategyContext = createContext<ExitStrategyContextType | null>(null)

// Provider component
export const ExitStrategyProvider = ({ children }: { children: ReactNode }) => {
  // Initialize with empty state, load from localStorage in useEffect
  const [state, dispatch] = useReducer(exitStrategyReducer, {
    strategies: [],
    selectedStrategy: null
  })
  
  // Track if we're in initial load to prevent saving during load
  const isInitialLoad = useRef(true)
  const lastSavedState = useRef<string>('')

  // Load from localStorage on mount, fallback to initial strategies
  useEffect(() => {
    const saved = localStorage.getItem('exit-strategy-store')
    
    // Debug: Check if data looks corrupted
    if (saved) {
      try {
        const parsed = JSON.parse(saved)
        if (parsed.strategies && parsed.strategies.length > 0) {
          console.log('Loading strategies from localStorage:', parsed.strategies.map(s => `${s.name} (default: ${s.isDefault})`))
          
          // Validate that only one strategy is marked as default
          const defaultStrategies = parsed.strategies.filter(s => s.isDefault)
          if (defaultStrategies.length !== 1) {
            console.warn('Invalid default strategy configuration detected. Resetting to initial strategies.')
            console.log('Using initial strategies (corrupted data detected)')
            dispatch({ type: 'LOAD_STRATEGIES', payload: initialStrategies })
            isInitialLoad.current = false
            return
          }
          
          dispatch({ type: 'LOAD_STRATEGIES', payload: parsed.strategies })
          
          // Also restore selected strategy if it exists and is still valid
          if (parsed.selectedStrategy) {
            const validSelected = parsed.strategies.find(s => s.id === parsed.selectedStrategy.id)
            if (validSelected) {
              console.log('Restoring selected strategy:', validSelected.name)
              dispatch({ type: 'SET_SELECTED_STRATEGY', payload: validSelected })
            }
          }
          isInitialLoad.current = false
          return
        }
      } catch (error) {
        console.error('Failed to load strategies from localStorage:', error)
      }
    }
    
    // If no saved data or loading failed, use initial strategies
    console.log('Using initial strategies')
    dispatch({ type: 'LOAD_STRATEGIES', payload: initialStrategies })
    isInitialLoad.current = false
  }, [])

  // Save to localStorage on state changes (only when strategies are loaded and state actually changed)
  useEffect(() => {
    // Don't save during initial load
    if (isInitialLoad.current || state.strategies.length === 0) {
      return
    }
    
    const stateString = JSON.stringify(state)
    // Only save if the state actually changed
    if (stateString !== lastSavedState.current) {
      console.log('Saving strategies to localStorage:', state.strategies.map(s => `${s.name} (default: ${s.isDefault})`))
      localStorage.setItem('exit-strategy-store', stateString)
      lastSavedState.current = stateString
    }
  }, [state.strategies, state.selectedStrategy])

  const addStrategy = (strategy: Omit<ExitStrategy, 'id' | 'usageCount' | 'winRate' | 'createdAt' | 'updatedAt'>) => {
    dispatch({ type: 'ADD_STRATEGY', payload: strategy })
  }

  const updateStrategy = (id: string, updates: Partial<ExitStrategy>) => {
    dispatch({ type: 'UPDATE_STRATEGY', payload: { id, updates } })
  }

  const deleteStrategy = (id: string) => {
    dispatch({ type: 'DELETE_STRATEGY', payload: id })
  }

  const setSelectedStrategy = (strategy: ExitStrategy | null) => {
    console.log('Setting selected strategy:', strategy?.name || 'null')
    dispatch({ type: 'SET_SELECTED_STRATEGY', payload: strategy })
  }

  const setDefaultStrategy = (id: string) => {
    dispatch({ type: 'SET_DEFAULT_STRATEGY', payload: id })
  }

  const getStrategyById = (id: string) => {
    return state.strategies.find(strategy => strategy.id === id)
  }

  const incrementUsage = (id: string) => {
    dispatch({ type: 'INCREMENT_USAGE', payload: id })
  }

  const updateWinRate = (id: string, won: boolean) => {
    dispatch({ type: 'UPDATE_WIN_RATE', payload: { id, won } })
  }

  const value: ExitStrategyContextType = {
    ...state,
    addStrategy,
    updateStrategy,
    deleteStrategy,
    setSelectedStrategy,
    setDefaultStrategy,
    getStrategyById,
    incrementUsage,
    updateWinRate
  }

  return (
    <ExitStrategyContext.Provider value={value}>
      {children}
    </ExitStrategyContext.Provider>
  )
}

// Hook to use the context
export const useExitStrategyStore = () => {
  const context = useContext(ExitStrategyContext)
  if (!context) {
    throw new Error('useExitStrategyStore must be used within an ExitStrategyProvider')
  }
  return context
}