'use client'

import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { useWebSocketStore } from './websocketStore'
import { jupiterPriceApi } from '@/lib/jupiterPriceApi'

interface PortfolioOverview {
  totalValue: number
  totalPnl: number
  totalPnlPercentage: number
  totalInvested: number
  activePositions: number
  exposurePercentage: number
  riskScore: number
  maxDrawdown: number
  winRate: number
  totalTrades: number
  profitFactor: number
  sharpeRatio: number
  riskBreakdown: {
    concentration: number
    correlationRisk: number
    liquidityRisk: number
    volatilityScore: number
  }
  maxExposureLimit: number
  lastUpdated: string
  updateFrequency: number
}

interface PriceUpdate {
  address: string
  symbol: string
  price: number
  change24h: number
  timestamp: number
}

interface PortfolioAllocation {
  tokenAddress: string
  tokenSymbol: string
  value: number
  percentage: number
  quantity: number
  avgEntryPrice: number
}

interface PortfolioStore {
  // State
  overview: PortfolioOverview | null
  allocations: PortfolioAllocation[]
  priceUpdates: Map<string, PriceUpdate>
  loading: boolean
  error: string | null
  lastUpdate: number
  autoRefreshEnabled: boolean
  refreshInterval: number
  
  // Actions
  loadPortfolio: () => Promise<void>
  loadAllocations: () => Promise<void>
  enableAutoRefresh: () => void
  disableAutoRefresh: () => void
  setRefreshInterval: (interval: number) => void
  
  // Real-time updates
  handlePriceUpdate: (update: PriceUpdate) => void
  subscribeToPortfolioPrices: () => void
  unsubscribeFromPortfolioPrices: () => void
  updatePortfolioMetrics: () => void
  
  // Utilities
  getTokenAllocation: (tokenAddress: string) => PortfolioAllocation | undefined
  getTotalPortfolioValue: () => number
  getPortfolioPerformance: () => {
    totalPnl: number
    totalPnlPercentage: number
    bestPerformer: PortfolioAllocation | null
    worstPerformer: PortfolioAllocation | null
  }
  clearError: () => void
  refresh: () => Promise<void>
}

export const usePortfolioStore = create<PortfolioStore>()(
  subscribeWithSelector((set, get) => {
    let refreshIntervalId: NodeJS.Timeout | null = null
    let priceSubscriptionTokens: string[] = []

    const clearRefreshInterval = () => {
      if (refreshIntervalId) {
        clearInterval(refreshIntervalId)
        refreshIntervalId = null
      }
    }

    const setupAutoRefresh = (interval: number) => {
      clearRefreshInterval()
      refreshIntervalId = setInterval(() => {
        get().loadPortfolio()
        get().updatePortfolioMetrics()
      }, interval)
    }

    return {
      // Initial state
      overview: null,
      allocations: [],
      priceUpdates: new Map(),
      loading: false,
      error: null,
      lastUpdate: 0,
      autoRefreshEnabled: false,
      refreshInterval: 30000, // 30 seconds default

      // Load portfolio overview
      loadPortfolio: async () => {
        set({ loading: true, error: null })
        
        try {
          const response = await fetch('/api/realtime/portfolio/overview', {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error(`Failed to load portfolio: ${response.statusText}`)
          }

          const data = await response.json()
          
          if (data.success) {
            set({ 
              overview: data.data,
              loading: false,
              lastUpdate: Date.now()
            })

            console.log('✅ Portfolio overview loaded')
            
            // Start price subscriptions for active positions
            get().subscribeToPortfolioPrices()
          } else {
            throw new Error(data.error || 'Failed to load portfolio')
          }
        } catch (error) {
          console.error('❌ Error loading portfolio:', error)
          set({ 
            error: error instanceof Error ? error.message : 'Unknown error',
            loading: false 
          })
        }
      },

      // Load portfolio allocations
      loadAllocations: async () => {
        try {
          const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:3001'
          const response = await fetch(`${backendUrl}/api/portfolio/allocation`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (response.ok) {
            const data = await response.json()
            if (data.success) {
              set({ allocations: data.data.allocations || [] })
              console.log(`✅ Loaded ${data.data.allocations?.length || 0} portfolio allocations`)
            }
          }
        } catch (error) {
          console.error('❌ Error loading allocations:', error)
        }
      },

      // Auto-refresh controls
      enableAutoRefresh: () => {
        const { refreshInterval } = get()
        set({ autoRefreshEnabled: true })
        setupAutoRefresh(refreshInterval)
        console.log(`🔄 Auto-refresh enabled (${refreshInterval}ms interval)`)
      },

      disableAutoRefresh: () => {
        set({ autoRefreshEnabled: false })
        clearRefreshInterval()
        console.log('⏸️ Auto-refresh disabled')
      },

      setRefreshInterval: (interval) => {
        set({ refreshInterval: interval })
        if (get().autoRefreshEnabled) {
          setupAutoRefresh(interval)
        }
      },

      // Handle real-time price updates
      handlePriceUpdate: (update) => {
        set(state => {
          const newPriceUpdates = new Map(state.priceUpdates)
          newPriceUpdates.set(update.address, update)
          return { 
            priceUpdates: newPriceUpdates,
            lastUpdate: Date.now()
          }
        })

        // Trigger portfolio metrics update when prices change
        get().updatePortfolioMetrics()
      },

      // Subscribe to price updates for portfolio tokens
      subscribeToPortfolioPrices: async () => {
        const { allocations } = get()
        const tokenAddresses = allocations.map(alloc => alloc.tokenAddress)
        
        if (tokenAddresses.length === 0) {
          // Load allocations first if empty
          await get().loadAllocations()
          const updatedAllocations = get().allocations
          priceSubscriptionTokens = updatedAllocations.map(alloc => alloc.tokenAddress)
        } else {
          priceSubscriptionTokens = tokenAddresses
        }

        if (priceSubscriptionTokens.length > 0) {
          console.log(`📈 Subscribing to price updates for ${priceSubscriptionTokens.length} tokens`)
          
          const wsStore = useWebSocketStore.getState()
          wsStore.subscribeToPrices(priceSubscriptionTokens)
        }
      },

      unsubscribeFromPortfolioPrices: () => {
        if (priceSubscriptionTokens.length > 0) {
          const wsStore = useWebSocketStore.getState()
          wsStore.unsubscribeFromPrices(priceSubscriptionTokens)
          priceSubscriptionTokens = []
        }
      },

      // Update portfolio metrics based on current prices
      updatePortfolioMetrics: () => {
        const { overview, allocations, priceUpdates } = get()
        if (!overview || allocations.length === 0) return

        let totalValue = 0
        let totalInvested = 0

        // Recalculate based on current prices
        allocations.forEach(allocation => {
          const priceUpdate = priceUpdates.get(allocation.tokenAddress)
          const currentPrice = priceUpdate?.price || allocation.avgEntryPrice
          
          const currentValue = allocation.quantity * currentPrice
          const investedValue = allocation.quantity * allocation.avgEntryPrice
          
          totalValue += currentValue
          totalInvested += investedValue
        })

        const totalPnl = totalValue - totalInvested
        const totalPnlPercentage = totalInvested > 0 ? (totalPnl / totalInvested) * 100 : 0

        // Update overview with real-time calculations
        set(state => ({
          overview: state.overview ? {
            ...state.overview,
            totalValue,
            totalPnl,
            totalPnlPercentage,
            totalInvested,
            lastUpdated: new Date().toISOString()
          } : null
        }))
      },

      // Utility functions
      getTokenAllocation: (tokenAddress) => {
        return get().allocations.find(alloc => alloc.tokenAddress === tokenAddress)
      },

      getTotalPortfolioValue: () => {
        return get().overview?.totalValue || 0
      },

      getPortfolioPerformance: () => {
        const { overview, allocations, priceUpdates } = get()
        
        // Calculate current performance
        const performanceData = allocations.map(allocation => {
          const priceUpdate = priceUpdates.get(allocation.tokenAddress)
          const currentPrice = priceUpdate?.price || allocation.avgEntryPrice
          const currentValue = allocation.quantity * currentPrice
          const investedValue = allocation.quantity * allocation.avgEntryPrice
          const pnl = currentValue - investedValue
          const pnlPercentage = investedValue > 0 ? (pnl / investedValue) * 100 : 0
          
          return {
            ...allocation,
            currentPrice,
            currentValue,
            pnl,
            pnlPercentage
          }
        })

        const totalPnl = overview?.totalPnl || 0
        const totalPnlPercentage = overview?.totalPnlPercentage || 0
        
        const bestPerformer = performanceData.reduce((best, current) => 
          !best || current.pnlPercentage > best.pnlPercentage ? current : best, null as any
        )
        
        const worstPerformer = performanceData.reduce((worst, current) => 
          !worst || current.pnlPercentage < worst.pnlPercentage ? current : worst, null as any
        )

        return {
          totalPnl,
          totalPnlPercentage,
          bestPerformer,
          worstPerformer
        }
      },

      clearError: () => set({ error: null }),

      refresh: async () => {
        await Promise.all([
          get().loadPortfolio(),
          get().loadAllocations()
        ])
      }
    }
  })
)

// Set up WebSocket integration for real-time price updates
const setupPortfolioWebSocketIntegration = () => {
  const wsStore = useWebSocketStore.getState()
  
  // Listen for price updates
  const unsubscribePriceUpdates = wsStore.onPriceUpdate((data) => {
    const portfolioStore = usePortfolioStore.getState()
    
    if (data.token && data.price) {
      portfolioStore.handlePriceUpdate({
        address: data.token,
        symbol: data.symbol || 'UNKNOWN',
        price: data.price,
        change24h: data.change24h || 0,
        timestamp: Date.now()
      })
    }
  })

  return () => {
    unsubscribePriceUpdates()
  }
}

// Auto-setup WebSocket integration
if (typeof window !== 'undefined') {
  setupPortfolioWebSocketIntegration()
}

// Hook for easy portfolio management
export const usePortfolio = (autoLoad = true, autoRefresh = true) => {
  const store = usePortfolioStore()
  
  React.useEffect(() => {
    if (autoLoad && !store.overview && !store.loading) {
      store.loadPortfolio()
    }
  }, [autoLoad, store.overview, store.loading, store.loadPortfolio])

  React.useEffect(() => {
    if (autoRefresh && !store.autoRefreshEnabled) {
      store.enableAutoRefresh()
    }

    return () => {
      if (autoRefresh && store.autoRefreshEnabled) {
        store.disableAutoRefresh()
      }
    }
  }, [autoRefresh, store.autoRefreshEnabled, store.enableAutoRefresh, store.disableAutoRefresh])

  return {
    overview: store.overview,
    allocations: store.allocations,
    loading: store.loading,
    error: store.error,
    lastUpdate: store.lastUpdate,
    performance: store.getPortfolioPerformance(),
    totalValue: store.getTotalPortfolioValue(),
    refresh: store.refresh,
    clearError: store.clearError,
    autoRefreshEnabled: store.autoRefreshEnabled,
    enableAutoRefresh: store.enableAutoRefresh,
    disableAutoRefresh: store.disableAutoRefresh
  }
}

// Hook for real-time portfolio metrics
export const usePortfolioMetrics = () => {
  const overview = usePortfolioStore(state => state.overview)
  const performance = usePortfolioStore(state => state.getPortfolioPerformance())
  const lastUpdate = usePortfolioStore(state => state.lastUpdate)
  
  return {
    overview,
    performance,
    lastUpdate,
    isStale: overview ? Date.now() - new Date(overview.lastUpdated).getTime() > 60000 : false
  }
}

import React