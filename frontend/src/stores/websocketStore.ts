'use client'

import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'

interface WebSocketMessage {
  type: 'price_update' | 'position_update' | 'transaction_update' | 'strategy_trigger' | 'alert' | 'heartbeat' | 'error'
  data?: any
  timestamp: number
  userId?: string
}

interface ConnectionStatus {
  connected: boolean
  connecting: boolean
  lastMessage: WebSocketMessage | null
  error: string | null
  reconnectAttempts: number
  subscriptions: Set<string>
}

interface WebSocketStore {
  // Connection state
  connections: Map<string, WebSocket>
  status: ConnectionStatus
  
  // Subscriptions
  priceSubscriptions: Set<string>
  positionSubscriptions: Set<string>
  transactionSubscriptions: Set<string>
  
  // Actions
  connect: (url?: string) => Promise<void>
  disconnect: () => void
  sendMessage: (message: Omit<WebSocketMessage, 'timestamp'>) => boolean
  
  // Subscription management
  subscribeToPrices: (tokens: string[]) => Promise<void>
  unsubscribeFromPrices: (tokens?: string[]) => Promise<void>
  subscribeToPositions: (positionIds: string[]) => Promise<void>
  subscribeToTransactions: (transactionHashes: string[]) => Promise<void>
  
  // Message handlers
  onPriceUpdate: (callback: (data: any) => void) => () => void
  onPositionUpdate: (callback: (data: any) => void) => () => void
  onTransactionUpdate: (callback: (data: any) => void) => () => void
  onStrategyTrigger: (callback: (data: any) => void) => () => void
  onAlert: (callback: (data: any) => void) => () => void
  
  // Utilities
  getConnectionStatus: () => ConnectionStatus
  clearError: () => void
}

const DEFAULT_WS_URL = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:5000/ws'
const RECONNECT_INTERVAL = 3000
const MAX_RECONNECT_ATTEMPTS = 10
const HEARTBEAT_INTERVAL = 30000

export const useWebSocketStore = create<WebSocketStore>()(
  subscribeWithSelector((set, get) => {
    let ws: WebSocket | null = null
    let reconnectTimeout: NodeJS.Timeout | null = null
    let heartbeatInterval: NodeJS.Timeout | null = null
    let messageHandlers = new Map<string, Set<(data: any) => void>>()

    const clearTimeouts = () => {
      if (reconnectTimeout) {
        clearTimeout(reconnectTimeout)
        reconnectTimeout = null
      }
      if (heartbeatInterval) {
        clearInterval(heartbeatInterval)
        heartbeatInterval = null
      }
    }

    const startHeartbeat = () => {
      heartbeatInterval = setInterval(() => {
        const currentWs = get().connections.get('main')
        if (currentWs && currentWs.readyState === WebSocket.OPEN) {
          currentWs.send(JSON.stringify({
            type: 'heartbeat',
            timestamp: Date.now()
          }))
        }
      }, HEARTBEAT_INTERVAL)
    }

    const handleMessage = (event: MessageEvent) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data)
        
        set(state => ({
          status: {
            ...state.status,
            lastMessage: message,
            error: null
          }
        }))

        // Route message to appropriate handlers
        const handlers = messageHandlers.get(message.type)
        if (handlers) {
          handlers.forEach(handler => {
            try {
              handler(message.data)
            } catch (error) {
              console.error(`Error in message handler for ${message.type}:`, error)
            }
          })
        }

        // Handle specific message types
        switch (message.type) {
          case 'heartbeat':
            // Heartbeat received, connection is healthy
            break
            
          case 'error':
            set(state => ({
              status: {
                ...state.status,
                error: message.data?.message || 'WebSocket error'
              }
            }))
            break
            
          default:
            console.debug('Received WebSocket message:', message.type, message.data)
        }
        
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error)
        set(state => ({
          status: {
            ...state.status,
            error: 'Invalid message format'
          }
        }))
      }
    }

    const scheduleReconnect = () => {
      const state = get()
      if (state.status.reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
        console.error('Max reconnect attempts reached')
        set(state => ({
          status: {
            ...state.status,
            connecting: false,
            error: 'Max reconnect attempts reached'
          }
        }))
        return
      }

      const delay = Math.min(
        RECONNECT_INTERVAL * Math.pow(2, state.status.reconnectAttempts),
        30000
      )

      console.log(`Scheduling reconnect in ${delay}ms (attempt ${state.status.reconnectAttempts + 1}/${MAX_RECONNECT_ATTEMPTS})`)
      
      reconnectTimeout = setTimeout(() => {
        get().connect()
      }, delay)
    }

    return {
      // Initial state
      connections: new Map(),
      status: {
        connected: false,
        connecting: false,
        lastMessage: null,
        error: null,
        reconnectAttempts: 0,
        subscriptions: new Set()
      },
      priceSubscriptions: new Set(),
      positionSubscriptions: new Set(),
      transactionSubscriptions: new Set(),

      // Connection management
      connect: async (url = DEFAULT_WS_URL) => {
        const currentState = get()
        const currentWs = currentState.connections.get('main')
        
        if (currentWs && currentWs.readyState === WebSocket.OPEN) {
          console.log('WebSocket already connected')
          return
        }
        
        if (currentState.status.connecting) {
          console.log('WebSocket already connecting')
          return
        }

        set(state => ({
          status: {
            ...state.status,
            connecting: true,
            error: null,
            reconnectAttempts: state.status.reconnectAttempts + 1
          }
        }))

        try {
          ws = new WebSocket(url)
          
          ws.onopen = () => {
            console.log('WebSocket connected')
            set(state => ({
              connections: new Map(state.connections).set('main', ws!),
              status: {
                ...state.status,
                connected: true,
                connecting: false,
                reconnectAttempts: 0,
                error: null
              }
            }))
            startHeartbeat()
          }

          ws.onmessage = handleMessage

          ws.onclose = (event) => {
            console.log('WebSocket closed:', event.code, event.reason)
            clearTimeouts()
            
            set(state => {
              const newConnections = new Map(state.connections)
              newConnections.delete('main')
              return {
                connections: newConnections,
                status: {
                  ...state.status,
                  connected: false,
                  connecting: false,
                  error: event.reason || `Connection closed (${event.code})`
                }
              }
            })

            // Only reconnect if it wasn't intentional (code 1000)
            if (event.code !== 1000) {
              scheduleReconnect()
            }
          }

          ws.onerror = (error) => {
            console.error('WebSocket error:', error)
            set(state => ({
              status: {
                ...state.status,
                error: 'Connection error',
                connecting: false
              }
            }))
          }

        } catch (error) {
          console.error('Failed to create WebSocket:', error)
          set(state => ({
            status: {
              ...state.status,
              error: 'Failed to create connection',
              connecting: false
            }
          }))
        }
      },

      disconnect: () => {
        const currentState = get()
        
        // Prevent multiple disconnects
        if (!currentState.status.connected && !currentState.status.connecting && currentState.connections.size === 0) {
          return
        }
        
        clearTimeouts()
        const connections = currentState.connections
        
        connections.forEach(ws => {
          if (ws.readyState === WebSocket.OPEN) {
            ws.close(1000, 'Intentional disconnect')
          }
        })

        set({
          connections: new Map(),
          status: {
            connected: false,
            connecting: false,
            lastMessage: null,
            error: null,
            reconnectAttempts: 0,
            subscriptions: new Set()
          },
          priceSubscriptions: new Set(),
          positionSubscriptions: new Set(),
          transactionSubscriptions: new Set()
        })
      },

      sendMessage: (message) => {
        const ws = get().connections.get('main')
        if (ws && ws.readyState === WebSocket.OPEN) {
          const messageWithTimestamp = {
            ...message,
            timestamp: Date.now()
          }
          ws.send(JSON.stringify(messageWithTimestamp))
          return true
        }
        return false
      },

      // Subscription management
      subscribeToPrices: async (tokens) => {
        const success = get().sendMessage({
          type: 'price_update',
          data: {
            action: 'subscribe',
            tokens
          }
        })

        if (success) {
          set(state => ({
            priceSubscriptions: new Set([...state.priceSubscriptions, ...tokens])
          }))
        }
      },

      unsubscribeFromPrices: async (tokens) => {
        const state = get()
        const tokensToUnsub = tokens || Array.from(state.priceSubscriptions)
        
        const success = get().sendMessage({
          type: 'price_update',
          data: {
            action: 'unsubscribe',
            tokens: tokensToUnsub
          }
        })

        if (success) {
          set(state => {
            const newSubscriptions = new Set(state.priceSubscriptions)
            tokensToUnsub.forEach(token => newSubscriptions.delete(token))
            return { priceSubscriptions: newSubscriptions }
          })
        }
      },

      subscribeToPositions: async (positionIds) => {
        const success = get().sendMessage({
          type: 'position_update',
          data: {
            action: 'subscribe',
            positionIds
          }
        })

        if (success) {
          set(state => ({
            positionSubscriptions: new Set([...state.positionSubscriptions, ...positionIds])
          }))
        }
      },

      subscribeToTransactions: async (transactionHashes) => {
        const success = get().sendMessage({
          type: 'transaction_update',
          data: {
            action: 'subscribe',
            transactionHashes
          }
        })

        if (success) {
          set(state => ({
            transactionSubscriptions: new Set([...state.transactionSubscriptions, ...transactionHashes])
          }))
        }
      },

      // Message handlers
      onPriceUpdate: (callback) => {
        const handlers = messageHandlers.get('price_update') || new Set()
        handlers.add(callback)
        messageHandlers.set('price_update', handlers)
        
        return () => {
          const handlers = messageHandlers.get('price_update')
          if (handlers) {
            handlers.delete(callback)
            if (handlers.size === 0) {
              messageHandlers.delete('price_update')
            }
          }
        }
      },

      onPositionUpdate: (callback) => {
        const handlers = messageHandlers.get('position_update') || new Set()
        handlers.add(callback)
        messageHandlers.set('position_update', handlers)
        
        return () => {
          const handlers = messageHandlers.get('position_update')
          if (handlers) {
            handlers.delete(callback)
            if (handlers.size === 0) {
              messageHandlers.delete('position_update')
            }
          }
        }
      },

      onTransactionUpdate: (callback) => {
        const handlers = messageHandlers.get('transaction_update') || new Set()
        handlers.add(callback)
        messageHandlers.set('transaction_update', handlers)
        
        return () => {
          const handlers = messageHandlers.get('transaction_update')
          if (handlers) {
            handlers.delete(callback)
            if (handlers.size === 0) {
              messageHandlers.delete('transaction_update')
            }
          }
        }
      },

      onStrategyTrigger: (callback) => {
        const handlers = messageHandlers.get('strategy_trigger') || new Set()
        handlers.add(callback)
        messageHandlers.set('strategy_trigger', handlers)
        
        return () => {
          const handlers = messageHandlers.get('strategy_trigger')
          if (handlers) {
            handlers.delete(callback)
            if (handlers.size === 0) {
              messageHandlers.delete('strategy_trigger')
            }
          }
        }
      },

      onAlert: (callback) => {
        const handlers = messageHandlers.get('alert') || new Set()
        handlers.add(callback)
        messageHandlers.set('alert', handlers)
        
        return () => {
          const handlers = messageHandlers.get('alert')
          if (handlers) {
            handlers.delete(callback)
            if (handlers.size === 0) {
              messageHandlers.delete('alert')
            }
          }
        }
      },

      // Utilities
      getConnectionStatus: () => get().status,

      clearError: () => {
        set(state => ({
          status: {
            ...state.status,
            error: null
          }
        }))
      }
    }
  })
)

// Auto-connect hook
export const useAutoConnect = (autoConnect = true) => {
  const status = useWebSocketStore(state => state.status)

  // Connect effect
  React.useEffect(() => {
    if (autoConnect && !status.connected && !status.connecting) {
      useWebSocketStore.getState().connect()
    }
  }, [autoConnect, status.connected, status.connecting])

  // Disconnect on unmount only
  React.useEffect(() => {
    return () => {
      if (autoConnect) {
        useWebSocketStore.getState().disconnect()
      }
    }
  }, [autoConnect]) // Only depend on autoConnect, not status

  return status
}

// Price subscription hook
export const usePriceSubscription = (tokens: string[], autoSubscribe = true) => {
  const connected = useWebSocketStore(state => state.status.connected)
  const [prices, setPrices] = React.useState<Map<string, any>>(new Map())

  React.useEffect(() => {
    if (!connected || !autoSubscribe || tokens.length === 0) {
      return
    }

    const store = useWebSocketStore.getState()
    store.subscribeToPrices(tokens)

    const unsubscribe = store.onPriceUpdate((data) => {
      if (data.token && tokens.includes(data.token)) {
        setPrices(prev => new Map(prev).set(data.token, data))
      }
    })

    return () => {
      unsubscribe()
      useWebSocketStore.getState().unsubscribeFromPrices(tokens)
    }
  }, [tokens, connected, autoSubscribe])

  return {
    prices: Object.fromEntries(prices),
    connected
  }
}

// Import React for hooks
import React from 'react'