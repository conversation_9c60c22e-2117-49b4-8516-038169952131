'use client'

import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { useWebSocketStore } from './websocketStore'

interface Position {
  id: string
  tokenAddress: string
  tokenSymbol: string
  tokenName?: string
  entryPrice: number
  currentPrice: number
  quantity: number
  entryTimestamp: Date
  status: 'ACTIVE' | 'PARTIAL' | 'CLOSED' | 'PENDING'
  pnl: number
  pnlPercentage: number
  marketValue: number
  age: number
  strategy?: {
    id: string
    type: string
    executionState: string
  }
  lastTransaction?: {
    hash: string
    timestamp: Date
  }
  priceData?: {
    price: number
    change24h: number
    volume24h: number
    timestamp: number
  }
}

interface PositionUpdate {
  positionId: string
  currentPrice: number
  pnl: number
  pnlPercentage: number
  marketValue: number
  timestamp: number
}

interface PositionStore {
  // State
  positions: Map<string, Position>
  loading: boolean
  error: string | null
  lastUpdate: number
  totalValue: number
  totalPnl: number
  totalPnlPercentage: number
  
  // Filters and sorting
  filters: {
    status: string[]
    tokens: string[]
    strategies: string[]
    minPnl: number | null
    maxPnl: number | null
  }
  sortBy: 'pnl' | 'pnlPercentage' | 'marketValue' | 'age' | 'entryPrice'
  sortOrder: 'asc' | 'desc'
  
  // Actions
  loadPositions: () => Promise<void>
  addPosition: (position: Position) => void
  updatePosition: (positionId: string, updates: Partial<Position>) => void
  removePosition: (positionId: string) => void
  
  // Real-time updates
  handlePositionUpdate: (update: PositionUpdate) => void
  handlePriceUpdate: (tokenAddress: string, newPrice: number) => void
  subscribeToPositions: (positionIds: string[]) => void
  unsubscribeFromPositions: (positionIds?: string[]) => void
  
  // Filtering and sorting
  setFilters: (filters: Partial<PositionStore['filters']>) => void
  setSorting: (sortBy: PositionStore['sortBy'], sortOrder: PositionStore['sortOrder']) => void
  getFilteredPositions: () => Position[]
  
  // Analytics
  calculatePortfolioMetrics: () => {
    totalValue: number
    totalPnl: number
    totalPnlPercentage: number
    winningPositions: number
    losingPositions: number
    winRate: number
    bestPerformer: Position | null
    worstPerformer: Position | null
  }
  
  // Utilities
  getPosition: (id: string) => Position | undefined
  getPositionsByToken: (tokenAddress: string) => Position[]
  getPositionsByStrategy: (strategyId: string) => Position[]
  clearError: () => void
  refresh: () => Promise<void>
}

export const usePositionStore = create<PositionStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    positions: new Map(),
    loading: false,
    error: null,
    lastUpdate: 0,
    totalValue: 0,
    totalPnl: 0,
    totalPnlPercentage: 0,
    
    filters: {
      status: ['ACTIVE', 'PARTIAL'],
      tokens: [],
      strategies: [],
      minPnl: null,
      maxPnl: null
    },
    sortBy: 'pnlPercentage',
    sortOrder: 'desc',

    // Load positions from API
    loadPositions: async () => {
      set({ loading: true, error: null })
      
      try {
        const response = await fetch('/api/realtime/positions/active', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
            'Content-Type': 'application/json'
          }
        })

        if (!response.ok) {
          throw new Error(`Failed to load positions: ${response.statusText}`)
        }

        const data = await response.json()
        
        if (data.success) {
          const positionsMap = new Map<string, Position>()
          
          data.data.forEach((pos: any) => {
            positionsMap.set(pos.id, {
              ...pos,
              entryTimestamp: new Date(pos.entryTimestamp),
              lastTransaction: pos.lastTransaction ? {
                ...pos.lastTransaction,
                timestamp: new Date(pos.lastTransaction.timestamp)
              } : undefined
            })
          })

          set({ 
            positions: positionsMap, 
            loading: false,
            lastUpdate: Date.now()
          })

          // Subscribe to real-time updates for loaded positions
          const positionIds = Array.from(positionsMap.keys())
          if (positionIds.length > 0) {
            get().subscribeToPositions(positionIds)
          }

          // Recalculate metrics
          get().calculatePortfolioMetrics()
          
        } else {
          throw new Error(data.message || 'Failed to load positions')
        }
      } catch (error) {
        console.error('Error loading positions:', error)
        set({ 
          error: error instanceof Error ? error.message : 'Unknown error',
          loading: false 
        })
      }
    },

    addPosition: (position) => {
      set(state => {
        const newPositions = new Map(state.positions)
        newPositions.set(position.id, position)
        return { 
          positions: newPositions,
          lastUpdate: Date.now()
        }
      })
      
      // Subscribe to updates for the new position
      get().subscribeToPositions([position.id])
    },

    updatePosition: (positionId, updates) => {
      set(state => {
        const position = state.positions.get(positionId)
        if (!position) return state

        const updatedPosition = { ...position, ...updates }
        const newPositions = new Map(state.positions)
        newPositions.set(positionId, updatedPosition)
        
        return { 
          positions: newPositions,
          lastUpdate: Date.now()
        }
      })
    },

    removePosition: (positionId) => {
      set(state => {
        const newPositions = new Map(state.positions)
        newPositions.delete(positionId)
        return { 
          positions: newPositions,
          lastUpdate: Date.now()
        }
      })
      
      // Unsubscribe from updates
      get().unsubscribeFromPositions([positionId])
    },

    // Handle real-time position updates
    handlePositionUpdate: (update) => {
      const position = get().positions.get(update.positionId)
      if (!position) return

      const updatedPosition: Position = {
        ...position,
        currentPrice: update.currentPrice,
        pnl: update.pnl,
        pnlPercentage: update.pnlPercentage,
        marketValue: update.marketValue,
        age: Date.now() - position.entryTimestamp.getTime()
      }

      get().updatePosition(update.positionId, updatedPosition)
      
      // Recalculate portfolio metrics after position update
      get().calculatePortfolioMetrics()
    },

    // Handle real-time price updates for positions
    handlePriceUpdate: (tokenAddress: string, newPrice: number) => {
      const { positions } = get()
      let hasUpdates = false

      positions.forEach((position, positionId) => {
        if (position.tokenAddress === tokenAddress) {
          const quantity = position.quantity
          const entryPrice = position.entryPrice
          const marketValue = newPrice * quantity
          const pnl = marketValue - (entryPrice * quantity)
          const pnlPercentage = entryPrice > 0 ? ((newPrice / entryPrice) - 1) * 100 : 0

          get().updatePosition(positionId, {
            currentPrice: newPrice,
            marketValue,
            pnl,
            pnlPercentage,
            priceData: {
              price: newPrice,
              change24h: position.priceData?.change24h || 0,
              volume24h: position.priceData?.volume24h || 0,
              timestamp: Date.now()
            }
          })
          hasUpdates = true
        }
      })

      if (hasUpdates) {
        // Recalculate portfolio metrics after price updates
        get().calculatePortfolioMetrics()
      }
    },

    subscribeToPositions: (positionIds) => {
      const wsStore = useWebSocketStore.getState()
      wsStore.subscribeToPositions(positionIds)
    },

    unsubscribeFromPositions: (positionIds) => {
      // WebSocket store handles unsubscription
      // This is a placeholder for future implementation
    },

    // Filtering and sorting
    setFilters: (newFilters) => {
      set(state => ({
        filters: { ...state.filters, ...newFilters }
      }))
    },

    setSorting: (sortBy, sortOrder) => {
      set({ sortBy, sortOrder })
    },

    getFilteredPositions: () => {
      const { positions, filters, sortBy, sortOrder } = get()
      
      let filteredPositions = Array.from(positions.values())

      // Apply status filter
      if (filters.status.length > 0) {
        filteredPositions = filteredPositions.filter(pos => 
          filters.status.includes(pos.status)
        )
      }

      // Apply token filter
      if (filters.tokens.length > 0) {
        filteredPositions = filteredPositions.filter(pos =>
          filters.tokens.includes(pos.tokenAddress) ||
          filters.tokens.includes(pos.tokenSymbol)
        )
      }

      // Apply strategy filter
      if (filters.strategies.length > 0) {
        filteredPositions = filteredPositions.filter(pos =>
          pos.strategy && filters.strategies.includes(pos.strategy.id)
        )
      }

      // Apply PnL filters
      if (filters.minPnl !== null) {
        filteredPositions = filteredPositions.filter(pos => pos.pnl >= filters.minPnl!)
      }
      if (filters.maxPnl !== null) {
        filteredPositions = filteredPositions.filter(pos => pos.pnl <= filters.maxPnl!)
      }

      // Sort positions
      filteredPositions.sort((a, b) => {
        let aValue: number, bValue: number

        switch (sortBy) {
          case 'pnl':
            aValue = a.pnl
            bValue = b.pnl
            break
          case 'pnlPercentage':
            aValue = a.pnlPercentage
            bValue = b.pnlPercentage
            break
          case 'marketValue':
            aValue = a.marketValue
            bValue = b.marketValue
            break
          case 'age':
            aValue = a.age
            bValue = b.age
            break
          case 'entryPrice':
            aValue = a.entryPrice
            bValue = b.entryPrice
            break
          default:
            aValue = a.pnlPercentage
            bValue = b.pnlPercentage
        }

        return sortOrder === 'asc' ? aValue - bValue : bValue - aValue
      })

      return filteredPositions
    },

    // Analytics
    calculatePortfolioMetrics: () => {
      const positions = Array.from(get().positions.values())
      
      if (positions.length === 0) {
        const emptyMetrics = {
          totalValue: 0,
          totalPnl: 0,
          totalPnlPercentage: 0,
          winningPositions: 0,
          losingPositions: 0,
          winRate: 0,
          bestPerformer: null,
          worstPerformer: null
        }
        
        set({
          totalValue: 0,
          totalPnl: 0,
          totalPnlPercentage: 0
        })
        
        return emptyMetrics
      }

      const totalValue = positions.reduce((sum, pos) => sum + pos.marketValue, 0)
      const totalPnl = positions.reduce((sum, pos) => sum + pos.pnl, 0)
      const totalInitialValue = positions.reduce((sum, pos) => sum + (pos.entryPrice * pos.quantity), 0)
      const totalPnlPercentage = totalInitialValue > 0 ? (totalPnl / totalInitialValue) * 100 : 0

      const winningPositions = positions.filter(pos => pos.pnl > 0).length
      const losingPositions = positions.filter(pos => pos.pnl < 0).length
      const winRate = positions.length > 0 ? (winningPositions / positions.length) * 100 : 0

      const bestPerformer = positions.reduce((best, pos) => 
        !best || pos.pnlPercentage > best.pnlPercentage ? pos : best, null as Position | null
      )
      
      const worstPerformer = positions.reduce((worst, pos) => 
        !worst || pos.pnlPercentage < worst.pnlPercentage ? pos : worst, null as Position | null
      )

      // Update store state
      set({
        totalValue,
        totalPnl,
        totalPnlPercentage
      })

      return {
        totalValue,
        totalPnl,
        totalPnlPercentage,
        winningPositions,
        losingPositions,
        winRate,
        bestPerformer,
        worstPerformer
      }
    },

    // Utilities
    getPosition: (id) => get().positions.get(id),

    getPositionsByToken: (tokenAddress) => {
      return Array.from(get().positions.values()).filter(pos => 
        pos.tokenAddress === tokenAddress
      )
    },

    getPositionsByStrategy: (strategyId) => {
      return Array.from(get().positions.values()).filter(pos => 
        pos.strategy?.id === strategyId
      )
    },

    clearError: () => set({ error: null }),

    refresh: async () => {
      await get().loadPositions()
    }
  }))
)

// Set up WebSocket integration
const setupWebSocketIntegration = () => {
  const wsStore = useWebSocketStore.getState()
  
  // Listen for position updates
  const unsubscribePositionUpdates = wsStore.onPositionUpdate((data) => {
    const positionStore = usePositionStore.getState()
    positionStore.handlePositionUpdate(data)
  })

  // Listen for price updates and update positions accordingly
  const unsubscribePriceUpdates = wsStore.onPriceUpdate((data) => {
    const positionStore = usePositionStore.getState()
    
    if (data.token && data.price) {
      positionStore.handlePriceUpdate(data.token, data.price)
    }
  })

  // Listen for strategy triggers
  const unsubscribeStrategyTriggers = wsStore.onStrategyTrigger((data) => {
    const positionStore = usePositionStore.getState()
    
    // Update position status when strategy triggers
    if (data.positionId) {
      const position = positionStore.getPosition(data.positionId)
      if (position) {
        positionStore.updatePosition(data.positionId, {
          quantity: position.quantity - data.sellAmount,
          status: position.quantity - data.sellAmount <= 0 ? 'CLOSED' : 'PARTIAL'
        })
      }
    }
  })

  return () => {
    unsubscribePositionUpdates()
    unsubscribePriceUpdates()
    unsubscribeStrategyTriggers()
  }
}

// Auto-setup WebSocket integration
if (typeof window !== 'undefined') {
  setupWebSocketIntegration()
}

// Hook for easy position management
export const usePositions = (autoLoad = true) => {
  const store = usePositionStore()
  
  React.useEffect(() => {
    if (autoLoad && store.positions.size === 0 && !store.loading) {
      store.loadPositions()
    }
  }, [autoLoad, store.positions.size, store.loading, store.loadPositions])

  return {
    positions: store.getFilteredPositions(),
    totalValue: store.totalValue,
    totalPnl: store.totalPnl,
    totalPnlPercentage: store.totalPnlPercentage,
    loading: store.loading,
    error: store.error,
    metrics: store.calculatePortfolioMetrics(),
    filters: store.filters,
    setFilters: store.setFilters,
    setSorting: store.setSorting,
    refresh: store.refresh,
    clearError: store.clearError
  }
}

// Hook for individual position tracking
export const usePosition = (positionId: string) => {
  const position = usePositionStore(state => state.getPosition(positionId))
  const updatePosition = usePositionStore(state => state.updatePosition)
  
  return {
    position,
    updatePosition: (updates: Partial<Position>) => updatePosition(positionId, updates)
  }
}

import React from 'react'