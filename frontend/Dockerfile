# Multi-stage Dockerfile for Next.js Frontend
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Copy workspace files
COPY package.json package-lock.json ./
COPY frontend/package.json ./frontend/
COPY shared/package.json ./shared/
COPY backend/package.json ./backend/

# Install workspace dependencies
RUN npm ci --only=production --workspaces

# Development stage
FROM base AS development
RUN apk add --no-cache libc6-compat curl
WORKDIR /app

# Copy workspace files for dependencies
COPY package.json package-lock.json ./
COPY frontend/package.json ./frontend/
COPY shared/package.json ./shared/
COPY backend/package.json ./backend/

# Install workspace dependencies 
RUN npm ci --workspaces

# Build shared package
COPY shared/ ./shared/
RUN npm run build --workspace=shared

# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy source code
COPY --chown=nextjs:nodejs frontend/ ./frontend/
COPY --chown=nextjs:nodejs shared/ ./shared/
COPY --chown=nextjs:nodejs package.json package-lock.json ./

# Ensure shared module is accessible via symlink  
RUN chown -R nextjs:nodejs ./shared

# Install frontend dependencies directly in frontend directory
WORKDIR /app/frontend
RUN npm install @tanstack/react-query@^5.84.1 react-hot-toast@^2.5.2

# Expose port
EXPOSE 3000

# Switch to non-root user
USER nextjs

# Development command with hot reload and increased memory
CMD ["npm", "run", "dev"]

# Production build stage
FROM base AS builder
WORKDIR /app

# Copy dependencies and workspace files from deps stage
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/frontend ./frontend
COPY --from=deps /app/shared ./shared
COPY --from=deps /app/package.json /app/package-lock.json ./
COPY frontend/ ./frontend/

# Set environment variables for build
ENV NEXT_TELEMETRY_DISABLED 1

# Build the application
WORKDIR /app/frontend
RUN npm run build

# Production stage
FROM base AS production
RUN apk add --no-cache curl
WORKDIR /app

# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Set environment variables
ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

# Copy built application
COPY --from=builder --chown=nextjs:nodejs /app/frontend/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/frontend/.next/static ./.next/static
COPY --from=builder --chown=nextjs:nodejs /app/frontend/public ./public

# Expose port
EXPOSE 3000

# Switch to non-root user
USER nextjs

# Production command
CMD ["node", "server.js"]