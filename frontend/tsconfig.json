{"compilerOptions": {"target": "ES2022", "lib": ["dom", "dom.iterable", "ES2022"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/lib/*": ["./src/lib/*"], "@/hooks/*": ["./src/hooks/*"], "@/stores/*": ["./src/stores/*"], "@/types/*": ["./src/types/*"], "@/utils/*": ["./src/utils/*"], "@/config/*": ["./src/config/*"], "shared/src": ["../shared/src"], "shared/src/*": ["../shared/src/*"], "@memetrader-pro/shared": ["../shared/src"], "@memetrader-pro/shared/*": ["../shared/src/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}