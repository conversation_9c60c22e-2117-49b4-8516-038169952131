{"name": "@memetrader-pro/frontend", "version": "1.0.0", "description": "MemeTrader Pro Next.js frontend application", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "clean": "rm -rf .next out dist"}, "dependencies": {"@memetrader-pro/shared": "file:../shared", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-slot": "^1.2.3", "@solana/web3.js": "^1.98.4", "bs58": "^6.0.0", "class-variance-authority": "^0.7.1", "jsonwebtoken": "^9.0.2", "clsx": "^2.0.0", "cmdk": "^1.1.1", "lucide-react": "^0.533.0", "next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-window": "^1.8.11", "recharts": "^3.1.0", "tailwind-merge": "^2.2.0", "zod": "^3.22.4", "zustand": "^5.0.6"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/react-window": "^1.8.8", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.3.3"}}