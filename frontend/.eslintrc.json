{"extends": ["next/core-web-vitals", "@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/prefer-const": "error", "prefer-const": "off", "react-hooks/exhaustive-deps": "warn", "react/no-unescaped-entities": "off"}}