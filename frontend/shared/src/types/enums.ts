// Core Trading Enums
export enum PresetType {
  DEFAULT = 'DEFAULT',
  VOL = 'VOL',        // High volatility
  DEAD = 'DEAD',      // Low volume
  NUN = 'NUN',        // New/unknown
  P5 = 'P5'           // Premium tokens
}

export enum RiskLevel {
  CONSERVATIVE = 'CONSERVATIVE',  // 0.5%
  MODERATE = 'MODERATE',         // 1-2%
  BALANCED = 'BALANCED',         // 3-5%
  AGGRESSIVE = 'AGGRESSIVE',     // 6-10%
  EXTREME = 'EXTREME'            // 10%+
}

export enum PositionStatus {
  ACTIVE = 'ACTIVE',
  CLOSED = 'CLOSED',
  PARTIAL = 'PARTIAL',
  LIQUIDATED = 'LIQUIDATED',
  PENDING = 'PENDING'
}

export enum StrategyType {
  STOP_LOSS = 'STOP_LOSS',
  PROFIT_TARGET = 'PROFIT_TARGET',
  TRAILING_STOP = 'TRAILING_STOP',
  MOON_BAG = 'MOON_BAG',
  CUSTOM = 'CUSTOM',
  PRD_COMPLIANT = 'PRD_COMPLIANT'
}

export enum TransactionType {
  BUY = 'BUY',
  SELL = 'SELL',
  PARTIAL_SELL = 'PARTIAL_SELL',
  STOP_LOSS = 'STOP_LOSS',
  PROFIT_TARGET = 'PROFIT_TARGET'
}

export enum AlertType {
  TRADE = 'TRADE',
  EXIT = 'EXIT',
  ERROR = 'ERROR',
  SYSTEM = 'SYSTEM',
  PRICE = 'PRICE',
  STRATEGY = 'STRATEGY'
}

export enum AlertPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export enum MEVProtectionLevel {
  NONE = 'NONE',
  BASIC = 'BASIC',
  STANDARD = 'STANDARD',
  MAXIMUM = 'MAXIMUM'
}

export enum StrategyExecutionState {
  INACTIVE = 'INACTIVE',
  MONITORING = 'MONITORING',
  TRIGGERED = 'TRIGGERED',
  EXECUTING = 'EXECUTING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

export enum ErrorType {
  // Network & Connectivity
  NETWORK_ERROR = 'NETWORK_ERROR',
  WEBSOCKET_DISCONNECTED = 'WEBSOCKET_DISCONNECTED',
  RPC_ERROR = 'RPC_ERROR',

  // Trading Execution
  INSUFFICIENT_FUNDS = 'INSUFFICIENT_FUNDS',
  SLIPPAGE_EXCEEDED = 'SLIPPAGE_EXCEEDED',
  MEV_PROTECTION_FAILED = 'MEV_PROTECTION_FAILED',
  JUPITER_API_ERROR = 'JUPITER_API_ERROR',

  // Strategy Execution
  STRATEGY_EXECUTION_FAILED = 'STRATEGY_EXECUTION_FAILED',
  TRIGGER_CONDITION_ERROR = 'TRIGGER_CONDITION_ERROR',
  POSITION_NOT_FOUND = 'POSITION_NOT_FOUND',

  // Validation
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  PRESET_INVALID = 'PRESET_INVALID',
  CUSTOM_STRATEGY_INVALID = 'CUSTOM_STRATEGY_INVALID'
}
