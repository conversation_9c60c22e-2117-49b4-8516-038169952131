# Development Server Guide

## Quick Start

Start the development server in the background:
```bash
./dev-server.sh start
```

The server will run with automatic hot reload enabled for both frontend and backend.

## Available Commands

### Start Server
```bash
./dev-server.sh start
```
- Starts both frontend (Next.js on port 3000) and backend (Express on port 5000)
- Runs in background with output redirected to `dev.log`
- Saves process ID to `dev.pid`

### Stop Server
```bash
./dev-server.sh stop
# or
kill $(cat dev.pid) && rm dev.pid dev.log
```

### Check Status
```bash
./dev-server.sh status
```
Shows if the server is running and which ports are active.

### View Logs
```bash
./dev-server.sh logs
# or
tail -f dev.log
```

### Restart Server
```bash
./dev-server.sh restart
```

## Hot Reload

Both frontend and backend automatically reload when you make changes:

- **Frontend (Next.js)**: Instant hot module replacement for React components
- **Backend (Express)**: Automatic restart via `tsx watch`

No manual restart needed! Just save your files and the changes will be reflected immediately.

## Troubleshooting

If the server fails to start:
1. Check `dev.log` for error messages
2. Ensure ports 3000 and 5000 are not in use
3. Make sure dependencies are installed: `npm install`

## Port Configuration

- Frontend: http://localhost:3000
- Backend API: http://localhost:5000