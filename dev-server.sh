#!/bin/bash

# Development Server Manager Script
# This script manages the development server with hot reload in the background

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to start the server
start_server() {
    if [ -f "dev.pid" ]; then
        PID=$(cat dev.pid)
        if ps -p $PID > /dev/null 2>&1; then
            echo -e "${YELLOW}Server is already running with PID: $PID${NC}"
            echo "Use 'tail -f dev.log' to view logs"
            return 1
        fi
    fi
    
    echo -e "${GREEN}Starting development server in background...${NC}"
    
    # Start the development server in background
    nohup npm run dev > dev.log 2>&1 &
    SERVER_PID=$!
    
    # Save the PID
    echo $SERVER_PID > dev.pid
    
    # Wait a moment for server to start
    sleep 3
    
    # Check if server started successfully
    if ps -p $SERVER_PID > /dev/null 2>&1; then
        echo -e "${GREEN}✓ Server started successfully!${NC}"
        echo -e "  PID: ${SERVER_PID}"
        echo -e "  Logs: tail -f dev.log"
        echo -e "  Stop: ./dev-server.sh stop"
        echo ""
        echo -e "${YELLOW}The server will automatically hot reload when you make changes.${NC}"
        
        # Show initial logs
        echo -e "\n${GREEN}Initial server output:${NC}"
        tail -n 20 dev.log
    else
        echo -e "${RED}✗ Failed to start server${NC}"
        echo "Check dev.log for errors"
        rm -f dev.pid
        return 1
    fi
}

# Function to stop the server
stop_server() {
    if [ -f "dev.pid" ]; then
        PID=$(cat dev.pid)
        if ps -p $PID > /dev/null 2>&1; then
            echo -e "${YELLOW}Stopping server (PID: $PID)...${NC}"
            kill $PID
            rm -f dev.pid
            echo -e "${GREEN}✓ Server stopped${NC}"
        else
            echo -e "${YELLOW}Server is not running (stale PID file)${NC}"
            rm -f dev.pid
        fi
    else
        echo -e "${YELLOW}Server is not running${NC}"
    fi
}

# Function to check server status
check_status() {
    if [ -f "dev.pid" ]; then
        PID=$(cat dev.pid)
        if ps -p $PID > /dev/null 2>&1; then
            echo -e "${GREEN}✓ Server is running${NC}"
            echo -e "  PID: ${PID}"
            echo -e "  Uptime: $(ps -o etime= -p $PID | xargs)"
            echo -e "  Logs: tail -f dev.log"
            
            # Check if ports are listening
            echo -e "\n${GREEN}Active ports:${NC}"
            lsof -i -P -n | grep LISTEN | grep -E "node|next" | grep -E ":3000|:5000" || echo "  Waiting for ports..."
        else
            echo -e "${RED}✗ Server is not running${NC} (stale PID file)"
            rm -f dev.pid
        fi
    else
        echo -e "${YELLOW}Server is not running${NC}"
    fi
}

# Function to show logs
show_logs() {
    if [ -f "dev.log" ]; then
        echo -e "${GREEN}Following server logs (Ctrl+C to exit)...${NC}"
        tail -f dev.log
    else
        echo -e "${RED}No log file found${NC}"
    fi
}

# Function to restart the server
restart_server() {
    echo -e "${YELLOW}Restarting server...${NC}"
    stop_server
    sleep 2
    start_server
}

# Main script logic
case "$1" in
    start)
        start_server
        ;;
    stop)
        stop_server
        ;;
    restart)
        restart_server
        ;;
    status)
        check_status
        ;;
    logs)
        show_logs
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status|logs}"
        echo ""
        echo "Commands:"
        echo "  start   - Start the development server in background"
        echo "  stop    - Stop the running server"
        echo "  restart - Restart the server"
        echo "  status  - Check if server is running"
        echo "  logs    - Follow the server logs"
        echo ""
        echo "Quick commands:"
        echo "  View logs:    tail -f dev.log"
        echo "  Stop server:  kill \$(cat dev.pid) && rm dev.pid dev.log"
        exit 1
        ;;
esac