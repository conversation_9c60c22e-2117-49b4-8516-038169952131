# Design Document

## Overview

MemeTrader Pro is architected as a full-stack, real-time trading platform built with modern web technologies optimized for speed and reliability. The system follows a microservices-inspired architecture with a React TypeScript frontend and Node.js TypeScript backend, connected via Socket.io for real-time communication. The platform integrates with Solana blockchain via Jupiter aggregator and Helius WebSocket for optimal trade execution and price feeds while providing comprehensive risk management and portfolio monitoring capabilities.

## Architecture

### High-Level System Architecture

```mermaid
graph TB
    subgraph "Frontend (Next.js + TypeScript)"
        UI[Next.js + React + TypeScript]
        Router[Next.js App Router]
        State[Zustand State Management]
        Components[shadcn/ui Components]
        Socket[Socket.io Client]
        Charts[Recharts Visualization]
    end

    subgraph "Backend (Node.js + TypeScript)"
        API[Express.js REST API]
        WS[Socket.io Server]
        Queue[BullMQ Job Queue]
        DB[Prisma ORM]
        Cache[Redis Cache]
        Logger[Pino Logging]
    end

    subgraph "Core Modules"
        TC[Trading Command Center]
        MC[Mission Control Dashboard]
        ES[Exit Strategy Manager]
        AP[Active Positions]
        AL[Alert System]
        TH[Transaction History]
    end

    subgraph "External Servi
   PG[(PostgreSQL)]
        RD[(Redis)]
        JP[Jupiter API]
        HWS[Helius WebSocket]
        SP[Solana RPC]
        WP[Wallet Provider]
    end

    UI --> State
    State --> Socket
    Socket --> WS
    API --> DB
    DB --> PG
    Queue --> RD
    Cache --> RD

    TC --> API
    MC --> Socket
    ES --> Queue
    AP --> Socket
    AL --> WS
    TH --> API

    API --> JP
    WS --> HWS
    Queue --> SP
    API --> WP
```

### Technology Stack Implementation

#### Frontend Stack
- **Next.js**: Production-ready React framework with Server-Side Rendering (SSR), Static Site Generation (SSG), and API routes.
- **React 18 + TypeScript**: Modern, type-safe UI with concurrent features, integrated within the Next.js framework.
- **Next.js App Router**: Advanced routing for server and client components, enabling seamless navigation and optimized performance.
- **shadcn/ui**: Composable, accessible UI components with Tailwind CSS
- **Zustand**: Lightweight, performant state management with TypeScript support
- **Socket.io-client**: Real-time bidirectional communication for live updates
- **Recharts**: Responsive charts for portfolio visualization and analytics
- **Framer Motion**: Smooth animations for enhanced user experience
- **react-hot-toast**: Toast notifications for trading actions and alerts

#### Backend Stack
- **Node.js + TypeScript**: High-performance runtime with type safety
- **Express.js**: Fast, minimalist web framework for REST API
- **Prisma**: Type-safe ORM with automatic migrations and query optimization
- **PostgreSQL**: Robust relational database for transaction and user data
- **Redis**: In-memory cache for sessions, real-time data, and job queues
- **BullMQ**: Reliable job queue for strategy execution and background tasks
- **Socket.io**: Real-time WebSocket communication for live updates
- **Zod**: Runtime type validation for API requests and responses
- **Pino**: High-performance JSON logging with structured output

#### Blockchain Integration
- **web3.js**: Solana blockchain interaction and wallet management
- **Jupiter SDK**: Optimal swap routing and execution
- **Helius WebSocket**: Real-time price feeds and blockchain events

## Components and Interfaces

### Frontend Architecture

#### Core State Management (Zustand)
```typescript
// Trading State Slice
interface TradingState {
  activePreset: PresetType
  presets: TradingPreset[]
  portfolioValue: number
  positionSizer: PositionSizer

  // Actions
  switchPreset: (preset: PresetType) => void
  updatePortfolioValue: (value: number) => void
  calculatePositionSize: (riskLevel: RiskLevel) => number
}

// Portfolio State Slice
interface PortfolioState {
  positions: Position[]
  metrics: PortfolioMetrics
  exposureMeter: ExposureMeter
  riskScore: RiskScore

  // Actions
  updatePosition: (position: Position) => void
  calculateRisk: () => void
  updateExposure: () => void
}

// Strategy State Slice
interface StrategyState {
  activeStrategies: Map<string, ExitStrategy>
  templates: StrategyTemplate[]
  customStrategies: CustomStrategy[]

  // Actions
  createStrategy: (template: StrategyTemplate) => ExitStrategy
  saveCustomStrategy: (strategy: CustomStrategy) => void
  executeStrategy: (positionId: string) => Promise<void>
}
```

#### Component Architecture
```typescript
// Trading Command Center Components
interface TradingCommandCenter {
  PresetTabs: React.FC<{ presets: TradingPreset[] }>
  PositionSizer: React.FC<{ portfolioValue: number }>
  SlippageManager: React.FC<{ preset: TradingPreset }>
  MEVProtection: React.FC<{ level: MEVProtectionLevel }>
  TradeExecutor: React.FC<{ params: TradeParams }>
}

// Mission Control Components
interface MissionControlDashboard {
  ExposureMeter: React.FC<{ exposure: ExposureMeter }>
  AllocationChart: React.FC<{ data: AllocationData[] }>
  RiskAnalytics: React.FC<{ riskScore: RiskScore }>
  PerformanceMetrics: React.FC<{ metrics: PerformanceMetrics }>
}

// Exit Strategy Components
interface ExitStrategyManager {
  StrategySelector: React.FC<{ templates: StrategyTemplate[] }>
  CustomStrategyEditor: React.FC<{ onSave: (strategy: CustomStrategy) => void }>
  StrategyProgress: React.FC<{ strategy: ExitStrategy }>
  PRDCompliance: React.FC<{ strategy: ExitStrategy }>
}
```

### Backend Architecture

#### API Layer (Express.js)
```typescript
// Trading API Routes
interface TradingAPI {
  'POST /api/trades/execute': (params: TradeParams) => Promise<TradeResult>
  'GET /api/trades/quote': (params: QuoteParams) => Promise<Quote>
  'POST /api/trades/simulate': (params: SimulationParams) => Promise<SimulationResult>
  'GET /api/presets': () => Promise<TradingPreset[]>
  'PUT /api/presets/:id': (preset: TradingPreset) => Promise<void>
}

// Portfolio API Routes
interface PortfolioAPI {
  'GET /api/portfolio/metrics': () => Promise<PortfolioMetrics>
  'GET /api/portfolio/positions': () => Promise<Position[]>
  'GET /api/portfolio/risk': () => Promise<RiskAnalytics>
  'POST /api/portfolio/rebalance': (params: RebalanceParams) => Promise<void>
}

// Strategy API Routes
interface StrategyAPI {
  'POST /api/strategies/create': (strategy: ExitStrategy) => Promise<string>
  'PUT /api/strategies/:id': (strategy: ExitStrategy) => Promise<void>
  'DELETE /api/strategies/:id': () => Promise<void>
  'GET /api/strategies/templates': () => Promise<StrategyTemplate[]>
  'POST /api/strategies/custom': (strategy: CustomStrategy) => Promise<void>
}
```

#### Job Queue System (BullMQ)
```typescript
// Strategy Execution Jobs
interface StrategyJobs {
  'strategy-monitor': {
    data: { positionId: string; strategyId: string }
    opts: { repeat: { every: 500 } } // 500ms monitoring
  }

  'profit-target-hit': {
    data: { positionId: string; targetLevel: number }
    opts: { priority: 1 } // High priority
  }

  'stop-loss-triggered': {
    data: { positionId: string; emergencySlippage: boolean }
    opts: { priority: 0 } // Highest priority
  }

  'trailing-stop-update': {
    data: { positionId: string; newStopPrice: number }
    opts: { repeat: { every: 500 } }
  }
}

// Price Monitoring Jobs
interface PriceJobs {
  'price-update': {
    data: { tokens: string[] }
    opts: { repeat: { every: 100 } } // 100ms price updates
  }

  'mev-analysis': {
    data: { tradeParams: TradeParams }
    opts: { priority: 2 }
  }
}
```

#### Database Schema (Prisma)
```prisma
model User {
  id          String    @id @default(cuid())
  walletAddress String  @unique
  preferences UserPreferences?
  positions   Position[]
  transactions Transaction[]
  strategies  ExitStrategy[]
  customStrategies CustomStrategy[]
  alerts      Alert[]
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

model Position {
  id            String    @id @default(cuid())
  userId        String
  tokenAddress  String
  tokenSymbol   String
  entryPrice    Decimal   @db.Decimal(20, 9)
  currentPrice  Decimal   @db.Decimal(20, 9)
  quantity      Decimal   @db.Decimal(20, 9)
  entryTimestamp DateTime
  strategyId    String?
  presetUsed    String
  riskLevel     RiskLevel
  status        PositionStatus

  user          User      @relation(fields: [userId], references: [id])
  strategy      ExitStrategy? @relation(fields: [strategyId], references: [id])
  transactions  Transaction[]

  @@index([userId, status])
  @@index([tokenAddress])
}

model ExitStrategy {
  id              String    @id @default(cuid())
  userId          String
  positionId      String?
  type            StrategyType
  stopLoss        Json      // StopLossConfig
  profitTargets   Json      // ProfitTarget[]
  moonBag         Json?     // MoonBagConfig
  locked          Boolean   @default(false)
  customName      String?

  user            User      @relation(fields: [userId], references: [id])
  positions       Position[]

  @@index([userId, type])
}

model CustomStrategy {
  id              String    @id @default(cuid())
  userId          String
  name            String
  description     String?
  config          Json      // Complete strategy configuration
  prdCompliant    Boolean   @default(false)

  user            User      @relation(fields: [userId], references: [id])

  @@index([userId])
}

model Transaction {
  id              String    @id @default(cuid())
  userId          String
  positionId      String?
  hash            String    @unique
  type            TransactionType
  tokenIn         String
  tokenOut        String
  amountIn        Decimal   @db.Decimal(20, 9)
  amountOut       Decimal   @db.Decimal(20, 9)
  price           Decimal   @db.Decimal(20, 9)
  fees            Json      // TransactionFees
  strategyId      String?
  presetUsed      String
  mevProtected    Boolean   @default(false)
  timestamp       DateTime  @default(now())

  user            User      @relation(fields: [userId], references: [id])
  position        Position? @relation(fields: [positionId], references: [id])

  @@index([userId, timestamp])
  @@index([hash])
}

model Alert {
  id              String    @id @default(cuid())
  userId          String
  type            AlertType
  priority        AlertPriority
  title           String
  message         String
  metadata        Json
  read            Boolean   @default(false)
  actionable      Boolean   @default(false)
  timestamp       DateTime  @default(now())

  user            User      @relation(fields: [userId], references: [id])

  @@index([userId, read, priority])
  @@index([timestamp])
}
```

## Data Models

### Core Trading Models
```typescript
// Trading Preset Configuration
interface TradingPreset {
  id: string
  name: PresetType
  priorityFee: number // SOL amount
  slippageLimit: number // Percentage
  mevProtectionLevel: MEVProtectionLevel
  brideAmount?: number // Optional SOL amount
  locked: boolean
  buySettings: TradeSettings
  sellSettings: TradeSettings
}

// Position Model with Real-time Updates
interface Position {
  id: string
  tokenAddress: string
  tokenSymbol: string
  entryPrice: number
  currentPrice: number
  quantity: number
  entryTimestamp: Date
  strategyId?: string
  presetUsed: PresetType
  riskLevel: RiskLevel
  status: PositionStatus

  // Calculated fields
  pnl: number
  pnlPercentage: number
  age: number // milliseconds
  marketData: TokenMarketData
}

// Exit Strategy with Custom Support
interface ExitStrategy {
  id: string
  userId: string
  positionId?: string
  type: StrategyType
  stopLoss: StopLossConfig
  profitTargets: ProfitTarget[]
  moonBag?: MoonBagConfig
  locked: boolean
  customName?: string

  // Execution state
  executionState: StrategyExecutionState
  lastUpdate: Date
}

// Custom Strategy Template
interface CustomStrategy {
  id: string
  userId: string
  name: string
  description?: string
  config: {
    stopLoss: StopLossConfig
    profitTargets: ProfitTarget[]
    moonBag?: MoonBagConfig
    riskParameters: RiskParameters
  }
  prdCompliant: boolean
  createdAt: Date
  updatedAt: Date
}
```

### Real-time Data Models
```typescript
// WebSocket Message Types
interface SocketMessage {
  type: 'PRICE_UPDATE' | 'POSITION_UPDATE' | 'STRATEGY_TRIGGER' | 'ALERT'
  data: any
  timestamp: Date
}

// Price Update Message
interface PriceUpdateMessage extends SocketMessage {
  type: 'PRICE_UPDATE'
  data: {
    tokenAddress: string
    price: number
    change24h: number
    volume24h: number
    marketCap: number
  }
}

// Strategy Execution Message
interface StrategyTriggerMessage extends SocketMessage {
  type: 'STRATEGY_TRIGGER'
  data: {
    positionId: string
    strategyId: string
    triggerType: 'PROFIT_TARGET' | 'STOP_LOSS' | 'TRAILING_STOP'
    executionResult: ExecutionResult
  }
}
```

## Error Handling

### Comprehensive Error Classification
```typescript
enum ErrorType {
  // Network & Connectivity
  NETWORK_ERROR = 'NETWORK_ERROR',
  WEBSOCKET_DISCONNECTED = 'WEBSOCKET_DISCONNECTED',
  RPC_ERROR = 'RPC_ERROR',

  // Trading Execution
  INSUFFICIENT_FUNDS = 'INSUFFICIENT_FUNDS',
  SLIPPAGE_EXCEEDED = 'SLIPPAGE_EXCEEDED',
  MEV_PROTECTION_FAILED = 'MEV_PROTECTION_FAILED',
  JUPITER_API_ERROR = 'JUPITER_API_ERROR',

  // Strategy Execution
  STRATEGY_EXECUTION_FAILED = 'STRATEGY_EXECUTION_FAILED',
  TRIGGER_CONDITION_ERROR = 'TRIGGER_CONDITION_ERROR',
  POSITION_NOT_FOUND = 'POSITION_NOT_FOUND',

  // Validation
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  PRESET_INVALID = 'PRESET_INVALID',
  CUSTOM_STRATEGY_INVALID = 'CUSTOM_STRATEGY_INVALID',

  // System
  DATABASE_ERROR = 'DATABASE_ERROR',
  QUEUE_ERROR = 'QUEUE_ERROR',
  CACHE_ERROR = 'CACHE_ERROR'
}

interface ErrorHandler {
  handleTradingError(error: TradingError): Promise<ErrorResponse>
  handleStrategyError(error: StrategyError): Promise<void>
  retryWithBackoff<T>(operation: () => Promise<T>, maxRetries: number): Promise<T>
  escalateToEmergency(error: CriticalError): Promise<void>
}
```

### Recovery Strategies
1. **Trading Execution Failures**: Auto-retry with escalated parameters, fallback to emergency execution
2. **Strategy Execution Failures**: Maintain state, alert user, provide manual override
3. **Network Issues**: Cache data locally, queue operations, reconnect automatically
4. **Database Failures**: Use Redis cache, implement circuit breaker pattern

## Testing Strategy

### Testing Pyramid Implementation

#### Unit Tests (70% Coverage)
```typescript
// Service Layer Tests
describe('TradingService', () => {
  it('should execute Jupiter swap with correct parameters', async () => {
    const mockQuote = createMockQuote()
    const result = await tradingService.executeSwap(mockQuote, mockPreset)
    expect(result.success).toBe(true)
  })

  it('should handle MEV protection correctly', async () => {
    const params = createMockTradeParams()
    const analysis = await tradingService.validateMEVSafety(params)
    expect(analysis.riskLevel).toBeDefined()
  })
})

// Strategy Tests
describe('ExitStrategyManager', () => {
  it('should create custom strategy with validation', () => {
    const customConfig = createMockCustomStrategy()
    const strategy = strategyManager.createCustomStrategy(customConfig)
    expect(strategy.prdCompliant).toBe(true)
  })
})
```

#### Integration Tests (20% Coverage)
```typescript
// API Integration Tests
describe('Trading API Integration', () => {
  it('should execute complete trade flow', async () => {
    const tradeParams = createMockTradeParams()
    const response = await request(app)
      .post('/api/trades/execute')
      .send(tradeParams)
      .expect(200)

    expect(response.body.transactionHash).toBeDefined()
  })
})

// WebSocket Integration Tests
describe('Real-time Updates', () => {
  it('should broadcast price updates to connected clients', (done) => {
    const client = io('http://localhost:3000')
    client.on('PRICE_UPDATE', (data) => {
      expect(data.tokenAddress).toBeDefined()
      done()
    })
  })
})
```

#### End-to-End Tests (10% Coverage)
```typescript
// Complete User Workflows
describe('Complete Trading Workflow', () => {
  it('should execute trade with custom strategy', async () => {
    // Create custom strategy
    await page.click('[data-testid="create-custom-strategy"]')
    await page.fill('[data-testid="strategy-name"]', 'Test Strategy')

    // Configure parameters
    await page.fill('[data-testid="stop-loss"]', '15')
    await page.click('[data-testid="save-strategy"]')

    // Execute trade
    await page.click('[data-testid="execute-trade"]')
    await expect(page.locator('[data-testid="trade-success"]')).toBeVisible()
  })
})
```

## Security Considerations

### Wallet Security
- Never store private keys in application state or database
- Use secure wallet connection protocols (Phantom, Solflare)
- Implement transaction signing validation with user confirmation
- Provide clear transaction preview with all parameters

### API Security
```typescript
// Input Validation with Zod
const TradeParamsSchema = z.object({
  tokenIn: z.string().min(32).max(44),
  tokenOut: z.string().min(32).max(44),
  amount: z.number().positive(),
  slippage: z.number().min(0.1).max(50),
  preset: z.enum(['default', 'vol', 'dead', 'nun', 'p5'])
})

// Rate Limiting
app.use('/api/trades', rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 100, // 100 requests per minute
  message: 'Too many trading requests'
}))

// CORS Configuration
app.use(cors({
  origin: process.env.FRONTEND_URL,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE']
}))
```

### Data Protection
- Encrypt sensitive user preferences with AES-256
- Use HTTPS/WSS for all communications
- Implement proper session management with Redis
- Sanitize all user inputs and API responses

## Performance Optimization

### Frontend Optimization
```typescript
// Efficient State Updates
const useOptimizedPositions = () => {
  const positions = usePositionStore(state => state.positions)

  // Memoize expensive calculations
  const totalPnL = useMemo(() =>
    positions.reduce((sum, pos) => sum + pos.pnl, 0),
    [positions]
  )

  // Selective subscriptions
  const criticalPositions = usePositionStore(
    state => state.positions.filter(p => p.riskLevel === 'HIGH'),
    shallow
  )

  return { positions, totalPnL, criticalPositions }
}

// Virtual Scrolling for Large Lists
const VirtualizedTransactionList = () => {
  return (
    <FixedSizeList
      height={600}
      itemCount={transactions.length}
      itemSize={80}
      itemData={transactions}
    >
      {TransactionRow}
    </FixedSizeList>
  )
}
```

### Backend Optimization
```typescript
// Database Query Optimization
const getPositionsWithStrategy = async (userId: string) => {
  return prisma.position.findMany({
    where: { userId, status: 'ACTIVE' },
    include: {
      strategy: {
        select: { id: true, type: true, stopLoss: true }
      }
    },
    orderBy: { entryTimestamp: 'desc' }
  })
}

// Redis Caching Strategy
const getCachedPrice = async (tokenAddress: string) => {
  const cached = await redis.get(`price:${tokenAddress}`)
  if (cached) return JSON.parse(cached)

  const price = await fetchPriceFromHelius(tokenAddress)
  await redis.setex(`price:${tokenAddress}`, 5, JSON.stringify(price))
  return price
}

// Job Queue Optimization
const strategyMonitorQueue = new Queue('strategy-monitor', {
  connection: redis,
  defaultJobOptions: {
    removeOnComplete: 100,
    removeOnFail: 50,
    attempts: 3,
    backoff: 'exponential'
  }
})
```

### Real-time Performance
- WebSocket connection pooling and heartbeat monitoring
- Efficient data serialization with MessagePack
- Smart throttling for high-frequency updates
- Connection state management with automatic reconnection

Extract and identify the key trading-relevant data fields from this token metadata JSON response that would be important for a Solana trading application like MemeTrader Pro. Focus on the essential information needed for:

1. **Token Identification**: Fields required to uniquely identify and display the token in the UI
2. **Trading Decisions**: Market data that traders need to make informed decisions
3. **Risk Assessment**: Information that helps evaluate the token's legitimacy and risk profile
4. **User Experience**: Data needed for proper token display and interaction in the trading interface

From this RekaAI token data:
```json
{
  "data": {
    "address": "6R3LxpHiE8RjTL7HnvKWtoQCHVA76CR1ebF9MYk61wzS",
    "name": "RekaAI",
    "symbol": "RekaAI",
    "decimals": 6,
    "extensions": {
      "twitter": "https://x.com/FusionLab_Inc",
      "website": "https://reka.ai",
      "description": "Multimodal AI you can deploy anywhere"
    },
    "logo_uri": "https://ipfs.io/ipfs/QmXuk2dXxM5hYARaxoPyoryJm5UQHdTkEhXrtsFFU7Siay",
    "price": 0.001,
    "liquidity": 0.2919295560985348,
    "circulating_supply": 1000000000,
    "market_cap": 1000000,
    "total_supply": 1000000000,
    "fdv": 1000000,
    "meme_info": {
      "source": "pump_dot_fun",
      "platform_id": "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P",
      "created_at": {
        "tx_hash": "25CRJJs4B5NVTgu6Sv1mMVpnXYMT5HG7LcfmvXTV5Tkp1GN8ajXDTjW59NTFN1UsHkXjkfCeUs7gKQAzTufCjpnw",
        "slot": *********,
        "block_time": 1743653081
      },
      "creation_time": 1743653081,
      "creator": "2suZVbGzdD1jdMFgyojG35TR6XWKFtSbprvjnTFAg8Ru",
      "graduated_at": {
        "slot": null,
        "tx_hash": null,
        "block_time": null
      },
      "graduated": false,
      "graduated_time": null,
      "pool": {
        "address": "2T6KViNFRuQb1MiFbmEtM4cA1mK2ayHKJTZHJ9K7aNw4",
        "real_sol_reserves": "0",
        "real_token_reserves": "793100000000000",
        "token_total_supply": "1000000000000000",
        "virtual_token_reserves": "1073000000000000"
      },
      "progress_percent": 0,
      "address": "6R3LxpHiE8RjTL7HnvKWtoQCHVA76CR1ebF9MYk61wzS"
    }
  },
  "success": true
}
```

