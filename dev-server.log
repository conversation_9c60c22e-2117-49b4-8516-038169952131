  ▲ Next.js 14.2.30
  - Local:        http://localhost:3000

 ✓ Starting...
 ✓ Ready in 3.4s
 ○ Compiling / ...
(node:27360) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 ✓ Compiled / in 3.4s (741 modules)
 HEAD / 200 in 3544ms
 GET / 200 in 2990ms
 ✓ Compiled in 333ms (374 modules)
 GET / 200 in 21ms
 GET / 200 in 23ms
 ✓ Compiled /_not-found in 204ms (721 modules)
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 251ms
 ✓ Compiled /trades in 327ms (771 modules)
 ✓ Compiled /exit-strategies in 205ms (795 modules)
 GET /exit-strategies 200 in 81ms
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 14ms
 ✓ Compiled in 379ms (409 modules)
 ✓ Compiled in 351ms (409 modules)
 ✓ Compiled in 409ms (806 modules)
 ✓ Compiled in 633ms (812 modules)
 ✓ Compiled in 367ms (812 modules)
 ✓ Compiled in 291ms (812 modules)
 ✓ Compiled in 646ms (812 modules)
 ✓ Compiled in 534ms (812 modules)
 ✓ Compiled in 277ms (412 modules)
 ✓ Compiled in 914ms (812 modules)
 ✓ Compiled in 534ms (812 modules)
 ✓ Compiled in 443ms (818 modules)
 ✓ Compiled in 372ms (818 modules)
 ✓ Compiled in 613ms (818 modules)
 ✓ Compiled in 247ms (818 modules)
 ✓ Compiled in 227ms (818 modules)
 ✓ Compiled in 391ms (818 modules)
 ✓ Compiled in 499ms (818 modules)
 ✓ Compiled in 381ms (818 modules)
 ✓ Compiled in 287ms (818 modules)
 ✓ Compiled in 191ms (818 modules)
 ✓ Compiled in 261ms (818 modules)
 ✓ Compiled in 288ms (818 modules)
 ✓ Compiled in 209ms (818 modules)
 ✓ Compiled in 340ms (818 modules)
 ✓ Compiled in 194ms (818 modules)
 ✓ Compiled in 351ms (818 modules)
 ✓ Compiled in 1318ms (818 modules)
 ✓ Compiled in 358ms (818 modules)
 ✓ Compiled in 420ms (818 modules)
 ✓ Compiled in 357ms (818 modules)
 ✓ Compiled in 310ms (818 modules)
 GET / 200 in 268ms
 GET / 200 in 43ms
 ✓ Compiled in 496ms (818 modules)
 ✓ Compiled in 635ms (818 modules)
 ✓ Compiled in 832ms (818 modules)
 ✓ Compiled in 693ms (818 modules)
 ✓ Compiled in 539ms (818 modules)
 ✓ Compiled in 321ms (818 modules)
 GET / 200 in 149ms
<w> [webpack.cache.PackFileCacheStrategy] Caching failed for pack: Error: ENOENT: no such file or directory, rename '/Users/<USER>/dev/github/agmentcode/frontend/.next/cache/webpack/client-development/2.pack.gz_' -> '/Users/<USER>/dev/github/agmentcode/frontend/.next/cache/webpack/client-development/2.pack.gz'
 ○ Compiling /_not-found ...
 ✓ Compiled /_not-found in 741ms (807 modules)
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 874ms
 ✓ Compiled in 999ms (818 modules)
 ✓ Compiled in 795ms (818 modules)
<w> [webpack.cache.PackFileCacheStrategy] Caching failed for pack: Error: ENOENT: no such file or directory, rename '/Users/<USER>/dev/github/agmentcode/frontend/.next/cache/webpack/server-development/3.pack.gz_' -> '/Users/<USER>/dev/github/agmentcode/frontend/.next/cache/webpack/server-development/3.pack.gz'
<w> [webpack.cache.PackFileCacheStrategy] Caching failed for pack: Error: ENOENT: no such file or directory, rename '/Users/<USER>/dev/github/agmentcode/frontend/.next/cache/webpack/client-development/0.pack.gz_' -> '/Users/<USER>/dev/github/agmentcode/frontend/.next/cache/webpack/client-development/0.pack.gz'
 ✓ Compiled in 811ms (818 modules)
 ✓ Compiled in 490ms (818 modules)
<w> [webpack.cache.PackFileCacheStrategy] Caching failed for pack: Error: ENOENT: no such file or directory, rename '/Users/<USER>/dev/github/agmentcode/frontend/.next/cache/webpack/server-development/0.pack.gz_' -> '/Users/<USER>/dev/github/agmentcode/frontend/.next/cache/webpack/server-development/0.pack.gz'
<w> [webpack.cache.PackFileCacheStrategy] Caching failed for pack: Error: ENOENT: no such file or directory, rename '/Users/<USER>/dev/github/agmentcode/frontend/.next/cache/webpack/client-development/0.pack.gz_' -> '/Users/<USER>/dev/github/agmentcode/frontend/.next/cache/webpack/client-development/0.pack.gz'
 ✓ Compiled in 881ms (818 modules)
 ✓ Compiled in 423ms (818 modules)
 GET / 200 in 172ms
 ✓ Compiled /_not-found in 198ms (807 modules)
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 309ms
 ✓ Compiled in 1004ms (818 modules)
 ✓ Compiled in 988ms (818 modules)
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⨯ src/components/trading/SwapInterface.tsx (457:30) @ testDirectStateUpdate
 ⨯ ReferenceError: testDirectStateUpdate is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:607:70)
digest: "3509598679"
[0m [90m 455 |[39m                 [33m<[39m[33mdiv[39m className[33m=[39m[32m"flex justify-end space-x-2 mb-2"[39m[33m>[39m[0m
[0m [90m 456 |[39m                   [33m<[39m[33mbutton[39m[0m
[0m[31m[1m>[22m[39m[90m 457 |[39m                     onClick[33m=[39m{testDirectStateUpdate}[0m
[0m [90m     |[39m                              [31m[1m^[22m[39m[0m
[0m [90m 458 |[39m                     className[33m=[39m[32m"px-3 py-1.5 text-xs font-medium bg-red-500/20 text-red-400 border border-red-500/30 rounded-lg hover:bg-red-500/30"[39m[0m
[0m [90m 459 |[39m                   [33m>[39m[0m
[0m [90m 460 |[39m                     [33mTEST[39m [33mSTATE[39m[0m
 GET / 500 in 452ms
 ⨯ src/components/trading/SwapInterface.tsx (457:30) @ testDirectStateUpdate
 ⨯ ReferenceError: testDirectStateUpdate is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:607:70)
digest: "3509598679"
[0m [90m 455 |[39m                 [33m<[39m[33mdiv[39m className[33m=[39m[32m"flex justify-end space-x-2 mb-2"[39m[33m>[39m[0m
[0m [90m 456 |[39m                   [33m<[39m[33mbutton[39m[0m
[0m[31m[1m>[22m[39m[90m 457 |[39m                     onClick[33m=[39m{testDirectStateUpdate}[0m
[0m [90m     |[39m                              [31m[1m^[22m[39m[0m
[0m [90m 458 |[39m                     className[33m=[39m[32m"px-3 py-1.5 text-xs font-medium bg-red-500/20 text-red-400 border border-red-500/30 rounded-lg hover:bg-red-500/30"[39m[0m
[0m [90m 459 |[39m                   [33m>[39m[0m
[0m [90m 460 |[39m                     [33mTEST[39m [33mSTATE[39m[0m
 GET / 500 in 296ms
 ○ Compiling /_not-found ...
 ✓ Compiled /_not-found in 524ms (807 modules)
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 665ms
 ✓ Compiled in 396ms (821 modules)
 GET / 200 in 17ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 GET / 200 in 47ms
 GET / 200 in 50ms
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 34ms
 ✓ Compiled in 476ms (821 modules)
 ✓ Compiled in 523ms (821 modules)
 ✓ Compiled in 460ms (818 modules)
<w> [webpack.cache.PackFileCacheStrategy] Caching failed for pack: Error: ENOENT: no such file or directory, rename '/Users/<USER>/dev/github/agmentcode/frontend/.next/cache/webpack/server-development/1.pack.gz_' -> '/Users/<USER>/dev/github/agmentcode/frontend/.next/cache/webpack/server-development/1.pack.gz'
 ✓ Compiled in 1267ms (818 modules)
 ✓ Compiled in 650ms (818 modules)
 ✓ Compiled in 592ms (818 modules)
 ✓ Compiled in 372ms (818 modules)
 ✓ Compiled in 452ms (818 modules)
 ✓ Compiled in 618ms (818 modules)
<w> [webpack.cache.PackFileCacheStrategy] Caching failed for pack: Error: ENOENT: no such file or directory, rename '/Users/<USER>/dev/github/agmentcode/frontend/.next/cache/webpack/client-development/0.pack.gz_' -> '/Users/<USER>/dev/github/agmentcode/frontend/.next/cache/webpack/client-development/0.pack.gz'
 ✓ Compiled in 470ms (818 modules)
<w> [webpack.cache.PackFileCacheStrategy] Caching failed for pack: Error: ENOENT: no such file or directory, rename '/Users/<USER>/dev/github/agmentcode/frontend/.next/cache/webpack/client-development/0.pack.gz_' -> '/Users/<USER>/dev/github/agmentcode/frontend/.next/cache/webpack/client-development/0.pack.gz'
 ✓ Compiled in 1159ms (818 modules)
 ✓ Compiled in 479ms (818 modules)
 ✓ Compiled in 466ms (818 modules)
 GET / 200 in 142ms
 ✓ Compiled /_not-found in 220ms (807 modules)
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 315ms
 ✓ Compiled in 620ms (818 modules)
 ✓ Compiled in 394ms (818 modules)
 ✓ Compiled in 611ms (818 modules)
 ✓ Compiled in 531ms (818 modules)
 ✓ Compiled in 598ms (818 modules)
 ✓ Compiled in 384ms (818 modules)
 ✓ Compiled in 531ms (818 modules)
 ✓ Compiled in 802ms (818 modules)
 ✓ Compiled in 519ms (818 modules)
 ✓ Compiled in 1117ms (812 modules)
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⨯ src/components/trading/SwapInterface.tsx (835:12) @ PositionSizer
 ⨯ ReferenceError: PositionSizer is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:1698:111)
digest: "2488555606"
[0m [90m 833 |[39m         {[90m/* Position Sizer */[39m}[0m
[0m [90m 834 |[39m         [33m<[39m[33mdiv[39m className[33m=[39m[32m"mt-6"[39m[33m>[39m[0m
[0m[31m[1m>[22m[39m[90m 835 |[39m           [33m<[39m[33mPositionSizer[39m[0m
[0m [90m     |[39m            [31m[1m^[22m[39m[0m
[0m [90m 836 |[39m             portfolioValue[33m=[39m{[35m15420.85[39m}[0m
[0m [90m 837 |[39m             onPositionCalculated[33m=[39m{handlePositionCalculated}[0m
[0m [90m 838 |[39m           [33m/[39m[33m>[39m[0m
 GET / 500 in 320ms
 ⨯ src/components/trading/SwapInterface.tsx (835:12) @ PositionSizer
 ⨯ ReferenceError: PositionSizer is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:1698:111)
digest: "2488555606"
[0m [90m 833 |[39m         {[90m/* Position Sizer */[39m}[0m
[0m [90m 834 |[39m         [33m<[39m[33mdiv[39m className[33m=[39m[32m"mt-6"[39m[33m>[39m[0m
[0m[31m[1m>[22m[39m[90m 835 |[39m           [33m<[39m[33mPositionSizer[39m[0m
[0m [90m     |[39m            [31m[1m^[22m[39m[0m
[0m [90m 836 |[39m             portfolioValue[33m=[39m{[35m15420.85[39m}[0m
[0m [90m 837 |[39m             onPositionCalculated[33m=[39m{handlePositionCalculated}[0m
[0m [90m 838 |[39m           [33m/[39m[33m>[39m[0m
 GET / 500 in 35ms
 ✓ Compiled /_not-found in 251ms (801 modules)
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 321ms
 ✓ Compiled in 836ms (815 modules)
 GET / 200 in 12ms
 ⨯ src/components/trading/SwapInterface.tsx (470:28) @ calculatedPositionAmount
 ⨯ ReferenceError: calculatedPositionAmount is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:627:72)
digest: "1409123095"
[0m [90m 468 |[39m                   [33m<[39m[33mbutton[39m[0m
[0m [90m 469 |[39m                     onClick[33m=[39m{set25PercentAmount}[0m
[0m[31m[1m>[22m[39m[90m 470 |[39m                     title[33m=[39m{calculatedPositionAmount [33m>[39m [35m0[39m [33m?[39m [32m"25% of Position Sizer amount"[39m [33m:[39m [32m"25% of wallet balance (0.625 SOL)"[39m}[0m
[0m [90m     |[39m                            [31m[1m^[22m[39m[0m
[0m [90m 471 |[39m                     className[33m=[39m{cn([0m
[0m [90m 472 |[39m                       [32m"px-3 py-1.5 text-xs font-medium border rounded-lg transition-all duration-200 hover:scale-105"[39m[33m,[39m[0m
[0m [90m 473 |[39m                       calculatedPositionAmount [33m>[39m [35m0[39m[0m
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⨯ src/components/trading/SwapInterface.tsx (470:28) @ calculatedPositionAmount
 ⨯ ReferenceError: calculatedPositionAmount is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:627:72)
digest: "1409123095"
[0m [90m 468 |[39m                   [33m<[39m[33mbutton[39m[0m
[0m [90m 469 |[39m                     onClick[33m=[39m{set25PercentAmount}[0m
[0m[31m[1m>[22m[39m[90m 470 |[39m                     title[33m=[39m{calculatedPositionAmount [33m>[39m [35m0[39m [33m?[39m [32m"25% of Position Sizer amount"[39m [33m:[39m [32m"25% of wallet balance (0.625 SOL)"[39m}[0m
[0m [90m     |[39m                            [31m[1m^[22m[39m[0m
[0m [90m 471 |[39m                     className[33m=[39m{cn([0m
[0m [90m 472 |[39m                       [32m"px-3 py-1.5 text-xs font-medium border rounded-lg transition-all duration-200 hover:scale-105"[39m[33m,[39m[0m
[0m [90m 473 |[39m                       calculatedPositionAmount [33m>[39m [35m0[39m[0m
 ⨯ src/components/trading/SwapInterface.tsx (470:28) @ calculatedPositionAmount
 ⨯ ReferenceError: calculatedPositionAmount is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:627:72)
digest: "1409123095"
[0m [90m 468 |[39m                   [33m<[39m[33mbutton[39m[0m
[0m [90m 469 |[39m                     onClick[33m=[39m{set25PercentAmount}[0m
[0m[31m[1m>[22m[39m[90m 470 |[39m                     title[33m=[39m{calculatedPositionAmount [33m>[39m [35m0[39m [33m?[39m [32m"25% of Position Sizer amount"[39m [33m:[39m [32m"25% of wallet balance (0.625 SOL)"[39m}[0m
[0m [90m     |[39m                            [31m[1m^[22m[39m[0m
[0m [90m 471 |[39m                     className[33m=[39m{cn([0m
[0m [90m 472 |[39m                       [32m"px-3 py-1.5 text-xs font-medium border rounded-lg transition-all duration-200 hover:scale-105"[39m[33m,[39m[0m
[0m [90m 473 |[39m                       calculatedPositionAmount [33m>[39m [35m0[39m[0m
 GET / 500 in 238ms
 ⨯ src/components/trading/SwapInterface.tsx (470:28) @ calculatedPositionAmount
 ⨯ ReferenceError: calculatedPositionAmount is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:627:72)
digest: "1409123095"
[0m [90m 468 |[39m                   [33m<[39m[33mbutton[39m[0m
[0m [90m 469 |[39m                     onClick[33m=[39m{set25PercentAmount}[0m
[0m[31m[1m>[22m[39m[90m 470 |[39m                     title[33m=[39m{calculatedPositionAmount [33m>[39m [35m0[39m [33m?[39m [32m"25% of Position Sizer amount"[39m [33m:[39m [32m"25% of wallet balance (0.625 SOL)"[39m}[0m
[0m [90m     |[39m                            [31m[1m^[22m[39m[0m
[0m [90m 471 |[39m                     className[33m=[39m{cn([0m
[0m [90m 472 |[39m                       [32m"px-3 py-1.5 text-xs font-medium border rounded-lg transition-all duration-200 hover:scale-105"[39m[33m,[39m[0m
[0m [90m 473 |[39m                       calculatedPositionAmount [33m>[39m [35m0[39m[0m
 GET / 500 in 71ms
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 42ms
 ✓ Compiled in 503ms (815 modules)
 ⨯ src/components/trading/SwapInterface.tsx (457:28) @ calculatedPositionAmount
 ⨯ ReferenceError: calculatedPositionAmount is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:612:72)
digest: "831346801"
[0m [90m 455 |[39m                   [33m<[39m[33mbutton[39m[0m
[0m [90m 456 |[39m                     onClick[33m=[39m{set25PercentAmount}[0m
[0m[31m[1m>[22m[39m[90m 457 |[39m                     title[33m=[39m{calculatedPositionAmount [33m>[39m [35m0[39m [33m?[39m [32m"25% of Position Sizer amount"[39m [33m:[39m [32m"25% of wallet balance (0.625 SOL)"[39m}[0m
[0m [90m     |[39m                            [31m[1m^[22m[39m[0m
[0m [90m 458 |[39m                     className[33m=[39m{cn([0m
[0m [90m 459 |[39m                       [32m"px-3 py-1.5 text-xs font-medium border rounded-lg transition-all duration-200 hover:scale-105"[39m[33m,[39m[0m
[0m [90m 460 |[39m                       calculatedPositionAmount [33m>[39m [35m0[39m[0m
 ⨯ src/components/trading/SwapInterface.tsx (457:28) @ calculatedPositionAmount
 ⨯ ReferenceError: calculatedPositionAmount is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:612:72)
digest: "831346801"
[0m [90m 455 |[39m                   [33m<[39m[33mbutton[39m[0m
[0m [90m 456 |[39m                     onClick[33m=[39m{set25PercentAmount}[0m
[0m[31m[1m>[22m[39m[90m 457 |[39m                     title[33m=[39m{calculatedPositionAmount [33m>[39m [35m0[39m [33m?[39m [32m"25% of Position Sizer amount"[39m [33m:[39m [32m"25% of wallet balance (0.625 SOL)"[39m}[0m
[0m [90m     |[39m                            [31m[1m^[22m[39m[0m
[0m [90m 458 |[39m                     className[33m=[39m{cn([0m
[0m [90m 459 |[39m                       [32m"px-3 py-1.5 text-xs font-medium border rounded-lg transition-all duration-200 hover:scale-105"[39m[33m,[39m[0m
[0m [90m 460 |[39m                       calculatedPositionAmount [33m>[39m [35m0[39m[0m
 ⨯ src/components/trading/SwapInterface.tsx (457:28) @ calculatedPositionAmount
 ⨯ ReferenceError: calculatedPositionAmount is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:612:72)
digest: "831346801"
[0m [90m 455 |[39m                   [33m<[39m[33mbutton[39m[0m
[0m [90m 456 |[39m                     onClick[33m=[39m{set25PercentAmount}[0m
[0m[31m[1m>[22m[39m[90m 457 |[39m                     title[33m=[39m{calculatedPositionAmount [33m>[39m [35m0[39m [33m?[39m [32m"25% of Position Sizer amount"[39m [33m:[39m [32m"25% of wallet balance (0.625 SOL)"[39m}[0m
[0m [90m     |[39m                            [31m[1m^[22m[39m[0m
[0m [90m 458 |[39m                     className[33m=[39m{cn([0m
[0m [90m 459 |[39m                       [32m"px-3 py-1.5 text-xs font-medium border rounded-lg transition-all duration-200 hover:scale-105"[39m[33m,[39m[0m
[0m [90m 460 |[39m                       calculatedPositionAmount [33m>[39m [35m0[39m[0m
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 GET / 500 in 226ms
 ⨯ src/components/trading/SwapInterface.tsx (457:28) @ calculatedPositionAmount
 ⨯ ReferenceError: calculatedPositionAmount is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:612:72)
digest: "831346801"
[0m [90m 455 |[39m                   [33m<[39m[33mbutton[39m[0m
[0m [90m 456 |[39m                     onClick[33m=[39m{set25PercentAmount}[0m
[0m[31m[1m>[22m[39m[90m 457 |[39m                     title[33m=[39m{calculatedPositionAmount [33m>[39m [35m0[39m [33m?[39m [32m"25% of Position Sizer amount"[39m [33m:[39m [32m"25% of wallet balance (0.625 SOL)"[39m}[0m
[0m [90m     |[39m                            [31m[1m^[22m[39m[0m
[0m [90m 458 |[39m                     className[33m=[39m{cn([0m
[0m [90m 459 |[39m                       [32m"px-3 py-1.5 text-xs font-medium border rounded-lg transition-all duration-200 hover:scale-105"[39m[33m,[39m[0m
[0m [90m 460 |[39m                       calculatedPositionAmount [33m>[39m [35m0[39m[0m
 GET / 500 in 67ms
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 97ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ✓ Compiled in 509ms (815 modules)
 ⨯ src/components/trading/SwapInterface.tsx (421:28) @ calculatedPositionAmount
 ⨯ ReferenceError: calculatedPositionAmount is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:572:72)
digest: "1207227828"
[0m [90m 419 |[39m                   [33m<[39m[33mbutton[39m[0m
[0m [90m 420 |[39m                     onClick[33m=[39m{set25PercentAmount}[0m
[0m[31m[1m>[22m[39m[90m 421 |[39m                     title[33m=[39m{calculatedPositionAmount [33m>[39m [35m0[39m [33m?[39m [32m"25% of Position Sizer amount"[39m [33m:[39m [32m"25% of wallet balance (0.625 SOL)"[39m}[0m
[0m [90m     |[39m                            [31m[1m^[22m[39m[0m
[0m [90m 422 |[39m                     className[33m=[39m{cn([0m
[0m [90m 423 |[39m                       [32m"px-3 py-1.5 text-xs font-medium border rounded-lg transition-all duration-200 hover:scale-105"[39m[33m,[39m[0m
[0m [90m 424 |[39m                       calculatedPositionAmount [33m>[39m [35m0[39m[0m
 ⨯ src/components/trading/SwapInterface.tsx (421:28) @ calculatedPositionAmount
 ⨯ ReferenceError: calculatedPositionAmount is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:572:72)
digest: "1207227828"
[0m [90m 419 |[39m                   [33m<[39m[33mbutton[39m[0m
[0m [90m 420 |[39m                     onClick[33m=[39m{set25PercentAmount}[0m
[0m[31m[1m>[22m[39m[90m 421 |[39m                     title[33m=[39m{calculatedPositionAmount [33m>[39m [35m0[39m [33m?[39m [32m"25% of Position Sizer amount"[39m [33m:[39m [32m"25% of wallet balance (0.625 SOL)"[39m}[0m
[0m [90m     |[39m                            [31m[1m^[22m[39m[0m
[0m [90m 422 |[39m                     className[33m=[39m{cn([0m
[0m [90m 423 |[39m                       [32m"px-3 py-1.5 text-xs font-medium border rounded-lg transition-all duration-200 hover:scale-105"[39m[33m,[39m[0m
[0m [90m 424 |[39m                       calculatedPositionAmount [33m>[39m [35m0[39m[0m
 GET / 500 in 238ms
 ⨯ src/components/trading/SwapInterface.tsx (421:28) @ calculatedPositionAmount
 ⨯ ReferenceError: calculatedPositionAmount is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:572:72)
digest: "1207227828"
[0m [90m 419 |[39m                   [33m<[39m[33mbutton[39m[0m
[0m [90m 420 |[39m                     onClick[33m=[39m{set25PercentAmount}[0m
[0m[31m[1m>[22m[39m[90m 421 |[39m                     title[33m=[39m{calculatedPositionAmount [33m>[39m [35m0[39m [33m?[39m [32m"25% of Position Sizer amount"[39m [33m:[39m [32m"25% of wallet balance (0.625 SOL)"[39m}[0m
[0m [90m     |[39m                            [31m[1m^[22m[39m[0m
[0m [90m 422 |[39m                     className[33m=[39m{cn([0m
[0m [90m 423 |[39m                       [32m"px-3 py-1.5 text-xs font-medium border rounded-lg transition-all duration-200 hover:scale-105"[39m[33m,[39m[0m
[0m [90m 424 |[39m                       calculatedPositionAmount [33m>[39m [35m0[39m[0m
 GET / 500 in 158ms
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 50ms
 ✓ Compiled in 403ms (815 modules)
 ⨯ src/components/trading/SwapInterface.tsx (428:28) @ calculatedPositionAmount
 ⨯ ReferenceError: calculatedPositionAmount is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:582:72)
digest: "3982260955"
[0m [90m 426 |[39m                   [33m<[39m[33mbutton[39m[0m
[0m [90m 427 |[39m                     onClick[33m=[39m{setHalfAmount}[0m
[0m[31m[1m>[22m[39m[90m 428 |[39m                     title[33m=[39m{calculatedPositionAmount [33m>[39m [35m0[39m [33m?[39m [32m"50% of Position Sizer amount"[39m [33m:[39m [32m"50% of wallet balance (1.25 SOL)"[39m}[0m
[0m [90m     |[39m                            [31m[1m^[22m[39m[0m
[0m [90m 429 |[39m                     className[33m=[39m{cn([0m
[0m [90m 430 |[39m                       [32m"px-3 py-1.5 text-xs font-medium border rounded-lg transition-all duration-200 hover:scale-105"[39m[33m,[39m[0m
[0m [90m 431 |[39m                       calculatedPositionAmount [33m>[39m [35m0[39m[0m
 ⨯ src/components/trading/SwapInterface.tsx (428:28) @ calculatedPositionAmount
 ⨯ ReferenceError: calculatedPositionAmount is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:582:72)
digest: "3982260955"
[0m [90m 426 |[39m                   [33m<[39m[33mbutton[39m[0m
[0m [90m 427 |[39m                     onClick[33m=[39m{setHalfAmount}[0m
[0m[31m[1m>[22m[39m[90m 428 |[39m                     title[33m=[39m{calculatedPositionAmount [33m>[39m [35m0[39m [33m?[39m [32m"50% of Position Sizer amount"[39m [33m:[39m [32m"50% of wallet balance (1.25 SOL)"[39m}[0m
[0m [90m     |[39m                            [31m[1m^[22m[39m[0m
[0m [90m 429 |[39m                     className[33m=[39m{cn([0m
[0m [90m 430 |[39m                       [32m"px-3 py-1.5 text-xs font-medium border rounded-lg transition-all duration-200 hover:scale-105"[39m[33m,[39m[0m
[0m [90m 431 |[39m                       calculatedPositionAmount [33m>[39m [35m0[39m[0m
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 GET / 500 in 107ms
 ⨯ src/components/trading/SwapInterface.tsx (428:28) @ calculatedPositionAmount
 ⨯ ReferenceError: calculatedPositionAmount is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:582:72)
digest: "3982260955"
[0m [90m 426 |[39m                   [33m<[39m[33mbutton[39m[0m
[0m [90m 427 |[39m                     onClick[33m=[39m{setHalfAmount}[0m
[0m[31m[1m>[22m[39m[90m 428 |[39m                     title[33m=[39m{calculatedPositionAmount [33m>[39m [35m0[39m [33m?[39m [32m"50% of Position Sizer amount"[39m [33m:[39m [32m"50% of wallet balance (1.25 SOL)"[39m}[0m
[0m [90m     |[39m                            [31m[1m^[22m[39m[0m
[0m [90m 429 |[39m                     className[33m=[39m{cn([0m
[0m [90m 430 |[39m                       [32m"px-3 py-1.5 text-xs font-medium border rounded-lg transition-all duration-200 hover:scale-105"[39m[33m,[39m[0m
[0m [90m 431 |[39m                       calculatedPositionAmount [33m>[39m [35m0[39m[0m
 GET / 500 in 76ms
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 37ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ✓ Compiled in 503ms (815 modules)
 ⨯ src/components/trading/SwapInterface.tsx (766:12) @ PositionSizer
 ⨯ ReferenceError: PositionSizer is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:1602:111)
digest: "3048270485"
[0m [90m 764 |[39m         {[90m/* Position Sizer */[39m}[0m
[0m [90m 765 |[39m         [33m<[39m[33mdiv[39m className[33m=[39m[32m"mt-6"[39m[33m>[39m[0m
[0m[31m[1m>[22m[39m[90m 766 |[39m           [33m<[39m[33mPositionSizer[39m[0m
[0m [90m     |[39m            [31m[1m^[22m[39m[0m
[0m [90m 767 |[39m             portfolioValue[33m=[39m{[35m15420.85[39m}[0m
[0m [90m 768 |[39m             onPositionCalculated[33m=[39m{handlePositionCalculated}[0m
[0m [90m 769 |[39m           [33m/[39m[33m>[39m[0m
 ⨯ src/components/trading/SwapInterface.tsx (766:12) @ PositionSizer
 ⨯ ReferenceError: PositionSizer is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:1602:111)
digest: "3048270485"
[0m [90m 764 |[39m         {[90m/* Position Sizer */[39m}[0m
[0m [90m 765 |[39m         [33m<[39m[33mdiv[39m className[33m=[39m[32m"mt-6"[39m[33m>[39m[0m
[0m[31m[1m>[22m[39m[90m 766 |[39m           [33m<[39m[33mPositionSizer[39m[0m
[0m [90m     |[39m            [31m[1m^[22m[39m[0m
[0m [90m 767 |[39m             portfolioValue[33m=[39m{[35m15420.85[39m}[0m
[0m [90m 768 |[39m             onPositionCalculated[33m=[39m{handlePositionCalculated}[0m
[0m [90m 769 |[39m           [33m/[39m[33m>[39m[0m
 GET / 500 in 214ms
 ⨯ src/components/trading/SwapInterface.tsx (766:12) @ PositionSizer
 ⨯ ReferenceError: PositionSizer is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:1602:111)
digest: "3048270485"
[0m [90m 764 |[39m         {[90m/* Position Sizer */[39m}[0m
[0m [90m 765 |[39m         [33m<[39m[33mdiv[39m className[33m=[39m[32m"mt-6"[39m[33m>[39m[0m
[0m[31m[1m>[22m[39m[90m 766 |[39m           [33m<[39m[33mPositionSizer[39m[0m
[0m [90m     |[39m            [31m[1m^[22m[39m[0m
[0m [90m 767 |[39m             portfolioValue[33m=[39m{[35m15420.85[39m}[0m
[0m [90m 768 |[39m             onPositionCalculated[33m=[39m{handlePositionCalculated}[0m
[0m [90m 769 |[39m           [33m/[39m[33m>[39m[0m
 GET / 500 in 59ms
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 38ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ✓ Compiled /_not-found in 1004ms (801 modules)
 GET / 200 in 400ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⨯ src/components/trading/SwapInterface.tsx (766:12) @ PositionSizer
 ⨯ ReferenceError: PositionSizer is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:1602:111)
digest: "3048270485"
[0m [90m 764 |[39m         {[90m/* Position Sizer */[39m}[0m
[0m [90m 765 |[39m         [33m<[39m[33mdiv[39m className[33m=[39m[32m"mt-6"[39m[33m>[39m[0m
[0m[31m[1m>[22m[39m[90m 766 |[39m           [33m<[39m[33mPositionSizer[39m[0m
[0m [90m     |[39m            [31m[1m^[22m[39m[0m
[0m [90m 767 |[39m             portfolioValue[33m=[39m{[35m15420.85[39m}[0m
[0m [90m 768 |[39m             onPositionCalculated[33m=[39m{handlePositionCalculated}[0m
[0m [90m 769 |[39m           [33m/[39m[33m>[39m[0m
 ⨯ src/components/trading/SwapInterface.tsx (766:12) @ PositionSizer
 ⨯ ReferenceError: PositionSizer is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:1602:111)
digest: "3048270485"
[0m [90m 764 |[39m         {[90m/* Position Sizer */[39m}[0m
[0m [90m 765 |[39m         [33m<[39m[33mdiv[39m className[33m=[39m[32m"mt-6"[39m[33m>[39m[0m
[0m[31m[1m>[22m[39m[90m 766 |[39m           [33m<[39m[33mPositionSizer[39m[0m
[0m [90m     |[39m            [31m[1m^[22m[39m[0m
[0m [90m 767 |[39m             portfolioValue[33m=[39m{[35m15420.85[39m}[0m
[0m [90m 768 |[39m             onPositionCalculated[33m=[39m{handlePositionCalculated}[0m
[0m [90m 769 |[39m           [33m/[39m[33m>[39m[0m
 GET /_next/static/webpack/cb354bb9ef3d60ba.webpack.hot-update.json 404 in 393ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 GET / 500 in 230ms
 GET /_next/static/webpack/cb354bb9ef3d60ba.webpack.hot-update.json 404 in 93ms
 ⨯ src/components/trading/SwapInterface.tsx (766:12) @ PositionSizer
 ⨯ ReferenceError: PositionSizer is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:1602:111)
digest: "3048270485"
[0m [90m 764 |[39m         {[90m/* Position Sizer */[39m}[0m
[0m [90m 765 |[39m         [33m<[39m[33mdiv[39m className[33m=[39m[32m"mt-6"[39m[33m>[39m[0m
[0m[31m[1m>[22m[39m[90m 766 |[39m           [33m<[39m[33mPositionSizer[39m[0m
[0m [90m     |[39m            [31m[1m^[22m[39m[0m
[0m [90m 767 |[39m             portfolioValue[33m=[39m{[35m15420.85[39m}[0m
[0m [90m 768 |[39m             onPositionCalculated[33m=[39m{handlePositionCalculated}[0m
[0m [90m 769 |[39m           [33m/[39m[33m>[39m[0m
 GET / 500 in 88ms
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 187ms
 ✓ Compiled in 305ms (414 modules)
 GET / 200 in 10ms
 ⨯ src/components/trading/SwapInterface.tsx (766:12) @ PositionSizer
 ⨯ ReferenceError: PositionSizer is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:1602:111)
digest: "3048270485"
[0m [90m 764 |[39m         {[90m/* Position Sizer */[39m}[0m
[0m [90m 765 |[39m         [33m<[39m[33mdiv[39m className[33m=[39m[32m"mt-6"[39m[33m>[39m[0m
[0m[31m[1m>[22m[39m[90m 766 |[39m           [33m<[39m[33mPositionSizer[39m[0m
[0m [90m     |[39m            [31m[1m^[22m[39m[0m
[0m [90m 767 |[39m             portfolioValue[33m=[39m{[35m15420.85[39m}[0m
[0m [90m 768 |[39m             onPositionCalculated[33m=[39m{handlePositionCalculated}[0m
[0m [90m 769 |[39m           [33m/[39m[33m>[39m[0m
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⨯ src/components/trading/SwapInterface.tsx (766:12) @ PositionSizer
 ⨯ ReferenceError: PositionSizer is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:1602:111)
digest: "3048270485"
[0m [90m 764 |[39m         {[90m/* Position Sizer */[39m}[0m
[0m [90m 765 |[39m         [33m<[39m[33mdiv[39m className[33m=[39m[32m"mt-6"[39m[33m>[39m[0m
[0m[31m[1m>[22m[39m[90m 766 |[39m           [33m<[39m[33mPositionSizer[39m[0m
[0m [90m     |[39m            [31m[1m^[22m[39m[0m
[0m [90m 767 |[39m             portfolioValue[33m=[39m{[35m15420.85[39m}[0m
[0m [90m 768 |[39m             onPositionCalculated[33m=[39m{handlePositionCalculated}[0m
[0m [90m 769 |[39m           [33m/[39m[33m>[39m[0m
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 GET / 500 in 102ms
 ⨯ src/components/trading/SwapInterface.tsx (766:12) @ PositionSizer
 ⨯ ReferenceError: PositionSizer is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:1602:111)
digest: "3048270485"
[0m [90m 764 |[39m         {[90m/* Position Sizer */[39m}[0m
[0m [90m 765 |[39m         [33m<[39m[33mdiv[39m className[33m=[39m[32m"mt-6"[39m[33m>[39m[0m
[0m[31m[1m>[22m[39m[90m 766 |[39m           [33m<[39m[33mPositionSizer[39m[0m
[0m [90m     |[39m            [31m[1m^[22m[39m[0m
[0m [90m 767 |[39m             portfolioValue[33m=[39m{[35m15420.85[39m}[0m
[0m [90m 768 |[39m             onPositionCalculated[33m=[39m{handlePositionCalculated}[0m
[0m [90m 769 |[39m           [33m/[39m[33m>[39m[0m
 GET / 500 in 181ms
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 24ms
 ✓ Compiled in 333ms (414 modules)
 ⨯ src/components/trading/SwapInterface.tsx (766:12) @ PositionSizer
 ⨯ ReferenceError: PositionSizer is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:1602:111)
digest: "3048270485"
[0m [90m 764 |[39m         {[90m/* Position Sizer */[39m}[0m
[0m [90m 765 |[39m         [33m<[39m[33mdiv[39m className[33m=[39m[32m"mt-6"[39m[33m>[39m[0m
[0m[31m[1m>[22m[39m[90m 766 |[39m           [33m<[39m[33mPositionSizer[39m[0m
[0m [90m     |[39m            [31m[1m^[22m[39m[0m
[0m [90m 767 |[39m             portfolioValue[33m=[39m{[35m15420.85[39m}[0m
[0m [90m 768 |[39m             onPositionCalculated[33m=[39m{handlePositionCalculated}[0m
[0m [90m 769 |[39m           [33m/[39m[33m>[39m[0m
 ⨯ src/components/trading/SwapInterface.tsx (766:12) @ PositionSizer
 ⨯ ReferenceError: PositionSizer is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:1602:111)
digest: "3048270485"
[0m [90m 764 |[39m         {[90m/* Position Sizer */[39m}[0m
[0m [90m 765 |[39m         [33m<[39m[33mdiv[39m className[33m=[39m[32m"mt-6"[39m[33m>[39m[0m
[0m[31m[1m>[22m[39m[90m 766 |[39m           [33m<[39m[33mPositionSizer[39m[0m
[0m [90m     |[39m            [31m[1m^[22m[39m[0m
[0m [90m 767 |[39m             portfolioValue[33m=[39m{[35m15420.85[39m}[0m
[0m [90m 768 |[39m             onPositionCalculated[33m=[39m{handlePositionCalculated}[0m
[0m [90m 769 |[39m           [33m/[39m[33m>[39m[0m
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 GET / 500 in 68ms
 ⨯ src/components/trading/SwapInterface.tsx (766:12) @ PositionSizer
 ⨯ ReferenceError: PositionSizer is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:1602:111)
digest: "3048270485"
[0m [90m 764 |[39m         {[90m/* Position Sizer */[39m}[0m
[0m [90m 765 |[39m         [33m<[39m[33mdiv[39m className[33m=[39m[32m"mt-6"[39m[33m>[39m[0m
[0m[31m[1m>[22m[39m[90m 766 |[39m           [33m<[39m[33mPositionSizer[39m[0m
[0m [90m     |[39m            [31m[1m^[22m[39m[0m
[0m [90m 767 |[39m             portfolioValue[33m=[39m{[35m15420.85[39m}[0m
[0m [90m 768 |[39m             onPositionCalculated[33m=[39m{handlePositionCalculated}[0m
[0m [90m 769 |[39m           [33m/[39m[33m>[39m[0m
 GET / 500 in 96ms
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 47ms
 ✓ Compiled in 277ms (414 modules)
 ⨯ src/components/trading/SwapInterface.tsx (766:12) @ PositionSizer
 ⨯ ReferenceError: PositionSizer is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:1602:111)
digest: "3048270485"
[0m [90m 764 |[39m         {[90m/* Position Sizer */[39m}[0m
[0m [90m 765 |[39m         [33m<[39m[33mdiv[39m className[33m=[39m[32m"mt-6"[39m[33m>[39m[0m
[0m[31m[1m>[22m[39m[90m 766 |[39m           [33m<[39m[33mPositionSizer[39m[0m
[0m [90m     |[39m            [31m[1m^[22m[39m[0m
[0m [90m 767 |[39m             portfolioValue[33m=[39m{[35m15420.85[39m}[0m
[0m [90m 768 |[39m             onPositionCalculated[33m=[39m{handlePositionCalculated}[0m
[0m [90m 769 |[39m           [33m/[39m[33m>[39m[0m
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⨯ src/components/trading/SwapInterface.tsx (766:12) @ PositionSizer
 ⨯ ReferenceError: PositionSizer is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:1602:111)
digest: "3048270485"
[0m [90m 764 |[39m         {[90m/* Position Sizer */[39m}[0m
[0m [90m 765 |[39m         [33m<[39m[33mdiv[39m className[33m=[39m[32m"mt-6"[39m[33m>[39m[0m
[0m[31m[1m>[22m[39m[90m 766 |[39m           [33m<[39m[33mPositionSizer[39m[0m
[0m [90m     |[39m            [31m[1m^[22m[39m[0m
[0m [90m 767 |[39m             portfolioValue[33m=[39m{[35m15420.85[39m}[0m
[0m [90m 768 |[39m             onPositionCalculated[33m=[39m{handlePositionCalculated}[0m
[0m [90m 769 |[39m           [33m/[39m[33m>[39m[0m
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 GET / 500 in 132ms
 ⨯ src/components/trading/SwapInterface.tsx (766:12) @ PositionSizer
 ⨯ ReferenceError: PositionSizer is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:1602:111)
digest: "3048270485"
[0m [90m 764 |[39m         {[90m/* Position Sizer */[39m}[0m
[0m [90m 765 |[39m         [33m<[39m[33mdiv[39m className[33m=[39m[32m"mt-6"[39m[33m>[39m[0m
[0m[31m[1m>[22m[39m[90m 766 |[39m           [33m<[39m[33mPositionSizer[39m[0m
[0m [90m     |[39m            [31m[1m^[22m[39m[0m
[0m [90m 767 |[39m             portfolioValue[33m=[39m{[35m15420.85[39m}[0m
[0m [90m 768 |[39m             onPositionCalculated[33m=[39m{handlePositionCalculated}[0m
[0m [90m 769 |[39m           [33m/[39m[33m>[39m[0m
 GET / 500 in 46ms
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 17ms
 ✓ Compiled in 504ms (815 modules)
 GET / 200 in 3ms
 GET / 200 in 57ms
 ⨯ src/components/trading/SwapInterface.tsx (766:12) @ PositionSizer
 ⨯ ReferenceError: PositionSizer is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:1602:111)
digest: "3048270485"
[0m [90m 764 |[39m         {[90m/* Position Sizer */[39m}[0m
[0m [90m 765 |[39m         [33m<[39m[33mdiv[39m className[33m=[39m[32m"mt-6"[39m[33m>[39m[0m
[0m[31m[1m>[22m[39m[90m 766 |[39m           [33m<[39m[33mPositionSizer[39m[0m
[0m [90m     |[39m            [31m[1m^[22m[39m[0m
[0m [90m 767 |[39m             portfolioValue[33m=[39m{[35m15420.85[39m}[0m
[0m [90m 768 |[39m             onPositionCalculated[33m=[39m{handlePositionCalculated}[0m
[0m [90m 769 |[39m           [33m/[39m[33m>[39m[0m
 ⨯ src/components/trading/SwapInterface.tsx (766:12) @ PositionSizer
 ⨯ ReferenceError: PositionSizer is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:1602:111)
digest: "3048270485"
[0m [90m 764 |[39m         {[90m/* Position Sizer */[39m}[0m
[0m [90m 765 |[39m         [33m<[39m[33mdiv[39m className[33m=[39m[32m"mt-6"[39m[33m>[39m[0m
[0m[31m[1m>[22m[39m[90m 766 |[39m           [33m<[39m[33mPositionSizer[39m[0m
[0m [90m     |[39m            [31m[1m^[22m[39m[0m
[0m [90m 767 |[39m             portfolioValue[33m=[39m{[35m15420.85[39m}[0m
[0m [90m 768 |[39m             onPositionCalculated[33m=[39m{handlePositionCalculated}[0m
[0m [90m 769 |[39m           [33m/[39m[33m>[39m[0m
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⨯ src/components/trading/SwapInterface.tsx (766:12) @ PositionSizer
 ⨯ ReferenceError: PositionSizer is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:1602:111)
digest: "3048270485"
[0m [90m 764 |[39m         {[90m/* Position Sizer */[39m}[0m
[0m [90m 765 |[39m         [33m<[39m[33mdiv[39m className[33m=[39m[32m"mt-6"[39m[33m>[39m[0m
[0m[31m[1m>[22m[39m[90m 766 |[39m           [33m<[39m[33mPositionSizer[39m[0m
[0m [90m     |[39m            [31m[1m^[22m[39m[0m
[0m [90m 767 |[39m             portfolioValue[33m=[39m{[35m15420.85[39m}[0m
[0m [90m 768 |[39m             onPositionCalculated[33m=[39m{handlePositionCalculated}[0m
[0m [90m 769 |[39m           [33m/[39m[33m>[39m[0m
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 GET / 500 in 56ms
 ⨯ src/components/trading/SwapInterface.tsx (766:12) @ PositionSizer
 ⨯ ReferenceError: PositionSizer is not defined
    at SwapInterface (./src/components/trading/SwapInterface.tsx:1602:111)
digest: "3048270485"
[0m [90m 764 |[39m         {[90m/* Position Sizer */[39m}[0m
[0m [90m 765 |[39m         [33m<[39m[33mdiv[39m className[33m=[39m[32m"mt-6"[39m[33m>[39m[0m
[0m[31m[1m>[22m[39m[90m 766 |[39m           [33m<[39m[33mPositionSizer[39m[0m
[0m [90m     |[39m            [31m[1m^[22m[39m[0m
[0m [90m 767 |[39m             portfolioValue[33m=[39m{[35m15420.85[39m}[0m
[0m [90m 768 |[39m             onPositionCalculated[33m=[39m{handlePositionCalculated}[0m
[0m [90m 769 |[39m           [33m/[39m[33m>[39m[0m
 GET / 500 in 33ms
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 18ms
<w> [webpack.cache.PackFileCacheStrategy] Caching failed for pack: Error: ENOENT: no such file or directory, rename '/Users/<USER>/dev/github/agmentcode/frontend/.next/cache/webpack/client-development/0.pack.gz_' -> '/Users/<USER>/dev/github/agmentcode/frontend/.next/cache/webpack/client-development/0.pack.gz'
 ✓ Compiled in 998ms (812 modules)
 GET / 200 in 218ms
 ✓ Compiled /_not-found in 197ms (801 modules)
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 250ms
 ✓ Compiled in 516ms (812 modules)
 ✓ Compiled in 244ms (812 modules)
 ✓ Compiled in 288ms (812 modules)
 ⚠ Fast Refresh had to perform a full reload. Read more: https://nextjs.org/docs/messages/fast-refresh-reload
 GET / 200 in 130ms
 ✓ Compiled /_not-found in 92ms (801 modules)
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 124ms
 ✓ Compiled in 316ms (815 modules)
 ⚠ Fast Refresh had to perform a full reload. Read more: https://nextjs.org/docs/messages/fast-refresh-reload
 GET / 200 in 60ms
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 20ms
 ✓ Compiled /dashboard in 339ms (825 modules)
 GET /history 404 in 46ms
 GET / 200 in 58ms
 GET /history 404 in 107ms
 GET / 200 in 81ms
 ✓ Compiled in 351ms (423 modules)
 ✓ Compiled in 235ms (423 modules)
 ✓ Compiled in 388ms (423 modules)
 ✓ Compiled in 365ms (423 modules)
 ✓ Compiled in 276ms (423 modules)
 ✓ Compiled in 293ms (423 modules)
 ✓ Compiled in 256ms (423 modules)
 ✓ Compiled in 453ms (822 modules)
 ✓ Compiled in 286ms (423 modules)
 ✓ Compiled in 252ms (423 modules)
 ✓ Compiled in 686ms (836 modules)
 ✓ Compiled in 424ms (836 modules)
 GET / 200 in 456ms
 ○ Compiling /transactions ...
 ✓ Compiled /transactions in 2.8s (1791 modules)
 GET / 200 in 175ms
