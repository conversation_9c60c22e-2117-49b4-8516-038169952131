
> memetrader-pro@1.0.0 dev:frontend
> npm run dev --workspace=frontend


> @memetrader-pro/frontend@1.0.0 dev
> next dev

  ▲ Next.js 14.2.30
  - Local:        http://localhost:3000

 ✓ Starting...
 ✓ Ready in 1318ms
 ○ Compiling / ...
(node:34675) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 ✓ Compiled / in 2.1s (749 modules)
 GET / 200 in 2409ms
 ✓ Compiled in 256ms (378 modules)
 ✓ Compiled /trades in 319ms (774 modules)
 ✓ Compiled /exit-strategies in 167ms (798 modules)
 ○ Compiling /transactions ...
 ✓ Compiled /transactions in 1386ms (1769 modules)
 ✓ Compiled /_not-found in 310ms (1772 modules)
 GET /alerts 404 in 46ms
 GET /transactions 200 in 105ms
 ⨯ ./src/components/trading/PendingPositionCard.tsx
Error: Failed to read source code from /Users/<USER>/dev/github/agmentcode/frontend/src/components/trading/PendingPositionCard.tsx

Caused by:
    No such file or directory (os error 2)

Import trace for requested module:
./src/components/trading/PendingPositionCard.tsx
./src/app/trades/page.tsx
 ✓ Compiled in 279ms (1778 modules)
 ✓ Compiled in 239ms (1778 modules)
 ✓ Compiled in 272ms (1778 modules)
 ✓ Compiled in 221ms (1761 modules)
 ✓ Compiled in 202ms (897 modules)
 ✓ Compiled in 206ms (895 modules)
 ✓ Compiled in 166ms (895 modules)
 ✓ Compiled in 168ms (895 modules)
 ✓ Compiled in 492ms (1758 modules)
 ✓ Compiled in 475ms (895 modules)
 ✓ Compiled in 310ms (895 modules)
 ✓ Compiled in 419ms (897 modules)
 ✓ Compiled in 164ms (897 modules)
 ✓ Compiled /trades in 309ms (882 modules)
 ✓ Compiled in 538ms (1779 modules)
 ✓ Compiled in 287ms (1779 modules)
 ✓ Compiled in 339ms (897 modules)
 ✓ Compiled in 184ms (897 modules)
 ✓ Compiled in 268ms (897 modules)
 ✓ Compiled in 178ms (897 modules)
 ✓ Compiled in 217ms (897 modules)
 ✓ Compiled in 220ms (897 modules)
 ✓ Compiled in 259ms (897 modules)
 ✓ Compiled in 186ms (897 modules)
 ✓ Compiled in 335ms (1779 modules)
 ✓ Compiled in 206ms (897 modules)
 ✓ Compiled in 175ms (897 modules)
 ✓ Compiled in 186ms (897 modules)
 ✓ Compiled in 210ms (897 modules)
 ○ Compiling /alerts ...
 ✓ Compiled /alerts in 866ms (1827 modules)
 ✓ Compiled in 632ms (1809 modules)
 ✓ Compiled in 295ms (1809 modules)
 ✓ Compiled in 293ms (1809 modules)
 ✓ Compiled in 242ms (1809 modules)
 ✓ Compiled in 550ms (1809 modules)
 ✓ Compiled in 587ms (1809 modules)
 ✓ Compiled in 284ms (1809 modules)
 ✓ Compiled in 473ms (1809 modules)
 ✓ Compiled in 635ms (1809 modules)
 ✓ Compiled in 440ms (1807 modules)
 ✓ Compiled in 383ms (1807 modules)
 ✓ Compiled in 270ms (1807 modules)
 ✓ Compiled in 167ms (910 modules)
 ✓ Compiled in 387ms (1807 modules)
 ✓ Compiled in 309ms (1811 modules)
 ○ Compiling /_not-found ...
 ✓ Compiled /_not-found in 658ms (1800 modules)
 GET /settings 404 in 71ms
 ✓ Compiled in 388ms (912 modules)
 ✓ Compiled /_not-found in 195ms (1800 modules)
 GET /settings 404 in 41ms
 ✓ Compiled in 169ms (914 modules)
 GET /settings 404 in 13ms
 ✓ Compiled in 208ms (914 modules)
 GET /settings 404 in 14ms
 ✓ Compiled in 198ms (914 modules)
 GET /settings 404 in 14ms
 ⨯ ./src/components/settings/SettingsModal.tsx:22:1
Module not found: Can't resolve './TradingSettingsPanel'
[0m [90m 20 |[39m } [36mfrom[39m [32m'lucide-react'[39m[0m
[0m [90m 21 |[39m [36mimport[39m { cn } [36mfrom[39m [32m'@/lib/utils'[39m[0m
[0m[31m[1m>[22m[39m[90m 22 |[39m [36mimport[39m { [33mTradingSettingsPanel[39m } [36mfrom[39m [32m'./TradingSettingsPanel'[39m[0m
[0m [90m    |[39m [31m[1m^[22m[39m[0m
[0m [90m 23 |[39m [36mimport[39m { [33mPortfolioSettingsPanel[39m } [36mfrom[39m [32m'./PortfolioSettingsPanel'[39m[0m
[0m [90m 24 |[39m [36mimport[39m { [33mSecuritySettingsPanel[39m } [36mfrom[39m [32m'./SecuritySettingsPanel'[39m[0m
[0m [90m 25 |[39m [36mimport[39m { [33mUISettingsPanel[39m } [36mfrom[39m [32m'./UISettingsPanel'[39m[0m

https://nextjs.org/docs/messages/module-not-found

Import trace for requested module:
./src/components/settings/SettingsTrigger.tsx
./src/components/layout/Header.tsx
./src/app/page.tsx
 ○ Compiling /_error ...
 ⨯ ./src/components/settings/SettingsModal.tsx:22:1
Module not found: Can't resolve './TradingSettingsPanel'
[0m [90m 20 |[39m } [36mfrom[39m [32m'lucide-react'[39m[0m
[0m [90m 21 |[39m [36mimport[39m { cn } [36mfrom[39m [32m'@/lib/utils'[39m[0m
[0m[31m[1m>[22m[39m[90m 22 |[39m [36mimport[39m { [33mTradingSettingsPanel[39m } [36mfrom[39m [32m'./TradingSettingsPanel'[39m[0m
[0m [90m    |[39m [31m[1m^[22m[39m[0m
[0m [90m 23 |[39m [36mimport[39m { [33mPortfolioSettingsPanel[39m } [36mfrom[39m [32m'./PortfolioSettingsPanel'[39m[0m
[0m [90m 24 |[39m [36mimport[39m { [33mSecuritySettingsPanel[39m } [36mfrom[39m [32m'./SecuritySettingsPanel'[39m[0m
[0m [90m 25 |[39m [36mimport[39m { [33mUISettingsPanel[39m } [36mfrom[39m [32m'./UISettingsPanel'[39m[0m

https://nextjs.org/docs/messages/module-not-found

Import trace for requested module:
./src/components/settings/SettingsTrigger.tsx
./src/components/layout/Header.tsx
./src/app/page.tsx
 GET /settings 500 in 7ms
 ⨯ ./src/components/settings/SettingsModal.tsx:22:1
Module not found: Can't resolve './TradingSettingsPanel'
[0m [90m 20 |[39m } [36mfrom[39m [32m'lucide-react'[39m[0m
[0m [90m 21 |[39m [36mimport[39m { cn } [36mfrom[39m [32m'@/lib/utils'[39m[0m
[0m[31m[1m>[22m[39m[90m 22 |[39m [36mimport[39m { [33mTradingSettingsPanel[39m } [36mfrom[39m [32m'./TradingSettingsPanel'[39m[0m
[0m [90m    |[39m [31m[1m^[22m[39m[0m
[0m [90m 23 |[39m [36mimport[39m { [33mPortfolioSettingsPanel[39m } [36mfrom[39m [32m'./PortfolioSettingsPanel'[39m[0m
[0m [90m 24 |[39m [36mimport[39m { [33mSecuritySettingsPanel[39m } [36mfrom[39m [32m'./SecuritySettingsPanel'[39m[0m
[0m [90m 25 |[39m [36mimport[39m { [33mUISettingsPanel[39m } [36mfrom[39m [32m'./UISettingsPanel'[39m[0m

https://nextjs.org/docs/messages/module-not-found

Import trace for requested module:
./src/components/settings/SettingsTrigger.tsx
./src/components/layout/Header.tsx
./src/app/page.tsx
 ⨯ ./src/components/settings/SettingsModal.tsx:22:1
Module not found: Can't resolve './TradingSettingsPanel'
[0m [90m 20 |[39m } [36mfrom[39m [32m'lucide-react'[39m[0m
[0m [90m 21 |[39m [36mimport[39m { cn } [36mfrom[39m [32m'@/lib/utils'[39m[0m
[0m[31m[1m>[22m[39m[90m 22 |[39m [36mimport[39m { [33mTradingSettingsPanel[39m } [36mfrom[39m [32m'./TradingSettingsPanel'[39m[0m
[0m [90m    |[39m [31m[1m^[22m[39m[0m
[0m [90m 23 |[39m [36mimport[39m { [33mPortfolioSettingsPanel[39m } [36mfrom[39m [32m'./PortfolioSettingsPanel'[39m[0m
[0m [90m 24 |[39m [36mimport[39m { [33mSecuritySettingsPanel[39m } [36mfrom[39m [32m'./SecuritySettingsPanel'[39m[0m
[0m [90m 25 |[39m [36mimport[39m { [33mUISettingsPanel[39m } [36mfrom[39m [32m'./UISettingsPanel'[39m[0m

https://nextjs.org/docs/messages/module-not-found

Import trace for requested module:
./src/components/settings/SettingsTrigger.tsx
./src/components/layout/Header.tsx
./src/app/page.tsx
 ⨯ ./src/components/settings/SettingsModal.tsx:22:1
Module not found: Can't resolve './TradingSettingsPanel'
[0m [90m 20 |[39m } [36mfrom[39m [32m'lucide-react'[39m[0m
[0m [90m 21 |[39m [36mimport[39m { cn } [36mfrom[39m [32m'@/lib/utils'[39m[0m
[0m[31m[1m>[22m[39m[90m 22 |[39m [36mimport[39m { [33mTradingSettingsPanel[39m } [36mfrom[39m [32m'./TradingSettingsPanel'[39m[0m
[0m [90m    |[39m [31m[1m^[22m[39m[0m
[0m [90m 23 |[39m [36mimport[39m { [33mPortfolioSettingsPanel[39m } [36mfrom[39m [32m'./PortfolioSettingsPanel'[39m[0m
[0m [90m 24 |[39m [36mimport[39m { [33mSecuritySettingsPanel[39m } [36mfrom[39m [32m'./SecuritySettingsPanel'[39m[0m
[0m [90m 25 |[39m [36mimport[39m { [33mUISettingsPanel[39m } [36mfrom[39m [32m'./UISettingsPanel'[39m[0m

https://nextjs.org/docs/messages/module-not-found

Import trace for requested module:
./src/components/settings/SettingsTrigger.tsx
./src/components/layout/Header.tsx
./src/app/page.tsx
 ⨯ ./src/components/settings/SettingsModal.tsx:23:1
Module not found: Can't resolve './PortfolioSettingsPanel'
[0m [90m 21 |[39m [36mimport[39m { cn } [36mfrom[39m [32m'@/lib/utils'[39m[0m
[0m [90m 22 |[39m [36mimport[39m { [33mTradingSettingsPanel[39m } [36mfrom[39m [32m'./TradingSettingsPanel'[39m[0m
[0m[31m[1m>[22m[39m[90m 23 |[39m [36mimport[39m { [33mPortfolioSettingsPanel[39m } [36mfrom[39m [32m'./PortfolioSettingsPanel'[39m[0m
[0m [90m    |[39m [31m[1m^[22m[39m[0m
[0m [90m 24 |[39m [36mimport[39m { [33mSecuritySettingsPanel[39m } [36mfrom[39m [32m'./SecuritySettingsPanel'[39m[0m
[0m [90m 25 |[39m [36mimport[39m { [33mUISettingsPanel[39m } [36mfrom[39m [32m'./UISettingsPanel'[39m[0m
[0m [90m 26 |[39m [36mimport[39m { [33mPerformanceSettingsPanel[39m } [36mfrom[39m [32m'./PerformanceSettingsPanel'[39m[0m

https://nextjs.org/docs/messages/module-not-found

Import trace for requested module:
./src/components/settings/SettingsTrigger.tsx
./src/components/layout/Sidebar.tsx
./src/app/page.tsx
 ⨯ ./src/components/settings/SettingsModal.tsx:24:1
Module not found: Can't resolve './SecuritySettingsPanel'
[0m [90m 22 |[39m [36mimport[39m { [33mTradingSettingsPanel[39m } [36mfrom[39m [32m'./TradingSettingsPanel'[39m[0m
[0m [90m 23 |[39m [36mimport[39m { [33mPortfolioSettingsPanel[39m } [36mfrom[39m [32m'./PortfolioSettingsPanel'[39m[0m
[0m[31m[1m>[22m[39m[90m 24 |[39m [36mimport[39m { [33mSecuritySettingsPanel[39m } [36mfrom[39m [32m'./SecuritySettingsPanel'[39m[0m
[0m [90m    |[39m [31m[1m^[22m[39m[0m
[0m [90m 25 |[39m [36mimport[39m { [33mUISettingsPanel[39m } [36mfrom[39m [32m'./UISettingsPanel'[39m[0m
[0m [90m 26 |[39m [36mimport[39m { [33mPerformanceSettingsPanel[39m } [36mfrom[39m [32m'./PerformanceSettingsPanel'[39m[0m
[0m [90m 27 |[39m [36mimport[39m { [33mAlertConfig[39m } [36mfrom[39m [32m'../alerts/AlertConfig'[39m[0m

https://nextjs.org/docs/messages/module-not-found

Import trace for requested module:
./src/components/settings/SettingsTrigger.tsx
./src/components/layout/Sidebar.tsx
./src/app/page.tsx
 ⨯ ./src/components/settings/SettingsModal.tsx:25:1
Module not found: Can't resolve './UISettingsPanel'
[0m [90m 23 |[39m [36mimport[39m { [33mPortfolioSettingsPanel[39m } [36mfrom[39m [32m'./PortfolioSettingsPanel'[39m[0m
[0m [90m 24 |[39m [36mimport[39m { [33mSecuritySettingsPanel[39m } [36mfrom[39m [32m'./SecuritySettingsPanel'[39m[0m
[0m[31m[1m>[22m[39m[90m 25 |[39m [36mimport[39m { [33mUISettingsPanel[39m } [36mfrom[39m [32m'./UISettingsPanel'[39m[0m
[0m [90m    |[39m [31m[1m^[22m[39m[0m
[0m [90m 26 |[39m [36mimport[39m { [33mPerformanceSettingsPanel[39m } [36mfrom[39m [32m'./PerformanceSettingsPanel'[39m[0m
[0m [90m 27 |[39m [36mimport[39m { [33mAlertConfig[39m } [36mfrom[39m [32m'../alerts/AlertConfig'[39m[0m
[0m [90m 28 |[39m[0m

https://nextjs.org/docs/messages/module-not-found

Import trace for requested module:
./src/components/settings/SettingsTrigger.tsx
./src/components/layout/Sidebar.tsx
./src/app/page.tsx
 ⨯ ./src/components/settings/SettingsModal.tsx:26:1
Module not found: Can't resolve './PerformanceSettingsPanel'
[0m [90m 24 |[39m [36mimport[39m { [33mSecuritySettingsPanel[39m } [36mfrom[39m [32m'./SecuritySettingsPanel'[39m[0m
[0m [90m 25 |[39m [36mimport[39m { [33mUISettingsPanel[39m } [36mfrom[39m [32m'./UISettingsPanel'[39m[0m
[0m[31m[1m>[22m[39m[90m 26 |[39m [36mimport[39m { [33mPerformanceSettingsPanel[39m } [36mfrom[39m [32m'./PerformanceSettingsPanel'[39m[0m
[0m [90m    |[39m [31m[1m^[22m[39m[0m
[0m [90m 27 |[39m [36mimport[39m { [33mAlertConfig[39m } [36mfrom[39m [32m'../alerts/AlertConfig'[39m[0m
[0m [90m 28 |[39m[0m
[0m [90m 29 |[39m [36minterface[39m [33mSettingsModalProps[39m {[0m

https://nextjs.org/docs/messages/module-not-found

Import trace for requested module:
./src/components/settings/SettingsTrigger.tsx
./src/components/layout/Sidebar.tsx
./src/app/page.tsx
 ⚠ ../shared/src/validation/settingsSchemas.ts
Attempted import error: 'TradingPresetSchema' is not exported from './schemas' (imported as 'TradingPresetSchema').

Import trace for requested module:
../shared/src/validation/settingsSchemas.ts
./src/stores/settingsStore.ts
./src/components/settings/SettingsTrigger.tsx
./src/components/layout/Sidebar.tsx
./src/app/page.tsx

../shared/src/validation/settingsSchemas.ts
Attempted import error: 'TradingPresetSchema' is not exported from './schemas' (imported as 'TradingPresetSchema').

Import trace for requested module:
../shared/src/validation/settingsSchemas.ts
./src/stores/settingsStore.ts
./src/components/settings/SettingsTrigger.tsx
./src/components/layout/Sidebar.tsx
./src/app/page.tsx
 ⚠ Fast Refresh had to perform a full reload. Read more: https://nextjs.org/docs/messages/fast-refresh-reload
 GET /settings 404 in 33ms
 GET /_next/static/webpack/0a4ca46874533087.webpack.hot-update.json 404 in 648ms
 GET /settings 404 in 18ms
 GET /settings 404 in 21ms
 GET /settings 404 in 28ms
 GET /alerts 200 in 98ms
 ✓ Compiled /_not-found in 577ms (2136 modules)
 ✓ Compiled in 253ms (2136 modules)
 ✓ Compiled in 649ms (2150 modules)
 ✓ Compiled in 472ms (2150 modules)
 ✓ Compiled in 350ms (2150 modules)
 ✓ Compiled in 402ms (2150 modules)
 ✓ Compiled in 410ms (2150 modules)
 GET /exit-strategies 200 in 115ms
 ✓ Compiled in 667ms (2136 modules)
 ✓ Compiled in 377ms (2136 modules)
 ✓ Compiled in 254ms (2136 modules)
 GET /exit-strategies 200 in 138ms
 ○ Compiling /dashboard ...
 ✓ Compiled /dashboard in 827ms (2160 modules)
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/alerts/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/alerts/page.js:1:9621
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/alerts/page.js:1:9670)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderPageComponent (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1910:24)
    at async DevServer.renderToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1962:32)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:272:17)
    at async DevServer.handleRequestImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:818:17)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:359:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/alerts/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ],
  page: '/alerts'
}
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/alerts/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/trades/page.js:1:14898
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/trades/page.js:1:14951)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderPageComponent (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1910:24)
    at async DevServer.renderToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1962:32)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:272:17)
    at async DevServer.handleRequestImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:818:17)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:359:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/alerts/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ],
  page: '/trades'
}
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/alerts/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/transactions/page.js:1:396518
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/transactions/page.js:1:396567)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderPageComponent (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1910:24)
    at async DevServer.renderToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1962:32)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:272:17)
    at async DevServer.handleRequestImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:818:17)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:359:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/alerts/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ],
  page: '/transactions'
}
 ✓ Compiled /_error in 394ms (2162 modules)
 ⨯ Error: Cannot find module './818.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/pages/_document.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/pages/_document.js:1:335
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/pages/_document.js:1:375)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:72:65
    at async Promise.all (index 0)
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:71:33)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/pages/_document.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 ⨯ Error: Cannot find module './818.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/pages/_document.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/pages/_document.js:1:335
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/pages/_document.js:1:375)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:72:65
    at async Promise.all (index 0)
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:71:33)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/pages/_document.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 ⨯ Error: Cannot find module './818.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/pages/_document.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/pages/_document.js:1:335
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/pages/_document.js:1:375)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:72:65
    at async Promise.all (index 0)
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:71:33)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/pages/_document.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 ✓ Compiled in 537ms (2147 modules)
 ✓ Compiled in 531ms (2161 modules)
 ✓ Compiled /_not-found in 334ms (2150 modules)
 GET /_next/static/css/app/layout.css?v=1754073438675 404 in 390ms
 ✓ Compiled in 485ms (2164 modules)
 GET /_next/static/css/app/layout.css?v=1754073451461 404 in 35ms
 ✓ Compiled in 333ms (2164 modules)
 GET /_next/static/css/app/layout.css?v=1754073459067 404 in 22ms
 ✓ Compiled in 384ms (2164 modules)
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/exit-strategies/page.js:1:13522
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/exit-strategies/page.js:1:13575)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderPageComponent (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1910:24)
    at async DevServer.renderToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1962:32)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:272:17)
    at async DevServer.handleRequestImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:818:17)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:359:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ],
  page: '/exit-strategies'
}
 ✓ Compiled /_error in 463ms (2149 modules)
 ⨯ Error: Cannot find module './818.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/pages/_document.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/pages/_document.js:1:335
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/pages/_document.js:1:375)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:72:65
    at async Promise.all (index 0)
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:71:33)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/pages/_document.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 GET /exit-strategies 500 in 508ms
 ✓ Compiled /_not-found in 165ms (2152 modules)
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 GET /_next/static/chunks/fallback/webpack.js 500 in 188ms
 GET /_next/static/chunks/fallback/main.js 500 in 187ms
 GET /_next/static/chunks/fallback/react-refresh.js 500 in 186ms
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 GET /_next/static/chunks/fallback/pages/_app.js 500 in 11ms
 GET /_next/static/chunks/fallback/pages/_error.js 500 in 10ms
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/page.js:5:68864
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/page.js:5:68917)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderPageComponent (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1910:24)
    at async DevServer.renderToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1962:32)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:272:17)
    at async DevServer.handleRequestImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:818:17)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:359:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ],
  page: '/'
}
 ⨯ Error: Cannot find module './818.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/pages/_document.js:1:335
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/pages/_document.js:1:375)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:72:65
    at async Promise.all (index 0)
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:71:33)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 GET / 500 in 16ms
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 GET /_next/static/chunks/fallback/webpack.js 500 in 16ms
 GET /_next/static/chunks/fallback/main.js 500 in 16ms
 GET /_next/static/chunks/fallback/react-refresh.js 500 in 15ms
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 GET /_next/static/chunks/fallback/pages/_app.js 500 in 10ms
 GET /_next/static/chunks/fallback/pages/_error.js 500 in 9ms
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/page.js:5:68864
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/page.js:5:68917)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderPageComponent (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1910:24)
    at async DevServer.renderToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1962:32)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:272:17)
    at async DevServer.handleRequestImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:818:17)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:359:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ],
  page: '/'
}
 ⨯ Error: Cannot find module './818.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/pages/_document.js:1:335
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/pages/_document.js:1:375)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:72:65
    at async Promise.all (index 0)
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:71:33)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 GET / 500 in 28ms
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 GET /_next/static/chunks/fallback/webpack.js 500 in 15ms
 GET /_next/static/chunks/fallback/main.js 500 in 14ms
 GET /_next/static/chunks/fallback/react-refresh.js 500 in 15ms
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 GET /_next/static/chunks/fallback/pages/_app.js 500 in 10ms
 GET /_next/static/chunks/fallback/pages/_error.js 500 in 9ms
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/page.js:5:68864
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/page.js:5:68917)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderPageComponent (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1910:24)
    at async DevServer.renderToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1962:32)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:272:17)
    at async DevServer.handleRequestImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:818:17)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:359:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ],
  page: '/'
}
 ⨯ Error: Cannot find module './818.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/pages/_document.js:1:335
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/pages/_document.js:1:375)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:72:65
    at async Promise.all (index 0)
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:71:33)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 GET / 500 in 28ms
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 GET /_next/static/chunks/fallback/webpack.js 500 in 11ms
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 GET /_next/static/chunks/fallback/main.js 500 in 12ms
 GET /_next/static/chunks/fallback/react-refresh.js 500 in 11ms
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 GET /_next/static/chunks/fallback/pages/_app.js 500 in 9ms
 GET /_next/static/chunks/fallback/pages/_error.js 500 in 8ms
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/page.js:5:68864
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/page.js:5:68917)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderPageComponent (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1910:24)
    at async DevServer.renderToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1962:32)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:272:17)
    at async DevServer.handleRequestImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:818:17)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:359:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ],
  page: '/'
}
 ⨯ Error: Cannot find module './818.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/pages/_document.js:1:335
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/pages/_document.js:1:375)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:72:65
    at async Promise.all (index 0)
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:71:33)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 GET / 500 in 17ms
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 GET /_next/static/chunks/fallback/webpack.js 500 in 13ms
 GET /_next/static/chunks/fallback/main.js 500 in 13ms
 GET /_next/static/chunks/fallback/react-refresh.js 500 in 12ms
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 GET /_next/static/chunks/fallback/pages/_app.js 500 in 8ms
 GET /_next/static/chunks/fallback/pages/_error.js 500 in 7ms
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/page.js:5:68864
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/page.js:5:68917)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderPageComponent (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1910:24)
    at async DevServer.renderToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1962:32)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:272:17)
    at async DevServer.handleRequestImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:818:17)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:359:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ],
  page: '/'
}
 ⨯ Error: Cannot find module './818.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/pages/_document.js:1:335
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/pages/_document.js:1:375)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:72:65
    at async Promise.all (index 0)
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:71:33)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 GET / 500 in 23ms
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 GET /_next/static/chunks/fallback/webpack.js 500 in 17ms
 GET /_next/static/chunks/fallback/main.js 500 in 16ms
 GET /_next/static/chunks/fallback/react-refresh.js 500 in 14ms
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 GET /_next/static/chunks/fallback/pages/_error.js 500 in 9ms
 GET /_next/static/chunks/fallback/pages/_app.js 500 in 8ms
 ⨯ Error: Cannot find module './440.js'
Require stack:
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js
- /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1244:15)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:55:36
    at Function._load (node:internal/modules/cjs/loader:1070:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at __webpack_require__.f.require (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:203:28)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9707
    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js:1:9748)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1335:12)
    at mod.require (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:136:16)
    at requirePage (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:84
    at async loadComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js:103:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:714:36)
    at async DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:577:20)
    at async DevServer.renderErrorToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2090:30)
    at async pipe.req.req (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:2056:30)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:374:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/webpack-runtime.js',
    '/Users/<USER>/dev/github/agmentcode/frontend/.next/server/app/_not-found/page.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/require.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js'
  ]
}
 GET /.well-known/appspecific/com.chrome.devtools.json 500 in 8ms
 ✓ Compiled in 1116ms (2166 modules)
 ✓ Compiled in 319ms (2161 modules)
 ✓ Compiled in 365ms (2161 modules)
 ✓ Compiled in 321ms (2161 modules)
 ✓ Compiled in 438ms (2161 modules)
 ✓ Compiled /_not-found in 234ms (2150 modules)
 GET /_next/static/css/app/layout.css?v=1754073655855 404 in 285ms
 ✓ Compiled in 393ms (2164 modules)
 GET /_next/static/css/app/layout.css?v=1754073660922 404 in 34ms
 ✓ Compiled in 315ms (2150 modules)
 ✓ Compiled /_error in 353ms (2152 modules)
 GET / 404 in 6ms
 GET / 404 in 9ms
 GET / 404 in 18ms
 GET / 404 in 20ms
 GET / 404 in 12ms
 GET / 404 in 7ms
[?25h
