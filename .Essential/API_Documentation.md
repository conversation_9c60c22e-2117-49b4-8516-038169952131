# API Documentation

## 1. Authentication

All API requests must be authenticated using a JSON Web Token (JWT). The token should be included in the `Authorization` header of each request:

`Authorization: Bearer <your_jwt>`

## 2. Endpoints

### User
- `POST /api/auth/register`: Register a new user.
- `POST /api/auth/login`: Authenticate a user and get a JWT.
- `GET /api/user/profile`: Get the profile of the authenticated user.

### Portfolio
- `GET /api/portfolio`: Get the current portfolio of the user.
- `GET /api/portfolio/positions`: Get all active positions.
- `GET /api/portfolio/history`: Get the transaction history.

### Trading
- `POST /api/trade/execute`: Execute a new trade.
- `GET /api/trade/presets`: Get the available trading presets.

### Market Data
- `GET /api/market/price`: Get the real-time price of a token.
- `GET /api/market/history`: Get historical market data for a token.
