# React Component Structure Guidelines

## 1. Directory Structure

Components should be organized by feature or domain within the `src/components` directory. Each core module of MemeTrader Pro will have its own subdirectory.

```
src/
  components/
    ├── common/         # Shared, reusable components (Button, Input, etc.)
    ├── layout/         # Layout components (<PERSON><PERSON>, Header, etc.)
    ├── trading/        # Components for the Advanced Trading Command Center
    ├── dashboard/      # Components for the Mission Control Dashboard
    ├── strategies/     # Components for the Exit Strategy Manager
    └── ...             # Other feature-based directories
```

## 2. Component File Structure

Each component should be in its own directory with the following structure:

```
- MyComponent/
  ├── index.ts          # Exports the component
  ├── MyComponent.tsx   # The main component file
  ├── MyComponent.module.css # CSS modules for component-specific styles (if needed)
  └── MyComponent.test.tsx   # Unit tests for the component
```

## 3. Component Design

- **Functional Components**: Use functional components with Hooks.
- **Props**: Use TypeScript interfaces to define `props` for each component.
- **State**: Keep components as stateless as possible. Lift state up to the nearest common ancestor or use a state management library.
- **Composition**: Favor composition over inheritance. Build complex components from smaller, single-purpose components.
