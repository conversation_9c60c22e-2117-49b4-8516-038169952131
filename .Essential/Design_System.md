# Design System Documentation

## 1. Color Palette (Jupiter-inspired Dark Theme)

- **Background**: Dark grays with subtle gradients (e.g., `#1A1A1A` to `#101010`).
- **Primary Accent**: A vibrant green for positive actions and states (e.g., `#00F5A0`).
- **Secondary Accent**: A cool blue for informational elements and active states (e.g., `#00D2FF`).
- **Text**: Off-white/light gray for readability (e.g., `#EAEAEA`).
- **Borders/Dividers**: A slightly lighter shade of the background color (e.g., `#2A2A2A`).

## 2. Typography

- **Font Family**: A clean, modern sans-serif font (e.g., Inter, Roboto).
- **Hierarchy**: Use a clear typographic hierarchy for headings, subheadings, and body text.

## 3. Component Library

A library of reusable components will be developed in `src/components/common`, including:

- `Button` (with variants for primary, secondary, and destructive actions)
- `Input` (with variants for text, number, and password)
- `Card` (for displaying content in a structured way)
- `Modal` (for displaying information or forms in a dialog)
- `Tooltip`
- `Spinner`

## 4. Spacing

- **Grid System**: Use a consistent grid system (e.g., 8-point grid) for spacing and alignment. All margins, paddings, and layout dimensions should be multiples of 8px.
