# Database Schema

## 1. Overview

We will use a relational database (PostgreSQL) to store user data, portfolio information, and transaction history. Time-series data, such as market prices, may be stored in a specialized time-series database like InfluxDB for performance.

## 2. ERD (Entity Relationship Diagram)

(A visual ERD will be created and maintained separately using a tool like dbdiagram.io or Lucidchart.)

## 3. Tables

### `users`
- `id` (PK)
- `username`
- `email`
- `password_hash`
- `created_at`

### `portfolios`
- `id` (PK)
- `user_id` (FK to `users.id`)
- `name`
- `created_at`

### `positions`
- `id` (PK)
- `portfolio_id` (FK to `portfolios.id`)
- `token_address`
- `amount`
- `entry_price`
- `entry_timestamp`

### `trades`
- `id` (PK)
- `position_id` (FK to `positions.id`)
- `type` ('buy' or 'sell')
- `amount`
- `price`
- `timestamp`
- `fees`

### `exit_strategies`
- `id` (PK)
- `position_id` (FK to `positions.id`)
- `strategy_type`
- `parameters` (JSONB)
- `is_active`
