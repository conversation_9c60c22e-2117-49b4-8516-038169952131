# Coding Standards

## 1. JavaScript & TypeScript

- **Style Guide**: Follow the [Airbnb JavaScript Style Guide](https://github.com/airbnb/javascript).
- **Naming Conventions**:
  - `camelCase` for variables and functions.
  - `PascalCase` for classes and components.
- **Quotes**: Use single quotes (`'`) for strings.
- **Indentation**: Use 2 spaces for indentation.
- **Type-Checking**: Use TypeScript for static type checking. Define types for all props, state, and function signatures.
- **Modules**: Use ES6 modules (`import`/`export`).

## 2. CSS & Tailwind CSS

- **Methodology**: Use Tailwind CSS for utility-first styling.
- **Custom CSS**: Avoid custom CSS where possible. If necessary, use CSS Modules and follow BEM naming conventions.
- **Class Ordering**: Organize Tailwind classes logically: Layout > Spacing > Typography > Color > Effects.
- **Responsive Design**: Use Tailwind's responsive prefixes (`sm:`, `md:`, `lg:`, `xl:`) for creating responsive layouts.

## 3. Linting & Formatting

- **Linter**: Use ESLint for identifying and fixing problems in JavaScript/TypeScript code.
- **Formatter**: Use Prettier for consistent code formatting.
- **Configuration**: A shared `.eslintrc` and `.prettierrc` will be provided in the root directory to enforce these standards automatically.
