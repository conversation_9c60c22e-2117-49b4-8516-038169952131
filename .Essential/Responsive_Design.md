# Responsive Design Specifications

## 1. Breakpoints

We will use Tailwind CSS's default breakpoints for responsive design:

- **sm**: `640px`
- **md**: `768px`
- **lg**: `1024px`
- **xl**: `1280px`
- **2xl**: `1536px`

## 2. Layout Adjustments

- **Mobile-First**: Design for mobile first, then scale up to larger screens.
- **Flexible Grids**: Use CSS Grid and Flexbox for creating flexible and responsive layouts.
- **Navigation**: The sidebar navigation will be collapsed into a hamburger menu on smaller screens (`< lg`).
- **Data Tables**: On smaller screens, data tables will be converted to a card-based layout or will have horizontal scrolling.
