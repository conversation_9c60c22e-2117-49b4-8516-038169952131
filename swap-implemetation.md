Implementing Server-Side Solana Token Swaps with Jupiter Aggregator and Helius RPC
Introduction
This guide demonstrates how to build a backend service that performs Solana token swaps programmatically using the Jupiter swap aggregator, with <PERSON><PERSON> as the RPC provider. Jupiter is Solana’s leading DEX aggregator that finds the best routes across exchanges for a given token swap
helius.dev
. By leveraging Jupiter’s API, we can fetch swap routes and serialized swap transactions for any token pair. Using Helius RPC for our Solana connection will improve reliability and unlock advanced features. Helius provides high-performance, drop-in replacement RPC endpoints for Solana, including staked validator routing for faster transaction finalization
helius.dev
. It also fully supports Solana’s WebSocket APIs (e.g. subscriptions for accounts and signatures) for real-time updates
helius.dev
. In this tutorial, we will revise a typical server-side swap implementation to use <PERSON><PERSON> instead of the default clusterApiUrl. We’ll cover configuring a Helius RPC endpoint, managing environment variables (for API keys and secrets), updating code to initialize the Solana connection with Helius, and performing token swaps via Jupiter’s API (including transaction signing and submission). We’ll also highlight optional Helius features like priority fees and WebSocket listeners for transaction tracking and account changes.
Prerequisites and Setup
Before starting, ensure you have the following:
Node.js (v16+ recommended) and NPM or Yarn installed on your system.
Familiarity with JavaScript/TypeScript and Solana’s web3.js library.
A Solana wallet (keypair) with funds (e.g. some SOL for gas and tokens to swap) – this guide will use a private key for signing transactions on the backend.
A Helius API key: sign up for a free account on Helius.dev and obtain an API key from the dashboard
helius.dev
. (Helius offers free and paid plans; paid plans enable staked RPC for higher reliability.)
Jupiter API endpoints: We will use Jupiter’s public REST API for quotes and swaps.
Installing Dependencies
Initialize a new Node.js project (if you haven’t already) and install the required libraries:
bash
Copy
npm init -y               # initialize package.json
npm install @solana/web3.js @solana/spl-token node-fetch dotenv bs58
Dependencies:
@solana/web3.js: Solana JavaScript API for blockchain interactions (connections, transactions, etc.).
@solana/spl-token: Utility library to fetch token mint info (to get decimals for amount conversion).
node-fetch (or you can use built-in fetch in newer Node versions): to call Jupiter’s REST API endpoints.
dotenv: loads environment variables from a .env file.
bs58: utility for Base58 encoding/decoding (used to handle Solana private keys).
Configuration: Using Helius RPC Endpoints
By default, Solana developers often connect using clusterApiUrl("mainnet-beta") or a standard endpoint like https://api.mainnet-beta.solana.com. Here, we will configure our app to use a Helius RPC endpoint instead. Helius endpoints are high-performance drop-in replacements that support all Solana JSON-RPC methods
helius.dev
. Using Helius will improve our swap reliability, as transactions sent through Helius (on paid plans) use staked validators by default for optimal landing rates
helius.dev
.
Setting Environment Variables
Create a .env file in your project root to store configuration. This should include your Helius RPC URL (with API key), Jupiter API URLs, and your wallet’s private key. For example:
bash
Copy
# .env file
HELIUS_RPC_URL=https://mainnet.helius-rpc.com/?api-key=<YOUR_HELIUS_API_KEY>
JUPITER_QUOTE_URL=https://quote-api.jup.ag/v6/quote
JUPITER_SWAP_URL=https://quote-api.jup.ag/v6/swap
WALLET_PRIVATE_KEY=<YOUR_WALLET_PRIVATE_KEY_BASE58>
Notes:
Replace <YOUR_HELIUS_API_KEY> with the API key from your Helius dashboard. The URL format for Helius mainnet is https://mainnet.helius-rpc.com/?api-key=YOUR_API_KEY
helius.dev
 (for devnet, use https://devnet.helius-rpc.com/?api-key=...). Keep this key secret – do not commit it to source control. We include it in the URL here for simplicity, but you could alternatively store just the key in an env variable and append it in code.
We use Jupiter’s v6 API endpoints for quotes and swaps. These are public REST endpoints provided by Jupiter (no authentication required for Jupiter API). The quote API is used with GET requests to retrieve swap routes, and the swap API is used with POST requests to generate a transaction. (Jupiter also offers a separate Payments API and others for different use cases, but here we focus on direct swaps).
<YOUR_WALLET_PRIVATE_KEY_BASE58> should be your wallet’s secret key in base58 format (for example, the output of solana-keygen grind or the 64-byte secret converted to base58). Alternatively, you can use the array of 64 numbers (the secret key bytes) in JSON format. Never expose this secret key publicly – using env variables helps keep it out of your code. In a production system, consider more secure secret management.
With the .env in place, load it at the start of your application:
js
Copy
import dotenv from 'dotenv';
dotenv.config();
Initializing the Solana Connection with Helius
Now, create a Solana Connection using the Helius RPC URL from the environment:
js
Copy
import { Connection, Keypair, PublicKey, VersionedTransaction } from '@solana/web3.js';
import fetch from 'node-fetch';
import bs58 from 'bs58';

// Load environment variables
dotenv.config();

// Initialize a connection to Solana using Helius RPC
const connection = new Connection(process.env.HELIUS_RPC_URL, { commitment: 'confirmed' });
Here we pass the Helius RPC URL and set the commitment level to 'confirmed'. This means our queries (e.g. balance checks or transaction confirmations) will consider a transaction confirmed after it has been finalized by a supermajority of validators (but not necessarily rooted/finalized in the longest chain). You can choose 'finalized' for the strongest confirmation or 'processed' for the fastest but least secure. The 'confirmed' level is a good middle ground for most applications. Helius’s RPC behaves like the standard Solana RPC node. Under the hood, if you have a paid Helius plan, your transactions will be sent via staked connections – i.e., directly to the current leaders – which boosts speed and reliability without any code changes
helius.dev
. On the free plan, Helius uses high-performance unstaked nodes (still often more reliable than the public endpoint). In either case, no changes to your Connection code are needed beyond using the Helius URL. Best Practice: Do not hardcode your RPC URL. We use an environment variable so you can easily switch endpoints (e.g., to devnet or a different provider) without code changes. The Jupiter team also recommends using your own dedicated RPC for production
dev.jup.ag
, rather than public shared endpoints, to avoid rate limits or outages.
Building and Executing a Swap Transaction
With our connection configured to use Helius, we can proceed to interact with Jupiter’s API to perform a token swap. The high-level steps are:
Define swap parameters: What token we want to swap from and to, and how much. Also specify slippage tolerance.
Fetch a quote from Jupiter: Jupiter’s quote API will return the best route and an estimated output amount for our swap request.
Request the swap transaction: We send the quote to Jupiter’s swap API, which returns a serialized (base64) transaction containing the instructions to perform the swap on-chain.
Deserialize and sign the transaction: Use our wallet’s private key to sign the transaction.
Send the transaction via Helius RPC: Transmit the signed transaction to the Solana network using our connection.
Confirm and track the transaction: Ensure the transaction is confirmed, and optionally listen for real-time confirmation or account changes.
We will illustrate these steps with code and explanations below.
1. Define Swap Parameters
First, specify the details of the swap you want to perform. This includes:
Input Mint – the token you are swapping from (as a mint address). For example, native SOL’s mint is the special address So11111111111111111111111111111111111111112 (the wrapped SOL token mint). USDC on Solana has mint address EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v. In code, we’ll use the mint addresses as strings or PublicKey objects.
Output Mint – the token you want to receive.
Amount – how much of the input token you want to swap. Jupiter’s API expects the amount in the token’s smallest units (often called “atomic” units, e.g. lamports for SOL). This means if you want to swap 0.1 SOL, you must specify 0.1 * 10^9 = 100,000,000 lamports as the amount. For USDC (6 decimals), 1 USDC = 1 * 10^6 units. We can handle this conversion dynamically by fetching the mint info.
Slippage – the maximum slippage tolerance (in percent) you are willing to accept for the swap. Jupiter will fail the swap if the price moves more than this. We will use 0.5% (a typical default). Jupiter’s API expects slippage in basis points, so 0.5% = 50 bps.
For example, let's set up a swap from SOL to USDC for 0.1 SOL:
js
Copy
// Swap parameters
const inputMint = new PublicKey('So11111111111111111111111111111111111111112');  // wSOL (represents SOL)
const outputMint = new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'); // USDC
const amountInSol = 0.1;
const slippage = 0.5; // percent

// Convert amount to smallest unit (lamports for SOL):
import { getMint } from '@solana/spl-token';

try {
  const inputMintInfo = await getMint(connection, inputMint);
  const decimals = inputMintInfo.decimals;
  const amountAtomic = Math.round(amountInSol * 10 ** decimals);
} catch (error) {
  console.error('Failed to fetch mint info:', error);
  throw new Error('Invalid token mint address');
}
In the above snippet, we fetched the mint info for the input token to get its decimals. This uses @solana/spl-token’s getMint function to query the token’s mint data on-chain
medium.com
medium.com
. We then convert 0.1 SOL to 0.1 * 10^9 = 100,000,000 lamports. (If you already know the decimals of your token, you can skip the chain lookup and just calculate directly.)
2. Fetching a Quote from Jupiter
With the parameters set, we call Jupiter’s Quote API to get the best swap route and expected output. This is a GET request to the Jupiter endpoint including query parameters for input/output mint, amount, and slippage. For Jupiter v6, the base URL is https://quote-api.jup.ag/v6/quote. For example:
js
Copy
// Construct the Jupiter quote API URL
const quoteUrl = `${process.env.JUPITER_QUOTE_URL}`
  + `?inputMint=${inputMint.toBase58()}`
  + `&outputMint=${outputMint.toBase58()}`
  + `&amount=${amountAtomic}`
  + `&slippageBps=${slippage * 100}`  // convert 0.5% to 50 bps
  + `&swapMode=ExactIn`;

// Fetch quote with error handling and retry logic
let quoteData;
try {
  const quoteResponse = await fetch(quoteUrl);
  
  if (!quoteResponse.ok) {
    throw new Error(`Jupiter quote API failed: ${quoteResponse.status} ${quoteResponse.statusText}`);
  }
  
  quoteData = await quoteResponse.json();
  
  // Validate quote response
  if (!quoteData || !quoteData.outAmount || quoteData.error) {
    throw new Error(`Invalid quote response: ${quoteData?.error || 'No output amount'}`);
  }
  
} catch (error) {
  console.error('Failed to get Jupiter quote:', error);
  throw error;
}
Here we use ExactIn mode indicating we are specifying the input amount (0.1 SOL) and want to know how much output we’ll get (as opposed to ExactOut where you specify desired output and calculate required input). The response (quoteData) will contain information about the best route, including an estimated amount out, the DEX route taken, fees, etc. We will pass this whole quote object to the swap API next. Note: The quote API returns an estimate. The actual executed amount may differ slightly due to price movement, hence the need for a slippage buffer. The quote object also includes a routePlan or similar which is needed by the swap API to know which route to execute.
3. Requesting the Swap Transaction (Jupiter Swap API)
After obtaining a quote, we request the actual swap transaction from Jupiter’s Swap API. This is a POST request to https://quote-api.jup.ag/v6/swap (or the URL in your JUPITER_SWAP_URL env). We send a JSON body containing at least the quoteResponse (the object from the previous step) and the userPublicKey (the public key of the wallet that will execute the swap). Jupiter will respond with a serialized transaction (Base64 string) that is prepared for the swap. We can also include some optional parameters in this request for convenience and performance:
wrapAndUnwrapSol: true – This instructs Jupiter to handle wrapping/unwrapping of SOL automatically if SOL or wSOL is involved (so we can treat SOL like an SPL token). This is usually true by default in v6, but we include it for clarity.
dynamicComputeUnitLimit: true – This asks Jupiter to automatically adjust the compute unit limit of the transaction if needed. If the swap route is complex, Jupiter can add a Compute Budget instruction to increase the CU limit so the transaction doesn’t run out of compute.
prioritizationFeeLamports: "auto" – This is a priority fee setting. By setting it to "auto", we ask Jupiter to automatically include a priority fee (in lamports) to speed up our transaction if the network is congested
medium.com
. Jupiter will determine an appropriate micro-lamport fee per compute unit to tip validators so our transaction is processed faster (similar to a gas tip). This leverages Solana’s priority fee mechanism introduced in 2023. (We’ll discuss more about priority fees in the Helius section below.)
Let’s call the swap API with these parameters:
js
Copy
// Load the user's keypair from the secret key first
const secretKeyBytes = bs58.decode(process.env.WALLET_PRIVATE_KEY);
const userKeypair = Keypair.fromSecretKey(secretKeyBytes);

// Call Jupiter Swap API with proper error handling
let swapResult;
try {
  const swapResponse = await fetch(process.env.JUPITER_SWAP_URL, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      quoteResponse: quoteData,
      userPublicKey: userKeypair.publicKey.toString(),
      wrapAndUnwrapSol: true,
      dynamicComputeUnitLimit: true,
      prioritizationFeeLamports: 'auto'
    })
  });
  
  if (!swapResponse.ok) {
    throw new Error(`Jupiter swap API failed: ${swapResponse.status} ${swapResponse.statusText}`);
  }
  
  swapResult = await swapResponse.json();
  
} catch (error) {
  console.error('Failed to get swap transaction:', error);
  throw error;
}

// Validate swap transaction response
if (swapResult.error || !swapResult.swapTransaction) {
  throw new Error(`Jupiter swap API error: ${swapResult.error ?? 'No swap transaction returned'}`);
}

const swapTransactionBase64 = swapResult.swapTransaction;
console.log('Swap transaction prepared successfully');
In the above snippet, userKeypair would be our loaded Keypair for the wallet (we’ll load it in the next step), and WALLET_PUBLIC_KEY could be an env var storing the public key if we wanted to avoid deriving it from the private key repeatedly. We include it to show how to get the public key string. After the POST, we check for errors. If all goes well, we obtain swapTransactionBase64, a base64-encoded string representing the compiled transaction. This transaction is partially signed by Jupiter’s program (and possibly by other program-derived addresses if required by the route). It still needs to be signed by our wallet (the userPublicKey) to authorize spending our funds. At this point, no Solana network calls have been made aside from reading the mint info and maybe the blockhash for conversion. We have not spent any funds yet; we just prepared the transaction.
4. Deserializing and Signing the Transaction
We will now convert the base64 transaction into a VersionedTransaction object (because Jupiter v6 uses Solana’s v0 versioned transactions with address lookup tables for more efficient routing
dev.jup.ag
). Then we'll use our private key to sign it. Since we already loaded the userKeypair above, we can now deserialize the transaction and sign it:
js
Copy
// Deserialize the base64 swap transaction to a VersionedTransaction object
const txBuffer = Buffer.from(swapTransactionBase64, 'base64');
const swapTransaction = VersionedTransaction.deserialize(txBuffer);

// Sign the transaction with the user's key (and any other required signers)
swapTransaction.sign([userKeypair]);
The sign method will apply our signature to all required signature positions that match the userKeypair’s public key. (If the transaction required multiple signers, we would include all their Keypairs in the array. But for a simple token swap, typically only the user’s signature is needed since Jupiter’s program accounts would have already been signed by Jupiter’s backend if necessary.) At this stage, the transaction is fully signed and ready to be sent to the Solana cluster.
5. Sending the Transaction via Helius RPC
Finally, we send the signed transaction using our connection (which points to Helius). We can use sendRawTransaction, which takes the serialized transaction bytes and forwards them to the RPC node. Helius will then propagate it to the network (with its speed optimizations). After sending, we’ll wait for confirmation to ensure the swap is executed.
js
Copy
// Send the transaction to the network with comprehensive error handling
let txid;
try {
  const rawTx = swapTransaction.serialize();
  const latestBlockhash = await connection.getLatestBlockhash();

  // Send transaction with retry logic
  let sendAttempts = 0;
  const maxSendAttempts = 3;
  
  while (sendAttempts < maxSendAttempts) {
    try {
      txid = await connection.sendRawTransaction(rawTx, {
        skipPreflight: false,  // Enable preflight for better error detection
        preflightCommitment: 'confirmed',
        maxRetries: 2
      });
      break; // Success, exit retry loop
    } catch (sendError) {
      sendAttempts++;
      console.warn(`Send attempt ${sendAttempts} failed:`, sendError.message);
      
      if (sendAttempts >= maxSendAttempts) {
        throw new Error(`Failed to send transaction after ${maxSendAttempts} attempts: ${sendError.message}`);
      }
      
      // Wait before retry (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, 1000 * sendAttempts));
    }
  }
  
  console.log(`Swap transaction submitted. Signature: ${txid}`);

  // Confirm the transaction with timeout
  const confirmationPromise = connection.confirmTransaction(
    {
      signature: txid,
      blockhash: latestBlockhash.blockhash,
      lastValidBlockHeight: latestBlockhash.lastValidBlockHeight
    },
    'confirmed'
  );
  
  // Add timeout for confirmation
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error('Transaction confirmation timeout')), 60000); // 60 second timeout
  });
  
  await Promise.race([confirmationPromise, timeoutPromise]);
  
  console.log('✅ Swap transaction confirmed');
  console.log(`View on Solana explorer: https://solscan.io/tx/${txid}`);
  
} catch (error) {
  console.error('Transaction execution failed:', error);
  
  // Provide specific error guidance
  if (error.message.includes('insufficient')) {
    throw new Error('Insufficient balance for transaction. Please check your wallet balance.');
  } else if (error.message.includes('slippage')) {
    throw new Error('Transaction failed due to slippage. Please try again with higher slippage tolerance.');
  } else if (error.message.includes('timeout')) {
    throw new Error('Transaction confirmation timeout. The transaction may still succeed - check the explorer.');
  }
  
  throw error;
}
A few notes on the above code:
We fetch the latest blockhash and last valid block height before sending. This is used to later confirm the transaction in a structured way, which is especially useful for versioned transactions. By providing the same blockhash info to confirmTransaction, we ensure the confirmation is checked against the correct block range, avoiding issues if the transaction lands just at the edge of expiration
dev.jup.ag
.
We use skipPreflight: true when sending. Since we trust Jupiter’s simulation (the swap API wouldn’t return a transaction that fails simulation unless something changed), skipping preflight saves time. It also avoids the case where a transaction might fail simulation due to some state change but could still succeed when executed (rare, but if you want to be safe, you can set skipPreflight: false to simulate again on send).
maxRetries: 3 instructs the client to retry sending a few times if it doesn’t get a confirmation from the RPC node that the transaction was received. This can help in case of transient issues.
We waited for the transaction to reach confirmed status. You could use 'finalized' for a stronger guarantee (transaction is permanently in the ledger), but that might take a bit longer. 'confirmed' is usually sufficient for a swap before proceeding with subsequent logic (and is the default commitment used in our Connection). Under the hood, Helius’s RPC uses either RPC polling or its WebSocket to verify confirmation. Using a WebSocket subscription (discussed below) can sometimes detect completion slightly faster than confirmTransaction.
If everything is successful, the transaction will be processed by the Solana network and the tokens will be swapped. We log the Solscan URL for convenience to manually verify the swap on a blockchain explorer. Error Handling: If sendRawTransaction throws an error or if confirmTransaction times out/returns an error, you should handle that in a production system (e.g., retry logic, reporting failure to user, etc.). Common issues could be blockhash expiration (if the process took too long before sending), insufficient funds, or slippage exceeding tolerance (in which case Jupiter’s on-chain program aborted the swap and the transaction did nothing, though still “successful” from a network perspective). Helius will return RPC error messages if the transaction failed; you can inspect those to debug (for example, if sendRawTransaction returns an error, it often includes a code and message).
Advanced Features with Helius (Optional)
The above flow covers the core integration of Jupiter’s swap with Helius as the RPC. Beyond simply sending transactions, Helius offers additional features that can enhance a backend token swap service. Here are a few you might consider:
Priority Fees for Faster Confirmation: We enabled Jupiter’s automatic priority fee by setting prioritizationFeeLamports: 'auto' in the swap request, which is the easiest way to ensure your swap includes a tip for validators
medium.com
. For more fine-grained control, Helius provides a Priority Fee API that can estimate the optimal priority fee based on current network congestion. You can call the RPC method getPriorityFeeEstimate via Helius to get a recommended micro-lamport per compute unit for different urgency levels (e.g., “low”, “medium”, “high”)
helius.dev
. You could then manually insert a Compute Budget instruction into your transaction with the returned microLamports value (using ComputeBudgetProgram.setComputeUnitPrice) before signing
helius.dev
. This approach is more advanced but gives you direct control over how much fee to attach. It’s especially useful if you’re building a service that might submit many transactions in congested conditions (like an arbitrage or trading bot) and you want to dynamically adjust fees for speed vs. cost trade-offs.
WebSocket Transaction Tracking: Instead of (or in addition to) using confirmTransaction polling, you can subscribe to transaction confirmations via WebSockets for real-time notifications. Using the Connection object, you can call connection.onSignature(txid, callback, commitment) to listen for your swap transaction’s status. This will invoke the callback as soon as the transaction reaches the specified commitment (e.g., 'confirmed' or 'finalized'). For example:
js
Copy
connection.onSignature(txid, (result, context) => {
  if (result.err) {
    console.error(`❌ Transaction ${txid} failed:`, result.err);
  } else {
    console.log(`✅ Transaction ${txid} is confirmed in slot ${context.slot}.`);
  }
}, 'confirmed');
Helius’s RPC fully supports Solana’s standard WebSocket subscriptions such as signatureSubscribe (used under the hood by onSignature)
helius.dev
. This approach can be more efficient for a backend that handles many swaps, as it avoids continuous polling. Ensure that your application maintains the WebSocket connection (Helius recommends sending periodic pings every 30-60 seconds to keep it alive
helius.dev
, though the web3.js library handles some of this internally).
Account Change Monitoring: Another powerful feature enabled by WebSockets is subscribing to account changes. You can use connection.onAccountChange(publicKey, callback, commitment) to watch a specific account and get notified whenever its data changes. In the context of a token swap, you might subscribe to the user’s token account for the output token. For instance, after submitting the swap, you could monitor the USDC account of the user to detect when the balance increases (indicating the swap succeeded and tokens were received). Helius RPC supports accountSubscribe and will push an update with the new account data as soon as the transaction is processed
helius.dev
. This can help you trigger post-swap actions immediately. Keep in mind you’ll receive the raw account data — for SPL Token accounts, you’d need to parse the Token Account state to get the token balance (the SPL Token library can help with that).
Enhanced Reliability via Staked Infrastructure: As mentioned, if you upgrade to a Helius paid plan, your transactions benefit from staked relay infrastructure automatically
helius.dev
. This means Helius routes your transaction directly to the current block producer (and upcoming ones), essentially bypassing the normal transaction queue. This significantly increases the likelihood your swap transaction is included in the next block, even under heavy network load. While you as a developer don’t need to change anything in code to use this (other than using your Helius endpoint and API key), it’s good to be aware of this advantage. It can make a difference for time-sensitive operations like arbitrage or handling high-volatility swaps. (Helius also offers an ultra-low-latency Sender service for critical trading use cases, which is beyond our scope but worth noting for advanced scenarios.)
Monitoring and Analytics: Once your swapping service is running, you might want to gather analytics on swaps or track specific addresses. Helius provides webhooks and data APIs that can be useful. For example, you could set up a webhook to notify your backend of any transactions involving your platform’s treasury or a certain program. Or use Helius’s enhanced transaction history API to get rich decoded data of past swaps for auditing. These are optional and outside the direct swap flow, but integrating them can improve observability of your service.
Conclusion
In this guide, we revised a server-side Solana token swap implementation to use Helius as the RPC provider. We covered configuring a Helius RPC endpoint (using an API key and environment variables), initializing the Solana connection to use that endpoint, and then using Jupiter’s API to perform a token swap (quote, transaction generation, signing, and submission). We maintained all the core swap logic (private key management, Jupiter API calls, and transaction handling), but now our infrastructure leverages Helius for improved performance and additional features. By following this guide, you should have a robust starting point for a backend token swap service:
Securely configured with environment variables for sensitive keys,
High-performance Solana RPC via Helius (with better reliability than the public cluster RPC),
Jupiter’s powerful aggregation for best-price swaps across the Solana DeFi ecosystem,
and the groundwork to add advanced features like priority fees and real-time listeners.
Next Steps: Test your implementation thoroughly on Solana devnet first (you can get a devnet API key from Helius and use devnet.helius-rpc.com as the URL, and request some tokens from a faucet). Once confident, you can move to mainnet. Always monitor transactions and handle errors gracefully – blockchain transactions can fail or behave unexpectedly under certain conditions (e.g., slippage or network issues). Keep your Helius API key usage within quota (upgrade if needed) and secure your private keys. For more information, refer to Jupiter’s official docs on swap API and Helius’s documentation for RPC and WebSocket features. Happy swapping!


# MemeTrader Pro – Backend Trading Architecture
## Helius RPC + Jupiter Swap Execution + Automated Selling via Exit Strategies

---

## 1. Overview
This backend architecture enables **manual or automated buy execution** via **Helius RPC** and **Jupiter Aggregator**, followed by **fully automated exit strategies** to protect profits and minimize losses without emotional decision-making.

---

## 2. Environment Setup

**Dependencies**
```bash
npm install @solana/web3.js bs58 dotenv cross-fetch
.env Example

env
Copy
Edit
HELIUS_RPC_URL=https://mainnet.helius-rpc.com/?api-key=<YOUR_HELIUS_API_KEY>
JUPITER_QUOTE_URL=https://quote-api.jup.ag/v6/quote
JUPITER_SWAP_URL=https://quote-api.jup.ag/v6/swap
WALLET_PRIVATE_KEY=<BASE58_PRIVATE_KEY>
3. Helius RPC Connection
ts
Copy
Edit
import { Connection } from '@solana/web3.js';
import dotenv from 'dotenv';
dotenv.config();

export const connection = new Connection(process.env.HELIUS_RPC_URL, {
  commitment: 'confirmed',
});
4. Buy Execution Flow with Jupiter
Step 1 – Fetch Quote
ts
Copy
Edit
const quoteUrl = `${process.env.JUPITER_QUOTE_URL}?inputMint=${inputMint}&outputMint=${outputMint}&amount=${amountAtomic}&slippageBps=${slippage * 100}`;
const quoteResponse = await fetch(quoteUrl);
const quoteData = await quoteResponse.json();
Step 2 – Get Swap Transaction
ts
Copy
Edit
const swapResponse = await fetch(process.env.JUPITER_SWAP_URL, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    quoteResponse: quoteData,
    userPublicKey: keypair.publicKey.toString(),
    wrapAndUnwrapSol: true,
    dynamicComputeUnitLimit: true,
    prioritizationFeeLamports: 'auto',
  }),
});
const { swapTransaction } = await swapResponse.json();
Step 3 – Sign & Send via Helius
ts
Copy
Edit
import { VersionedTransaction, Keypair } from '@solana/web3.js';
import bs58 from 'bs58';

const secretKey = bs58.decode(process.env.WALLET_PRIVATE_KEY);
const keypair = Keypair.fromSecretKey(secretKey);

const tx = VersionedTransaction.deserialize(Buffer.from(swapTransaction, 'base64'));
tx.sign([keypair]);

const latestBlockhash = await connection.getLatestBlockhash();
const txid = await connection.sendRawTransaction(tx.serialize(), { skipPreflight: true, maxRetries: 3 });
await connection.confirmTransaction({ signature: txid, ...latestBlockhash }, 'confirmed');
5. Automated Selling via Exit Strategies
Trader Goal
As a trader, I want sophisticated automated exit strategies so I can maximize profits, minimize losses, and trade without emotions.

Acceptance Criteria
WHEN a user attempts to execute any trade THEN the system SHALL block execution until an exit strategy is selected

WHEN using default strategy THEN enforce LOCKED parameters: 15% initial stop loss, 15% trailing stop loss, 100% position sale on stop trigger

WHEN profit milestones are configured THEN set targets at +50%/+100%/+150%/+200% with exactly 15% position sales at each level

WHEN moon bag allocation is set THEN reserve exactly 25% of original position with +500% automatic exit target

WHEN any profit target is hit THEN execute partial sells within 5 seconds with MEV protection and optimal slippage

WHEN stop loss price is reached THEN immediately market sell 100% of remaining position with emergency slippage up to 10%

WHEN trailing stop is active THEN update stop price every 500ms, following price increases but never decreasing

WHEN any trigger condition is met THEN evaluate and execute within 2 seconds (P95 < 5 seconds)

WHEN a user selects "From Scratch" THEN provide advanced visual editor with real-time preview

WHEN custom strategies are created THEN allow saving, naming, and modifying strategy configurations with reset capabilities

WHEN custom strategy templates are saved THEN make them available for future use with user-defined names and descriptions

Developer Integration Notes
Trigger Detection:
Use Helius WebSockets to subscribe to price updates (or token account balance changes) for near-instant trigger detection.

Sell Execution:
Use Jupiter swap API with:

wrapAndUnwrapSol: true (if SOL involved)

prioritizationFeeLamports: 'auto'

MEV protection by setting optimal slippage & priority fee.

State Management:
Track:

Entry price

Position size

Stop loss level

Trailing stop %

Profit milestones hit

Remaining position

Performance Goal:
95% of sells should execute within 5 seconds of trigger detection.

Example Pseudocode
ts
Copy
Edit
function attachExitStrategy(position) {
  subscribePriceFeed(position.token, (price) => {
    if (price <= position.stopLoss) {
      executeSell(position.size, 'emergency');
    }
    position.profitMilestones.forEach((milestone) => {
      if (!milestone.hit && price >= milestone.targetPrice) {
        executeSell(position.size * milestone.sellFraction, 'target');
        milestone.hit = true;
      }
    });
    if (position.trailingStopActive) {
      updateTrailingStop(position, price);
    }
  });
}

function executeSell(amount, reason) {
  const quote = getJupiterQuote(amount, USDC_MINT, { slippage: reason === 'emergency' ? 1000 : 50 });
  const tx = buildJupiterSwapTx(quote);
  signAndSendTx(tx, HELIUS_RPC_URL);
}
6. Backend Architecture Flow Diagram
mermaid
Copy
Edit
flowchart TD
    A[User/Strategy] -->|Place Buy| B[Helius + Jupiter Buy Execution]
    B -->|Record Position| C[Position Manager]
    C -->|Attach Exit Rules| D[Exit Strategy Engine]
    D -->|Monitor Prices via Helius WS| E[Trigger Detection]
    E -->|Trigger Hit| F[Sell Execution - Jupiter + Helius]
    F -->|Tx Sent| G[Confirmation & Log]
7. Enhanced Error Handling and MEV Protection

### 7.1 Advanced Slippage Protection
```ts
interface SlippageConfig {
  tolerance: number;     // 0.1% to 5% range
  dynamicAdjustment: boolean;
  marketConditionFactor: number;
}

function calculateOptimalSlippage(tokenPair: string, marketVolatility: number): SlippageConfig {
  const baseSlippage = 0.5; // 0.5% base
  const volatilityMultiplier = Math.min(marketVolatility * 2, 10); // Cap at 10x
  
  return {
    tolerance: Math.min(baseSlippage * volatilityMultiplier, 5.0), // Max 5%
    dynamicAdjustment: true,
    marketConditionFactor: volatilityMultiplier
  };
}
```

### 7.2 MEV Protection Implementation
```ts
interface MEVProtectionConfig {
  priorityFeeStrategy: 'auto' | 'aggressive' | 'conservative';
  bundling: boolean;
  privateMempool: boolean;
  maxMEVTolerance: number;
}

async function getMEVProtectedTransaction(quote: any, protection: MEVProtectionConfig) {
  // Use Helius Priority Fee API for optimal pricing
  const priorityFee = await fetch(`${HELIUS_RPC_URL}/v0/priority-fee-estimate`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      transaction: quote.swapTransaction,
      options: { recommended: true }
    })
  });
  
  const feeEstimate = await priorityFee.json();
  
  return {
    ...quote,
    priorityFeeLamports: feeEstimate.priorityFeeEstimate
  };
}
```

### 7.3 Database Integration for Transaction Tracking
```ts
interface TransactionRecord {
  id: string;
  userId: string;
  tokenIn: string;
  tokenOut: string;
  amountIn: number;
  amountOut: number;
  executionPrice: number;
  slippageUsed: number;
  feesPaid: TransactionFees;
  signature: string;
  status: 'pending' | 'confirmed' | 'failed';
  timestamp: Date;
}

async function recordTransaction(txData: TransactionRecord): Promise<void> {
  try {
    await prisma.transaction.create({
      data: {
        ...txData,
        fees: txData.feesPaid, // JSON field in Prisma schema
        type: 'SWAP'
      }
    });
    
    // Update position tracking
    await updatePortfolioPosition(txData);
    
  } catch (error) {
    console.error('Failed to record transaction:', error);
    // Don't throw - transaction succeeded, logging failed
  }
}

async function updatePortfolioPosition(txData: TransactionRecord): Promise<void> {
  // Calculate real-time P&L and update position
  const position = await prisma.position.findFirst({
    where: { tokenAddress: txData.tokenOut, userId: txData.userId }
  });
  
  if (position) {
    const newPnl = calculatePnL(position, txData.executionPrice);
    await prisma.position.update({
      where: { id: position.id },
      data: {
        currentPrice: txData.executionPrice,
        pnl: newPnl.amount,
        pnlPercent: newPnl.percentage
      }
    });
  }
}
```

### 7.4 Real-time Portfolio Tracking
```ts
class PortfolioTracker {
  private heliusWS: WebSocket;
  
  async subscribeToPortfolioUpdates(userId: string): Promise<void> {
    const userPositions = await prisma.position.findMany({
      where: { userId, status: 'ACTIVE' }
    });
    
    // Subscribe to price updates for all tokens
    const tokenAddresses = userPositions.map(p => p.tokenAddress);
    
    this.heliusWS = new WebSocket(process.env.HELIUS_WS_URL);
    
    this.heliusWS.onopen = () => {
      // Subscribe to account changes for all user token accounts
      tokenAddresses.forEach(token => {
        this.heliusWS.send(JSON.stringify({
          jsonrpc: '2.0',
          id: 1,
          method: 'accountSubscribe',
          params: [token, { commitment: 'confirmed' }]
        }));
      });
    };
    
    this.heliusWS.onmessage = async (event) => {
      const data = JSON.parse(event.data);
      if (data.method === 'accountNotification') {
        await this.handleAccountUpdate(data.params, userId);
      }
    };
  }
  
  private async handleAccountUpdate(params: any, userId: string): Promise<void> {
    // Update portfolio positions in real-time
    const accountInfo = params.result.value;
    // Parse account data and update database
    await this.updatePositionPricing(accountInfo, userId);
  }
}
```

8. Summary
Helius RPC → High-speed, reliable Solana connection for all buy/sell operations

Jupiter Aggregator → Best route finding for swaps with enhanced error handling

Exit Strategies → Enforce discipline, protect capital, lock in profits automatically

MEV Protection & Priority Fees → Sell orders land fast & avoid front-running

Database Integration → Complete transaction history and portfolio tracking

Real-time Updates → WebSocket-powered position monitoring and P&L calculations

