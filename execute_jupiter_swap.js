#!/usr/bin/env node

/**
 * Jupiter Token Swap Execution Script
 * Direct integration with Jupiter API for reliable swap execution
 * 
 * Target: 0.25 SOL → ATEe3MqHCzy4dKMm9DgCvd2cYBKxMTCPPb1ZnLnmbonk
 */

const { Connection, Keypair, VersionedTransaction, LAMPORTS_PER_SOL } = require('@solana/web3.js');
const bs58 = require('bs58');
const fetch = require('node-fetch');
require('dotenv').config();

// Swap configuration
const SWAP_CONFIG = {
  inputMint: 'So11111111111111111111111111111111111111112', // SOL
  outputMint: 'ATEe3MqHCzy4dKMm9DgCvd2cYBKxMTCPPb1ZnLnmbonk', // Target token
  amount: 0.25 * LAMPORTS_PER_SOL, // 0.25 SOL in lamports
  slippageBps: 100, // 1% slippage (DEFAULT preset)
  priorityFeeLamports: 0.01 * LAMPORTS_PER_SOL, // 0.01 SOL priority fee
};

// Jupiter API endpoints
const JUPITER_QUOTE_API = 'https://quote-api.jup.ag/v6/quote';
const JUPITER_SWAP_API = 'https://quote-api.jup.ag/v6/swap';

class JupiterSwapExecutor {
  constructor() {
    // Use SOLANA_RPC_URL as primary, fallback to HELIUS_RPC_URL for backwards compatibility
    const rpcUrl = process.env.SOLANA_RPC_URL || process.env.HELIUS_RPC_URL;
    
    if (!rpcUrl) {
      throw new Error('SOLANA_RPC_URL or HELIUS_RPC_URL must be set');
    }
    
    this.connection = new Connection(rpcUrl, 'confirmed');
    this.wallet = Keypair.fromSecretKey(bs58.decode(process.env.WALLET_PRIVATE_KEY));
    
    console.log('🚀 Jupiter Swap Executor Initialized');
    console.log(`📡 RPC: ${rpcUrl}`);
    console.log(`💼 Wallet: ${this.wallet.publicKey.toString()}`);
    console.log('');
  }

  async checkBalance() {
    console.log('💰 Checking wallet balance...');
    
    const balance = await this.connection.getBalance(this.wallet.publicKey);
    const solBalance = balance / LAMPORTS_PER_SOL;
    
    console.log(`   Current SOL Balance: ${solBalance.toFixed(4)} SOL`);
    
    const requiredAmount = (SWAP_CONFIG.amount + SWAP_CONFIG.priorityFeeLamports + 5000000) / LAMPORTS_PER_SOL; // +0.005 for tx fees
    
    if (solBalance < requiredAmount) {
      throw new Error(`Insufficient balance. Required: ${requiredAmount.toFixed(4)} SOL, Available: ${solBalance.toFixed(4)} SOL`);
    }
    
    console.log(`✅ Sufficient balance confirmed (need ${requiredAmount.toFixed(4)} SOL)`);
    return solBalance;
  }

  async getQuote() {
    console.log('📈 Getting Jupiter quote...');
    
    const params = new URLSearchParams({
      inputMint: SWAP_CONFIG.inputMint,
      outputMint: SWAP_CONFIG.outputMint,
      amount: SWAP_CONFIG.amount.toString(),
      slippageBps: SWAP_CONFIG.slippageBps.toString(),
      onlyDirectRoutes: 'false',
      asLegacyTransaction: 'false'
    });

    const response = await fetch(`${JUPITER_QUOTE_API}?${params}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to get quote: ${response.status} ${errorText}`);
    }

    const quote = await response.json();
    
    if (!quote || quote.error) {
      throw new Error(`Quote error: ${quote?.error || 'Unknown error'}`);
    }

    console.log('✅ Quote received:');
    console.log(`   Input: ${quote.inAmount} lamports (${(quote.inAmount / LAMPORTS_PER_SOL).toFixed(4)} SOL)`);
    console.log(`   Expected Output: ${quote.outAmount} tokens`);
    console.log(`   Price Impact: ${quote.priceImpactPct || 'N/A'}%`);
    console.log(`   Route: ${quote.routePlan?.length || 1} step(s)`);
    
    return quote;
  }

  async getSwapTransaction(quote) {
    console.log('🔄 Getting swap transaction...');
    
    const response = await fetch(JUPITER_SWAP_API, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        quoteResponse: quote,
        userPublicKey: this.wallet.publicKey.toString(),
        wrapAndUnwrapSol: true,
        prioritizationFeeLamports: SWAP_CONFIG.priorityFeeLamports,
        dynamicComputeUnitLimit: true,
        skipUserAccountsRpcCalls: true
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to get swap transaction: ${response.status} ${errorText}`);
    }

    const { swapTransaction } = await response.json();
    
    if (!swapTransaction) {
      throw new Error('No swap transaction returned from Jupiter API');
    }

    console.log('✅ Swap transaction prepared');
    return swapTransaction;
  }

  async executeSwap(swapTransaction) {
    console.log('⚡ Executing swap transaction...');
    
    // Deserialize the transaction
    const transactionBuf = Buffer.from(swapTransaction, 'base64');
    const transaction = VersionedTransaction.deserialize(transactionBuf);
    
    // Sign the transaction
    transaction.sign([this.wallet]);
    
    console.log('📝 Transaction signed, broadcasting...');
    
    // Send and confirm transaction
    const signature = await this.connection.sendTransaction(transaction, {
      maxRetries: 3,
      skipPreflight: false,
      preflightCommitment: 'confirmed'
    });

    console.log(`📡 Transaction submitted: ${signature}`);
    console.log('⏳ Waiting for confirmation...');

    // Wait for confirmation
    const confirmation = await this.connection.confirmTransaction(signature, 'confirmed');
    
    if (confirmation.value.err) {
      throw new Error(`Transaction failed: ${JSON.stringify(confirmation.value.err)}`);
    }

    return signature;
  }

  async logResults(signature) {
    console.log('');
    console.log('🎉 SWAP COMPLETED SUCCESSFULLY! 🎉');
    console.log('=' .repeat(60));
    console.log(`📋 Transaction Details:`);
    console.log(`   Signature: ${signature}`);
    console.log(`   Amount Swapped: 0.25 SOL`);
    console.log(`   Target Token: ATEe3MqHCzy4dKMm9DgCvd2cYBKxMTCPPb1ZnLnmbonk`);
    console.log(`   Slippage Used: 1% (DEFAULT preset)`);
    console.log(`   Priority Fee: 0.01 SOL`);
    console.log('');
    console.log('🔗 View Transaction:');
    console.log(`   Solscan: https://solscan.io/tx/${signature}`);
    console.log(`   SolanaFM: https://solana.fm/tx/${signature}`);
    console.log(`   Solana Beach: https://solanabeach.io/transaction/${signature}`);
    console.log('');
    console.log('✅ Jupiter aggregator swap executed via Helius RPC successfully!');
  }
}

async function main() {
  console.log('🚀 MemeTrader Pro - Jupiter Token Swap');
  console.log('=' .repeat(60));
  console.log(`📊 Swap Configuration:`);
  console.log(`   From: SOL (So11111111111111111111111111111111111111112)`);
  console.log(`   To: ATEe3MqHCzy4dKMm9DgCvd2cYBKxMTCPPb1ZnLnmbonk`);
  console.log(`   Amount: 0.25 SOL`);
  console.log(`   Slippage: 1% (DEFAULT preset)`);
  console.log(`   Priority Fee: 0.01 SOL`);
  console.log('');

  const executor = new JupiterSwapExecutor();

  try {
    // Step 1: Check balance
    await executor.checkBalance();
    console.log('');

    // Step 2: Get quote
    const quote = await executor.getQuote();
    console.log('');

    // Step 3: Get swap transaction
    const swapTransaction = await executor.getSwapTransaction(quote);
    console.log('');

    // Step 4: Execute swap
    const signature = await executor.executeSwap(swapTransaction);
    
    // Step 5: Log results
    await executor.logResults(signature);

  } catch (error) {
    console.error('');
    console.error('❌ SWAP EXECUTION FAILED');
    console.error('=' .repeat(60));
    console.error(`Error: ${error.message}`);
    
    if (error.stack) {
      console.error(`Stack: ${error.stack}`);
    }
    
    console.error('');
    console.error('💡 Troubleshooting Tips:');
    console.error('   1. Check wallet balance (need ~0.265 SOL total)');
    console.error('   2. Verify Helius RPC is accessible');
    console.error('   3. Ensure private key is correct');
    console.error('   4. Check if target token exists and is tradeable');
    
    process.exit(1);
  }
}

main();