# How To Contribute

## Before Writing/Editing Code

Before you create or modify code **in any way**, you **MUST**:

1. Read the **spec.md** and **requirment.md** files in the current directory.
2. Read /docs folder for any additional information.
3. Query the **Context7 MCP server** for up‑to‑date, verified information.
4. If the MCP query returns insufficient data, perform a detailed web search **with citations**.
5. If the web search still lacks clarity, **pause and request clarification**.

### Note About Assumptions

- **NEVER** assume you know how to implement or debug a feature without first researching via Context7 MCP.


---
