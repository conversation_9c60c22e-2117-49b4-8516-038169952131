#!/usr/bin/env node

const { Connection, PublicKey } = require('@solana/web3.js');
const fetch = require('node-fetch');

// Your wallet address from the backend logs
const WALLET_ADDRESS = '968aqNdZ5ZRLByseFo18YiSHywzqXj3S9z56Vwr62CGT';
const RPC_URL = 'https://mainnet.helius-rpc.com/?api-key=45b66ac9-b024-4c15-b6de-06ff59f4b690';

async function getWalletTokens() {
  try {
    console.log(`🔍 Checking tokens for wallet: ${WALLET_ADDRESS}\n`);
    
    const connection = new Connection(RPC_URL, 'confirmed');
    const walletPubkey = new PublicKey(WALLET_ADDRESS);
    
    // Get SOL balance
    const solBalance = await connection.getBalance(walletPubkey);
    const solAmount = solBalance / 1e9; // Convert lamports to SOL
    
    console.log(`💰 SOL Balance: ${solAmount.toFixed(4)} SOL`);
    
    // Get all token accounts
    const tokenAccounts = await connection.getParsedTokenAccountsByOwner(
      walletPubkey,
      { programId: new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') }
    );
    
    console.log(`\n🪙 Token Accounts Found: ${tokenAccounts.value.length}\n`);
    
    if (tokenAccounts.value.length === 0) {
      console.log('No token accounts found in this wallet.');
      return;
    }
    
    // Process each token account
    const tokens = [];
    
    for (const tokenAccount of tokenAccounts.value) {
      const accountInfo = tokenAccount.account.data.parsed.info;
      const tokenAmount = parseFloat(accountInfo.tokenAmount.uiAmount);
      const decimals = accountInfo.tokenAmount.decimals;
      const mint = accountInfo.mint;
      
      if (tokenAmount > 0) {
        tokens.push({
          mint,
          amount: tokenAmount,
          decimals,
          accountAddress: tokenAccount.pubkey.toString()
        });
      }
    }
    
    // Sort by amount (descending)
    tokens.sort((a, b) => b.amount - a.amount);
    
    console.log(`📊 Active Tokens (${tokens.length} with balance > 0):\n`);
    console.log('┌─────────────────────────────────────────────────┬─────────────────┬──────────┐');
    console.log('│ Token Mint Address                              │ Balance         │ Decimals │');
    console.log('├─────────────────────────────────────────────────┼─────────────────┼──────────┤');
    
    for (const token of tokens) {
      const mintShort = `${token.mint.slice(0, 8)}...${token.mint.slice(-8)}`;
      const balanceStr = token.amount.toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 6
      });
      
      console.log(`│ ${mintShort.padEnd(47)} │ ${balanceStr.padStart(15)} │ ${token.decimals.toString().padStart(8)} │`);
    }
    
    console.log('└─────────────────────────────────────────────────┴─────────────────┴──────────┘');
    
    // Show full details for tokens with significant balances
    const significantTokens = tokens.filter(t => t.amount > 1);
    if (significantTokens.length > 0) {
      console.log('\n🔍 Tokens with Balance > 1:\n');
      for (const token of significantTokens) {
        console.log(`Mint: ${token.mint}`);
        console.log(`Balance: ${token.amount.toLocaleString()}`);
        console.log(`Account: ${token.accountAddress}`);
        console.log('─'.repeat(80));
      }
    }
    
  } catch (error) {
    console.error('❌ Error fetching wallet tokens:', error.message);
    
    if (error.message.includes('Invalid public key')) {
      console.error('The wallet address appears to be invalid.');
    } else if (error.message.includes('429')) {
      console.error('Rate limited by RPC endpoint. Try again in a moment.');
    } else {
      console.error('Full error:', error);
    }
  }
}

// Run the script
getWalletTokens();