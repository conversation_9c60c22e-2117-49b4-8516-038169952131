services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: memetrader-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DATABASE_NAME:-memetrader}
      POSTGRES_USER: ${DATABASE_USER:-postgres}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD:-postgres123}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256 --auth-local=scram-sha-256"
    ports:
      - "${DATABASE_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/prisma/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - memetrader-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DATABASE_USER:-postgres} -d ${DATABASE_NAME:-memetrader}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: memetrader-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis123}
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    networks:
      - memetrader-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 10s

  # Backend API Server
  backend:
    build:
      context: .
      dockerfile: ./backend/Dockerfile
      target: development
    container_name: memetrader-backend
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-development}
      PORT: ${BACKEND_PORT:-3001}
      DATABASE_URL: postgresql://${DATABASE_USER:-postgres}:${DATABASE_PASSWORD:-postgres123}@postgres:5432/${DATABASE_NAME:-memetrader}?schema=public
      REDIS_URL: redis://:${REDIS_PASSWORD:-redis123}@redis:6379
      QUEUE_REDIS_URL: redis://:${REDIS_PASSWORD:-redis123}@redis:6379
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-minimum-32-chars}
      JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET:-your-super-secret-jwt-refresh-key-minimum-32-chars}
      CORS_ORIGIN: ${CORS_ORIGIN:-http://localhost:3000}
      SOLANA_RPC_URL: ${SOLANA_RPC_URL}
      SOLANA_WS_URL: ${SOLANA_WS_URL}
      HELIUS_API_KEY: ${HELIUS_API_KEY}
      HELIUS_WS_URL: ${HELIUS_WS_URL}
      JUPITER_API_URL: ${JUPITER_API_URL:-https://quote-api.jup.ag/v6}
      LOG_LEVEL: ${LOG_LEVEL:-info}
    ports:
      - "${BACKEND_PORT:-3001}:3001"
    volumes:
      - ./backend:/app
      - /app/node_modules
      - /app/dist
    networks:
      - memetrader-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    develop:
      watch:
        - action: sync
          path: ./backend/src
          target: /app/src
          ignore:
            - node_modules/
        - action: rebuild
          path: ./backend/package.json
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend Next.js Application
  frontend:
    build:
      context: .
      dockerfile: ./frontend/Dockerfile
      target: development
    container_name: memetrader-frontend
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-development}
      NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL:-http://localhost:3001/api}
      NEXT_PUBLIC_WS_URL: ${NEXT_PUBLIC_WS_URL:-ws://localhost:3001}
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    networks:
      - memetrader-network
    depends_on:
      - backend
    develop:
      watch:
        - action: sync
          path: ./frontend/src
          target: /app/src
          ignore:
            - node_modules/
        - action: rebuild
          path: ./frontend/package.json
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

# Named volumes for data persistence
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

# Custom network for service communication
networks:
  memetrader-network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16