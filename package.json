{"name": "memetrader-pro", "version": "1.0.0", "description": "Advanced Solana meme coin trading platform with real-time execution and risk management", "private": true, "workspaces": ["frontend", "backend", "shared"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "npm run dev --workspace=frontend", "dev:backend": "npm run dev --workspace=backend", "build": "npm run build --workspace=shared && npm run build --workspace=backend && npm run build --workspace=frontend", "build:frontend": "npm run build --workspace=frontend", "build:backend": "npm run build --workspace=backend", "build:shared": "npm run build --workspace=shared", "test": "npm run test --workspaces", "test:frontend": "npm run test --workspace=frontend", "test:backend": "npm run test --workspace=backend", "lint": "npm run lint --workspaces", "lint:fix": "npm run lint:fix --workspaces", "typecheck": "npm run type-check --workspaces", "db:migrate": "npm run db:migrate --workspace=backend", "db:generate": "npm run db:generate --workspace=backend", "db:seed": "npm run db:seed --workspace=backend", "docker:up": "docker compose up -d", "docker:down": "docker compose down", "docker:build": "docker compose build", "docker:rebuild": "docker compose build --no-cache", "docker:logs": "docker compose logs -f", "docker:dev": "docker compose -f compose.yaml -f docker-compose.override.yml up -d", "docker:dev:build": "docker compose -f compose.yaml -f docker-compose.override.yml up -d --build", "docker:dev:watch": "docker compose -f compose.yaml -f docker-compose.override.yml watch", "docker:prod": "docker compose -f compose.yaml up -d", "docker:clean": "docker compose down -v --rmi all --remove-orphans", "docker:reset": "docker compose down -v && docker system prune -f && npm run docker:dev:build", "clean": "npm run clean --workspaces && rm -rf node_modules", "install:all": "npm install && npm install --workspaces"}, "devDependencies": {"concurrently": "^8.2.2", "typescript": "^5.3.3", "@types/node": "^20.10.5"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/memetrader-pro.git"}, "keywords": ["solana", "trading", "defi", "meme-coins", "jupiter", "typescript", "nextjs", "express"], "author": "MemeTrader Pro Team", "license": "MIT"}