## Relevant Files

### Backend Core
- `src/lib/database.ts` - Prisma client configuration and connection
- `src/lib/redis.ts` - Redis client configuration and utilities
- `src/lib/queue.ts` - BullMQ queue setup and job processors
- `prisma/schema.prisma` - Database schema with all models
- `prisma/migrations/` - Database migration files
- `src/api/` - API route handlers directory

### Trading Services
- `src/services/TradingService.ts` - Jupiter integration and trade execution
- `src/services/PriceService.ts` - Helius WebSocket price feeds
- `src/services/RiskService.ts` - Risk calculations and position sizing
- `src/services/NotificationService.ts` - Multi-channel alert delivery
- `src/services/StrategyService.ts` - Exit strategy execution engine

### Frontend Core
- `src/stores/tradingStore.ts` - Zustand trading state management
- `src/stores/portfolioStore.ts` - Portfolio and position state
- `src/stores/strategyStore.ts` - Exit strategy state management
- `src/stores/alertStore.ts` - Alert and notification state
- `src/hooks/useSocket.ts` - Socket.io client integration
- `src/hooks/useTrading.ts` - Trading operations hook

### UI Components
- `src/components/trading/TradingCommandCenter.tsx` - Main trading interface
- `src/components/trading/PresetTabs.tsx` - Trading preset selection
- `src/components/trading/PositionSizer.tsx` - Position sizing calculator
- `src/components/mission-control/MissionControlDashboard.tsx` - Portfolio overview
- `src/components/mission-control/ExposureMeter.tsx` - Portfolio exposure tracking
- `src/components/strategies/ExitStrategyManager.tsx` - Strategy configuration
- `src/components/strategies/CustomStrategyEditor.tsx` - Custom strategy builder
- `src/components/positions/ActivePositions.tsx` - Position monitoring
- `src/components/positions/PositionCard.tsx` - Individual position display
- `src/components/alerts/AlertCenter.tsx` - Alert management interface
- `src/components/transactions/TransactionHistory.tsx` - Transaction analytics

### Configuration
- `src/config/presets.ts` - Trading preset configurations
- `src/config/websocket.ts` - WebSocket event handlers
- `src/types/trading.ts` - Trading-related TypeScript types
- `src/types/strategy.ts` - Strategy and exit management types
- `src/types/portfolio.ts` - Portfolio and position types

### Test Files
- `src/services/TradingService.test.ts` - Unit tests for trading service
- `src/services/StrategyService.test.ts` - Strategy execution tests
- `src/components/trading/TradingCommandCenter.test.tsx` - Component tests
- `src/api/trades.test.ts` - API integration tests
- `e2e/trading-workflow.spec.ts` - End-to-end trading tests

### Notes

- Use `npm run dev` for development server with hot reload
- Use `npm run build` for production build
- Use `npm run lint` for code linting
- Use `npm test` for running Jest unit tests
- Database migrations: `npx prisma migrate dev`
- Generate Prisma client: `npx prisma generate`

## Tasks

- [ ] 1.0 Foundation Setup and Core Infrastructure
  - [ ] 1.1 Initialize Vite React TypeScript project with optimal development configuration
  - [ ] 1.2 Set up Node.js TypeScript backend with Express.js and proper project structure
  - [ ] 1.3 Configure Prisma with PostgreSQL database and connection pooling
  - [ ] 1.4 Install and configure Redis for caching and session management
  - [ ] 1.5 Set up BullMQ job queue system for background task processing
  - [ ] 1.6 Configure CORS, middleware, and Pino logging system
  - [ ] 1.7 Install core dependencies: Zustand, Socket.io, shadcn/ui, Recharts, Zod
  - [ ] 1.8 Set up TypeScript configuration and build optimization

- [ ] 2.0 Database Schema and Backend Services
  - [ ] 2.1 Create comprehensive Prisma schema with User, Position, ExitStrategy, CustomStrategy, Transaction, Alert models
  - [ ] 2.2 Implement database migrations and indexing strategy for optimal query performance
  - [ ] 2.3 Create TypeScript interfaces matching Prisma models for type safety
  - [ ] 2.4 Build Express.js REST API with proper routing structure for all modules
  - [ ] 2.5 Implement TradingService class with Jupiter API integration for swap execution
  - [ ] 2.6 Build PriceService with Helius WebSocket integration for real-time price feeds
  - [ ] 2.7 Create RiskService for portfolio risk calculations and position sizing algorithms
  - [ ] 2.8 Implement NotificationService for multi-channel alert delivery system
  - [ ] 2.9 Set up Socket.io server for real-time bidirectional communication
  - [ ] 2.10 Create data validation schemas using Zod for all API endpoints

- [ ] 3.0 Trading Command Center Implementation
  - [ ] 3.1 Create TradingPreset class with five preset configurations (Default, vol, dead, nun, P5)
  - [ ] 3.2 Implement preset switching logic with automatic parameter configuration and visual confirmation
  - [ ] 3.3 Build position sizing calculator with risk-based recommendations (Conservative to Aggressive)
  - [ ] 3.4 Create priority fee management system with granular SOL-based controls and real-time cost display
  - [ ] 3.5 Implement slippage management with manual, auto, and preset-based modes
  - [ ] 3.6 Add MEV protection integration with simulation-first routing and price impact validation
  - [ ] 3.7 Build comprehensive trade validation logic checking position size, risk limits, and liquidity depth
  - [ ] 3.8 Create Jupiter SDK integration for quote fetching and optimal swap execution
  - [ ] 3.9 Implement MEV protection wrapper with anti-sandwich bundling and front-running detection
  - [ ] 3.10 Build bribe system for premium routing with configurable amounts and ROI tracking
  - [ ] 3.11 Add execution monitoring with real-time status updates and intelligent retry logic
  - [ ] 3.12 Create emergency execution fallback with escalated parameters and detailed failure analysis

- [ ] 4.0 Exit Strategy Manager with Custom Strategy Support
  - [ ] 4.1 Create ExitStrategy class with mandatory strategy assignment validation blocking trade execution
  - [ ] 4.2 Implement PRD-compliant default framework with locked parameters (15% stops, profit targets)
  - [ ] 4.3 Build profit milestone system with +50%/+100%/+150%/+200% targets and 15% position sales
  - [ ] 4.4 Create moon bag allocation system with 25% reservation and +500% exit target
  - [ ] 4.5 Implement custom strategy visual editor with real-time preview and PRD compliance checking
  - [ ] 4.6 Add custom strategy template system with save, name, and modify capabilities
  - [ ] 4.7 Create BullMQ job queues for strategy monitoring with 500ms price update frequency
  - [ ] 4.8 Implement trigger condition monitoring with 100ms evaluation frequency using background jobs
  - [ ] 4.9 Build automatic execution system for profit targets with 5-second execution SLA
  - [ ] 4.10 Create stop loss execution with emergency slippage handling up to 10%
  - [ ] 4.11 Implement trailing stop logic with dynamic price tracking and never-decreasing stops
  - [ ] 4.12 Add strategy state persistence and recovery mechanisms for system reliability

- [ ] 5.0 Mission Control Dashboard and Portfolio Management
  - [ ] 5.1 Create portfolio metrics calculation engine with 200ms update frequency via WebSocket
  - [ ] 5.2 Build exposure meter with real-time usage tracking and 90% warning thresholds
  - [ ] 5.3 Implement dynamic allocation visualization using Recharts with smooth animations
  - [ ] 5.4 Create risk scoring algorithm using weighted factors (position size 30%, volatility 30%, market cap 20%, strategy 20%)
  - [ ] 5.5 Build performance analytics with best/worst performer identification and attribution analysis
  - [ ] 5.6 Add correlation analysis and concentration risk detection with actionable recommendations
  - [ ] 5.7 Implement real-time state synchronization between Trading Command Center and Mission Control Dashboard
  - [ ] 5.8 Create centralized Socket.io event system for inter-module communication

- [ ] 6.0 Active Positions and Transaction Management
  - [ ] 6.1 Create position card components with real-time P&L calculations and color-coded visual feedback
  - [ ] 6.2 Implement strategy progress tracking with visual progress bars and milestone badges
  - [ ] 6.3 Build market data integration showing 24h changes, market cap, volume, and liquidity metrics
  - [ ] 6.4 Create risk assessment system with Low/Medium/High risk categorization based on volatility and size
  - [ ] 6.5 Implement quick action buttons for strategy modification, position scaling, and emergency closure
  - [ ] 6.6 Add lamport-level precision for all P&L calculations (9 decimal places) with mathematical accuracy
  - [ ] 6.7 Create comprehensive transaction recording system with complete details, fees, and blockchain hashes
  - [ ] 6.8 Implement multi-currency value storage (lamports, SOL, USD) with historical exchange rates
  - [ ] 6.9 Build analytics dashboard with summary metrics using Recharts
  - [ ] 6.10 Create performance analysis engine calculating win rates, hold times, profit factors, and Sharpe ratios
  - [ ] 6.11 Implement strategy attribution system showing performance by strategy type with optimization recommendations
  - [ ] 6.12 Add advanced filtering system by date ranges, token types, strategy types, and P&L ranges

- [ ] 7.0 Multi-Channel Alert System and Real-time Integration
  - [ ] 7.1 Create alert categorization system with priority levels (Trades, Exits, Errors, System)
  - [ ] 7.2 Build filtering interface with category, priority, status, time range, and token-specific options
  - [ ] 7.3 Implement priority-based delivery system with immediate HIGH priority alerts via all channels
  - [ ] 7.4 Create intelligent deduplication system to prevent spam while preserving important updates
  - [ ] 7.5 Build rich alert content system with transaction details, strategy context, and actionable next steps
  - [ ] 7.6 Add quiet hours functionality (11 PM - 7 AM) with HIGH priority override capabilities
  - [ ] 7.7 Build integration between Exit Strategy Manager and Active Positions for strategy progress tracking
  - [ ] 7.8 Create alert generation triggers from all modules with appropriate context and priority levels
  - [ ] 7.9 Implement transaction history integration with strategy attribution and performance tracking
  - [ ] 7.10 Add portfolio exposure updates across all modules when positions change with immediate reflection

- [ ] 8.0 Testing, Security, and Production Optimization
  - [ ] 8.1 Create unit tests for all service classes with mock external dependencies
  - [ ] 8.2 Implement integration tests for API endpoints, WebSocket connections, and database operations
  - [ ] 8.3 Build component tests for all UI modules with user interaction scenarios using React Testing Library
  - [ ] 8.4 Create end-to-end tests for complete trading workflows from entry to exit using Playwright
  - [ ] 8.5 Implement performance tests ensuring 200ms update frequencies and 2-second execution times
  - [ ] 8.6 Add error scenario testing for network failures, execution errors, and edge cases
  - [ ] 8.7 Create secure wallet connection system with transaction signing validation
  - [ ] 8.8 Implement comprehensive input validation and sanitization using Zod
  - [ ] 8.9 Add rate limiting, CORS configuration, and API security middleware
  - [ ] 8.10 Create emergency stop mechanisms and maximum position size limits with validation
  - [ ] 8.11 Implement secure session management with Redis and encrypted user preferences storage
  - [ ] 8.12 Add comprehensive audit logging system with Pino for security monitoring
  - [ ] 8.13 Implement efficient WebSocket connection pooling and data throttling
  - [ ] 8.14 Create smart caching system using Redis for frequently accessed data
  - [ ] 8.15 Optimize React rendering with memo, useMemo, and selective Zustand subscriptions
  - [ ] 8.16 Implement virtual scrolling for large transaction lists using react-window
  - [ ] 8.17 Add progressive loading for dashboard widgets and non-critical components
  - [ ] 8.18 Create Bull Board admin interface for job queue monitoring and management