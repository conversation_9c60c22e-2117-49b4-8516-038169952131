# =============================================================================
# DOCKER COMPOSE DEVELOPMENT OVERRIDES
# =============================================================================
# This file provides development-specific configurations that override
# the main compose.yaml settings when running in development mode

services:
  # PostgreSQL Development Overrides
  postgres:
    environment:
      POSTGRES_DB: memetrader_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
    ports:
      - "5432:5432"
    volumes:
      # Development data volume (separate from production)
      - postgres_dev_data:/var/lib/postgresql/data
      # Enable easier database inspection
      - ./backend/prisma/seed.sql:/docker-entrypoint-initdb.d/seed.sql:ro
    # More relaxed health check for development
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d memetrader_dev"]
      interval: 5s
      timeout: 3s
      retries: 3
      start_period: 15s

  # Redis Development Overrides
  redis:
    environment:
      REDIS_PASSWORD: redis123
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    # Disable persistence in development for faster startup
    command: redis-server --appendonly no --requirepass redis123
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 5s
      timeout: 2s
      retries: 3
      start_period: 5s

  # Backend Development Overrides
  backend:
    build:
      target: development
      args:
        NODE_ENV: development
    environment:
      NODE_ENV: development
      PORT: 3001
      DATABASE_URL: ***********************************************/memetrader_dev?schema=public
      REDIS_URL: redis://:redis123@redis:6379
      JWT_SECRET: dev-jwt-secret-key-minimum-32-chars-long
      JWT_REFRESH_SECRET: dev-jwt-refresh-secret-key-minimum-32-chars
      CORS_ORIGIN: http://localhost:3000,http://localhost:3001
      LOG_LEVEL: debug
    ports:
      - "3001:3001"
      - "9229:9229"  # Node.js debugger port
    volumes:
      # Enable hot reload with source code mounting
      - ./backend/src:/app/src:ro
      - ./backend/prisma:/app/prisma:ro
      - ./backend/package.json:/app/package.json:ro
      - ./backend/tsconfig.json:/app/tsconfig.json:ro
      # Preserve node_modules and dist
      - backend_node_modules:/app/node_modules
      - backend_dist:/app/dist
    # Override command for development with debugger and watch mode
    command: ["npm", "run", "dev"]
    # Development health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/health || exit 1"]
      interval: 15s
      timeout: 5s
      retries: 3
      start_period: 30s

  # Frontend Development Overrides
  frontend:
    build:
      target: development
      args:
        NODE_ENV: development
    environment:
      NODE_ENV: development
      NEXT_PUBLIC_API_URL: http://localhost:3001/api
      NEXT_PUBLIC_WS_URL: ws://localhost:3001
      # Enable Next.js development features
      FAST_REFRESH: true
      NEXT_TELEMETRY_DISABLED: 1
      # Increase Node.js memory limit for development
      NODE_OPTIONS: "--max-old-space-size=4096"
      # Add required environment variables for trading
      HELIUS_RPC_URL: ${HELIUS_RPC_URL}
      SOLANA_RPC_URL: ${SOLANA_RPC_URL}
      WALLET_PRIVATE_KEY: ${WALLET_PRIVATE_KEY}
      JWT_SECRET: ${JWT_SECRET}
      TRADING_WALLET_ADDRESS: ${TRADING_WALLET_ADDRESS}
    deploy:
      resources:
        limits:
          memory: 6G
        reservations:
          memory: 2G
    ports:
      - "3000:3000"
    volumes:
      # Enable hot reload with source code mounting
      - ./frontend/src:/app/src:ro
      - ./frontend/public:/app/public:ro
      - ./frontend/package.json:/app/package.json:ro
      - ./frontend/next.config.js:/app/next.config.js:ro
      - ./frontend/tailwind.config.js:/app/tailwind.config.js:ro
      - ./frontend/tsconfig.json:/app/tsconfig.json:ro
      - ./frontend/postcss.config.js:/app/postcss.config.js:ro
      # Preserve node_modules and .next
      - frontend_node_modules:/app/node_modules
      - frontend_next:/app/.next
    # Override command for development with hot reload
    command: ["npm", "run", "dev"]
    # Development health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 45s

# Development-specific volumes
volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  backend_node_modules:
    driver: local
  backend_dist:
    driver: local
  frontend_node_modules:
    driver: local
  frontend_next:
    driver: local

# Development network configuration
networks:
  memetrader-network:
    driver: bridge