# MemeTrader Pro - Mainnet Configuration Guide

## ⚠️ IMPORTANT SECURITY NOTICE

You are configuring MemeTrader Pro to use **Solana mainnet** for local development. This means:
- **Real SOL and tokens** will be used
- **Transaction fees** will be charged in real SOL
- **Failed transactions** will still consume fees
- **Smart contract risks** apply to all operations

## 🔧 Required Setup Steps

### 1. Create a Dedicated Development Wallet

```bash
# Generate a new mainnet development wallet
solana-keygen new --outfile ~/.config/solana/mainnet-dev.json

# Set the wallet as default (optional)
solana config set --keypair ~/.config/solana/mainnet-dev.json

# Set mainnet cluster
solana config set --url mainnet-beta

# Check your wallet address
solana address

# Fund your wallet (transfer a small amount of SOL for testing)
# DO NOT transfer large amounts - start with 0.1-0.5 SOL maximum
```

### 2. Get Required API Keys

#### Helius API Key (Required)
1. Sign up at [helius.dev](https://helius.dev)
2. Create a new project
3. Copy your API key
4. Update `HELIUS_API_KEY` in your `.env` file

#### Telegram Integration (Optional)
1. Go to [my.telegram.org](https://my.telegram.org)
2. Create an application
3. Get your `API_ID` and `API_HASH`
4. Generate session string using a Python script or library

### 3. Update Environment Variables

Copy `.env` to `.env.local` and update these critical values:

```env
# Your mainnet development wallet
WALLET_PRIVATE_KEY="YOUR_ACTUAL_MAINNET_PRIVATE_KEY"
TRADING_WALLET_ADDRESS="YOUR_ACTUAL_MAINNET_ADDRESS"
SOLANA_FEE_ACCOUNT="YOUR_FEE_COLLECTION_ADDRESS"

# Your real Helius API key
HELIUS_API_KEY="your-actual-helius-api-key"
HELIUS_RPC_URL="https://mainnet.helius-rpc.com/?api-key=your-actual-helius-api-key"
HELIUS_WS_URL="wss://atlas-mainnet.helius-rpc.com/?api-key=your-actual-helius-api-key"

# Strong JWT secrets (generate new ones)
JWT_SECRET="generate-a-strong-64-character-secret-for-mainnet-use"
JWT_REFRESH_SECRET="generate-another-strong-64-character-secret-for-refresh"
```

### 4. Generate Strong Secrets

```bash
# Generate JWT secrets
openssl rand -base64 64

# Generate encryption key
openssl rand -hex 32
```

## 🚀 Mainnet Configuration Changes Applied

### ✅ Network Endpoints Updated
- **Solana RPC**: `api.mainnet-beta.solana.com`
- **WebSocket**: `wss://api.mainnet-beta.solana.com/`
- **Network**: `mainnet-beta`

### ✅ Jupiter API (Mainnet)
- **Quote API**: `https://quote-api.jup.ag/v6`
- **Swap API**: `https://quote-api.jup.ag/v6/swap`
- **Price API**: `https://price.jup.ag/v4/price`
- **Token List**: `https://token.jup.ag/all`

### ✅ Helius Integration (Mainnet)
- **RPC URL**: `https://mainnet.helius-rpc.com/?api-key=YOUR_KEY`
- **WebSocket**: `wss://atlas-mainnet.helius-rpc.com/?api-key=YOUR_KEY`

### ✅ Mainnet Token Addresses
- **SOL**: `So11111111111111111111111111111111111111112`
- **USDC**: `EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v`
- **USDT**: `Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB`

### ✅ Program IDs (Mainnet)
- **Jupiter V6**: `JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4`
- **Raydium AMM**: `675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8`
- **Orca**: `9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM`

### ✅ Trading Parameters (Conservative)
- **Max Slippage**: 1% (down from 5% testnet)
- **Priority Fee**: 0.00001 SOL (increased for reliability)
- **Rate Limits**: Conservative for mainnet usage

## 🔒 Security Best Practices

### Development Wallet Security
1. **Limited Funding**: Never put more than 1-2 SOL in development wallet
2. **Separate Wallets**: Use different wallets for development vs production
3. **Regular Monitoring**: Check balance and transactions frequently
4. **Backup Keys**: Securely backup your private keys

### Environment Security
1. **Never Commit**: Add `.env.local` to `.gitignore`
2. **Strong Secrets**: Use cryptographically strong JWT secrets
3. **Regular Rotation**: Rotate API keys and secrets periodically
4. **Access Control**: Limit who has access to mainnet credentials

### Testing Protocol
1. **Start Small**: Begin with minimal SOL amounts (0.01-0.1 SOL)
2. **Test Thoroughly**: Verify all functionality before scaling up
3. **Monitor Fees**: Track transaction costs and failed transactions
4. **Error Handling**: Ensure robust error handling for failed transactions

## 🧪 Testing Checklist

Before using mainnet configuration:

- [ ] Wallet generated and funded with small amount (0.1-0.5 SOL)
- [ ] Helius API key configured and tested
- [ ] Database connection working (separate mainnet DB)
- [ ] Jupiter API integration functional
- [ ] WebSocket connections established
- [ ] Error handling tested with small transactions
- [ ] Fee calculations accurate
- [ ] All environment variables properly set

## 📊 Monitoring and Alerts

### Transaction Monitoring
- Monitor all transactions on [Solscan](https://solscan.io)
- Set up alerts for failed transactions
- Track fee consumption and wallet balance

### API Usage Monitoring
- Monitor Helius API rate limits
- Track Jupiter API response times
- Set up alerts for API failures

### System Health
- Database performance monitoring
- WebSocket connection stability
- Error rate tracking

## 🚨 Emergency Procedures

### If Wallet Compromised
1. Immediately transfer remaining funds to secure wallet
2. Generate new wallet and update environment variables
3. Rotate all API keys and secrets
4. Review transaction history for unauthorized activity

### If High Fees/Failed Transactions
1. Check Solana network status
2. Adjust priority fees and slippage tolerance
3. Implement circuit breakers for high-cost operations
4. Review and optimize transaction logic

## 📚 Additional Resources

- [Solana Documentation](https://docs.solana.com/)
- [Jupiter Aggregator Docs](https://docs.jup.ag/)
- [Helius Developer Docs](https://docs.helius.dev/)
- [Solana Security Best Practices](https://github.com/solana-labs/solana/blob/master/docs/src/developing/programming-model/calling-between-programs.md)

---

**Remember**: Mainnet development requires extra caution. Always test with small amounts and have proper monitoring in place before scaling up operations.
