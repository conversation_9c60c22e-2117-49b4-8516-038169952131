#!/usr/bin/env node

/**
 * Execute Token Swap Script
 * Swaps 0.25 SOL for ATEe3MqHCzy4dKMm9DgCvd2cYBKxMTCPPb1ZnLnmbonk using Jupiter aggregator
 */

const https = require('https');
const http = require('http');

// Trading parameters
const SWAP_PARAMS = {
  tokenIn: 'So11111111111111111111111111111111111111112', // SOL
  tokenOut: 'ATEe3MqHCzy4dKMm9DgCvd2cYBKxMTCPPb1ZnLnmbonk', // Target token
  amount: 250000000, // 0.25 SOL in lamports (250,000,000)
  slippage: 0.01, // 1% slippage (DEFAULT preset)
  priorityFeeLamports: 10000000, // 0.01 SOL priority fee (DEFAULT preset)
  strategyId: 'default-strategy-id' // Use default strategy
};

const API_BASE_URL = 'http://localhost:5001/api';

// Mock auth token for testing (in real app this would come from login)
const AUTH_TOKEN = 'mock-auth-token-for-testing';

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const protocol = options.protocol === 'https:' ? https : http;
    
    const req = protocol.request(options, (res) => {
      let body = '';
      
      res.on('data', (chunk) => {
        body += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: jsonBody
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function executeSwap() {
  console.log('🚀 Starting token swap execution...');
  console.log(`📊 Swap Details:`);
  console.log(`   From: SOL (${SWAP_PARAMS.tokenIn})`);
  console.log(`   To: ATEe3MqHCzy4dKMm9DgCvd2cYBKxMTCPPb1ZnLnmbonk`);
  console.log(`   Amount: 0.25 SOL (${SWAP_PARAMS.amount} lamports)`);
  console.log(`   Slippage: ${SWAP_PARAMS.slippage * 100}% (DEFAULT preset)`);
  console.log(`   Priority Fee: ${SWAP_PARAMS.priorityFeeLamports / 1000000000} SOL`);
  console.log('');

  try {
    // Step 1: Get a quote for the swap
    console.log('📈 Step 1: Getting Jupiter quote...');
    
    const quoteOptions = {
      hostname: 'localhost',
      port: 5001,
      path: '/api/trading/quote',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${AUTH_TOKEN}`
      }
    };

    const quoteResponse = await makeRequest(quoteOptions, {
      tokenIn: SWAP_PARAMS.tokenIn,
      tokenOut: SWAP_PARAMS.tokenOut,
      amount: SWAP_PARAMS.amount,
      slippage: SWAP_PARAMS.slippage
    });

    if (quoteResponse.statusCode !== 200) {
      console.error('❌ Failed to get quote:', quoteResponse.body);
      return;
    }

    const quote = quoteResponse.body.data;
    console.log('✅ Quote received:');
    console.log(`   Expected output: ${quote.outAmount} tokens`);
    console.log(`   Price impact: ${quote.priceImpactPct || 'N/A'}%`);
    console.log('');

    // Step 2: Execute the swap
    console.log('⚡ Step 2: Executing swap transaction...');
    
    const executeOptions = {
      hostname: 'localhost',
      port: 5001,
      path: '/api/trading/execute',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${AUTH_TOKEN}`
      }
    };

    const executeResponse = await makeRequest(executeOptions, {
      tokenIn: SWAP_PARAMS.tokenIn,
      tokenOut: SWAP_PARAMS.tokenOut,
      amount: SWAP_PARAMS.amount,
      slippage: SWAP_PARAMS.slippage,
      strategyId: SWAP_PARAMS.strategyId,
      priorityFeeLamports: SWAP_PARAMS.priorityFeeLamports
    });

    if (executeResponse.statusCode === 200 && executeResponse.body.success) {
      console.log('🎉 Swap executed successfully!');
      console.log(`📋 Transaction Details:`);
      console.log(`   Transaction Hash: ${executeResponse.body.data.signature}`);
      console.log(`   Status: ${executeResponse.body.data.status || 'Confirmed'}`);
      
      if (executeResponse.body.data.outputAmount) {
        console.log(`   Tokens Received: ${executeResponse.body.data.outputAmount}`);
      }
      
      if (executeResponse.body.warnings && executeResponse.body.warnings.length > 0) {
        console.log(`⚠️  Warnings: ${executeResponse.body.warnings.join(', ')}`);
      }
      
      console.log('');
      console.log('🔗 View transaction on Solana Explorer:');
      console.log(`   https://solscan.io/tx/${executeResponse.body.data.signature}`);
      
    } else {
      console.error('❌ Swap execution failed:');
      console.error(`   Status: ${executeResponse.statusCode}`);
      console.error(`   Error: ${JSON.stringify(executeResponse.body, null, 2)}`);
    }

  } catch (error) {
    console.error('💥 Fatal error during swap execution:', error.message);
    console.error(error.stack);
  }
}

// Execute the swap
executeSwap().catch(console.error);