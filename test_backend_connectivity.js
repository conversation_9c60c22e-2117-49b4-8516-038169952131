#!/usr/bin/env node

/**
 * Test Backend Connectivity
 * Simple test to verify backend API is accessible
 */

const http = require('http');

function testEndpoint(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 5001,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };
    
    console.log(`Testing: ${method} http://localhost:5001${path}`);
    
    const req = http.request(options, (res) => {
      let body = '';
      
      res.on('data', (chunk) => {
        body += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: jsonBody
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testConnectivity() {
  console.log('🔍 Testing Backend Connectivity...');
  console.log('');

  try {
    // Test 1: Health endpoint (should be public)
    console.log('1. Testing health endpoint...');
    const healthResponse = await testEndpoint('/health');
    console.log(`   Status: ${healthResponse.statusCode}`);
    console.log(`   Response: ${JSON.stringify(healthResponse.body, null, 2)}`);
    console.log('');

    // Test 2: Trading health endpoint
    console.log('2. Testing trading health endpoint...');
    const tradingHealthResponse = await testEndpoint('/api/trading/health');
    console.log(`   Status: ${tradingHealthResponse.statusCode}`);
    console.log(`   Response: ${JSON.stringify(tradingHealthResponse.body, null, 2)}`);
    console.log('');

    // Test 3: Test quote endpoint without auth (to see auth error)
    console.log('3. Testing quote endpoint without auth...');
    try {
      const quoteResponse = await testEndpoint('/api/trading/quote', 'POST', {
        tokenIn: 'So11111111111111111111111111111111111111112',
        tokenOut: 'ATEe3MqHCzy4dKMm9DgCvd2cYBKxMTCPPb1ZnLnmbonk',
        amount: 80000000,
        slippage: 0.01
      });
      console.log(`   Status: ${quoteResponse.statusCode}`);
      console.log(`   Response: ${JSON.stringify(quoteResponse.body, null, 2)}`);
    } catch (error) {
      console.log(`   Error: ${error.message}`);
    }
    console.log('');

    console.log('✅ Backend connectivity test completed');

  } catch (error) {
    console.error('❌ Connectivity test failed:', error.message);
  }
}

testConnectivity();