# =============================================================================
# TESTNET CONFIGURATION - SAFE FOR DEVELOPMENT
# =============================================================================
# 
# ⚠️  NEVER use real mainnet credentials in development!
# This testnet configuration is safe for testing swap functionality
#
# Instructions:
# 1. Create a new devnet/testnet wallet
# 2. Get some SOL from faucet: https://faucet.solana.com/
# 3. Use this configuration for safe testing
# =============================================================================

# =============================================================================
# DATABASE AND REDIS CONFIGURATION
# =============================================================================
DATABASE_URL="postgresql://user:password@localhost:5432/memetrader_testnet"
REDIS_URL="redis://localhost:6379"

# =============================================================================
# JWT CONFIGURATION
# =============================================================================
JWT_SECRET="your-super-secret-jwt-key-at-least-32-characters-long-testnet"
JWT_EXPIRES_IN="7d"
JWT_REFRESH_SECRET="your-super-secret-refresh-key-at-least-32-characters-long-testnet"
JWT_REFRESH_EXPIRES_IN="30d"

# =============================================================================
# SOLANA TESTNET CONFIGURATION
# =============================================================================
# Use devnet/testnet for safe development
SOLANA_RPC_URL="https://api.devnet.solana.com"
SOLANA_WS_URL="wss://api.devnet.solana.com/"
SOLANA_NETWORK="devnet"

# =============================================================================
# TESTNET WALLET CONFIGURATION
# =============================================================================
# ⚠️  REPLACE WITH YOUR TESTNET WALLET - NEVER USE MAINNET KEYS!
# Generate a new keypair for testnet: solana-keygen new --outfile testnet-keypair.json
WALLET_PRIVATE_KEY="REPLACE_WITH_YOUR_TESTNET_PRIVATE_KEY_BASE58"
TRADING_WALLET_ADDRESS="REPLACE_WITH_YOUR_TESTNET_PUBLIC_KEY"
SOLANA_FEE_ACCOUNT="REPLACE_WITH_YOUR_TESTNET_PUBLIC_KEY"

# =============================================================================
# HELIUS CONFIGURATION (Optional - can use free tier for testnet)
# =============================================================================
HELIUS_API_KEY="demo"  # Use demo key for testnet or your free tier key
HELIUS_WS_URL="wss://atlas-devnet.helius-rpc.com/"

# =============================================================================
# TRADING CONFIGURATION - TESTNET SAFE VALUES
# =============================================================================
MAX_SLIPPAGE_BPS="500"  # 5% max slippage for testnet
DEFAULT_PRIORITY_FEE="5000"  # 0.000005 SOL
NODE_ENV="development"
PORT="3001"
CORS_ORIGIN="http://localhost:3000"

# =============================================================================
# RATE LIMITING AND SECURITY
# =============================================================================
RATE_LIMIT_WINDOW_MS="900000"
RATE_LIMIT_MAX_REQUESTS="1000"  # Higher limits for development
LOG_LEVEL="debug"
BCRYPT_ROUNDS="10"  # Lower for faster testing
WS_HEARTBEAT_INTERVAL="30000"