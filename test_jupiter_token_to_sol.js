#!/usr/bin/env node

// Test Jupiter API for Token -> SOL swap
async function testJupiterTokenToSol() {
  try {
    console.log('🚀 Testing Jupiter Token -> SOL swap quote...')
    
    // User's token info
    const tokenIn = 'ATEe3MqHCzy4dKMm9DgCvd2cYBKxMTCPPb1ZnLnmbonk' // User's token
    const tokenOut = 'So11111111111111111111111111111111111111112' // SOL
    const amount = '1000000' // 1 token (6 decimals)
    const slippageBps = '300' // 3%
    
    // Jupiter v1 API endpoint with optimization parameters
    const jupiterUrl = `https://lite-api.jup.ag/swap/v1/quote?inputMint=${tokenIn}&outputMint=${tokenOut}&amount=${amount}&slippageBps=${slippageBps}&restrictIntermediateTokens=true&dynamicSlippage=true&maxAccounts=64`
    
    console.log('📡 Fetching quote from:', jupiterUrl)
    
    const response = await fetch(jupiterUrl)
    const data = await response.json()
    
    if (!response.ok) {
      console.error('❌ Jupiter API error:', data)
      return
    }
    
    console.log('✅ Quote successful!')
    console.log('📊 Quote details:')
    console.log(`  Input: ${data.inAmount} ${tokenIn}`)
    console.log(`  Output: ${data.outAmount} SOL`)
    console.log(`  Price impact: ${data.priceImpactPct || 'N/A'}%`)
    console.log(`  Routes found: ${data.routePlan?.length || 0}`)
    
    if (data.routePlan && data.routePlan.length > 0) {
      console.log('🛣️  Route plan:')
      data.routePlan.forEach((route, i) => {
        console.log(`    ${i + 1}. ${route.swapInfo?.ammKey || route.ammKey || 'Unknown DEX'}`)
      })
    }
    
    // Test SOL -> Token direction as well
    console.log('\n🔄 Testing reverse direction (SOL -> Token)...')
    
    const reverseUrl = `https://lite-api.jup.ag/swap/v1/quote?inputMint=${tokenOut}&outputMint=${tokenIn}&amount=********&slippageBps=${slippageBps}&restrictIntermediateTokens=true&dynamicSlippage=true&maxAccounts=64`
    
    const reverseResponse = await fetch(reverseUrl)
    const reverseData = await reverseResponse.json()
    
    if (!reverseResponse.ok) {
      console.error('❌ Reverse quote failed:', reverseData)
    } else {
      console.log('✅ Reverse quote successful!')
      console.log(`  Input: ${reverseData.inAmount} SOL`)
      console.log(`  Output: ${reverseData.outAmount} ${tokenIn}`)
      console.log(`  Price impact: ${reverseData.priceImpactPct || 'N/A'}%`)
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

testJupiterTokenToSol()