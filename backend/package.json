{"name": "@memetrader-pro/backend", "version": "1.0.0", "description": "MemeTrader Pro Backend API", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:seed": "tsx src/scripts/seed.ts", "db:studio": "prisma studio", "clean": "rm -rf dist node_modules"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "dotenv": "^16.3.1", "winston": "^3.11.0", "redis": "^4.6.11", "bullmq": "^4.15.4", "ioredis": "^5.3.2", "@prisma/client": "^5.7.1", "@solana/web3.js": "^1.87.6", "@jup-ag/api": "^6.0.0", "ws": "^8.14.2", "axios": "^1.6.2", "zod": "^3.22.4", "uuid": "^9.0.1", "decimal.js": "^10.4.3"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.5", "@types/ws": "^8.5.10", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.55.0", "jest": "^29.7.0", "@types/jest": "^29.5.8", "ts-jest": "^29.1.1", "tsx": "^4.6.2", "typescript": "^5.3.3", "prisma": "^5.7.1", "nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}