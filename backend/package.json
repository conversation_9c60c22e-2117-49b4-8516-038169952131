{"name": "@memetrader-pro/backend", "version": "1.0.0", "description": "MemeTrader Pro Backend API", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=__tests__/services", "test:integration": "jest --testPathPattern=__tests__/integration", "test:e2e": "jest --testPathPattern=__tests__/e2e", "test:services": "jest --testPathPattern=services", "test:routes": "jest --testPathPattern=routes", "test:utils": "jest --testPathPattern=utils", "test:silent": "jest --silent", "test:verbose": "jest --verbose", "test:ci": "jest --ci --coverage --watchAll=false --maxWorkers=2", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "test:setup": "npm run test:db:setup", "test:teardown": "npm run test:db:teardown", "test:db:setup": "cross-env NODE_ENV=test DATABASE_URL=$TEST_DATABASE_URL npx prisma migrate deploy && npx prisma db seed", "test:db:teardown": "cross-env NODE_ENV=test DATABASE_URL=$TEST_DATABASE_URL npx prisma migrate reset --force --skip-seed", "test:db:reset": "npm run test:db:teardown && npm run test:db:setup", "test:error-scenarios": "jest --testPathPattern=errorScenarios --testTimeout=300000 --maxWorkers=4", "test:error-scenarios:critical": "jest --testPathPattern=errorScenarios --testNamePattern=\"Database Failures|External API Failures|Chaos Engineering|Security Breach\" --testTimeout=300000", "test:error-scenarios:coverage": "jest --testPathPattern=errorScenarios --coverage --testTimeout=300000 --maxWorkers=2", "test:error-scenarios:runner": "tsx src/__tests__/errorScenarios/runErrorScenarios.ts", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit", "validate:env": "tsx src/scripts/validateEnvironment.ts", "verify:schema": "tsx src/scripts/verifySchema.ts", "db:migrate": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:generate": "prisma generate", "db:seed": "tsx src/scripts/seed.ts", "db:setup": "npm run db:generate && npm run db:migrate && npm run db:seed && npm run verify:schema", "db:reset": "prisma migrate reset --force && npm run db:seed", "db:studio": "prisma studio", "clean": "rm -rf dist node_modules coverage .jest-cache test-results"}, "dependencies": {"@jup-ag/api": "^6.0.0", "@memetrader-pro/shared": "file:../shared", "@prisma/client": "^5.7.1", "@solana/spl-token": "^0.4.13", "@solana/web3.js": "^1.98.4", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "bs58": "^6.0.0", "bullmq": "^4.15.4", "compression": "^1.7.4", "cors": "^2.8.5", "decimal.js": "^10.4.3", "dotenv": "^16.6.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "node-fetch": "^3.3.2", "nodemailer": "^6.10.1", "redis": "^4.6.11", "socket.io": "^4.7.4", "uuid": "^9.0.1", "winston": "^3.11.0", "ws": "^8.14.2", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/node": "^20.10.5", "@types/nodemailer": "^6.4.17", "@types/supertest": "^6.0.2", "@types/uuid": "^9.0.7", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "cross-env": "^7.0.3", "eslint": "^8.55.0", "jest": "^29.7.0", "jest-junit": "^16.0.0", "jest-mock-extended": "^3.0.5", "nodemon": "^3.0.2", "prisma": "^5.7.1", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tsx": "^4.20.3", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}