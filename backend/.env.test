# Test Environment Configuration
NODE_ENV=test

# Database Configuration
DATABASE_URL=postgresql://test:test@localhost:5432/memetrader_test
TEST_DATABASE_URL=postgresql://test:test@localhost:5432/memetrader_test

# Redis Configuration
REDIS_URL=redis://localhost:6379/1
TEST_REDIS_URL=redis://localhost:6379/1

# API Keys (Test/Mock values)
JUPITER_API_URL=https://quote-api.jup.ag/v6
HELIUS_API_KEY=test-helius-key
HELIUS_RPC_URL=https://rpc.helius.xyz

# Wallet Configuration (Test values - DO NOT USE IN PRODUCTION)
WALLET_PRIVATE_KEY=test-private-key-for-testing-only
WALLET_ADDRESS=test-wallet-address-for-testing

# Application Configuration
PORT=3001
JWT_SECRET=test-jwt-secret-for-testing
CORS_ORIGIN=http://localhost:3000

# Feature Flags for Testing
ENABLE_TRADING=true
ENABLE_STRATEGIES=true
ENABLE_ALERTS=true
ENABLE_WEBSOCKETS=true

# Logging Configuration
LOG_LEVEL=warn
LOG_FILE_ENABLED=false

# Rate Limiting (Relaxed for testing)
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000

# Testing Specific
DISABLE_AUTH_FOR_TESTS=true
MOCK_EXTERNAL_APIS=true
TEST_TIMEOUT=30000