#!/usr/bin/env node

/**
 * Direct Token Swap Execution Script
 * Executes swap using TradingService directly without HTTP API
 */

const { TradingService } = require('./dist/services/tradingService');
const { WalletService } = require('./dist/services/walletService');

// Import environment config
require('dotenv').config();

// Trading parameters from user request
const SWAP_PARAMS = {
  inputMint: 'So11111111111111111111111111111111111111112', // SOL
  outputMint: 'ATEe3MqHCzy4dKMm9DgCvd2cYBKxMTCPPb1ZnLnmbonk', // Target token
  amount: 250000000, // 0.25 SOL in lamports (250,000,000)
  slippageBps: 100, // 1% slippage (DEFAULT preset) = 100 basis points
  priorityFeeLamports: 10000000, // 0.01 SOL priority fee (DEFAULT preset)
  userPublicKey: process.env.TRADING_WALLET_ADDRESS
};

async function executeSwap() {
  console.log('🚀 MemeTrader Pro - Direct Token Swap Execution');
  console.log('=' .repeat(60));
  console.log(`📊 Swap Configuration:`);
  console.log(`   From Token: SOL (${SWAP_PARAMS.inputMint})`);
  console.log(`   To Token: ${SWAP_PARAMS.outputMint}`);
  console.log(`   Amount: 0.25 SOL (${SWAP_PARAMS.amount.toLocaleString()} lamports)`);
  console.log(`   Slippage: ${SWAP_PARAMS.slippageBps / 100}% (DEFAULT preset)`);
  console.log(`   Priority Fee: ${SWAP_PARAMS.priorityFeeLamports / 1000000000} SOL`);
  console.log(`   Wallet: ${SWAP_PARAMS.userPublicKey}`);
  console.log('');

  try {
    // Step 1: Check wallet balance
    console.log('💰 Step 1: Checking wallet balance...');
    
    const balance = await WalletService.getSOLBalance();
    console.log(`   Current SOL Balance: ${balance} SOL`);
    
    if (balance < 0.25) {
      console.error(`❌ Insufficient SOL balance. Required: 0.25 SOL, Available: ${balance} SOL`);
      return;
    }
    
    if (balance < 0.26) { // Need extra for fees
      console.warn(`⚠️  Low SOL balance. Consider keeping more SOL for transaction fees.`);
    }
    
    console.log('✅ Sufficient balance confirmed');
    console.log('');

    // Step 2: Get Jupiter quote
    console.log('📈 Step 2: Getting Jupiter aggregator quote...');
    
    const quote = await TradingService.getQuote(SWAP_PARAMS);
    
    console.log('✅ Quote received successfully:');
    console.log(`   Input Amount: ${quote.inAmount} lamports (${quote.inAmount / 1000000000} SOL)`);
    console.log(`   Expected Output: ${quote.outAmount} tokens`);
    console.log(`   Price Impact: ${quote.priceImpactPct || 'N/A'}%`);
    console.log(`   Platform Fee: ${quote.platformFee || 0} lamports`);
    console.log(`   Route: ${quote.routePlan?.length || 1} hop(s)`);
    console.log('');

    // Step 3: Execute the swap
    console.log('⚡ Step 3: Executing swap transaction...');
    console.log('🔄 Preparing transaction with Jupiter aggregator...');
    
    const tradeParams = {
      tokenIn: SWAP_PARAMS.inputMint,
      tokenOut: SWAP_PARAMS.outputMint,
      amount: SWAP_PARAMS.amount,
      slippage: SWAP_PARAMS.slippageBps / 10000, // Convert basis points to decimal
      priorityFeeLamports: SWAP_PARAMS.priorityFeeLamports,
      strategyId: null // No exit strategy for this test
    };
    
    const result = await TradingService.executeTrade(
      tradeParams,
      SWAP_PARAMS.userPublicKey,
      'test-user-id' // Mock user ID for testing
    );

    if (result.success) {
      console.log('🎉 SWAP EXECUTED SUCCESSFULLY! 🎉');
      console.log('=' .repeat(60));
      console.log(`📋 Transaction Details:`);
      console.log(`   Transaction Signature: ${result.signature}`);
      console.log(`   Status: ${result.status || 'Confirmed'}`);
      console.log(`   Block Time: ${new Date().toISOString()}`);
      
      if (result.outputAmount) {
        console.log(`   Tokens Received: ${result.outputAmount}`);
      }
      
      if (result.actualPrice) {
        console.log(`   Execution Price: ${result.actualPrice}`);
      }
      
      console.log('');
      console.log('🔗 Transaction Links:');
      console.log(`   Solscan: https://solscan.io/tx/${result.signature}`);
      console.log(`   SolanaFM: https://solana.fm/tx/${result.signature}`);
      console.log(`   Solana Beach: https://solanabeach.io/transaction/${result.signature}`);
      console.log('');
      console.log('✅ Token swap completed successfully using Jupiter aggregator via Helius RPC!');
      
    } else {
      console.error('❌ SWAP EXECUTION FAILED');
      console.error('=' .repeat(60));
      console.error(`   Error: ${result.error || 'Unknown error'}`);
      if (result.details) {
        console.error(`   Details: ${JSON.stringify(result.details, null, 2)}`);
      }
    }

  } catch (error) {
    console.error('💥 FATAL ERROR DURING SWAP EXECUTION');
    console.error('=' .repeat(60));
    console.error(`Error Type: ${error.name}`);
    console.error(`Message: ${error.message}`);
    console.error(`Stack: ${error.stack}`);
    
    if (error.code) {
      console.error(`Error Code: ${error.code}`);
    }
    
    if (error.response) {
      console.error(`Response: ${JSON.stringify(error.response, null, 2)}`);
    }
  }
}

// Execute the swap
console.log('Starting MemeTrader Pro swap execution...');
executeSwap()
  .then(() => {
    console.log('Script execution completed.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Script failed:', error);
    process.exit(1);
  });