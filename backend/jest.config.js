/** @type {import('jest').Config} */
module.exports = {
  // Test environment
  testEnvironment: 'node',
  
  // TypeScript support
  preset: 'ts-jest',
  
  // Root directories
  rootDir: './src',
  testMatch: [
    '**/__tests__/**/*.test.ts',
    '**/__tests__/**/*.spec.ts',
    '**/*.test.ts',
    '**/*.spec.ts'
  ],
  
  // Module paths matching tsconfig.json
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/$1',
    '^@/config/(.*)$': '<rootDir>/config/$1',
    '^@/controllers/(.*)$': '<rootDir>/controllers/$1',
    '^@/middleware/(.*)$': '<rootDir>/middleware/$1',
    '^@/routes/(.*)$': '<rootDir>/routes/$1',
    '^@/services/(.*)$': '<rootDir>/services/$1',
    '^@/types/(.*)$': '<rootDir>/types/$1',
    '^@/utils/(.*)$': '<rootDir>/utils/$1',
    '^@memetrader-pro/shared$': '<rootDir>/../shared/src',
    '^@memetrader-pro/shared/(.*)$': '<rootDir>/../shared/src/$1'
  },
  
  // Setup files
  setupFilesAfterEnv: [
    '<rootDir>/__tests__/setup/jest.setup.ts'
  ],
  globalSetup: '<rootDir>/__tests__/setup/global.setup.ts',
  globalTeardown: '<rootDir>/__tests__/setup/global.teardown.ts',
  
  // Coverage configuration
  collectCoverage: false, // Enable with --coverage flag
  collectCoverageFrom: [
    'services/**/*.ts',
    'routes/**/*.ts',
    'middleware/**/*.ts',
    'utils/**/*.ts',
    '!**/*.d.ts',
    '!**/__tests__/**',
    '!**/node_modules/**',
    '!**/dist/**'
  ],
  coverageDirectory: '<rootDir>/../coverage',
  coverageReporters: ['text', 'lcov', 'html', 'json'],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    },
    // Service-specific thresholds
    'services/tradingService.ts': {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    },
    'services/exitStrategyService.ts': {
      branches: 75,
      functions: 75,
      lines: 75,
      statements: 75
    },
    'services/portfolioService.ts': {
      branches: 75,
      functions: 75,
      lines: 75,
      statements: 75
    }
  },
  
  // Test timeout
  testTimeout: 30000, // 30 seconds for integration tests
  
  // Clear mocks between tests
  clearMocks: true,
  restoreMocks: true,
  resetMocks: true,
  
  // Transform configuration
  transform: {
    '^.+\\.ts$': ['ts-jest', {
      tsconfig: '<rootDir>/../tsconfig.json'
    }]
  },
  
  // Module file extensions
  moduleFileExtensions: ['ts', 'js', 'json'],
  
  // Ignore patterns
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/coverage/'
  ],
  
  // Verbose output for better debugging
  verbose: true,
  
  // Error handling
  errorOnDeprecated: true,
  
  // Test environment variables
  testEnvironmentOptions: {
    NODE_ENV: 'test'
  },
  
  // Globals
  globals: {
    'ts-jest': {
      isolatedModules: true
    }
  },
  
  // Run tests in parallel
  maxWorkers: '50%',
  
  // Cache directory
  cacheDirectory: '<rootDir>/../.jest-cache',
  
  // Reporter configuration for CI/CD
  reporters: [
    'default',
    ['jest-junit', {
      outputDirectory: '<rootDir>/../test-results',
      outputName: 'junit.xml',
      suiteName: 'Backend Tests'
    }]
  ]
}