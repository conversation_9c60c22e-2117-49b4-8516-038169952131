// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl-arm64-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums
enum RiskLevel {
  LOW
  MEDIUM
  HIGH
  EXTREME
}

enum PositionStatus {
  ACTIVE
  CLOSED
  PARTIAL
  PENDING
}

enum StrategyType {
  PRD_COMPLIANT
  AGGRESSIVE
  CONSERVATIVE
  CUSTOM
}

enum TransactionType {
  BUY
  SELL
  SWAP
  TRANSFER
}

enum AlertType {
  TRADE_EXECUTED
  POSITION_UPDATE
  STRATEGY_TRIGGER
  PRICE_ALERT
  SYSTEM_ALERT
  ERROR_ALERT
}

enum AlertPriority {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum PresetType {
  DEFAULT
  VOL
  DEAD
  NUN
  P5
}

enum MEVProtectionLevel {
  NONE
  BASIC
  ADVANCED
  MAXIMUM
}

enum StrategyExecutionState {
  PENDING
  ACTIVE
  TRIGGERED
  COMPLETED
  FAILED
}

// User model
model User {
  id                String              @id @default(cuid())
  email             String              @unique
  walletAddress     String              @unique
  role              String              @default("user")
  isActive          Boolean             @default(true)
  passwordHash      String?
  passwordChangedAt DateTime?

  // User preferences
  preferences       UserPreferences?
  
  // Related models
  positions         Position[]
  transactions      Transaction[]
  strategies        ExitStrategy[]
  customStrategies  CustomStrategy[]
  alerts            Alert[]
  watchlist         Watchlist[]
  portfolios        Portfolio[]

  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@index([email])
  @@index([walletAddress])
}

// User preferences
model UserPreferences {
  id                    String    @id @default(cuid())
  userId                String    @unique
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Trading preferences
  defaultPreset         PresetType @default(DEFAULT)
  defaultSlippage       Float     @default(1.0)
  defaultPriorityFee    Float     @default(0.001)
  riskTolerance         RiskLevel @default(MEDIUM)
  maxPositionSize       Float     @default(5.0)
  
  // Alert preferences
  alertsEnabled         Boolean   @default(true)
  emailAlerts           Boolean   @default(false)
  desktopAlerts         Boolean   @default(true)
  soundAlerts           Boolean   @default(true)
  quietHoursEnabled     Boolean   @default(false)
  quietHoursStart       String?   // "22:00" format
  quietHoursEnd         String?   // "08:00" format

  // UI preferences
  theme                 String    @default("dark")
  currency              String    @default("USD")
  timezone              String    @default("UTC")

  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
}

// Portfolio model
model Portfolio {
  id                String    @id @default(cuid())
  userId            String
  user              User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  totalValue        Decimal   @db.Decimal(20, 9)
  totalValueUsd     Decimal   @db.Decimal(20, 2)
  totalPnl          Decimal   @db.Decimal(20, 9)
  totalPnlUsd       Decimal   @db.Decimal(20, 2)
  totalPnlPercent   Float

  // Risk metrics
  riskScore         Float
  exposurePercent   Float
  maxDrawdown       Float

  // Performance metrics
  winRate           Float     @default(0)
  totalTrades       Int       @default(0)
  profitFactor      Float     @default(0)
  sharpeRatio       Float     @default(0)

  lastUpdated       DateTime  @default(now())
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  @@index([userId])
}

// Position model
model Position {
  id                String         @id @default(cuid())
  userId            String
  user              User           @relation(fields: [userId], references: [id], onDelete: Cascade)

  tokenAddress      String
  tokenSymbol       String
  tokenName         String?
  entryPrice        Decimal        @db.Decimal(20, 9)
  currentPrice      Decimal        @db.Decimal(20, 9)
  quantity          Decimal        @db.Decimal(20, 9)
  entryTimestamp    DateTime
  exitTimestamp     DateTime?

  // Strategy info
  strategyId        String?
  strategy          ExitStrategy?  @relation(fields: [strategyId], references: [id])
  presetUsed        PresetType
  riskLevel         RiskLevel

  // Status and metrics
  status            PositionStatus
  pnl               Decimal        @db.Decimal(20, 9)
  pnlPercent        Float
  pnlUsd            Decimal?       @db.Decimal(20, 2)

  // Market data (cached)
  marketCap         Decimal?       @db.Decimal(20, 2)
  volume24h         Decimal?       @db.Decimal(20, 2)
  change24h         Float?
  liquidity         Decimal?       @db.Decimal(20, 9)

  // Related transactions
  transactions      Transaction[]

  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt

  @@index([userId, status])
  @@index([tokenAddress])
  @@index([strategyId])
}

// Exit strategy model
model ExitStrategy {
  id                String                 @id @default(cuid())
  userId            String
  user              User                   @relation(fields: [userId], references: [id], onDelete: Cascade)

  positionId        String?
  type              StrategyType
  customName        String?
  
  // Strategy configuration (JSON fields)
  stopLoss          Json                   // StopLossConfig
  profitTargets     Json                   // ProfitTarget[]
  moonBag           Json?                  // MoonBagConfig
  
  // Execution state
  executionState    StrategyExecutionState @default(PENDING)
  locked            Boolean                @default(false)
  lastTriggered     DateTime?
  
  // Performance tracking
  totalTriggers     Int                   @default(0)
  totalPnl          Decimal               @db.Decimal(20, 9) @default(0)
  successRate       Float                 @default(0)

  // Related models
  positions         Position[]
  
  createdAt         DateTime              @default(now())
  updatedAt         DateTime              @updatedAt

  @@index([userId, type])
  @@index([executionState])
}

// Custom strategy templates
model CustomStrategy {
  id                String    @id @default(cuid())
  userId            String
  user              User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  name              String
  description       String?
  config            Json      // Complete strategy configuration
  prdCompliant      Boolean   @default(false)
  
  // Usage statistics
  timesUsed         Int       @default(0)
  lastUsed          DateTime?
  averagePerformance Float    @default(0)

  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  @@index([userId])
  @@index([prdCompliant])
}

// Transaction model - Enhanced for comprehensive tracking
model Transaction {
  id                String          @id @default(cuid())
  userId            String
  user              User            @relation(fields: [userId], references: [id], onDelete: Cascade)

  positionId        String?
  position          Position?       @relation(fields: [positionId], references: [id])

  // Transaction details
  hash              String          @unique
  type              TransactionType
  status            String          @default("CONFIRMED") // PENDING, CONFIRMED, FAILED, CANCELLED
  
  // Blockchain details
  blockNumber       BigInt?
  blockTime         Int?
  slot              BigInt?
  confirmations     Int             @default(0)
  
  // Token information  
  tokenIn           String
  tokenOut          String
  tokenInSymbol     String
  tokenOutSymbol    String
  tokenInName       String?
  tokenOutName      String?
  tokenInDecimals   Int?
  tokenOutDecimals  Int?
  
  // Amounts and pricing
  amountIn          Decimal         @db.Decimal(20, 9)
  amountOut         Decimal         @db.Decimal(20, 9)
  amountInRaw       String          // Raw amounts to avoid precision loss
  amountOutRaw      String
  price             Decimal         @db.Decimal(20, 9)
  priceUsd          Decimal?        @db.Decimal(20, 2)
  priceImpact       Float?

  // Fees (JSON field for complex fee structure)
  fees              Json            // TransactionFees
  priorityFee       Float?
  networkFee        Float?
  totalCost         Float           @default(0)
  
  // Strategy context
  strategyId        String?
  presetUsed        PresetType
  slippageUsed      Float
  slippageActual    Float?
  mevProtected      Boolean         @default(false)
  mevRiskLevel      String?

  // Execution metrics
  executionTime     Int?            // milliseconds
  confirmationTime  Int?            // milliseconds to confirm
  retryCount        Int             @default(0)
  errorReason       String?
  
  // Market conditions (JSON for flexibility)
  marketConditions  Json?           // NetworkConditions at time of trade
  
  // Advanced analytics (JSON for complex data)
  analytics         Json?           // Performance and routing analytics

  // Timestamps
  timestamp         DateTime        @default(now())
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  confirmedAt       DateTime?
  archivedAt        DateTime?

  @@index([userId, timestamp])
  @@index([hash])
  @@index([type, status])
  @@index([tokenIn, tokenOut])
  @@index([status, createdAt])
  @@index([mevProtected, createdAt])
}

// Alert model
model Alert {
  id                String        @id @default(cuid())
  userId            String
  user              User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  type              AlertType
  priority          AlertPriority
  title             String
  message           String
  metadata          Json?         // Additional context data

  // Status
  read              Boolean       @default(false)
  archived          Boolean       @default(false)
  actionable        Boolean       @default(false)
  dismissed         Boolean       @default(false)

  // Delivery tracking
  deliveredAt       DateTime?
  channels          Json?         // Which channels were used
  retryCount        Int           @default(0)

  // Related entities
  positionId        String?
  transactionId     String?
  strategyId        String?

  expiresAt         DateTime?
  timestamp         DateTime      @default(now())
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  @@index([userId, read, priority])
  @@index([timestamp])
  @@index([type, priority])
}

// Watchlist model
model Watchlist {
  id                String    @id @default(cuid())
  userId            String
  user              User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  name              String    @default("Default")
  description       String?
  tokens            Json      // Array of token addresses with metadata
  
  // Alert configuration
  priceAlerts       Json?     // Price alert configurations
  volumeAlerts      Json?     // Volume alert configurations

  isDefault         Boolean   @default(false)
  isPublic          Boolean   @default(false)

  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  @@index([userId])
  @@unique([userId, name])
}

// Trading presets (system-wide configurations)
model TradingPreset {
  id                String            @id @default(cuid())
  name              PresetType        @unique
  
  // Fee configuration
  priorityFee       Decimal           @db.Decimal(10, 9)
  slippageLimit     Float
  mevProtectionLevel MEVProtectionLevel
  brideAmount       Decimal?          @db.Decimal(10, 9)
  
  // Settings
  buySettings       Json              // TradeSettings
  sellSettings      Json              // TradeSettings
  
  locked            Boolean           @default(true)
  systemDefault     Boolean           @default(false)
  
  // Performance tracking
  totalUsage        Int               @default(0)
  averagePerformance Float            @default(0)
  
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  @@index([name])
}

// Price history for analytics
model PriceHistory {
  id                String    @id @default(cuid())
  tokenAddress      String
  price             Decimal   @db.Decimal(20, 9)
  priceUsd          Decimal   @db.Decimal(20, 2)
  volume24h         Decimal?  @db.Decimal(20, 2)
  marketCap         Decimal?  @db.Decimal(20, 2)
  change24h         Float?
  
  timestamp         DateTime  @default(now())

  @@index([tokenAddress, timestamp])
  @@unique([tokenAddress, timestamp])
}

// System configuration
model SystemConfig {
  id                String    @id @default(cuid())
  key               String    @unique
  value             Json
  description       String?
  category          String    @default("general")
  
  // Metadata
  isPublic          Boolean   @default(false)
  lastModifiedBy    String?
  
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  @@index([category])
}