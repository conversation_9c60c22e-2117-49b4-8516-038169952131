-- =============================================================================
-- MEMETRADER PRO - DEVELOPMENT SEED DATA
-- =============================================================================
-- This script is executed in development mode to populate the database with sample data

-- Insert sample system configurations for development
INSERT INTO "SystemConfig" (id, key, value, description, category, "isPublic") 
VALUES 
  (gen_random_uuid(), 'dev_mode', 'true', 'Development mode flag', 'system', false),
  (gen_random_uuid(), 'api_rate_limit', '1000', 'API rate limit per minute for development', 'api', false),
  (gen_random_uuid(), 'debug_logging', 'true', 'Enable debug logging', 'system', false),
  (gen_random_uuid(), 'mock_trading', 'true', 'Enable mock trading for development', 'trading', false)
ON CONFLICT (key) DO UPDATE SET value = EXCLUDED.value;

-- Insert development trading presets
INSERT INTO "TradingPreset" (id, name, "priorityFee", "slippageLimit", "mevProtectionLevel", "buySettings", "sellSettings", locked, "systemDefault")
VALUES 
  (gen_random_uuid(), 'NUN', 0.002, 1.5, 'BASIC',
   '{"autoConfirm": false, "maxRetries": 2, "simulateOnly": true}',
   '{"autoConfirm": false, "maxRetries": 2, "simulateOnly": true}',
   false, false),
  (gen_random_uuid(), 'P5', 0.0005, 0.5, 'NONE',
   '{"autoConfirm": false, "maxRetries": 1, "simulateOnly": true}',
   '{"autoConfirm": false, "maxRetries": 1, "simulateOnly": true}',
   false, false)
ON CONFLICT (name) DO UPDATE SET 
  "priorityFee" = EXCLUDED."priorityFee",
  "slippageLimit" = EXCLUDED."slippageLimit",
  "mevProtectionLevel" = EXCLUDED."mevProtectionLevel",
  "buySettings" = EXCLUDED."buySettings",
  "sellSettings" = EXCLUDED."sellSettings";

-- Create a test user for development
INSERT INTO "User" (id, email, "walletAddress", role, "isActive")
VALUES 
  ('dev-test-user-001', '<EMAIL>', '********************************', 'admin', true)
ON CONFLICT (email) DO NOTHING;

-- Insert user preferences for the test user
INSERT INTO "UserPreferences" (id, "userId", "defaultPreset", "defaultSlippage", "riskTolerance", "alertsEnabled", theme)
VALUES 
  (gen_random_uuid(), 'dev-test-user-001', 'DEFAULT', 1.0, 'MEDIUM', true, 'dark')
ON CONFLICT ("userId") DO UPDATE SET
  "defaultPreset" = EXCLUDED."defaultPreset",
  "defaultSlippage" = EXCLUDED."defaultSlippage";

-- Insert sample portfolio data
INSERT INTO "Portfolio" (id, "userId", "totalValue", "totalValueUsd", "totalPnl", "totalPnlUsd", "totalPnlPercent", "riskScore", "exposurePercent", "maxDrawdown")
VALUES 
  (gen_random_uuid(), 'dev-test-user-001', 1000.0, 50000.0, 0.0, 0.0, 0.0, 0.5, 0.0, 0.0)
ON CONFLICT DO NOTHING;

-- Insert sample watchlist
INSERT INTO "Watchlist" (id, "userId", name, description, tokens, "isDefault")
VALUES 
  (gen_random_uuid(), 'dev-test-user-001', 'My Watchlist', 'Development test watchlist', 
   '[{"address":"So111111111********************************","symbol":"SOL","name":"Solana"}]', 
   true)
ON CONFLICT ("userId", name) DO NOTHING;

-- Development comment
COMMENT ON TABLE "User" IS 'Development database seeded with test data';