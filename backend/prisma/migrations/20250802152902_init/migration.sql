/*
  Warnings:

  - Added the required column `amountInRaw` to the `Transaction` table without a default value. This is not possible if the table is not empty.
  - Added the required column `amountOutRaw` to the `Transaction` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "Transaction_type_idx";

-- AlterTable
ALTER TABLE "Transaction" ADD COLUMN     "amountInRaw" TEXT NOT NULL,
ADD COLUMN     "amountOutRaw" TEXT NOT NULL,
ADD COLUMN     "analytics" JSONB,
ADD COLUMN     "archivedAt" TIMESTAMP(3),
ADD COLUMN     "blockTime" INTEGER,
ADD COLUMN     "confirmationTime" INTEGER,
ADD COLUMN     "confirmedAt" TIMESTAMP(3),
ADD COLUMN     "errorReason" TEXT,
ADD COLUMN     "marketConditions" JSONB,
ADD COLUMN     "mevRiskLevel" TEXT,
ADD COLUMN     "networkFee" DOUBLE PRECISION,
ADD COLUMN     "priceImpact" DOUBLE PRECISION,
ADD COLUMN     "priorityFee" DOUBLE PRECISION,
ADD COLUMN     "retryCount" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "slippageActual" DOUBLE PRECISION,
ADD COLUMN     "slot" BIGINT,
ADD COLUMN     "status" TEXT NOT NULL DEFAULT 'CONFIRMED',
ADD COLUMN     "tokenInDecimals" INTEGER,
ADD COLUMN     "tokenInName" TEXT,
ADD COLUMN     "tokenOutDecimals" INTEGER,
ADD COLUMN     "tokenOutName" TEXT,
ADD COLUMN     "totalCost" DOUBLE PRECISION NOT NULL DEFAULT 0;

-- CreateIndex
CREATE INDEX "Transaction_type_status_idx" ON "Transaction"("type", "status");

-- CreateIndex
CREATE INDEX "Transaction_status_createdAt_idx" ON "Transaction"("status", "createdAt");

-- CreateIndex
CREATE INDEX "Transaction_mevProtected_createdAt_idx" ON "Transaction"("mevProtected", "createdAt");
