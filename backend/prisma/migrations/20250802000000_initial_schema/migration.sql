-- CreateEnum
CREATE TYPE "RiskLevel" AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'EXTREME');

-- CreateEnum
CREATE TYPE "PositionStatus" AS ENUM ('ACTIVE', 'CLOSED', 'PARTIAL', 'PENDING');

-- Create<PERSON><PERSON>
CREATE TYPE "StrategyType" AS ENUM ('PRD_COMPLIANT', 'AGGRESSIVE', 'CONSERVATIVE', 'CUSTOM');

-- CreateEnum
CREATE TYPE "TransactionType" AS ENUM ('BUY', 'SELL', 'SWAP', 'TRANSFER');

-- CreateEnum
CREATE TYPE "AlertType" AS ENUM ('TRADE_EXECUTED', 'POSITION_UPDATE', 'STRATEGY_TRIGGER', 'PRICE_ALERT', 'SYSTEM_ALERT', 'ERROR_ALERT');

-- CreateEnum
CREATE TYPE "AlertPriority" AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL');

-- <PERSON>reate<PERSON><PERSON>
CREATE TYPE "PresetType" AS ENUM ('DEFAULT', 'VOL', 'DEAD', 'NUN', 'P5');

-- CreateEnum
CREATE TYPE "MEVProtectionLevel" AS ENUM ('NONE', 'BASIC', 'ADVANCED', 'MAXIMUM');

-- CreateEnum
CREATE TYPE "StrategyExecutionState" AS ENUM ('PENDING', 'ACTIVE', 'TRIGGERED', 'COMPLETED', 'FAILED');

-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "walletAddress" TEXT NOT NULL,
    "role" TEXT NOT NULL DEFAULT 'user',
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "passwordHash" TEXT,
    "passwordChangedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserPreferences" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "defaultPreset" "PresetType" NOT NULL DEFAULT 'DEFAULT',
    "defaultSlippage" DOUBLE PRECISION NOT NULL DEFAULT 1.0,
    "defaultPriorityFee" DOUBLE PRECISION NOT NULL DEFAULT 0.001,
    "riskTolerance" "RiskLevel" NOT NULL DEFAULT 'MEDIUM',
    "maxPositionSize" DOUBLE PRECISION NOT NULL DEFAULT 5.0,
    "alertsEnabled" BOOLEAN NOT NULL DEFAULT true,
    "emailAlerts" BOOLEAN NOT NULL DEFAULT false,
    "desktopAlerts" BOOLEAN NOT NULL DEFAULT true,
    "soundAlerts" BOOLEAN NOT NULL DEFAULT true,
    "quietHoursEnabled" BOOLEAN NOT NULL DEFAULT false,
    "quietHoursStart" TEXT,
    "quietHoursEnd" TEXT,
    "theme" TEXT NOT NULL DEFAULT 'dark',
    "currency" TEXT NOT NULL DEFAULT 'USD',
    "timezone" TEXT NOT NULL DEFAULT 'UTC',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserPreferences_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Portfolio" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "totalValue" DECIMAL(20,9) NOT NULL,
    "totalValueUsd" DECIMAL(20,2) NOT NULL,
    "totalPnl" DECIMAL(20,9) NOT NULL,
    "totalPnlUsd" DECIMAL(20,2) NOT NULL,
    "totalPnlPercent" DOUBLE PRECISION NOT NULL,
    "riskScore" DOUBLE PRECISION NOT NULL,
    "exposurePercent" DOUBLE PRECISION NOT NULL,
    "maxDrawdown" DOUBLE PRECISION NOT NULL,
    "winRate" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "totalTrades" INTEGER NOT NULL DEFAULT 0,
    "profitFactor" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "sharpeRatio" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "lastUpdated" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Portfolio_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Position" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "tokenAddress" TEXT NOT NULL,
    "tokenSymbol" TEXT NOT NULL,
    "tokenName" TEXT,
    "entryPrice" DECIMAL(20,9) NOT NULL,
    "currentPrice" DECIMAL(20,9) NOT NULL,
    "quantity" DECIMAL(20,9) NOT NULL,
    "entryTimestamp" TIMESTAMP(3) NOT NULL,
    "exitTimestamp" TIMESTAMP(3),
    "strategyId" TEXT,
    "presetUsed" "PresetType" NOT NULL,
    "riskLevel" "RiskLevel" NOT NULL,
    "status" "PositionStatus" NOT NULL,
    "pnl" DECIMAL(20,9) NOT NULL,
    "pnlPercent" DOUBLE PRECISION NOT NULL,
    "pnlUsd" DECIMAL(20,2),
    "marketCap" DECIMAL(20,2),
    "volume24h" DECIMAL(20,2),
    "change24h" DOUBLE PRECISION,
    "liquidity" DECIMAL(20,9),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Position_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ExitStrategy" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "positionId" TEXT,
    "type" "StrategyType" NOT NULL,
    "customName" TEXT,
    "stopLoss" JSONB NOT NULL,
    "profitTargets" JSONB NOT NULL,
    "moonBag" JSONB,
    "executionState" "StrategyExecutionState" NOT NULL DEFAULT 'PENDING',
    "locked" BOOLEAN NOT NULL DEFAULT false,
    "lastTriggered" TIMESTAMP(3),
    "totalTriggers" INTEGER NOT NULL DEFAULT 0,
    "totalPnl" DECIMAL(20,9) NOT NULL DEFAULT 0,
    "successRate" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ExitStrategy_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CustomStrategy" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "config" JSONB NOT NULL,
    "prdCompliant" BOOLEAN NOT NULL DEFAULT false,
    "timesUsed" INTEGER NOT NULL DEFAULT 0,
    "lastUsed" TIMESTAMP(3),
    "averagePerformance" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CustomStrategy_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Transaction" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "positionId" TEXT,
    "hash" TEXT NOT NULL,
    "type" "TransactionType" NOT NULL,
    "tokenIn" TEXT NOT NULL,
    "tokenOut" TEXT NOT NULL,
    "tokenInSymbol" TEXT NOT NULL,
    "tokenOutSymbol" TEXT NOT NULL,
    "amountIn" DECIMAL(20,9) NOT NULL,
    "amountOut" DECIMAL(20,9) NOT NULL,
    "price" DECIMAL(20,9) NOT NULL,
    "priceUsd" DECIMAL(20,2),
    "fees" JSONB NOT NULL,
    "strategyId" TEXT,
    "presetUsed" "PresetType" NOT NULL,
    "mevProtected" BOOLEAN NOT NULL DEFAULT false,
    "slippageUsed" DOUBLE PRECISION NOT NULL,
    "executionTime" INTEGER,
    "blockNumber" BIGINT,
    "confirmations" INTEGER NOT NULL DEFAULT 0,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Transaction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Alert" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" "AlertType" NOT NULL,
    "priority" "AlertPriority" NOT NULL,
    "title" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "metadata" JSONB,
    "read" BOOLEAN NOT NULL DEFAULT false,
    "archived" BOOLEAN NOT NULL DEFAULT false,
    "actionable" BOOLEAN NOT NULL DEFAULT false,
    "dismissed" BOOLEAN NOT NULL DEFAULT false,
    "deliveredAt" TIMESTAMP(3),
    "channels" JSONB,
    "retryCount" INTEGER NOT NULL DEFAULT 0,
    "positionId" TEXT,
    "transactionId" TEXT,
    "strategyId" TEXT,
    "expiresAt" TIMESTAMP(3),
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Alert_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Watchlist" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "name" TEXT NOT NULL DEFAULT 'Default',
    "description" TEXT,
    "tokens" JSONB NOT NULL,
    "priceAlerts" JSONB,
    "volumeAlerts" JSONB,
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "isPublic" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Watchlist_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TradingPreset" (
    "id" TEXT NOT NULL,
    "name" "PresetType" NOT NULL,
    "priorityFee" DECIMAL(10,9) NOT NULL,
    "slippageLimit" DOUBLE PRECISION NOT NULL,
    "mevProtectionLevel" "MEVProtectionLevel" NOT NULL,
    "brideAmount" DECIMAL(10,9),
    "buySettings" JSONB NOT NULL,
    "sellSettings" JSONB NOT NULL,
    "locked" BOOLEAN NOT NULL DEFAULT true,
    "systemDefault" BOOLEAN NOT NULL DEFAULT false,
    "totalUsage" INTEGER NOT NULL DEFAULT 0,
    "averagePerformance" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TradingPreset_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PriceHistory" (
    "id" TEXT NOT NULL,
    "tokenAddress" TEXT NOT NULL,
    "price" DECIMAL(20,9) NOT NULL,
    "priceUsd" DECIMAL(20,2) NOT NULL,
    "volume24h" DECIMAL(20,2),
    "marketCap" DECIMAL(20,2),
    "change24h" DOUBLE PRECISION,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PriceHistory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SystemConfig" (
    "id" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "value" JSONB NOT NULL,
    "description" TEXT,
    "category" TEXT NOT NULL DEFAULT 'general',
    "isPublic" BOOLEAN NOT NULL DEFAULT false,
    "lastModifiedBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SystemConfig_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");

-- CreateIndex
CREATE UNIQUE INDEX "User_walletAddress_key" ON "User"("walletAddress");

-- CreateIndex
CREATE INDEX "User_email_idx" ON "User"("email");

-- CreateIndex
CREATE INDEX "User_walletAddress_idx" ON "User"("walletAddress");

-- CreateIndex
CREATE UNIQUE INDEX "UserPreferences_userId_key" ON "UserPreferences"("userId");

-- CreateIndex
CREATE INDEX "Portfolio_userId_idx" ON "Portfolio"("userId");

-- CreateIndex
CREATE INDEX "Position_userId_status_idx" ON "Position"("userId", "status");

-- CreateIndex
CREATE INDEX "Position_tokenAddress_idx" ON "Position"("tokenAddress");

-- CreateIndex
CREATE INDEX "Position_strategyId_idx" ON "Position"("strategyId");

-- CreateIndex
CREATE INDEX "ExitStrategy_userId_type_idx" ON "ExitStrategy"("userId", "type");

-- CreateIndex
CREATE INDEX "ExitStrategy_executionState_idx" ON "ExitStrategy"("executionState");

-- CreateIndex
CREATE INDEX "CustomStrategy_userId_idx" ON "CustomStrategy"("userId");

-- CreateIndex
CREATE INDEX "CustomStrategy_prdCompliant_idx" ON "CustomStrategy"("prdCompliant");

-- CreateIndex
CREATE UNIQUE INDEX "Transaction_hash_key" ON "Transaction"("hash");

-- CreateIndex
CREATE INDEX "Transaction_userId_timestamp_idx" ON "Transaction"("userId", "timestamp");

-- CreateIndex
CREATE INDEX "Transaction_hash_idx" ON "Transaction"("hash");

-- CreateIndex
CREATE INDEX "Transaction_type_idx" ON "Transaction"("type");

-- CreateIndex
CREATE INDEX "Transaction_tokenIn_tokenOut_idx" ON "Transaction"("tokenIn", "tokenOut");

-- CreateIndex
CREATE INDEX "Alert_userId_read_priority_idx" ON "Alert"("userId", "read", "priority");

-- CreateIndex
CREATE INDEX "Alert_timestamp_idx" ON "Alert"("timestamp");

-- CreateIndex
CREATE INDEX "Alert_type_priority_idx" ON "Alert"("type", "priority");

-- CreateIndex
CREATE INDEX "Watchlist_userId_idx" ON "Watchlist"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "Watchlist_userId_name_key" ON "Watchlist"("userId", "name");

-- CreateIndex
CREATE UNIQUE INDEX "TradingPreset_name_key" ON "TradingPreset"("name");

-- CreateIndex
CREATE INDEX "TradingPreset_name_idx" ON "TradingPreset"("name");

-- CreateIndex
CREATE INDEX "PriceHistory_tokenAddress_timestamp_idx" ON "PriceHistory"("tokenAddress", "timestamp");

-- CreateIndex
CREATE UNIQUE INDEX "PriceHistory_tokenAddress_timestamp_key" ON "PriceHistory"("tokenAddress", "timestamp");

-- CreateIndex
CREATE UNIQUE INDEX "SystemConfig_key_key" ON "SystemConfig"("key");

-- CreateIndex
CREATE INDEX "SystemConfig_category_idx" ON "SystemConfig"("category");

-- AddForeignKey
ALTER TABLE "UserPreferences" ADD CONSTRAINT "UserPreferences_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Portfolio" ADD CONSTRAINT "Portfolio_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Position" ADD CONSTRAINT "Position_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Position" ADD CONSTRAINT "Position_strategyId_fkey" FOREIGN KEY ("strategyId") REFERENCES "ExitStrategy"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ExitStrategy" ADD CONSTRAINT "ExitStrategy_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CustomStrategy" ADD CONSTRAINT "CustomStrategy_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_positionId_fkey" FOREIGN KEY ("positionId") REFERENCES "Position"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Alert" ADD CONSTRAINT "Alert_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Watchlist" ADD CONSTRAINT "Watchlist_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;