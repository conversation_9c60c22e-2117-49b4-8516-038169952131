-- =============================================================================
-- MEMETRADER PRO - DATABASE INITIALIZATION SCRIPT
-- =============================================================================
-- This script is executed when the PostgreSQL container starts for the first time
-- It sets up the database with proper permissions and extensions only
-- 
-- NOTE: Table creation is handled by Prisma migrations
-- NOTE: Data seeding is handled by the TypeScript seed script

-- Create extensions if they don't exist
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- Create database user if it doesn't exist (for production environments)
DO $$
BEGIN
   IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'memetrader_user') THEN
      CREATE ROLE memetrader_user WITH LOGIN PASSWORD 'memetrader_pass';
   END IF;
END
$$;

-- Grant necessary permissions
GRANT CONNECT ON DATABASE memetrader TO memetrader_user;
GRANT USAGE ON SCHEMA public TO memetrader_user;
GRANT CREATE ON SCHEMA public TO memetrader_user;

-- Create a function to update the updated_at timestamp
-- This function will be used by triggers on tables with updated_at columns
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create additional performance optimization functions
CREATE OR REPLACE FUNCTION calculate_pnl_percentage(entry_price DECIMAL, current_price DECIMAL)
RETURNS DOUBLE PRECISION AS $$
BEGIN
  IF entry_price = 0 THEN
    RETURN 0;
  END IF;
  RETURN ((current_price - entry_price) / entry_price * 100)::DOUBLE PRECISION;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Create function for calculating position age in hours
CREATE OR REPLACE FUNCTION calculate_position_age_hours(entry_timestamp TIMESTAMP)
RETURNS DOUBLE PRECISION AS $$
BEGIN
  RETURN EXTRACT(EPOCH FROM (NOW() - entry_timestamp)) / 3600;
END;
$$ LANGUAGE plpgsql STABLE;

-- Performance optimization settings
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET track_activity_query_size = 2048;
ALTER SYSTEM SET log_statement = 'all';
ALTER SYSTEM SET log_duration = on;
ALTER SYSTEM SET log_min_duration_statement = 1000;

-- Database metadata
COMMENT ON DATABASE memetrader IS 'MemeTrader Pro - Solana Meme Coin Trading Platform Database';

-- Log initialization completion
DO $$
BEGIN
  RAISE NOTICE 'MemeTrader Pro database initialization completed successfully';
  RAISE NOTICE 'Extensions created: uuid-ossp, pgcrypto, btree_gin';
  RAISE NOTICE 'Helper functions created: trigger_set_timestamp, calculate_pnl_percentage, calculate_position_age_hours';
  RAISE NOTICE 'Next steps: Run Prisma migrations and seed data';
END
$$;