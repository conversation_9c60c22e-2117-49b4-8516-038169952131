-- =============================================================================
-- MEMETRADER PRO - DATABASE INITIALIZATION SCRIPT
-- =============================================================================
-- This script is executed when the PostgreSQL container starts for the first time
-- It sets up the database with proper permissions and initial configuration

-- Create extensions if they don't exist
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create database user if it doesn't exist (for production environments)
DO $$
BEGIN
   IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'memetrader_user') THEN
      CREATE ROLE memetrader_user WITH LOGIN PASSWORD 'memetrader_pass';
   END IF;
END
$$;

-- Grant necessary permissions
GRANT CONNECT ON DATABASE memetrader TO memetrader_user;
GRANT USAGE ON SCHEMA public TO memetrader_user;
GRANT CREATE ON SCHEMA public TO memetrader_user;

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Initial configuration settings
INSERT INTO "SystemConfig" (id, key, value, description, category, "isPublic") 
VALUES 
  (gen_random_uuid(), 'app_version', '"1.0.0"', 'Application version', 'system', true),
  (gen_random_uuid(), 'maintenance_mode', 'false', 'Maintenance mode flag', 'system', false),
  (gen_random_uuid(), 'max_positions_per_user', '50', 'Maximum positions per user', 'trading', false),
  (gen_random_uuid(), 'default_slippage_tolerance', '1.0', 'Default slippage tolerance percentage', 'trading', true)
ON CONFLICT (key) DO NOTHING;

-- Insert default trading presets if they don't exist
INSERT INTO "TradingPreset" (id, name, "priorityFee", "slippageLimit", "mevProtectionLevel", "buySettings", "sellSettings", locked, "systemDefault")
VALUES 
  (gen_random_uuid(), 'DEFAULT', 0.001, 1.0, 'BASIC', 
   '{"autoConfirm": false, "maxRetries": 3}', 
   '{"autoConfirm": false, "maxRetries": 3}', 
   true, true),
  (gen_random_uuid(), 'VOL', 0.005, 2.0, 'ADVANCED',
   '{"autoConfirm": true, "maxRetries": 5}',
   '{"autoConfirm": true, "maxRetries": 5}',
   true, false),
  (gen_random_uuid(), 'DEAD', 0.01, 5.0, 'MAXIMUM',
   '{"autoConfirm": true, "maxRetries": 10}',
   '{"autoConfirm": true, "maxRetries": 10}',
   true, false)
ON CONFLICT (name) DO NOTHING;

-- Create indexes for better performance (these will be created by Prisma migrations)
-- But we can create some additional performance indexes here

-- Performance optimization comment
COMMENT ON DATABASE memetrader IS 'MemeTrader Pro - Solana Meme Coin Trading Platform Database';