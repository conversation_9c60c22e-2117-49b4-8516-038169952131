
> @memetrader-pro/backend@1.0.0 dev
> tsx watch src/index.ts

2025-08-02 21:01:07 [[32minfo[39m]: [32mInitialized 4 RPC endpoints[39m
{
  "endpoints": [
    {
      "id": "helius-mainnet-primary",
      "priority": 1,
      "maxRPS": 100
    },
    {
      "id": "helius-mainnet-secondary",
      "priority": 2,
      "maxRPS": 80
    },
    {
      "id": "solana-mainnet-public",
      "priority": 10,
      "maxRPS": 20
    },
    {
      "id": "quicknode-mainnet",
      "priority": 11,
      "maxRPS": 10
    }
  ]
}
2025-08-02 21:01:07 [[32minfo[39m]: [32mTrading wallet initialized[39m
{
  "publicKey": "968aqNdZ5ZRLByseFo18YiSHywzqXj3S9z56Vwr62CGT"
}
2025-08-02 21:01:07 [[32minfo[39m]: [32mEmail configuration not found - email notifications disabled[39m
{
  "service": "notification"
}
2025-08-02 21:01:08 [[31merror[39m]: [31mFailed to load active strategies: Database not initialized. Call initialize() first.[39m
{
  "stack": "Error: Database not initialized. Call initialize() first.\n    at DatabaseServiceClass.get client (/Users/<USER>/dev/github/agmentcode/backend/src/services/database.ts:96:13)\n    at ExitStrategyService.loadActiveStrategies (/Users/<USER>/dev/github/agmentcode/backend/src/services/exitStrategyService.ts:1159:50)\n    at ExitStrategyService.initializeService (/Users/<USER>/dev/github/agmentcode/backend/src/services/exitStrategyService.ts:1144:10)\n    at new ExitStrategyService (/Users/<USER>/dev/github/agmentcode/backend/src/services/exitStrategyService.ts:268:10)\n    at Function.getInstance (/Users/<USER>/dev/github/agmentcode/backend/src/services/exitStrategyService.ts:273:38)\n    at <anonymous> (/Users/<USER>/dev/github/agmentcode/backend/src/services/exitStrategyService.ts:1294:57)\n    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/backend/src/services/exitStrategyService.ts:1294:69)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object.transformer (/Users/<USER>/dev/github/agmentcode/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)"
}
2025-08-02 21:01:08 [[32minfo[39m]: [32m🚀 Starting MemeTrader Pro Backend...[39m
2025-08-02 21:01:08 [[32minfo[39m]: [32m🔍 Running environment validation...[39m
2025-08-02 21:01:08 [[32minfo[39m]: [32m🔍 Starting comprehensive environment validation...[39m
(node:20321) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
2025-08-02 21:01:08 [[31merror[39m]: [31mUnhandled Promise Rejection[39m
{
  "error": {
    "name": "Error",
    "message": "Error: Redis subscriber not initialized. Call initialize() first.",
    "stack": "Error: Error: Redis subscriber not initialized. Call initialize() first.\n    at process.<anonymous> (/Users/<USER>/dev/github/agmentcode/backend/src/middleware/errorHandler.ts:204:43)\n    at process.emit (node:events:524:28)\n    at process.emit (/Users/<USER>/dev/github/agmentcode/node_modules/tsx/dist/suppress-warnings.cjs:1:472)\n    at emitUnhandledRejection (node:internal/process/promises:252:13)\n    at throwUnhandledRejectionsMode (node:internal/process/promises:388:19)\n    at processPromiseRejections (node:internal/process/promises:475:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:106:32)"
  },
  "promise": {}
}
9:02:12 PM [tsx] change in ./../node_modules/.prisma/client/index.js Rerunning...
c2025-08-02 21:02:12 [[32minfo[39m]: [32mInitialized 4 RPC endpoints[39m
{
  "endpoints": [
    {
      "id": "helius-mainnet-primary",
      "priority": 1,
      "maxRPS": 100
    },
    {
      "id": "helius-mainnet-secondary",
      "priority": 2,
      "maxRPS": 80
    },
    {
      "id": "solana-mainnet-public",
      "priority": 10,
      "maxRPS": 20
    },
    {
      "id": "quicknode-mainnet",
      "priority": 11,
      "maxRPS": 10
    }
  ]
}
2025-08-02 21:02:12 [[32minfo[39m]: [32mTrading wallet initialized[39m
{
  "publicKey": "968aqNdZ5ZRLByseFo18YiSHywzqXj3S9z56Vwr62CGT"
}
2025-08-02 21:02:13 [[32minfo[39m]: [32mEmail configuration not found - email notifications disabled[39m
{
  "service": "notification"
}
2025-08-02 21:02:13 [[31merror[39m]: [31mFailed to load active strategies: Database not initialized. Call initialize() first.[39m
{
  "stack": "Error: Database not initialized. Call initialize() first.\n    at DatabaseServiceClass.get client (/Users/<USER>/dev/github/agmentcode/backend/src/services/database.ts:96:13)\n    at ExitStrategyService.loadActiveStrategies (/Users/<USER>/dev/github/agmentcode/backend/src/services/exitStrategyService.ts:1159:50)\n    at ExitStrategyService.initializeService (/Users/<USER>/dev/github/agmentcode/backend/src/services/exitStrategyService.ts:1144:10)\n    at new ExitStrategyService (/Users/<USER>/dev/github/agmentcode/backend/src/services/exitStrategyService.ts:268:10)\n    at Function.getInstance (/Users/<USER>/dev/github/agmentcode/backend/src/services/exitStrategyService.ts:273:38)\n    at <anonymous> (/Users/<USER>/dev/github/agmentcode/backend/src/services/exitStrategyService.ts:1294:57)\n    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/backend/src/services/exitStrategyService.ts:1294:69)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object.transformer (/Users/<USER>/dev/github/agmentcode/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)"
}
2025-08-02 21:02:13 [[32minfo[39m]: [32m🚀 Starting MemeTrader Pro Backend...[39m
2025-08-02 21:02:13 [[32minfo[39m]: [32m🔍 Running environment validation...[39m
2025-08-02 21:02:13 [[32minfo[39m]: [32m🔍 Starting comprehensive environment validation...[39m
(node:20729) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
2025-08-02 21:02:13 [[31merror[39m]: [31mUnhandled Promise Rejection[39m
{
  "error": {
    "name": "Error",
    "message": "Error: Redis subscriber not initialized. Call initialize() first.",
    "stack": "Error: Error: Redis subscriber not initialized. Call initialize() first.\n    at process.<anonymous> (/Users/<USER>/dev/github/agmentcode/backend/src/middleware/errorHandler.ts:204:43)\n    at process.emit (node:events:524:28)\n    at process.emit (/Users/<USER>/dev/github/agmentcode/node_modules/tsx/dist/suppress-warnings.cjs:1:472)\n    at emitUnhandledRejection (node:internal/process/promises:252:13)\n    at throwUnhandledRejectionsMode (node:internal/process/promises:388:19)\n    at processPromiseRejections (node:internal/process/promises:475:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:106:32)"
  },
  "promise": {}
}
9:41:06 PM [tsx] change in ./../node_modules/.prisma/client/default.js Rerunning...
c2025-08-02 21:41:08 [[32minfo[39m]: [32mInitialized 4 RPC endpoints[39m
{
  "endpoints": [
    {
      "id": "helius-mainnet-primary",
      "priority": 1,
      "maxRPS": 100
    },
    {
      "id": "helius-mainnet-secondary",
      "priority": 2,
      "maxRPS": 80
    },
    {
      "id": "solana-mainnet-public",
      "priority": 10,
      "maxRPS": 20
    },
    {
      "id": "quicknode-mainnet",
      "priority": 11,
      "maxRPS": 10
    }
  ]
}
2025-08-02 21:41:08 [[32minfo[39m]: [32mTrading wallet initialized[39m
{
  "publicKey": "968aqNdZ5ZRLByseFo18YiSHywzqXj3S9z56Vwr62CGT"
}
2025-08-02 21:41:09 [[32minfo[39m]: [32mEmail configuration not found - email notifications disabled[39m
{
  "service": "notification"
}
2025-08-02 21:41:09 [[31merror[39m]: [31mFailed to load active strategies: Database not initialized. Call initialize() first.[39m
{
  "stack": "Error: Database not initialized. Call initialize() first.\n    at DatabaseServiceClass.get client (/Users/<USER>/dev/github/agmentcode/backend/src/services/database.ts:96:13)\n    at ExitStrategyService.loadActiveStrategies (/Users/<USER>/dev/github/agmentcode/backend/src/services/exitStrategyService.ts:1159:50)\n    at ExitStrategyService.initializeService (/Users/<USER>/dev/github/agmentcode/backend/src/services/exitStrategyService.ts:1144:10)\n    at new ExitStrategyService (/Users/<USER>/dev/github/agmentcode/backend/src/services/exitStrategyService.ts:268:10)\n    at Function.getInstance (/Users/<USER>/dev/github/agmentcode/backend/src/services/exitStrategyService.ts:273:38)\n    at <anonymous> (/Users/<USER>/dev/github/agmentcode/backend/src/services/exitStrategyService.ts:1294:57)\n    at Object.<anonymous> (/Users/<USER>/dev/github/agmentcode/backend/src/services/exitStrategyService.ts:1294:69)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object.transformer (/Users/<USER>/dev/github/agmentcode/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)"
}
2025-08-02 21:41:10 [[32minfo[39m]: [32m🚀 Starting MemeTrader Pro Backend...[39m
2025-08-02 21:41:10 [[32minfo[39m]: [32m🔍 Running environment validation...[39m
2025-08-02 21:41:10 [[32minfo[39m]: [32m🔍 Starting comprehensive environment validation...[39m
(node:31672) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
2025-08-02 21:41:10 [[31merror[39m]: [31mUnhandled Promise Rejection[39m
{
  "error": {
    "name": "Error",
    "message": "Error: Redis subscriber not initialized. Call initialize() first.",
    "stack": "Error: Error: Redis subscriber not initialized. Call initialize() first.\n    at process.<anonymous> (/Users/<USER>/dev/github/agmentcode/backend/src/middleware/errorHandler.ts:204:43)\n    at process.emit (node:events:524:28)\n    at process.emit (/Users/<USER>/dev/github/agmentcode/node_modules/tsx/dist/suppress-warnings.cjs:1:472)\n    at emitUnhandledRejection (node:internal/process/promises:252:13)\n    at throwUnhandledRejectionsMode (node:internal/process/promises:388:19)\n    at processPromiseRejections (node:internal/process/promises:475:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:106:32)"
  },
  "promise": {}
}
npm error Lifecycle script `dev` failed with error:
npm error code 15
npm error path /Users/<USER>/dev/github/agmentcode/backend
npm error workspace @memetrader-pro/backend@1.0.0
npm error location /Users/<USER>/dev/github/agmentcode/backend
npm error command failed
npm error command sh -c tsx watch src/index.ts
