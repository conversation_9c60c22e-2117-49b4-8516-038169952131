# Testing Infrastructure Documentation

## Overview

This document outlines the comprehensive testing infrastructure for the MemeTrader Pro trading system backend. The testing setup includes unit tests, integration tests, end-to-end tests, and performance tests with comprehensive mocking of external dependencies.

## Test Structure

```
backend/src/__tests__/
├── setup/                     # Test configuration and setup
│   ├── jest.setup.ts          # Jest configuration and custom matchers
│   ├── global.setup.ts        # Global test environment setup
│   └── global.teardown.ts     # Global test cleanup
├── utils/                     # Testing utilities
│   ├── testUtils.ts           # Common test utilities and helpers
│   └── databaseHelpers.ts     # Database testing helpers
├── mocks/                     # Mock implementations
│   ├── redisMock.ts           # Redis service mocking
│   └── externalApiMocks.ts    # External API mocks (Jupiter, <PERSON>lius, etc.)
├── fixtures/                  # Test data fixtures
│   └── testFixtures.ts        # Comprehensive test data
├── services/                  # Service unit tests
│   ├── tradingService.test.ts
│   ├── transactionRecordingService.test.ts
│   └── [other service tests]
├── integration/               # Integration tests
│   └── [integration test files]
└── e2e/                      # End-to-end tests
    └── [e2e test files]
```

## Configuration

### Jest Configuration
- **File**: `jest.config.js`
- **Features**:
  - TypeScript support with `ts-jest`
  - Path mapping matching `tsconfig.json`
  - Coverage reporting with thresholds
  - Custom matchers for trading system validation
  - Parallel test execution
  - Global setup/teardown

### Test Environment
- **File**: `.env.test`
- **Features**:
  - Isolated test database
  - Test Redis instance
  - Mock API keys and configurations
  - Disabled authentication for testing
  - Relaxed rate limiting

## Test Utilities

### Database Helpers (`DatabaseTestHelper`)
```typescript
// Create test users, positions, transactions, strategies
const dbHelper = new DatabaseTestHelper()
const user = await dbHelper.createTestUser()
const position = await dbHelper.createTestPosition(user.id)
const transaction = await dbHelper.createTestTransaction(user.id)

// Create complete test scenarios
const scenario = await dbHelper.createTestScenario({
  positionCount: 3,
  transactionCount: 5,
  strategyCount: 2
})

// Clean up test data
await dbHelper.cleanupUserData(user.id)
```

### Redis Mock (`RedisMock`)
```typescript
// Full Redis API mock with pub/sub support
const redisMock = createRedisMock()
await redisMock.set('key', 'value')
await redisMock.publish('channel', 'message')

// Test utilities
const helper = new RedisTestHelper(redisMock)
await helper.seedTestData()
await helper.assertRedisState({
  keyExists: ['user:123:portfolio'],
  values: [{ key: 'trading:enabled', value: 'true' }]
})
```

### External API Mocks
```typescript
// Configure mock responses
externalMocks.jupiter.setCustomQuoteResponse(mockQuoteData)
externalMocks.helius.confirmTransaction('tx-hash')
externalMocks.priceService.setPriceData('token-address', 100.50)

// Simulate failures
externalMocks.setFailureState()
externalMocks.setDelays(1000) // 1 second delay
```

## Test Data Fixtures

### Comprehensive Fixtures
- **Users**: Basic, premium, admin, inactive users
- **Tokens**: SOL, USDC, BONK, WIF, PEPE with realistic data
- **Positions**: Profitable, losing, closed positions
- **Transactions**: Buy, sell, failed transactions
- **Strategies**: Basic stop-loss, advanced multi-target, trailing stop
- **Market Data**: Jupiter responses, market conditions

### Fixture Factory
```typescript
// Generate complete scenarios
const scenario = FixtureFactory.createCompleteScenario({
  user: { email: '<EMAIL>' },
  positions: [{ tokenSymbol: 'CUSTOM' }]
})

// Create specific trading scenarios
const tradingScenario = FixtureFactory.createTradingScenario('profitable')
```

## Custom Jest Matchers

```typescript
// Trading system specific matchers
expect(address).toBeValidSolanaAddress()
expect(transaction).toBeValidTransaction()
expect(quote).toBeValidQuote()
expect(result).toMatchTradeResult()
```

## Test Categories

### Unit Tests
- **Location**: `src/__tests__/services/`
- **Focus**: Individual service methods
- **Mocking**: External dependencies mocked
- **Coverage**: High coverage requirements (80%+)

### Integration Tests
- **Location**: `src/__tests__/integration/`
- **Focus**: Service interactions, database operations
- **Mocking**: Minimal mocking, real database
- **Coverage**: Critical user flows

### End-to-End Tests
- **Location**: `src/__tests__/e2e/`
- **Focus**: Complete trading workflows
- **Mocking**: External APIs only
- **Coverage**: Happy paths and error scenarios

## Running Tests

### Basic Commands
```bash
# Run all tests
npm test

# Run with coverage
npm run test:coverage

# Watch mode for development
npm run test:watch

# Run specific test categories
npm run test:unit
npm run test:integration
npm run test:e2e

# Run tests for specific services
npm run test:services
npm run test:routes

# CI/CD optimized run
npm run test:ci
```

### Advanced Commands
```bash
# Debug tests
npm run test:debug

# Silent mode (less output)
npm run test:silent

# Verbose mode (detailed output)
npm run test:verbose

# Setup/teardown test database
npm run test:db:setup
npm run test:db:teardown
npm run test:db:reset
```

## Database Testing

### Test Database Setup
1. **Isolated Database**: Each test run uses a clean test database
2. **Migrations**: Automatic migration deployment
3. **Seeding**: Optional test data seeding
4. **Cleanup**: Automatic cleanup between tests

### Database Helpers
```typescript
// Reset database to clean state
await resetTestDatabase()

// Create isolated test database
const testDbUrl = await createIsolatedTestDatabase()

// Cleanup after tests
await dropIsolatedTestDatabase(dbName)
```

## Mocking Strategy

### External Services
- **Jupiter API**: Complete mock with realistic responses
- **Helius RPC**: Mock blockchain interactions
- **Price Services**: Configurable price data
- **WebSocket**: Real-time update simulation

### Internal Services
- **Database**: Real database for integration tests, mocked for unit tests
- **Redis**: In-memory mock with full API compatibility
- **Logger**: Configurable console mocking

## Performance Testing

### Timing Utilities
```typescript
// Measure execution time
const timer = new PerformanceTimer()
timer.start()
await someOperation()
const duration = timer.end()

// Automated timing
const { result, duration } = await PerformanceTimer.measure(async () => {
  return await expensiveOperation()
})
```

### Load Testing
- Concurrent request testing
- Database connection pooling tests
- Memory leak detection
- Response time validation

## Error Handling Tests

### Comprehensive Error Scenarios
- Network timeouts
- Database connection failures
- External API failures
- Invalid input validation
- Edge cases and boundary conditions

### Error Assertion Utilities
```typescript
// Test error conditions
await expect(service.method()).rejects.toThrow('Expected error message')

// Test error types
await expect(service.method()).rejects.toThrow(AppError)
```

## Continuous Integration

### GitHub Actions Integration
```yaml
# .github/workflows/test.yml
- name: Run Tests
  run: npm run test:ci
  env:
    DATABASE_URL: ${{ secrets.TEST_DATABASE_URL }}
    REDIS_URL: ${{ secrets.TEST_REDIS_URL }}
```

### Coverage Reporting
- **Minimum Coverage**: 70% overall, 80% for critical services
- **Coverage Reports**: HTML, LCOV, JSON formats
- **CI Integration**: Coverage uploaded to CodeCov/Coveralls

## Best Practices

### Test Organization
1. **AAA Pattern**: Arrange, Act, Assert
2. **Descriptive Names**: Clear test descriptions
3. **Single Responsibility**: One assertion per test when possible
4. **Setup/Teardown**: Proper test isolation
5. **Mock Management**: Consistent mock configuration

### Performance Considerations
1. **Parallel Execution**: Tests run in parallel by default
2. **Database Optimization**: Efficient test data creation
3. **Mock Caching**: Reuse mock instances where appropriate
4. **Resource Cleanup**: Proper cleanup to prevent memory leaks

### Debugging
1. **Debug Mode**: Node.js debugging support
2. **Verbose Logging**: Configurable log levels
3. **Test Isolation**: Ability to run individual tests
4. **Mock Inspection**: Utilities to inspect mock state

## Common Testing Patterns

### Service Testing Pattern
```typescript
describe('ServiceName', () => {
  let service: ServiceType
  let dbHelper: DatabaseTestHelper
  let testUser: User

  beforeAll(async () => {
    dbHelper = new DatabaseTestHelper()
  })

  beforeEach(async () => {
    await resetTestDatabase()
    testUser = await dbHelper.createTestUser()
    service = new ServiceType()
  })

  afterAll(async () => {
    await dbHelper.cleanupTestPrismaClient()
  })

  describe('methodName', () => {
    it('should handle success case', async () => {
      // Arrange
      const input = { /* test data */ }
      
      // Act
      const result = await service.methodName(input)
      
      // Assert
      expect(result).toBeDefined()
      expect(result.success).toBe(true)
    })

    it('should handle error case', async () => {
      // Arrange
      const invalidInput = { /* invalid data */ }
      
      // Act & Assert
      await expect(service.methodName(invalidInput))
        .rejects.toThrow('Expected error message')
    })
  })
})
```

### Integration Testing Pattern
```typescript
describe('Integration: Service Interactions', () => {
  let dbHelper: DatabaseTestHelper
  let scenario: any

  beforeEach(async () => {
    dbHelper = new DatabaseTestHelper()
    scenario = await dbHelper.createTestScenario()
  })

  it('should complete end-to-end workflow', async () => {
    // Test complete user journey
    const quote = await tradingService.getQuote(params)
    const trade = await tradingService.executeTrade(tradeParams)
    const portfolio = await portfolioService.getPortfolioSummary(userId)
    
    expect(portfolio.positions).toHaveLength(1)
    expect(portfolio.totalValue).toBeGreaterThan(0)
  })
})
```

## Troubleshooting

### Common Issues
1. **Database Connection**: Ensure test database is running
2. **Redis Connection**: Check Redis instance availability
3. **Port Conflicts**: Use different ports for test services
4. **Environment Variables**: Verify `.env.test` configuration
5. **Memory Issues**: Check for memory leaks in long test runs

### Debug Commands
```bash
# Check test database connection
npm run verify:schema

# Validate environment
npm run validate:env

# Reset everything
npm run clean && npm install
```

This testing infrastructure provides comprehensive coverage for the trading system backend, ensuring reliability, maintainability, and confidence in the codebase.