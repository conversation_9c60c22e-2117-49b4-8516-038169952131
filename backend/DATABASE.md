# MemeTrader Pro Database Setup Guide

This guide covers database setup, migrations, and management for the MemeTrader Pro backend.

## Quick Start

### 1. Environment Setup
```bash
# Copy environment template
cp ../exampleenv.md .env

# Edit .env with your database credentials
# Ensure DATABASE_URL is set correctly
DATABASE_URL="postgresql://postgres:postgres123@localhost:5432/memetrader?schema=public"
```

### 2. Complete Database Setup
```bash
# Run the complete setup (recommended for first-time setup)
npm run db:setup
```

This command will:
- Generate Prisma client
- Run database migrations
- Seed initial data
- Verify schema integrity

### 3. Start Development
```bash
# Start the backend server
npm run dev
```

## Database Commands

### Core Commands

| Command | Description |
|---------|-------------|
| `npm run db:setup` | Complete database setup (generate, migrate, seed, verify) |
| `npm run db:migrate` | Run database migrations |
| `npm run db:seed` | Seed database with initial data |
| `npm run verify:schema` | Verify database schema integrity |
| `npm run db:studio` | Open Prisma Studio (database GUI) |

### Advanced Commands

| Command | Description |
|---------|-------------|
| `npm run db:generate` | Generate Prisma client only |
| `npm run db:migrate:deploy` | Deploy migrations (production) |
| `npm run db:reset` | Reset database and reseed |
| `npm run validate:env` | Validate environment configuration |

## Database Architecture

### Core Tables

#### Users & Authentication
- **User**: User accounts and authentication
- **UserPreferences**: User-specific settings and preferences

#### Trading & Positions
- **Position**: Active and closed trading positions
- **Transaction**: All trading transactions and swaps
- **Portfolio**: Aggregated portfolio metrics

#### Strategy Management
- **ExitStrategy**: Automated exit strategies
- **CustomStrategy**: User-defined custom strategies
- **TradingPreset**: System trading presets (DEFAULT, VOL, DEAD, NUN, P5)

#### Monitoring & Alerts
- **Alert**: System and user alerts
- **Watchlist**: User token watchlists
- **PriceHistory**: Historical price data

#### System Configuration
- **SystemConfig**: Application configuration
- **TradingPreset**: Trading execution presets

### Database Features

#### Extensions
- **uuid-ossp**: UUID generation
- **pgcrypto**: Cryptographic functions
- **btree_gin**: GIN indexing for better performance

#### Custom Functions
- `trigger_set_timestamp()`: Auto-update timestamp triggers
- `calculate_pnl_percentage(entry_price, current_price)`: PnL calculations
- `calculate_position_age_hours(entry_timestamp)`: Position age calculations

#### Indexes
- Primary keys on all tables
- Unique constraints on critical fields (email, wallet address, transaction hash)
- Composite indexes for common query patterns
- JSON/JSONB indexes for metadata searches

## Migration Management

### Creating Migrations

```bash
# Create a new migration
npx prisma migrate dev --name "description_of_changes"
```

### Migration Files Structure
```
prisma/
├── migrations/
│   ├── migration_lock.toml
│   ├── 20250802000000_initial_schema/
│   │   └── migration.sql
│   └── [timestamp]_[description]/
│       └── migration.sql
├── schema.prisma
├── init.sql
└── seed.sql
```

### Production Deployment

```bash
# Deploy migrations to production
npm run db:migrate:deploy

# Generate client after deployment
npm run db:generate
```

## Data Seeding

### Default Data Included

#### Trading Presets
- **DEFAULT**: Conservative settings (1% slippage, basic MEV protection)
- **VOL**: Volatile token settings (3% slippage, advanced MEV protection)
- **DEAD**: Low volatility (0.5% slippage, no MEV protection)
- **NUN**: High-risk settings (5% slippage, maximum MEV protection)
- **P5**: Premium settings (2% slippage, maximum MEV protection)

#### System Configuration
- Maximum position size limits
- Default slippage tolerances
- MEV protection settings
- Monitoring intervals

#### Development Data (NODE_ENV=development)
- Sample price history for major tokens
- Test user accounts
- Sample portfolio data

### Custom Seeding

Edit `src/scripts/seed.ts` to add custom seed data:

```typescript
// Add custom data
const customData = {
  // Your custom seed data
}

await prisma.yourTable.create({
  data: customData
})
```

## Schema Verification

The verification script checks:

### Critical Components
- ✅ Table existence and structure
- ✅ Column types and constraints
- ✅ Index presence and configuration
- ✅ Foreign key relationships
- ✅ Enum definitions
- ✅ Custom functions
- ✅ Basic CRUD operations

### Running Verification
```bash
# Verify current schema
npm run verify:schema
```

### Troubleshooting Verification Issues

| Issue | Solution |
|-------|----------|
| Missing tables | Run `npm run db:migrate` |
| Missing functions | Check `init.sql` execution |
| Missing data | Run `npm run db:seed` |
| Schema mismatch | Run `npm run db:reset` |

## Docker Setup

### Using Docker Compose

The PostgreSQL database is configured in `docker-compose.yml`:

```bash
# Start database only
docker-compose up postgres redis

# Start full stack
docker-compose up

# Reset database in Docker
docker-compose down -v
docker-compose up postgres redis
```

### Environment Variables for Docker

```env
# Docker environment
DATABASE_URL="***********************************************/memetrader_dev?schema=public"
REDIS_URL="redis://:redis123@redis:6379"
```

## Performance Optimization

### Query Performance
- Use appropriate indexes for your query patterns
- Monitor slow queries with `log_min_duration_statement`
- Use connection pooling in production

### Database Monitoring
```sql
-- Check table sizes
SELECT 
  schemaname,
  tablename,
  pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Check index usage
SELECT 
  tablename,
  indexname,
  idx_scan,
  idx_tup_read,
  idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;
```

### Optimization Settings
Added to `init.sql`:
- Statement tracking
- Query duration logging
- Performance statistics

## Backup & Recovery

### Development Backup
```bash
# Backup development database
pg_dump -h localhost -U postgres -d memetrader_dev > backup.sql

# Restore from backup
psql -h localhost -U postgres -d memetrader_dev < backup.sql
```

### Production Backup
```bash
# Automated backup script
pg_dump $DATABASE_URL > "backup_$(date +%Y%m%d_%H%M%S).sql"
```

## Common Issues & Solutions

### Connection Issues
1. **Database not running**: Start PostgreSQL service
2. **Wrong credentials**: Check DATABASE_URL in .env
3. **Network issues**: Verify firewall and network configuration

### Migration Issues
1. **Migration failed**: Check database permissions
2. **Schema drift**: Run `prisma db pull` to sync
3. **Lock conflicts**: Ensure no other processes are accessing DB

### Performance Issues
1. **Slow queries**: Add appropriate indexes
2. **Connection limits**: Implement connection pooling
3. **Large tables**: Consider partitioning strategies

## Development Workflow

### Daily Development
```bash
# Pull latest changes
git pull

# Update database if schema changed
npm run db:migrate

# Start development
npm run dev
```

### Adding New Features
1. Modify `schema.prisma`
2. Create migration: `npx prisma migrate dev --name "feature_name"`
3. Update seed data if needed
4. Run verification: `npm run verify:schema`
5. Test changes

### Production Deployment
1. Test migrations locally
2. Deploy code changes
3. Run production migrations: `npm run db:migrate:deploy`
4. Verify deployment: `npm run verify:schema`

## Security Considerations

### Database Security
- Use strong passwords
- Enable SSL in production
- Restrict network access
- Regular security updates

### Application Security
- Input validation with Zod schemas
- Prepared statements (Prisma handles this)
- Row-level security for multi-tenant features
- Audit logging for sensitive operations

## Support

### Documentation
- [Prisma Documentation](https://www.prisma.io/docs/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

### Debugging
```bash
# Enable Prisma debug logs
DEBUG="prisma:*" npm run dev

# Database query logs
export PRISMA_LOG_LEVEL=debug
```

### Getting Help
1. Check this documentation
2. Review error logs
3. Use `npm run verify:schema` for diagnostics
4. Check Docker logs if using containers