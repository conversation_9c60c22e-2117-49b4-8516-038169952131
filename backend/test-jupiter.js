#!/usr/bin/env node

const axios = require('axios');

const JUPITER_BASE_URL = 'https://lite-api.jup.ag/swap/v1';

async function testJupiterAPI() {
  console.log('🚀 Testing Jupiter Lite API integration...\n');

  try {
    // Test 1: Health check via quote endpoint
    console.log('1. Testing health check (quote endpoint)...');
    const healthResponse = await axios.get(`${JUPITER_BASE_URL}/quote`, {
      params: {
        inputMint: 'So11111111111111111111111111111111111111112', // SOL
        outputMint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
        amount: 1000000, // 0.001 SOL
        slippageBps: 50
      },
      timeout: 10000
    });

    console.log('✅ Health check passed');
    console.log(`   Quote received: ${healthResponse.data.outAmount} USDC for 0.001 SOL`);
    console.log(`   Route: ${healthResponse.data.routePlan[0]?.swapInfo?.label || 'Unknown'}`);
    console.log(`   Price impact: ${healthResponse.data.priceImpactPct}%\n`);

    // Test 2: Larger quote
    console.log('2. Testing larger quote (1 SOL to USDC)...');
    const largeQuoteResponse = await axios.get(`${JUPITER_BASE_URL}/quote`, {
      params: {
        inputMint: 'So11111111111111111111111111111111111111112',
        outputMint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
        amount: **********, // 1 SOL
        slippageBps: 50
      },
      timeout: 10000
    });

    console.log('✅ Large quote successful');
    console.log(`   Quote: ${(parseInt(largeQuoteResponse.data.outAmount) / 1000000).toFixed(6)} USDC for 1 SOL`);
    console.log(`   Route: ${largeQuoteResponse.data.routePlan[0]?.swapInfo?.label || 'Unknown'}`);
    console.log(`   Price impact: ${largeQuoteResponse.data.priceImpactPct}%\n`);

    // Test 3: Different token pair (SOL to BONK)
    console.log('3. Testing different token pair (SOL to BONK)...');
    const bonkQuoteResponse = await axios.get(`${JUPITER_BASE_URL}/quote`, {
      params: {
        inputMint: 'So11111111111111111111111111111111111111112',
        outputMint: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263', // BONK
        amount: 100000000, // 0.1 SOL
        slippageBps: 100
      },
      timeout: 10000
    });

    console.log('✅ BONK quote successful');
    console.log(`   Quote: ${(parseInt(bonkQuoteResponse.data.outAmount) / Math.pow(10, 5)).toFixed(2)} BONK for 0.1 SOL`);
    console.log(`   Route: ${bonkQuoteResponse.data.routePlan[0]?.swapInfo?.label || 'Unknown'}`);
    console.log(`   Price impact: ${bonkQuoteResponse.data.priceImpactPct}%\n`);

    console.log('🎉 All Jupiter Lite API tests passed successfully!');
    console.log('\n📊 Summary:');
    console.log('   ✅ API is accessible without authentication');
    console.log('   ✅ Quote endpoint responds correctly');
    console.log('   ✅ Multiple token pairs supported');
    console.log('   ✅ Route planning works');
    console.log('   ✅ Price impact calculations included');

  } catch (error) {
    console.error('❌ Jupiter API test failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Response:', error.response.data);
    }
    process.exit(1);
  }
}

testJupiterAPI();
