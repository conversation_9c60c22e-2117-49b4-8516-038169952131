# MemeTrader Pro Backend

A comprehensive Node.js TypeScript backend for the MemeTrader Pro trading platform, featuring real-time trading execution, risk management, exit strategy automation, and multi-channel notifications.

## 🏗️ Architecture Overview

The backend is built with a microservices-inspired architecture featuring:

- **Express.js REST API** - Core HTTP endpoints
- **Prisma ORM** - Type-safe database operations
- **BullMQ** - Redis-based job queue system
- **Socket.io/WebSocket** - Real-time communication
- **Redis** - Caching and pub/sub messaging
- **PostgreSQL** - Primary database
- **Winston** - Structured logging

## 🚀 Core Services

### TradingService
- Jupiter API integration for optimal swap routing
- MEV protection and analysis
- Trade execution with multiple retry strategies
- Real-time quote fetching and validation

### PriceService
- Helius WebSocket integration for real-time price feeds
- Price alert management and triggering
- Historical price data storage
- Multi-token price fetching with batching

### StrategyService
- PRD-compliant exit strategy enforcement
- Custom strategy creation and management
- Real-time strategy monitoring (500ms intervals)
- Automated execution of profit targets and stop losses

### RiskService
- Portfolio risk assessment and scoring
- Position sizing calculations based on risk tolerance
- Real-time exposure monitoring
- Risk limit validation and enforcement

### NotificationService
- Multi-channel alert delivery (WebSocket, Email, Desktop, Sound, Webhook)
- Priority-based routing and filtering
- Quiet hours and user preference management
- Delivery tracking and retry mechanisms

### QueueManager (BullMQ)
- Strategy monitoring jobs
- Price update jobs
- MEV analysis jobs
- Notification delivery jobs
- Background cleanup tasks

## 📊 Database Schema

### Core Models
- **User** - User accounts and wallet addresses
- **Position** - Active and historical trading positions
- **Transaction** - Complete trade execution records
- **ExitStrategy** - Automated exit strategy configurations
- **Alert** - User notifications and alerts
- **Portfolio** - Portfolio metrics and performance data

### Supporting Models
- **UserPreferences** - User settings and preferences
- **CustomStrategy** - User-defined strategy templates
- **TradingPreset** - System trading presets (DEFAULT, VOL, DEAD, NUN, P5)
- **PriceHistory** - Historical price data for analytics
- **Watchlist** - User token watchlists
- **SystemConfig** - Global system configuration

## 🔐 Security Features

- JWT-based authentication with refresh tokens
- Role-based access control
- Input validation with Zod schemas
- Rate limiting and CORS protection
- Secure password hashing with bcrypt
- API key authentication for external services

## 📡 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/refresh` - Token refresh
- `POST /api/auth/logout` - User logout
- `PUT /api/auth/change-password` - Password change

### Trading
- `POST /api/trading/quote` - Get trading quote
- `POST /api/trading/execute` - Execute trade
- `POST /api/trading/simulate` - Simulate trade
- `GET /api/trading/presets` - Get trading presets
- `POST /api/trading/strategies` - Create exit strategy

### Portfolio
- `GET /api/portfolio/overview` - Portfolio metrics
- `GET /api/portfolio/positions` - User positions
- `GET /api/portfolio/allocation` - Asset allocation
- `GET /api/portfolio/performance` - Performance analytics
- `POST /api/portfolio/rebalance` - Portfolio rebalancing

### Alerts
- `GET /api/alerts` - Get user alerts
- `POST /api/alerts/price` - Create price alert
- `PUT /api/alerts/config` - Update alert settings
- `GET /api/alerts/statistics` - Alert analytics

### User Management
- `GET /api/user/profile` - User profile
- `GET /api/user/preferences` - User preferences
- `GET /api/user/dashboard` - Dashboard data
- `POST /api/user/export` - Export user data

## 🔧 Installation & Setup

### Prerequisites
- Node.js 18+
- PostgreSQL 14+
- Redis 6+
- npm/yarn

### Environment Variables
Create a `.env` file with the following variables:

```env
# Server Configuration
NODE_ENV=development
PORT=3001

# Database
DATABASE_URL="postgresql://username:password@localhost:5432/memetrader_pro"

# Redis
REDIS_URL="redis://localhost:6379"

# JWT Secrets
JWT_SECRET="your-super-secret-jwt-key-at-least-32-characters-long"
JWT_REFRESH_SECRET="your-super-secret-refresh-key-at-least-32-characters-long"

# CORS
CORS_ORIGIN="http://localhost:3000"

# Solana Configuration
SOLANA_RPC_URL="https://api.mainnet-beta.solana.com"
SOLANA_WS_URL="wss://api.mainnet-beta.solana.com"

# Jupiter API
JUPITER_API_URL="https://quote-api.jup.ag/v6"

# Helius Configuration
HELIUS_API_KEY="your-helius-api-key"
HELIUS_WS_URL="wss://atlas-mainnet.helius-rpc.com"

# Email Configuration (Optional)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
SMTP_FROM="MemeTrader Pro <<EMAIL>>"
```

### Installation Steps

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Setup database**
   ```bash
   npm run db:generate
   npm run db:migrate
   npm run db:seed
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Build for production**
   ```bash
   npm run build
   npm start
   ```

## 📚 Available Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm start` - Start production server
- `npm run test` - Run tests
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking
- `npm run db:migrate` - Run database migrations
- `npm run db:generate` - Generate Prisma client
- `npm run db:seed` - Seed database with initial data
- `npm run db:studio` - Open Prisma Studio

## 🏃‍♂️ Real-time Features

### Strategy Monitoring
- Continuous monitoring of active positions every 500ms
- Automatic execution of profit targets and stop losses
- Trailing stop loss updates based on price movements
- Emergency execution with increased slippage tolerance

### Price Feeds
- Real-time price updates via Helius WebSocket
- Price alert triggering and notification delivery
- Historical price data collection for analytics
- Multi-token price fetching with intelligent batching

### Live Notifications
- Instant WebSocket delivery for critical alerts
- Email notifications for important events
- Desktop notifications via browser API
- Sound alerts for high-priority events

## 🔍 Monitoring & Logging

### Structured Logging
- Service-specific log namespacing
- User action tracking with user IDs
- Error tracking with stack traces
- Performance monitoring with execution times

### Health Checks
- Individual service health monitoring
- Database connectivity checks
- External API availability verification
- Queue system status monitoring

### Queue Monitoring
- Job processing statistics
- Failed job tracking and retry logic
- Queue size monitoring and alerts
- Background cleanup automation

## 🧪 Testing Strategy

### Unit Tests (70% Coverage Target)
- Service layer testing with mocks
- Database operation testing
- Utility function validation
- Business logic verification

### Integration Tests (20% Coverage Target)
- API endpoint testing
- Database integration verification
- External service integration testing
- WebSocket communication testing

### End-to-End Tests (10% Coverage Target)
- Complete user workflow testing
- Trading execution flow verification
- Strategy automation testing
- Alert delivery confirmation

## 🚦 Performance Optimizations

### Caching Strategy
- Redis caching for frequently accessed data
- Price data caching with intelligent TTL
- User preference caching
- Trading preset caching

### Database Optimizations
- Proper indexing on frequently queried fields
- Connection pooling with Prisma
- Query optimization and batching
- Efficient pagination implementation

### Queue Processing
- Concurrent job processing
- Priority-based job scheduling
- Intelligent retry mechanisms
- Background cleanup automation

## 🔒 Security Considerations

### Authentication & Authorization
- JWT tokens with short expiration times
- Refresh token rotation
- Role-based access control
- Session management with Redis

### Input Validation
- Zod schema validation for all inputs
- Solana address format validation
- Transaction hash verification
- Numeric range validation

### Rate Limiting
- API endpoint rate limiting
- User-specific rate limiting
- IP-based protection
- Trading frequency limits

## 📈 Scalability Features

### Horizontal Scaling Ready
- Stateless service design
- Redis-based session storage
- Queue-based background processing
- Database connection pooling

### Load Balancing Support
- Health check endpoints
- Graceful shutdown handling
- Connection draining
- Service discovery ready

## 🐛 Error Handling

### Comprehensive Error Classification
- Network and connectivity errors
- Trading execution failures
- Strategy execution errors
- Validation and business logic errors

### Recovery Strategies
- Automatic retry with exponential backoff
- Circuit breaker pattern implementation
- Fallback execution strategies
- Emergency execution modes

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Implement changes with tests
4. Run linting and type checks
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Contact the development team

---

Built with ❤️ for the MemeTrader Pro trading platform