# Health Monitoring System

This document describes the comprehensive health monitoring system implemented for MemeTrader Pro backend.

## Overview

The health monitoring system provides real-time visibility into the health and performance of all system components, with automatic alerting and recovery mechanisms.

## Architecture

### Core Components

1. **HealthMonitor Service**: Central orchestrator for all health checks
2. **Individual Service Health Checks**: Service-specific health validation
3. **Health Routes**: HTTP endpoints for health status
4. **Dashboard Routes**: Comprehensive monitoring dashboard
5. **RPC Health Monitoring**: Specialized RPC endpoint monitoring
6. **Alert System**: Real-time health alerts and notifications

### Monitored Services

#### Critical Services
- **Database (PostgreSQL)**: Connection, query performance, schema validation
- **Redis**: Connection, pub/sub functionality, cache operations
- **RPC Failover System**: Endpoint health, failover logic, response times

#### Important Services
- **Trading Service**: Jupiter API connectivity, wallet validation
- **WebSocket Service**: Connection status, subscription management
- **Jupiter API**: Token data availability, response times
- **Helius API**: RPC endpoint availability, data integrity

#### Supporting Services
- **Price Service**: Data freshness, update frequency
- **Notification Service**: Delivery capability, queue status
- **Queue Manager**: Job processing, Redis connectivity
- **System Resources**: Memory usage, CPU performance

## Health Check Types

### Service Status Levels
- **Healthy**: Service operating normally
- **Degraded**: Service operating with reduced performance
- **Unhealthy**: Service not functioning properly
- **Unknown**: Health status could not be determined

### Check Categories
- **Connectivity**: Basic connection and authentication
- **Performance**: Response times and throughput
- **Functionality**: Core feature availability
- **Resources**: Memory, CPU, and storage usage

## API Endpoints

### Basic Health Endpoints

#### `GET /health`
Simple health check for load balancers
```json
{
  "status": "healthy",
  "timestamp": "2025-08-02T10:30:00.000Z",
  "version": "1.0.0",
  "environment": "production"
}
```

#### `GET /health/live`
Kubernetes liveness probe
```json
{
  "status": "alive",
  "timestamp": "2025-08-02T10:30:00.000Z"
}
```

#### `GET /health/ready`
Kubernetes readiness probe (checks critical services)
```json
{
  "status": "ready",
  "timestamp": "2025-08-02T10:30:00.000Z",
  "criticalServices": [
    { "name": "database", "status": "healthy" },
    { "name": "redis", "status": "healthy" },
    { "name": "rpc_failover", "status": "healthy" }
  ]
}
```

### Comprehensive Health Endpoints

#### `GET /health/detailed`
Complete system health with all services
```json
{
  "status": "healthy",
  "timestamp": 1691234567890,
  "uptime": 86400000,
  "environment": "production",
  "version": "1.0.0",
  "services": [
    {
      "name": "database",
      "status": "healthy",
      "lastCheck": 1691234567890,
      "responseTime": 15,
      "details": { "type": "postgresql", "responseTime": 15 }
    }
  ],
  "summary": {
    "total": 11,
    "healthy": 10,
    "degraded": 1,
    "unhealthy": 0,
    "unknown": 0
  }
}
```

#### `GET /health/system`
System health summary
```json
{
  "status": "healthy",
  "timestamp": 1691234567890,
  "uptime": 86400000,
  "services": [...],
  "summary": {...}
}
```

#### `GET /health/service/:serviceName`
Individual service health check
```json
{
  "name": "database",
  "status": "healthy",
  "lastCheck": 1691234567890,
  "responseTime": 15,
  "details": { "type": "postgresql" }
}
```

### RPC Monitoring Endpoints

#### `GET /api/rpc/health`
RPC system health with endpoint details
```json
{
  "status": "healthy",
  "summary": {
    "totalEndpoints": 4,
    "healthyEndpoints": 3,
    "totalRequests": 1500,
    "successfulRequests": 1485,
    "avgResponseTime": 125
  },
  "endpoints": [...]
}
```

#### `GET /api/rpc/endpoints`
Detailed endpoint status
```json
{
  "endpoints": [
    {
      "id": "helius-mainnet-primary",
      "priority": 1,
      "healthy": true,
      "circuitBreakerOpen": false,
      "successRate": 99.2,
      "avgResponseTimeMs": 95
    }
  ]
}
```

#### `GET /api/rpc/metrics`
RPC performance metrics
```json
{
  "timestamp": "2025-08-02T10:30:00.000Z",
  "endpoints": { "total": 4, "healthy": 3 },
  "requests": {
    "total": 1500,
    "successful": 1485,
    "successRate": 99.0
  },
  "performance": {
    "avgResponseTimeMs": 125,
    "requestsPerSecond": 25.5
  }
}
```

### Dashboard Endpoints

#### `GET /api/dashboard/overview`
Comprehensive system overview
```json
{
  "status": {
    "overall": "healthy",
    "services": { "total": 11, "healthy": 10, "degraded": 1 }
  },
  "rpc": {
    "status": "healthy",
    "endpoints": { "total": 4, "healthy": 3 },
    "performance": { "successRate": 99 }
  },
  "websocket": {
    "connections": { "total": 15, "authenticated": 12 }
  },
  "system": {
    "memory": { "used": 256, "total": 512, "usage_percent": 50 }
  }
}
```

#### `GET /api/dashboard/metrics`
Real-time metrics for monitoring
```json
{
  "timestamp": 1691234567890,
  "health_scores": {
    "system": 91,
    "rpc": 95,
    "memory": 75,
    "overall": 93
  },
  "service_counts": {
    "total": 11,
    "healthy": 10,
    "degraded": 1,
    "unhealthy": 0
  }
}
```

#### `GET /api/dashboard/alerts`
Recent health alerts and current issues
```json
{
  "current_issues": [
    {
      "service": "price_service",
      "status": "degraded",
      "error": "Slow response time",
      "type": "service_health"
    }
  ],
  "recent_alerts": [...],
  "summary": {
    "total_current_issues": 1,
    "critical_issues": 0
  }
}
```

## Configuration

### Health Check Configuration
```typescript
const config: HealthCheckConfig = {
  interval: 30000,      // Health check interval (30 seconds)
  timeout: 10000,       // Individual check timeout (10 seconds)
  retries: 2,           // Retry attempts for failed checks
  alertThreshold: 3     // Consecutive failures before alerting
}
```

### Service Categories
- **Critical**: Database, Redis, RPC Failover (must be healthy for system readiness)
- **Important**: Trading, WebSocket, APIs (degradation affects functionality)
- **Supporting**: Price, Notification, Queue services (degradation is acceptable)

## Monitoring Integration

### Kubernetes Integration
The health endpoints are designed for Kubernetes deployments:

```yaml
# Deployment configuration
livenessProbe:
  httpGet:
    path: /health/live
    port: 5000
  initialDelaySeconds: 30
  periodSeconds: 10

readinessProbe:
  httpGet:
    path: /health/ready
    port: 5000
  initialDelaySeconds: 10
  periodSeconds: 5
```

### Prometheus Metrics
The `/api/dashboard/metrics` endpoint provides metrics suitable for Prometheus scraping:

```yaml
# Prometheus configuration
scrape_configs:
  - job_name: 'memetrader-backend'
    static_configs:
      - targets: ['backend:5000']
    metrics_path: '/api/dashboard/metrics'
    scrape_interval: 30s
```

### Grafana Dashboard
Use the dashboard endpoints to create comprehensive Grafana dashboards:
- System overview panel (`/api/dashboard/overview`)
- Real-time metrics (`/api/dashboard/metrics`)
- Alert status (`/api/dashboard/alerts`)

## Alert System

### Alert Types
- **Service Health**: Individual service status changes
- **RPC Endpoint**: RPC endpoint failures or degradation
- **System Resources**: Memory/CPU threshold violations
- **Performance**: Response time degradation

### Alert Channels
- **Redis Pub/Sub**: Real-time notifications for frontend
- **Logs**: Structured logging for aggregation
- **Event Emitters**: For custom integrations

### Alert Lifecycle
1. **Detection**: Health check failure detected
2. **Threshold**: Multiple consecutive failures required
3. **Alerting**: Alert published to configured channels
4. **Recovery**: Automatic reset when service recovers
5. **Tracking**: Alert history maintained for analysis

## Best Practices

### Implementation
- Monitor critical services more frequently than supporting services
- Use circuit breakers for external service dependencies
- Implement exponential backoff for failed health checks
- Cache health status to reduce overhead
- Provide detailed error information for debugging

### Operations
- Set up proper alerting thresholds to avoid noise
- Monitor health check performance itself
- Use readiness probes for rolling deployments
- Implement proper logging for health events
- Regular review of health metrics and trends

### Development
- Add health checks to new services by default
- Test health check behavior during development
- Document service-specific health indicators
- Implement graceful degradation for non-critical failures
- Consider health check impact on service performance

## Troubleshooting

### Common Issues

#### Service Showing as Unhealthy
1. Check service logs for errors
2. Verify network connectivity
3. Check resource usage (memory, CPU)
4. Review recent deployments or changes

#### Frequent Health Check Failures
1. Increase timeout values if needed
2. Check for network issues
3. Review service load and performance
4. Consider adjusting check frequency

#### Circuit Breaker Issues
1. Check endpoint availability
2. Review error rates and patterns
3. Manually reset circuit breakers if needed
4. Adjust circuit breaker thresholds

### Debugging Commands

```bash
# Check overall system health
curl http://localhost:5000/health/system

# Check specific service
curl http://localhost:5000/health/service/database

# Get detailed RPC status
curl http://localhost:5000/api/rpc/health

# View current alerts
curl http://localhost:5000/api/dashboard/alerts

# Test alert system
curl -X POST http://localhost:5000/api/dashboard/test-alert \
  -H "Content-Type: application/json" \
  -d '{"service": "test", "severity": "warning"}'
```

## Security Considerations

- Health endpoints expose system information - consider access controls in production
- API keys are masked in RPC endpoint responses
- Sensitive configuration details are excluded from health responses
- Health check failures are logged but sensitive data is redacted
- Consider rate limiting for health endpoints to prevent abuse

## Performance Impact

- Health checks run every 30 seconds by default
- Individual checks timeout after 10 seconds
- Results are cached to minimize repeated calls
- Asynchronous execution prevents blocking operations
- Resource usage is monitored as part of system health