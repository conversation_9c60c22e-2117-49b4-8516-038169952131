{"parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2022, "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "@typescript-eslint/recommended"], "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/explicit-function-return-type": "warn", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/prefer-const": "error", "prefer-const": "off"}, "env": {"node": true, "es2022": true}}