import winston from 'winston'
import { config } from '@/config/environment'

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  debug: 3,
}

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  debug: 'blue',
}

winston.addColors(colors)

// Custom format for console output
const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let log = `${timestamp} [${level}]: ${message}`
    
    // Add metadata if present
    if (Object.keys(meta).length > 0) {
      log += `\n${JSON.stringify(meta, null, 2)}`
    }
    
    return log
  })
)

// Custom format for file output
const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.json()
)

// Create transports array
const transports: winston.transport[] = []

// Console transport (always enabled in development)
if (config.nodeEnv === 'development') {
  transports.push(
    new winston.transports.Console({
      level: config.logging.level,
      format: consoleFormat,
    })
  )
}

// File transports (enabled in production)
if (config.nodeEnv === 'production') {
  // Error log file
  transports.push(
    new winston.transports.File({
      filename: 'logs/error.log',
      level: 'error',
      format: fileFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    })
  )

  // Combined log file
  transports.push(
    new winston.transports.File({
      filename: 'logs/combined.log',
      level: config.logging.level,
      format: fileFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    })
  )

  // Console transport for production (less verbose)
  transports.push(
    new winston.transports.Console({
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.simple()
      ),
    })
  )
}

// Create logger instance
export const logger = winston.createLogger({
  levels,
  level: config.logging.level,
  format: fileFormat,
  transports,
  exitOnError: false,
})

// Create a stream object for Morgan HTTP logging
export const loggerStream = {
  write: (message: string) => {
    logger.info(message.trim())
  },
}

// Helper functions for structured logging
export const logError = (message: string, error?: Error, meta?: any) => {
  logger.error(message, {
    error: error ? {
      name: error.name,
      message: error.message,
      stack: error.stack,
    } : undefined,
    ...meta,
  })
}

export const logInfo = (message: string, meta?: any) => {
  logger.info(message, meta)
}

export const logWarn = (message: string, meta?: any) => {
  logger.warn(message, meta)
}

export const logDebug = (message: string, meta?: any) => {
  logger.debug(message, meta)
}

// Trading-specific logging helpers
export const logTrade = (action: string, data: any) => {
  logger.info(`TRADE: ${action}`, {
    category: 'trading',
    action,
    ...data,
  })
}

export const logWebSocket = (event: string, data?: any) => {
  logger.debug(`WS: ${event}`, {
    category: 'websocket',
    event,
    ...data,
  })
}

export const logAuth = (action: string, userId?: string, meta?: any) => {
  logger.info(`AUTH: ${action}`, {
    category: 'auth',
    action,
    userId,
    ...meta,
  })
}

export const logDatabase = (operation: string, table?: string, meta?: any) => {
  logger.debug(`DB: ${operation}`, {
    category: 'database',
    operation,
    table,
    ...meta,
  })
}

export const logAPI = (method: string, endpoint: string, statusCode: number, responseTime?: number, meta?: any) => {
  logger.info(`API: ${method} ${endpoint}`, {
    category: 'api',
    method,
    endpoint,
    statusCode,
    responseTime,
    ...meta,
  })
}

// Performance logging
export const logPerformance = (operation: string, duration: number, meta?: any) => {
  logger.info(`PERF: ${operation} completed in ${duration}ms`, {
    category: 'performance',
    operation,
    duration,
    ...meta,
  })
}

// Create logs directory if it doesn't exist (for production)
if (config.nodeEnv === 'production') {
  const fs = require('fs')
  const path = require('path')
  
  const logsDir = path.join(process.cwd(), 'logs')
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true })
  }
}
