import { Queue, Worker, Job, QueueEvents } from 'bullmq'
import { config } from '@/config/environment'
import { logger, logQueue } from '@/utils/logger'
import { RedisService } from '@/services/redis'

// Job types and their data interfaces
export interface StrategyMonitorJobData {
  strategyId: string
  positionId: string
  userId: string
  tokenAddress: string
}

export interface PriceUpdateJobData {
  tokenAddresses: string[]
  priority: 'high' | 'medium' | 'low'
}

export interface MEVAnalysisJobData {
  tradeParams: {
    tokenIn: string
    tokenOut: string
    amount: number
    slippage: number
  }
  userId: string
  urgency: 'immediate' | 'standard'
}

export interface NotificationJobData {
  userId: string
  type: string
  priority: string
  title: string
  message: string
  channels: string[]
  metadata?: Record<string, any>
}

export interface AlertProcessingJobData {
  alertId: string
  userId: string
  alertType: string
  conditions: Record<string, any>
}

export interface PortfolioUpdateJobData {
  userId: string
  recalculateRisk: boolean
  updatePositions: boolean
}

// Job queue names
export enum QueueName {
  STRATEGY_MONITORING = 'strategy-monitoring',
  PRICE_UPDATES = 'price-updates',
  MEV_ANALYSIS = 'mev-analysis',
  NOTIFICATIONS = 'notifications',
  ALERT_PROCESSING = 'alert-processing',
  PORTFOLIO_UPDATES = 'portfolio-updates',
  CLEANUP = 'cleanup'
}

class QueueManagerClass {
  private static instance: QueueManagerClass
  private queues: Map<QueueName, Queue> = new Map()
  private workers: Map<QueueName, Worker> = new Map()
  private queueEvents: Map<QueueName, QueueEvents> = new Map()
  private isInitialized: boolean = false

  private constructor() {}

  public static getInstance(): QueueManagerClass {
    if (!QueueManagerClass.instance) {
      QueueManagerClass.instance = new QueueManagerClass()
    }
    return QueueManagerClass.instance
  }

  /**
   * Initialize all queues and workers
   */
  public async initialize(): Promise<void> {
    try {
      if (this.isInitialized) {
        return
      }

      logQueue('Initializing queue manager')

      // Initialize Redis connection for BullMQ
      const connection = {
        host: new URL(config.redis.queueUrl).hostname,
        port: parseInt(new URL(config.redis.queueUrl).port) || 6379,
        password: new URL(config.redis.queueUrl).password || undefined,
        db: 0,
        maxRetriesPerRequest: null,
        retryDelayOnFailover: 100,
        enableReadyCheck: false,
        lazyConnect: true
      }

      // Create queues
      await this.createQueues(connection)

      // Create workers
      await this.createWorkers(connection)

      // Setup queue events
      await this.setupQueueEvents(connection)

      this.isInitialized = true
      logQueue('Queue manager initialized successfully')

    } catch (error) {
      logQueue('Failed to initialize queue manager', undefined, {
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      throw error
    }
  }

  /**
   * Create all job queues
   */
  private async createQueues(connection: any): Promise<void> {
    const queueConfigs = [
      {
        name: QueueName.STRATEGY_MONITORING,
        options: {
          defaultJobOptions: {
            removeOnComplete: 100,
            removeOnFail: 50,
            attempts: 3,
            backoff: {
              type: 'exponential' as const,
              delay: 2000
            }
          }
        }
      },
      {
        name: QueueName.PRICE_UPDATES,
        options: {
          defaultJobOptions: {
            removeOnComplete: 50,
            removeOnFail: 25,
            attempts: 2,
            backoff: {
              type: 'fixed' as const,
              delay: 1000
            }
          }
        }
      },
      {
        name: QueueName.MEV_ANALYSIS,
        options: {
          defaultJobOptions: {
            removeOnComplete: 200,
            removeOnFail: 100,
            attempts: 2,
            delay: 0 // Process immediately
          }
        }
      },
      {
        name: QueueName.NOTIFICATIONS,
        options: {
          defaultJobOptions: {
            removeOnComplete: 500,
            removeOnFail: 100,
            attempts: 3,
            backoff: {
              type: 'exponential' as const,
              delay: 5000
            }
          }
        }
      },
      {
        name: QueueName.ALERT_PROCESSING,
        options: {
          defaultJobOptions: {
            removeOnComplete: 100,
            removeOnFail: 50,
            attempts: 3,
            backoff: {
              type: 'exponential' as const,
              delay: 3000
            }
          }
        }
      },
      {
        name: QueueName.PORTFOLIO_UPDATES,
        options: {
          defaultJobOptions: {
            removeOnComplete: 100,
            removeOnFail: 50,
            attempts: 2,
            backoff: {
              type: 'fixed' as const,
              delay: 5000
            }
          }
        }
      },
      {
        name: QueueName.CLEANUP,
        options: {
          defaultJobOptions: {
            removeOnComplete: 10,
            removeOnFail: 10,
            attempts: 1
          }
        }
      }
    ]

    for (const { name, options } of queueConfigs) {
      const queue = new Queue(name, {
        connection,
        ...options
      })

      this.queues.set(name, queue)
      logQueue(`Created queue: ${name}`)
    }
  }

  /**
   * Create workers for processing jobs
   */
  private async createWorkers(connection: any): Promise<void> {
    const workerConfigs = [
      {
        queueName: QueueName.STRATEGY_MONITORING,
        processor: this.processStrategyMonitoringJob.bind(this),
        concurrency: 10
      },
      {
        queueName: QueueName.PRICE_UPDATES,
        processor: this.processPriceUpdateJob.bind(this),
        concurrency: 5
      },
      {
        queueName: QueueName.MEV_ANALYSIS,
        processor: this.processMEVAnalysisJob.bind(this),
        concurrency: 3
      },
      {
        queueName: QueueName.NOTIFICATIONS,
        processor: this.processNotificationJob.bind(this),
        concurrency: 8
      },
      {
        queueName: QueueName.ALERT_PROCESSING,
        processor: this.processAlertJob.bind(this),
        concurrency: 5
      },
      {
        queueName: QueueName.PORTFOLIO_UPDATES,
        processor: this.processPortfolioUpdateJob.bind(this),
        concurrency: 3
      },
      {
        queueName: QueueName.CLEANUP,
        processor: this.processCleanupJob.bind(this),
        concurrency: 1
      }
    ]

    for (const { queueName, processor, concurrency } of workerConfigs) {
      const worker = new Worker(queueName, processor, {
        connection,
        concurrency,
        removeOnComplete: 100,
        removeOnFail: 50
      })

      // Setup worker event handlers
      worker.on('ready', () => {
        logQueue(`Worker ready: ${queueName}`)
      })

      worker.on('error', (error) => {
        logQueue(`Worker error in ${queueName}`, undefined, {
          error: error.message
        })
      })

      worker.on('failed', (job, error) => {
        logQueue(`Job failed in ${queueName}`, undefined, {
          jobId: job?.id,
          error: error.message,
          attempts: job?.attemptsMade
        })
      })

      worker.on('completed', (job) => {
        logQueue(`Job completed in ${queueName}`, undefined, {
          jobId: job.id,
          duration: Date.now() - job.processedOn!
        })
      })

      this.workers.set(queueName, worker)
      logQueue(`Created worker: ${queueName} (concurrency: ${concurrency})`)
    }
  }

  /**
   * Setup queue events for monitoring
   */
  private async setupQueueEvents(connection: any): Promise<void> {
    for (const [queueName] of this.queues) {
      const queueEvents = new QueueEvents(queueName, { connection })

      queueEvents.on('waiting', ({ jobId }) => {
        logQueue(`Job waiting: ${jobId} in ${queueName}`)
      })

      queueEvents.on('active', ({ jobId }) => {
        logQueue(`Job active: ${jobId} in ${queueName}`)
      })

      queueEvents.on('completed', ({ jobId, returnvalue }) => {
        logQueue(`Job completed: ${jobId} in ${queueName}`, undefined, {
          returnValue: returnvalue
        })
      })

      queueEvents.on('failed', ({ jobId, failedReason }) => {
        logQueue(`Job failed: ${jobId} in ${queueName}`, undefined, {
          reason: failedReason
        })
      })

      queueEvents.on('stalled', ({ jobId }) => {
        logQueue(`Job stalled: ${jobId} in ${queueName}`)
      })

      this.queueEvents.set(queueName, queueEvents)
    }
  }

  /**
   * Add strategy monitoring job
   */
  public async addStrategyMonitoringJob(
    data: StrategyMonitorJobData,
    options: {
      delay?: number
      repeat?: { every: number }
      priority?: number
    } = {}
  ): Promise<string> {
    const queue = this.queues.get(QueueName.STRATEGY_MONITORING)
    if (!queue) throw new Error('Strategy monitoring queue not initialized')

    const job = await queue.add('monitor-strategy', data, {
      delay: options.delay || 0,
      repeat: options.repeat,
      priority: options.priority || 0,
      jobId: `strategy-${data.strategyId}-${Date.now()}`
    })

    logQueue('Strategy monitoring job added', data.userId, {
      jobId: job.id,
      strategyId: data.strategyId
    })

    return job.id!
  }

  /**
   * Add price update job
   */
  public async addPriceUpdateJob(
    data: PriceUpdateJobData,
    options: {
      delay?: number
      repeat?: { every: number }
      priority?: number
    } = {}
  ): Promise<string> {
    const queue = this.queues.get(QueueName.PRICE_UPDATES)
    if (!queue) throw new Error('Price updates queue not initialized')

    const priority = data.priority === 'high' ? 1 : data.priority === 'medium' ? 5 : 10

    const job = await queue.add('update-prices', data, {
      delay: options.delay || 0,
      repeat: options.repeat,
      priority: options.priority || priority,
      jobId: `price-update-${Date.now()}`
    })

    return job.id!
  }

  /**
   * Add MEV analysis job
   */
  public async addMEVAnalysisJob(
    data: MEVAnalysisJobData,
    options: {
      delay?: number
      priority?: number
    } = {}
  ): Promise<string> {
    const queue = this.queues.get(QueueName.MEV_ANALYSIS)
    if (!queue) throw new Error('MEV analysis queue not initialized')

    const priority = data.urgency === 'immediate' ? 1 : 5

    const job = await queue.add('analyze-mev', data, {
      delay: options.delay || 0,
      priority: options.priority || priority,
      jobId: `mev-${data.userId}-${Date.now()}`
    })

    return job.id!
  }

  /**
   * Add notification job
   */
  public async addNotificationJob(
    data: NotificationJobData,
    options: {
      delay?: number
      priority?: number
    } = {}
  ): Promise<string> {
    const queue = this.queues.get(QueueName.NOTIFICATIONS)
    if (!queue) throw new Error('Notifications queue not initialized')

    const priority = data.priority === 'CRITICAL' ? 1 : data.priority === 'HIGH' ? 3 : 5

    const job = await queue.add('send-notification', data, {
      delay: options.delay || 0,
      priority: options.priority || priority,
      jobId: `notification-${data.userId}-${Date.now()}`
    })

    return job.id!
  }

  /**
   * Process strategy monitoring job
   */
  private async processStrategyMonitoringJob(job: Job<StrategyMonitorJobData>): Promise<void> {
    const { StrategyService } = await import('@/services/strategyService')
    const { strategyId, positionId, userId, tokenAddress } = job.data

    logQueue('Processing strategy monitoring job', userId, {
      jobId: job.id,
      strategyId
    })

    try {
      // Check if strategy is still active
      const strategy = await DatabaseService.client.exitStrategy.findUnique({
        where: { id: strategyId }
      })

      if (!strategy || strategy.executionState !== 'ACTIVE') {
        logQueue('Strategy no longer active, skipping monitoring', userId, { strategyId })
        return
      }

      // The actual monitoring is handled by StrategyService's internal monitoring
      // This job serves as a heartbeat and backup mechanism
      await StrategyService.healthCheck()

    } catch (error) {
      logQueue('Strategy monitoring job failed', userId, {
        jobId: job.id,
        strategyId,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      throw error
    }
  }

  /**
   * Process price update job
   */
  private async processPriceUpdateJob(job: Job<PriceUpdateJobData>): Promise<void> {
    const { PriceService } = await import('@/services/priceService')
    const { tokenAddresses, priority } = job.data

    logQueue('Processing price update job', undefined, {
      jobId: job.id,
      tokenCount: tokenAddresses.length,
      priority
    })

    try {
      // Update prices for all tokens
      const results = await PriceService.getMultipleTokenPrices(tokenAddresses)
      
      logQueue('Price update job completed', undefined, {
        jobId: job.id,
        updatedTokens: results.size
      })

    } catch (error) {
      logQueue('Price update job failed', undefined, {
        jobId: job.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      throw error
    }
  }

  /**
   * Process MEV analysis job
   */
  private async processMEVAnalysisJob(job: Job<MEVAnalysisJobData>): Promise<void> {
    const { TradingService } = await import('@/services/tradingService')
    const { tradeParams, userId, urgency } = job.data

    logQueue('Processing MEV analysis job', userId, {
      jobId: job.id,
      urgency
    })

    try {
      // Perform MEV analysis via TradingService
      // This is a placeholder - actual implementation would analyze MEV risks
      const analysisResult = {
        riskLevel: 'MEDIUM',
        recommendation: 'Proceed with caution',
        timestamp: new Date()
      }

      // Store analysis result in Redis for quick retrieval
      await RedisService.setJSON(
        `mev_analysis:${userId}:${job.id}`,
        analysisResult,
        300 // 5 minutes TTL
      )

      logQueue('MEV analysis job completed', userId, {
        jobId: job.id,
        riskLevel: analysisResult.riskLevel
      })

    } catch (error) {
      logQueue('MEV analysis job failed', userId, {
        jobId: job.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      throw error
    }
  }

  /**
   * Process notification job
   */
  private async processNotificationJob(job: Job<NotificationJobData>): Promise<void> {
    const { NotificationService } = await import('@/services/notificationService')
    const { userId, type, priority, title, message, channels, metadata } = job.data

    logQueue('Processing notification job', userId, {
      jobId: job.id,
      type,
      priority
    })

    try {
      await NotificationService.sendNotification(
        userId,
        type as any,
        priority as any,
        title,
        message,
        metadata
      )

      logQueue('Notification job completed', userId, {
        jobId: job.id
      })

    } catch (error) {
      logQueue('Notification job failed', userId, {
        jobId: job.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      throw error
    }
  }

  /**
   * Process alert job
   */
  private async processAlertJob(job: Job<AlertProcessingJobData>): Promise<void> {
    const { alertId, userId, alertType, conditions } = job.data

    logQueue('Processing alert job', userId, {
      jobId: job.id,
      alertId,
      alertType
    })

    try {
      // Process alert conditions and trigger notifications if needed
      // This is a placeholder for alert processing logic
      
      await DatabaseService.client.alert.update({
        where: { id: alertId },
        data: {
          read: false // Mark as unread to ensure user sees it
        }
      })

      logQueue('Alert job completed', userId, {
        jobId: job.id,
        alertId
      })

    } catch (error) {
      logQueue('Alert job failed', userId, {
        jobId: job.id,
        alertId,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      throw error
    }
  }

  /**
   * Process portfolio update job
   */
  private async processPortfolioUpdateJob(job: Job<PortfolioUpdateJobData>): Promise<void> {
    const { RiskService } = await import('@/services/riskService')
    const { userId, recalculateRisk, updatePositions } = job.data

    logQueue('Processing portfolio update job', userId, {
      jobId: job.id,
      recalculateRisk,
      updatePositions
    })

    try {
      if (recalculateRisk) {
        await RiskService.calculatePortfolioRisk(userId)
      }

      if (updatePositions) {
        // Update position values and P&L
        const positions = await DatabaseService.client.position.findMany({
          where: { userId, status: 'ACTIVE' }
        })

        for (const position of positions) {
          const { PriceService } = await import('@/services/priceService')
          const marketData = await PriceService.getTokenMarketData(position.tokenAddress)
          
          if (marketData) {
            const currentPrice = marketData.price
            const entryPrice = parseFloat(position.entryPrice.toString())
            const quantity = parseFloat(position.quantity.toString())
            
            await DatabaseService.client.position.update({
              where: { id: position.id },
              data: {
                currentPrice,
                pnl: (currentPrice - entryPrice) * quantity,
                pnlPercent: ((currentPrice / entryPrice) - 1) * 100
              }
            })
          }
        }
      }

      logQueue('Portfolio update job completed', userId, {
        jobId: job.id
      })

    } catch (error) {
      logQueue('Portfolio update job failed', userId, {
        jobId: job.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      throw error
    }
  }

  /**
   * Process cleanup job
   */
  private async processCleanupJob(job: Job): Promise<void> {
    logQueue('Processing cleanup job', undefined, {
      jobId: job.id
    })

    try {
      // Clean up old completed jobs
      for (const [queueName, queue] of this.queues) {
        await queue.clean(24 * 60 * 60 * 1000, 100, 'completed') // Clean completed jobs older than 24 hours
        await queue.clean(7 * 24 * 60 * 60 * 1000, 50, 'failed') // Clean failed jobs older than 7 days
      }

      // Clean up old Redis cache entries
      const keys = await RedisService.mainClient.keys('price:*')
      if (keys.length > 1000) {
        // Remove oldest price cache entries
        const oldKeys = keys.slice(0, 500)
        if (oldKeys.length > 0) {
          await RedisService.mainClient.del(...oldKeys)
        }
      }

      logQueue('Cleanup job completed', undefined, {
        jobId: job.id,
        cleanedPriceKeys: Math.min(keys.length, 500)
      })

    } catch (error) {
      logQueue('Cleanup job failed', undefined, {
        jobId: job.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      throw error
    }
  }

  /**
   * Schedule recurring jobs
   */
  public async scheduleRecurringJobs(): Promise<void> {
    try {
      // Schedule price update jobs every 5 seconds for high-priority tokens
      await this.addPriceUpdateJob(
        {
          tokenAddresses: [], // Will be populated by active positions
          priority: 'high'
        },
        {
          repeat: { every: 5000 } // 5 seconds
        }
      )

      // Schedule portfolio updates every 30 seconds
      const cleanupQueue = this.queues.get(QueueName.CLEANUP)
      if (cleanupQueue) {
        await cleanupQueue.add(
          'cleanup',
          {},
          {
            repeat: { cron: '0 */6 * * *' }, // Every 6 hours
            jobId: 'recurring-cleanup'
          }
        )
      }

      logQueue('Recurring jobs scheduled')

    } catch (error) {
      logQueue('Failed to schedule recurring jobs', undefined, {
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      throw error
    }
  }

  /**
   * Get queue statistics
   */
  public async getQueueStats(): Promise<Record<string, any>> {
    const stats: Record<string, any> = {}

    for (const [queueName, queue] of this.queues) {
      const [waiting, active, completed, failed, delayed] = await Promise.all([
        queue.getWaiting(),
        queue.getActive(),
        queue.getCompleted(),
        queue.getFailed(),
        queue.getDelayed()
      ])

      stats[queueName] = {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        delayed: delayed.length
      }
    }

    return stats
  }

  /**
   * Health check
   */
  public async healthCheck(): Promise<boolean> {
    try {
      if (!this.isInitialized) return false

      // Check if all queues are responsive
      for (const [queueName, queue] of this.queues) {
        await queue.getWaiting()
      }

      // Check if all workers are running
      for (const [queueName, worker] of this.workers) {
        if (worker.isRunning() === false) {
          logger.error(`Worker ${queueName} is not running`)
          return false
        }
      }

      return true
    } catch (error) {
      logger.error('Queue manager health check failed:', error)
      return false
    }
  }

  /**
   * Shutdown gracefully
   */
  public async shutdown(): Promise<void> {
    try {
      logQueue('Shutting down queue manager')

      // Close all workers
      for (const [queueName, worker] of this.workers) {
        await worker.close()
        logQueue(`Worker ${queueName} closed`)
      }

      // Close all queue events
      for (const [queueName, events] of this.queueEvents) {
        await events.close()
        logQueue(`Queue events ${queueName} closed`)
      }

      // Close all queues
      for (const [queueName, queue] of this.queues) {
        await queue.close()
        logQueue(`Queue ${queueName} closed`)
      }

      this.workers.clear()
      this.queueEvents.clear()
      this.queues.clear()
      this.isInitialized = false

      logQueue('Queue manager shutdown completed')

    } catch (error) {
      logger.error('Error during queue manager shutdown:', error)
    }
  }
}

// Export singleton instance
export const QueueManager = QueueManagerClass.getInstance()

// Import DatabaseService after exports to avoid circular dependencies
import { DatabaseService } from '@/services/database'