import { z } from 'zod'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

// Environment validation schema
const envSchema = z.object({
  // Server configuration
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().transform(Number).default('3001'),
  
  // Database configuration
  DATABASE_URL: z.string().min(1, 'DATABASE_URL is required'),
  
  // Redis configuration
  REDIS_URL: z.string().default('redis://localhost:6379'),
  
  // JWT configuration
  JWT_SECRET: z.string().min(32, 'JWT_SECRET must be at least 32 characters'),
  JWT_EXPIRES_IN: z.string().default('7d'),
  JWT_REFRESH_SECRET: z.string().min(32, 'JWT_REFRESH_SECRET must be at least 32 characters'),
  JWT_REFRESH_EXPIRES_IN: z.string().default('30d'),
  
  // CORS configuration
  CORS_ORIGIN: z.string().default('http://localhost:3000'),
  
  // Solana configuration
  SOLANA_RPC_URL: z.string().url('SOLANA_RPC_URL must be a valid URL'),
  SOLANA_WS_URL: z.string().url('SOLANA_WS_URL must be a valid URL'),
  
  // Wallet configuration (support both WALLET_PRIVATE_KEY and ENCRYPTED_PRIVATE_KEY)
  WALLET_PRIVATE_KEY: z.string().min(1).optional(),
  ENCRYPTED_PRIVATE_KEY: z.string().min(1).optional(),
  WALLET_PASSWORD: z.string().optional(),
  TRADING_WALLET_ADDRESS: z.string().min(1, 'TRADING_WALLET_ADDRESS is required'),
  SOLANA_FEE_ACCOUNT: z.string().optional(),
  
  // Jupiter API configuration
  JUPITER_API_URL: z.string().url().default('https://lite-api.jup.ag'),
  
  // Helius configuration (extracted from RPC URL)
  HELIUS_RPC_URL: z.string().url('HELIUS_RPC_URL must be a valid URL').optional(),
  HELIUS_API_KEY: z.string().min(1, 'HELIUS_API_KEY is required').optional(),
  HELIUS_WS_URL: z.string().url('HELIUS_WS_URL must be a valid URL').optional(),
  
  // Rate limiting
  RATE_LIMIT_WINDOW_MS: z.string().transform(Number).default('900000'), // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: z.string().transform(Number).default('100'),
  
  // Logging
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  
  // Security
  BCRYPT_ROUNDS: z.string().transform(Number).default('12'),
  
  // WebSocket
  WS_HEARTBEAT_INTERVAL: z.string().transform(Number).default('30000'), // 30 seconds
  
  // Trading configuration
  MAX_SLIPPAGE_BPS: z.string().transform(Number).default('300'), // 3%
  DEFAULT_PRIORITY_FEE: z.string().transform(Number).default('1000'), // 0.001 SOL
  
  // Queue configuration
  QUEUE_REDIS_URL: z.string().optional(),
})

// Validate environment variables
const envValidation = envSchema.safeParse(process.env)

if (!envValidation.success) {
  console.error('❌ Invalid environment variables:')
  envValidation.error.issues.forEach((issue) => {
    console.error(`  ${issue.path.join('.')}: ${issue.message}`)
  })
  process.exit(1)
}

const env = envValidation.data

// Additional validation: require at least one wallet key
if (!env.WALLET_PRIVATE_KEY && !env.ENCRYPTED_PRIVATE_KEY) {
  console.error('❌ Either WALLET_PRIVATE_KEY or ENCRYPTED_PRIVATE_KEY is required')
  process.exit(1)
}

// Helper function to extract API key from Helius URL
function extractApiKeyFromUrl(url: string): string {
  try {
    const urlObj = new URL(url)
    const apiKey = urlObj.searchParams.get('api-key')
    return apiKey || 'missing-api-key'
  } catch {
    return 'missing-api-key'
  }
}

// Export configuration object
export const config = {
  // Server
  nodeEnv: env.NODE_ENV,
  port: env.PORT,
  
  // Database
  database: {
    url: env.DATABASE_URL,
  },
  
  // Redis
  redis: {
    url: env.REDIS_URL,
    queueUrl: env.QUEUE_REDIS_URL || env.REDIS_URL,
  },
  
  // JWT
  jwt: {
    secret: env.JWT_SECRET,
    expiresIn: env.JWT_EXPIRES_IN,
    refreshSecret: env.JWT_REFRESH_SECRET,
    refreshExpiresIn: env.JWT_REFRESH_EXPIRES_IN,
  },
  
  // CORS
  cors: {
    origin: env.CORS_ORIGIN.split(',').map(origin => origin.trim()),
  },
  
  // Solana
  solana: {
    rpcUrl: env.SOLANA_RPC_URL,
    wsUrl: env.SOLANA_WS_URL,
  },
  
  // Wallet
  wallet: {
    privateKey: env.WALLET_PRIVATE_KEY || null,
    encryptedPrivateKey: env.ENCRYPTED_PRIVATE_KEY || null,
    walletPassword: env.WALLET_PASSWORD || null,
    address: env.TRADING_WALLET_ADDRESS,
    feeAccount: env.SOLANA_FEE_ACCOUNT,
  },
  
  // Jupiter
  jupiter: {
    apiUrl: env.JUPITER_API_URL,
  },
  
  // Helius (extract API key from RPC URL if not provided separately)
  helius: {
    rpcUrl: env.HELIUS_RPC_URL || env.SOLANA_RPC_URL,
    apiKey: env.HELIUS_API_KEY || extractApiKeyFromUrl(env.HELIUS_RPC_URL || env.SOLANA_RPC_URL),
    wsUrl: env.HELIUS_WS_URL || env.SOLANA_WS_URL,
  },
  
  // Rate limiting
  rateLimit: {
    windowMs: env.RATE_LIMIT_WINDOW_MS,
    maxRequests: env.RATE_LIMIT_MAX_REQUESTS,
  },
  
  // Logging
  logging: {
    level: env.LOG_LEVEL,
  },
  
  // Security
  security: {
    bcryptRounds: env.BCRYPT_ROUNDS,
  },
  
  // WebSocket
  websocket: {
    heartbeatInterval: env.WS_HEARTBEAT_INTERVAL,
  },
  
  // Trading
  trading: {
    maxSlippageBps: env.MAX_SLIPPAGE_BPS,
    defaultPriorityFee: env.DEFAULT_PRIORITY_FEE,
  },
} as const

// Type for configuration
export type Config = typeof config
