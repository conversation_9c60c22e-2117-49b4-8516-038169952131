#!/usr/bin/env tsx

/**
 * Standalone Environment Validation Script
 * 
 * This script can be run independently to validate the environment
 * configuration without starting the full application.
 * 
 * Usage:
 *   npm run validate:env
 *   or
 *   npx tsx src/scripts/validateEnvironment.ts
 */

import dotenv from 'dotenv'
import { EnvironmentValidator } from '@/services/environmentValidator'
import { logger } from '@/utils/logger'

// Load environment variables
dotenv.config()

async function main() {
  try {
    console.log('🔍 MemeTrader Pro Environment Validation')
    console.log('========================================\n')

    // Run comprehensive validation
    const summary = await EnvironmentValidator.validateEnvironment()

    console.log('\n========================================')
    
    if (summary.success) {
      console.log('✅ ALL VALIDATIONS PASSED')
      console.log('The environment is properly configured and ready for use.')
      
      if (summary.warnings.length > 0) {
        console.log('\n⚠️  WARNINGS:')
        summary.warnings.forEach(warning => console.log(`  - ${warning}`))
        console.log('\nWarnings do not prevent startup but should be reviewed.')
      }
      
      process.exit(0)
    } else {
      console.log('❌ VALIDATION FAILED')
      console.log('The following issues must be resolved before starting the application:')
      console.log('')
      
      summary.errors.forEach(error => console.log(`  ❌ ${error}`))
      
      console.log('\n📖 TROUBLESHOOTING GUIDE:')
      console.log('=======================')
      
      // Provide specific troubleshooting guidance
      if (summary.errors.some(e => e.includes('Database'))) {
        console.log('\n🗄️  DATABASE ISSUES:')
        console.log('  1. Ensure PostgreSQL is running')
        console.log('  2. Verify DATABASE_URL is correct')
        console.log('  3. Run database migrations: npm run db:migrate')
        console.log('  4. Check database credentials and permissions')
      }
      
      if (summary.errors.some(e => e.includes('Redis'))) {
        console.log('\n🔄 REDIS ISSUES:')
        console.log('  1. Ensure Redis server is running')
        console.log('  2. Verify REDIS_URL is correct')
        console.log('  3. Check Redis authentication if required')
        console.log('  4. Test Redis connection: redis-cli ping')
      }
      
      if (summary.errors.some(e => e.includes('Wallet'))) {
        console.log('\n👛 WALLET ISSUES:')
        console.log('  1. Verify WALLET_PRIVATE_KEY is base58 encoded')
        console.log('  2. Ensure TRADING_WALLET_ADDRESS matches private key')
        console.log('  3. Check wallet has sufficient SOL balance (min 0.01 SOL)')
        console.log('  4. Verify wallet is on the correct network (mainnet/devnet)')
      }
      
      if (summary.errors.some(e => e.includes('Solana'))) {
        console.log('\n⛓️  SOLANA RPC ISSUES:')
        console.log('  1. Verify SOLANA_RPC_URL is accessible')
        console.log('  2. Check HELIUS_API_KEY is valid')
        console.log('  3. Test RPC endpoint manually')
        console.log('  4. Ensure network connectivity')
      }
      
      if (summary.errors.some(e => e.includes('Jupiter'))) {
        console.log('\n🪐 JUPITER API ISSUES:')
        console.log('  1. Verify Jupiter API is accessible')
        console.log('  2. Check network connectivity')
        console.log('  3. Ensure JUPITER_API_URL is correct')
        console.log('  4. Try accessing Jupiter API manually')
      }
      
      if (summary.errors.some(e => e.includes('Security'))) {
        console.log('\n🔒 SECURITY ISSUES:')
        console.log('  1. Ensure JWT_SECRET is at least 32 characters')
        console.log('  2. Use different secrets for JWT_SECRET and JWT_REFRESH_SECRET')
        console.log('  3. Use strong, unique secrets in production')
        console.log('  4. Verify bcrypt rounds are between 10-15')
      }
      
      console.log('\n📝 ENVIRONMENT FILE:')
      console.log('  Copy exampleenv.md to .env and fill in the required values')
      console.log('  Ensure all required environment variables are set')
      
      console.log('\n🆘 NEED HELP?')
      console.log('  1. Check the documentation in /docs')
      console.log('  2. Review the setup guide in README.md')
      console.log('  3. Verify all prerequisites are installed')
      
      process.exit(1)
    }
    
  } catch (error) {
    console.error('\n💥 VALIDATION SCRIPT FAILED:')
    console.error(error instanceof Error ? error.message : 'Unknown error')
    console.error('\nThis indicates a serious configuration or code issue.')
    process.exit(1)
  }
}

// Handle script termination
process.on('SIGINT', () => {
  console.log('\n\n👋 Validation interrupted by user')
  process.exit(0)
})

process.on('SIGTERM', () => {
  console.log('\n\n👋 Validation terminated')
  process.exit(0)
})

// Run the validation
main().catch((error) => {
  console.error('Unexpected error:', error)
  process.exit(1)
})