#!/usr/bin/env tsx

/**
 * Database Schema Verification Script
 * 
 * This script verifies that the database schema matches expectations
 * and all required tables, indexes, and constraints are in place.
 * 
 * Usage:
 *   npm run verify:schema
 *   or
 *   npx tsx src/scripts/verifySchema.ts
 */

import { PrismaClient } from '@prisma/client'
import { logger } from '@/utils/logger'

const prisma = new PrismaClient()

interface TableInfo {
  table_name: string
  column_name: string
  data_type: string
  is_nullable: string
  column_default: string | null
}

interface IndexInfo {
  indexname: string
  tablename: string
  indexdef: string
}

interface ConstraintInfo {
  constraint_name: string
  table_name: string
  constraint_type: string
}

class SchemaVerifier {
  private errors: string[] = []
  private warnings: string[] = []

  async verifySchema(): Promise<boolean> {
    try {
      console.log('🔍 Starting database schema verification...\n')

      // Test basic connectivity
      await this.testConnectivity()

      // Verify core tables exist
      await this.verifyTables()

      // Verify critical columns
      await this.verifyColumns()

      // Verify indexes
      await this.verifyIndexes()

      // Verify constraints and foreign keys
      await this.verifyConstraints()

      // Verify enums
      await this.verifyEnums()

      // Test data integrity functions
      await this.verifyFunctions()

      // Run basic CRUD operations
      await this.testCrudOperations()

      // Summary
      this.printSummary()

      return this.errors.length === 0

    } catch (error) {
      console.error('❌ Schema verification failed:', error)
      return false
    } finally {
      await prisma.$disconnect()
    }
  }

  private async testConnectivity(): Promise<void> {
    try {
      await prisma.$queryRaw`SELECT 1 as test`
      console.log('✅ Database connectivity verified')
    } catch (error) {
      this.errors.push('Database connectivity failed')
      throw error
    }
  }

  private async verifyTables(): Promise<void> {
    console.log('📋 Verifying tables...')
    
    const expectedTables = [
      'User', 'UserPreferences', 'Portfolio', 'Position', 'ExitStrategy',
      'CustomStrategy', 'Transaction', 'Alert', 'Watchlist', 'TradingPreset',
      'PriceHistory', 'SystemConfig'
    ]

    const tables = await prisma.$queryRaw<Array<{ tablename: string }>>`
      SELECT tablename 
      FROM pg_catalog.pg_tables 
      WHERE schemaname = 'public'
    `

    const existingTables = tables.map(t => t.tablename)

    for (const expectedTable of expectedTables) {
      if (existingTables.includes(expectedTable)) {
        console.log(`  ✅ Table ${expectedTable} exists`)
      } else {
        this.errors.push(`Missing table: ${expectedTable}`)
        console.log(`  ❌ Table ${expectedTable} missing`)
      }
    }

    // Check for unexpected tables (could indicate migration issues)
    const unexpectedTables = existingTables.filter(t => 
      !expectedTables.includes(t) && 
      !t.startsWith('_prisma') &&
      t !== 'spatial_ref_sys' // PostGIS table if extension is loaded
    )

    if (unexpectedTables.length > 0) {
      this.warnings.push(`Unexpected tables found: ${unexpectedTables.join(', ')}`)
    }
  }

  private async verifyColumns(): Promise<void> {
    console.log('📊 Verifying critical columns...')

    const criticalColumns = [
      { table: 'User', column: 'id', type: 'text' },
      { table: 'User', column: 'email', type: 'text' },
      { table: 'User', column: 'walletAddress', type: 'text' },
      { table: 'Position', column: 'entryPrice', type: 'numeric' },
      { table: 'Position', column: 'currentPrice', type: 'numeric' },
      { table: 'Transaction', column: 'hash', type: 'text' },
      { table: 'Transaction', column: 'fees', type: 'jsonb' },
      { table: 'TradingPreset', column: 'buySettings', type: 'jsonb' },
      { table: 'SystemConfig', column: 'value', type: 'jsonb' }
    ]

    for (const { table, column, type } of criticalColumns) {
      try {
        const result = await prisma.$queryRaw<TableInfo[]>`
          SELECT column_name, data_type, is_nullable, column_default
          FROM information_schema.columns 
          WHERE table_name = ${table} AND column_name = ${column}
        `

        if (result.length === 0) {
          this.errors.push(`Missing column ${table}.${column}`)
          console.log(`  ❌ Column ${table}.${column} missing`)
        } else {
          const col = result[0]
          if (col.data_type === type || (type === 'numeric' && col.data_type === 'numeric')) {
            console.log(`  ✅ Column ${table}.${column} (${col.data_type})`)
          } else {
            this.warnings.push(`Column ${table}.${column} has type ${col.data_type}, expected ${type}`)
          }
        }
      } catch (error) {
        this.errors.push(`Error checking column ${table}.${column}: ${error}`)
      }
    }
  }

  private async verifyIndexes(): Promise<void> {
    console.log('📇 Verifying indexes...')

    const criticalIndexes = [
      'User_email_key',
      'User_walletAddress_key',
      'Transaction_hash_key',
      'TradingPreset_name_key',
      'SystemConfig_key_key',
      'Position_userId_status_idx',
      'Transaction_userId_timestamp_idx'
    ]

    const indexes = await prisma.$queryRaw<IndexInfo[]>`
      SELECT indexname, tablename, indexdef
      FROM pg_indexes 
      WHERE schemaname = 'public'
    `

    const existingIndexes = indexes.map(i => i.indexname)

    for (const expectedIndex of criticalIndexes) {
      if (existingIndexes.includes(expectedIndex)) {
        console.log(`  ✅ Index ${expectedIndex} exists`)
      } else {
        this.errors.push(`Missing index: ${expectedIndex}`)
        console.log(`  ❌ Index ${expectedIndex} missing`)
      }
    }
  }

  private async verifyConstraints(): Promise<void> {
    console.log('🔗 Verifying constraints...')

    const constraints = await prisma.$queryRaw<ConstraintInfo[]>`
      SELECT constraint_name, table_name, constraint_type
      FROM information_schema.table_constraints 
      WHERE table_schema = 'public'
    `

    const primaryKeys = constraints.filter(c => c.constraint_type === 'PRIMARY KEY')
    const foreignKeys = constraints.filter(c => c.constraint_type === 'FOREIGN KEY')
    const uniqueKeys = constraints.filter(c => c.constraint_type === 'UNIQUE')

    console.log(`  ✅ Primary keys: ${primaryKeys.length}`)
    console.log(`  ✅ Foreign keys: ${foreignKeys.length}`)
    console.log(`  ✅ Unique constraints: ${uniqueKeys.length}`)

    // Verify critical constraints exist
    const criticalTables = ['User', 'Position', 'Transaction', 'TradingPreset']
    const tablesWithPK = primaryKeys.map(pk => pk.table_name)

    for (const table of criticalTables) {
      if (!tablesWithPK.includes(table)) {
        this.errors.push(`Missing primary key on table: ${table}`)
      }
    }
  }

  private async verifyEnums(): Promise<void> {
    console.log('🏷️  Verifying enums...')

    const expectedEnums = [
      'RiskLevel', 'PositionStatus', 'StrategyType', 'TransactionType',
      'AlertType', 'AlertPriority', 'PresetType', 'MEVProtectionLevel',
      'StrategyExecutionState'
    ]

    const enums = await prisma.$queryRaw<Array<{ typname: string }>>`
      SELECT typname 
      FROM pg_type 
      WHERE typtype = 'e'
    `

    const existingEnums = enums.map(e => e.typname)

    for (const expectedEnum of expectedEnums) {
      if (existingEnums.includes(expectedEnum)) {
        console.log(`  ✅ Enum ${expectedEnum} exists`)
      } else {
        this.errors.push(`Missing enum: ${expectedEnum}`)
        console.log(`  ❌ Enum ${expectedEnum} missing`)
      }
    }
  }

  private async verifyFunctions(): Promise<void> {
    console.log('⚙️  Verifying database functions...')

    const expectedFunctions = [
      'trigger_set_timestamp',
      'calculate_pnl_percentage',
      'calculate_position_age_hours'
    ]

    for (const functionName of expectedFunctions) {
      try {
        const result = await prisma.$queryRaw<Array<{ proname: string }>>`
          SELECT proname 
          FROM pg_proc 
          WHERE proname = ${functionName}
        `

        if (result.length > 0) {
          console.log(`  ✅ Function ${functionName} exists`)
        } else {
          this.warnings.push(`Missing function: ${functionName}`)
          console.log(`  ⚠️  Function ${functionName} missing`)
        }
      } catch (error) {
        this.warnings.push(`Error checking function ${functionName}: ${error}`)
      }
    }

    // Test PnL calculation function
    try {
      const result = await prisma.$queryRaw<Array<{ result: number }>>`
        SELECT calculate_pnl_percentage(100::DECIMAL, 110::DECIMAL) as result
      `
      
      if (result[0]?.result === 10) {
        console.log('  ✅ PnL calculation function working correctly')
      } else {
        this.warnings.push('PnL calculation function returned unexpected result')
      }
    } catch (error) {
      this.warnings.push(`PnL calculation function test failed: ${error}`)
    }
  }

  private async testCrudOperations(): Promise<void> {
    console.log('🧪 Testing basic CRUD operations...')

    try {
      // Test system config CRUD
      const testConfig = await prisma.systemConfig.create({
        data: {
          key: 'TEST_CONFIG',
          value: { test: true },
          description: 'Test configuration',
          category: 'test'
        }
      })

      const retrieved = await prisma.systemConfig.findUnique({
        where: { key: 'TEST_CONFIG' }
      })

      if (retrieved?.key === 'TEST_CONFIG') {
        console.log('  ✅ System config create/read operations working')
      } else {
        this.errors.push('System config CRUD test failed')
      }

      // Cleanup
      await prisma.systemConfig.delete({
        where: { key: 'TEST_CONFIG' }
      })

      console.log('  ✅ System config delete operation working')

    } catch (error) {
      this.errors.push(`CRUD operations test failed: ${error}`)
    }

    try {
      // Test trading preset read
      const presets = await prisma.tradingPreset.findMany()
      console.log(`  ✅ Trading presets query working (${presets.length} presets found)`)
    } catch (error) {
      this.errors.push(`Trading preset query failed: ${error}`)
    }
  }

  private printSummary(): void {
    console.log('\n========================================')
    console.log('SCHEMA VERIFICATION SUMMARY')
    console.log('========================================')

    if (this.errors.length === 0) {
      console.log('✅ All schema verifications passed!')
      console.log('The database schema is properly configured and ready for use.')
    } else {
      console.log('❌ Schema verification failed!')
      console.log('\nERRORS:')
      this.errors.forEach(error => console.log(`  ❌ ${error}`))
      console.log('\nPlease run database migrations or fix schema issues.')
    }

    if (this.warnings.length > 0) {
      console.log('\nWARNINGS:')
      this.warnings.forEach(warning => console.log(`  ⚠️  ${warning}`))
    }

    console.log('\n📖 TROUBLESHOOTING:')
    console.log('  - If tables are missing: Run "npm run db:migrate"')
    console.log('  - If functions are missing: Check init.sql execution')
    console.log('  - If data is missing: Run "npm run db:seed"')
    console.log('  - For fresh start: Run "npx prisma db push --force-reset"')
  }
}

async function main() {
  const verifier = new SchemaVerifier()
  const success = await verifier.verifySchema()
  process.exit(success ? 0 : 1)
}

// Handle script termination
process.on('SIGINT', () => {
  console.log('\n\n👋 Schema verification interrupted')
  process.exit(0)
})

// Run verification
main().catch((error) => {
  console.error('Unexpected error:', error)
  process.exit(1)
})