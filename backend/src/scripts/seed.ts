import { PrismaClient } from '@prisma/client'
import { PresetType, MEVProtectionLevel } from '@memetrader-pro/shared'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  try {
    // Create default trading presets
    console.log('Creating trading presets...')
    
    const presets = [
      {
        name: PresetType.DEFAULT,
        priorityFee: 0.001,
        slippageLimit: 1.0,
        mevProtectionLevel: MEVProtectionLevel.BASIC,
        locked: true,
        systemDefault: true,
        buySettings: {
          maxSlippage: 1.0,
          priorityFee: 0.001,
          mevProtection: true,
          simulateFirst: true,
          maxRetries: 3
        },
        sellSettings: {
          maxSlippage: 1.5,
          priorityFee: 0.001,
          mevProtection: true,
          simulateFirst: true,
          maxRetries: 3
        }
      },
      {
        name: PresetType.VOL,
        priorityFee: 0.002,
        slippageLimit: 3.0,
        mevProtectionLevel: MEVProtectionLevel.ADVANCED,
        brideAmount: 0.005,
        locked: true,
        systemDefault: false,
        buySettings: {
          maxSlippage: 3.0,
          priorityFee: 0.002,
          mevProtection: true,
          simulateFirst: true,
          maxRetries: 5
        },
        sellSettings: {
          maxSlippage: 5.0,
          priorityFee: 0.002,
          mevProtection: true,
          simulateFirst: true,
          maxRetries: 5
        }
      },
      {
        name: PresetType.DEAD,
        priorityFee: 0.0005,
        slippageLimit: 0.5,
        mevProtectionLevel: MEVProtectionLevel.NONE,
        locked: true,
        systemDefault: false,
        buySettings: {
          maxSlippage: 0.5,
          priorityFee: 0.0005,
          mevProtection: false,
          simulateFirst: true,
          maxRetries: 2
        },
        sellSettings: {
          maxSlippage: 1.0,
          priorityFee: 0.0005,
          mevProtection: false,
          simulateFirst: true,
          maxRetries: 2
        }
      },
      {
        name: PresetType.NUN,
        priorityFee: 0.003,
        slippageLimit: 5.0,
        mevProtectionLevel: MEVProtectionLevel.MAXIMUM,
        brideAmount: 0.01,
        locked: true,
        systemDefault: false,
        buySettings: {
          maxSlippage: 5.0,
          priorityFee: 0.003,
          mevProtection: true,
          simulateFirst: true,
          maxRetries: 3
        },
        sellSettings: {
          maxSlippage: 10.0,
          priorityFee: 0.003,
          mevProtection: true,
          simulateFirst: true,
          maxRetries: 3
        }
      },
      {
        name: PresetType.P5,
        priorityFee: 0.005,
        slippageLimit: 2.0,
        mevProtectionLevel: MEVProtectionLevel.MAXIMUM,
        brideAmount: 0.02,
        locked: true,
        systemDefault: false,
        buySettings: {
          maxSlippage: 2.0,
          priorityFee: 0.005,
          mevProtection: true,
          simulateFirst: true,
          maxRetries: 5
        },
        sellSettings: {
          maxSlippage: 3.0,
          priorityFee: 0.005,
          mevProtection: true,
          simulateFirst: true,
          maxRetries: 5
        }
      }
    ]

    for (const preset of presets) {
      await prisma.tradingPreset.upsert({
        where: { name: preset.name },
        update: preset,
        create: preset
      })
    }

    console.log('✅ Trading presets created')

    // Create system configurations
    console.log('Creating system configurations...')
    
    const systemConfigs = [
      {
        key: 'MAX_POSITION_SIZE',
        value: { percentage: 10, description: 'Maximum position size as percentage of portfolio' },
        description: 'Maximum allowed position size',
        category: 'risk'
      },
      {
        key: 'DEFAULT_SLIPPAGE',
        value: { percentage: 1.0, description: 'Default slippage tolerance' },
        description: 'Default slippage tolerance for trades',
        category: 'trading'
      },
      {
        key: 'MEV_PROTECTION_ENABLED',
        value: { enabled: true, description: 'Enable MEV protection by default' },
        description: 'MEV protection settings',
        category: 'security'
      },
      {
        key: 'STRATEGY_MONITORING_INTERVAL',
        value: { milliseconds: 500, description: 'Strategy monitoring interval' },
        description: 'How often to check strategy conditions',
        category: 'system'
      },
      {
        key: 'PRICE_UPDATE_INTERVAL',
        value: { milliseconds: 100, description: 'Price update interval' },
        description: 'How often to update token prices',
        category: 'system'
      }
    ]

    for (const config of systemConfigs) {
      await prisma.systemConfig.upsert({
        where: { key: config.key },
        update: config,
        create: config
      })
    }

    console.log('✅ System configurations created')

    // Create some sample price history data (for development)
    if (process.env.NODE_ENV === 'development') {
      console.log('Creating sample price history...')
      
      const sampleTokens = [
        'So11111111111111111111111111111111111111112', // SOL
        'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
        'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB'  // USDT
      ]

      const now = new Date()
      for (let i = 0; i < 24; i++) { // 24 hours of data
        const timestamp = new Date(now.getTime() - (i * 60 * 60 * 1000)) // Each hour
        
        for (const tokenAddress of sampleTokens) {
          // Generate sample price data
          const basePrice = tokenAddress === 'So11111111111111111111111111111111111111112' ? 100 : 1 // SOL vs stablecoins
          const randomVariation = (Math.random() - 0.5) * 0.1 // ±5% variation
          const price = basePrice * (1 + randomVariation)
          
          await prisma.priceHistory.upsert({
            where: {
              tokenAddress_timestamp: {
                tokenAddress,
                timestamp
              }
            },
            update: {
              price,
              priceUsd: price,
              volume24h: Math.random() * 1000000,
              marketCap: price * 500000000,
              change24h: randomVariation * 100
            },
            create: {
              tokenAddress,
              price,
              priceUsd: price,
              volume24h: Math.random() * 1000000,
              marketCap: price * 500000000,
              change24h: randomVariation * 100,
              timestamp
            }
          })
        }
      }

      console.log('✅ Sample price history created')
    }

    console.log('🎉 Database seeding completed successfully!')

  } catch (error) {
    console.error('❌ Error during seeding:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e)
    process.exit(1)
  })