import { logger } from '@/utils/logger'
import { RedisService } from '@/services/redis'
import { AppError } from '@/middleware/errorHandler'
import { config } from '@/config/environment'

export interface SlippageConfig {
  userDefined: number        // User-set tolerance (0.1% - 5.0%)
  marketBased: number       // Dynamic market conditions adjustment
  emergencyMax: number      // Hard cap (never exceed this)
  timeDecay: {
    enabled: boolean
    initialSlippage: number // Starting slippage
    decayRate: number      // % increase per minute
    maxDecay: number       // Maximum total decay
  }
  mevAdjustment: {
    enabled: boolean
    lowRisk: number        // Additional slippage for low MEV risk
    mediumRisk: number     // Additional slippage for medium MEV risk
    highRisk: number       // Additional slippage for high MEV risk
  }
  volatilityAdjustment: {
    enabled: boolean
    low: number           // < 5% volatility adjustment
    medium: number        // 5-20% volatility adjustment
    high: number          // > 20% volatility adjustment
  }
}

export interface SlippageAnalysis {
  finalSlippage: number           // Final calculated slippage
  breakdown: {
    userDefined: number
    marketAdjustment: number
    timeDecayAdjustment: number
    mevAdjustment: number
    volatilityAdjustment: number
    emergencyCap: number
  }
  reasoning: string[]             // Human-readable explanations
  riskLevel: 'low' | 'medium' | 'high' | 'extreme'
  recommendation: 'proceed' | 'increase_slippage' | 'reduce_trade_size' | 'wait_for_better_conditions'
  confidence: number              // 0-1, confidence in the slippage calculation
  historicalComparison: {
    averageForPair: number
    percentile: number           // Where this slippage sits in historical distribution
  }
}

export interface MarketCondition {
  volatility24h: number
  liquidityDepth: string         // 'shallow' | 'moderate' | 'deep' | 'very_deep'
  networkCongestion: 'low' | 'medium' | 'high'
  mevRisk: 'low' | 'medium' | 'high'
  timeOfDay: 'peak' | 'normal' | 'quiet'
  tradingVolume: 'low' | 'medium' | 'high'
}

class SlippageProtectionService {
  private static instance: SlippageProtectionService
  private defaultConfig: SlippageConfig
  private userConfigs: Map<string, SlippageConfig> = new Map()
  private marketConditionCache: Map<string, MarketCondition> = new Map()

  private constructor() {
    this.defaultConfig = {
      userDefined: 0.5,           // 0.5% default
      marketBased: 0,             // No market adjustment by default
      emergencyMax: 5.0,          // 5% absolute maximum
      timeDecay: {
        enabled: true,
        initialSlippage: 0.5,     // Start at 0.5%
        decayRate: 0.1,           // 0.1% per minute
        maxDecay: 2.0             // Max 2% additional
      },
      mevAdjustment: {
        enabled: true,
        lowRisk: 0.1,             // +0.1% for low MEV risk
        mediumRisk: 0.3,          // +0.3% for medium MEV risk
        highRisk: 0.8             // +0.8% for high MEV risk
      },
      volatilityAdjustment: {
        enabled: true,
        low: 0.1,                 // +0.1% for low volatility
        medium: 0.3,              // +0.3% for medium volatility
        high: 0.8                 // +0.8% for high volatility
      }
    }
  }

  public static getInstance(): SlippageProtectionService {
    if (!SlippageProtectionService.instance) {
      SlippageProtectionService.instance = new SlippageProtectionService()
    }
    return SlippageProtectionService.instance
  }

  /**
   * Calculate optimal slippage based on all factors
   */
  public async calculateOptimalSlippage(params: {
    inputMint: string
    outputMint: string
    amount: number
    userSlippage?: number
    userId?: string
    mevRisk?: 'low' | 'medium' | 'high'
    quoteTimestamp?: number
    routeComplexity?: number
  }): Promise<SlippageAnalysis> {
    try {
      logger.debug('Calculating optimal slippage', {
        tokenPair: `${params.inputMint.slice(0, 8)}.../${params.outputMint.slice(0, 8)}...`,
        amount: params.amount,
        userSlippage: params.userSlippage
      })

      // Get user-specific or default configuration
      const slippageConfig = await this.getSlippageConfig(params.userId)
      
      // Get current market conditions
      const marketConditions = await this.getMarketConditions(params.inputMint, params.outputMint)
      
      // Get historical slippage data
      const historicalData = await this.getHistoricalSlippageData(params.inputMint, params.outputMint, params.amount)

      // Start with user-defined slippage
      const userDefined = params.userSlippage !== undefined 
        ? Math.max(0.1, Math.min(5.0, params.userSlippage))  // Clamp to 0.1-5%
        : slippageConfig.userDefined

      let breakdown = {
        userDefined,
        marketAdjustment: 0,
        timeDecayAdjustment: 0,
        mevAdjustment: 0,
        volatilityAdjustment: 0,
        emergencyCap: 0
      }

      let reasoning: string[] = []
      reasoning.push(`Base slippage: ${userDefined}%`)

      // Market-based adjustment
      if (slippageConfig.marketBased > 0) {
        const marketAdjustment = this.calculateMarketAdjustment(marketConditions, slippageConfig)
        breakdown.marketAdjustment = marketAdjustment
        if (marketAdjustment > 0) {
          reasoning.push(`Market conditions: +${marketAdjustment.toFixed(2)}%`)
        }
      }

      // Time decay adjustment
      if (slippageConfig.timeDecay.enabled && params.quoteTimestamp) {
        const timeDecayAdjustment = this.calculateTimeDecayAdjustment(params.quoteTimestamp, slippageConfig)
        breakdown.timeDecayAdjustment = timeDecayAdjustment
        if (timeDecayAdjustment > 0) {
          reasoning.push(`Quote age: +${timeDecayAdjustment.toFixed(2)}%`)
        }
      }

      // MEV risk adjustment
      if (slippageConfig.mevAdjustment.enabled && params.mevRisk) {
        const mevAdjustment = this.calculateMEVAdjustment(params.mevRisk, slippageConfig)
        breakdown.mevAdjustment = mevAdjustment
        if (mevAdjustment > 0) {
          reasoning.push(`MEV protection: +${mevAdjustment.toFixed(2)}%`)
        }
      }

      // Volatility adjustment
      if (slippageConfig.volatilityAdjustment.enabled) {
        const volatilityAdjustment = this.calculateVolatilityAdjustment(marketConditions, slippageConfig)
        breakdown.volatilityAdjustment = volatilityAdjustment
        if (volatilityAdjustment > 0) {
          reasoning.push(`Volatility protection: +${volatilityAdjustment.toFixed(2)}%`)
        }
      }

      // Route complexity adjustment
      if (params.routeComplexity && params.routeComplexity > 2) {
        const routeAdjustment = (params.routeComplexity - 2) * 0.1
        breakdown.marketAdjustment += routeAdjustment
        reasoning.push(`Route complexity: +${routeAdjustment.toFixed(2)}%`)
      }

      // Calculate total before emergency cap
      let finalSlippage = Object.values(breakdown).reduce((sum, val) => sum + val, 0)

      // Apply emergency cap
      if (finalSlippage > slippageConfig.emergencyMax) {
        breakdown.emergencyCap = slippageConfig.emergencyMax - finalSlippage + breakdown.emergencyCap
        finalSlippage = slippageConfig.emergencyMax
        reasoning.push(`Emergency cap applied: ${slippageConfig.emergencyMax}%`)
      }

      // Determine risk level
      const riskLevel = this.determineRiskLevel(finalSlippage, marketConditions)
      
      // Generate recommendation
      const recommendation = this.generateRecommendation(finalSlippage, riskLevel, marketConditions)
      
      // Calculate confidence score
      const confidence = this.calculateConfidence(marketConditions, historicalData, finalSlippage)

      const analysis: SlippageAnalysis = {
        finalSlippage: Math.round(finalSlippage * 1000) / 1000, // Round to 3 decimal places
        breakdown,
        reasoning,
        riskLevel,
        recommendation,
        confidence,
        historicalComparison: {
          averageForPair: historicalData.averageSlippage,
          percentile: this.calculatePercentile(finalSlippage, historicalData.distribution)
        }
      }

      logger.debug('Slippage calculation completed', {
        finalSlippage: analysis.finalSlippage,
        riskLevel: analysis.riskLevel,
        recommendation: analysis.recommendation,
        confidence: analysis.confidence
      })

      return analysis

    } catch (error) {
      logger.error('Slippage calculation failed:', error)
      throw new AppError('Failed to calculate optimal slippage', 500, 'SLIPPAGE_CALCULATION_FAILED')
    }
  }

  /**
   * Calculate market-based adjustment
   */
  private calculateMarketAdjustment(conditions: MarketCondition, config: SlippageConfig): number {
    let adjustment = 0

    // Network congestion adjustment
    switch (conditions.networkCongestion) {
      case 'high': adjustment += 0.2; break
      case 'medium': adjustment += 0.1; break
      case 'low': adjustment += 0; break
    }

    // Liquidity depth adjustment
    switch (conditions.liquidityDepth) {
      case 'shallow': adjustment += 0.3; break
      case 'moderate': adjustment += 0.1; break
      case 'deep': adjustment += 0; break
      case 'very_deep': adjustment -= 0.1; break
    }

    // Time of day adjustment
    switch (conditions.timeOfDay) {
      case 'peak': adjustment += 0.1; break
      case 'normal': adjustment += 0; break
      case 'quiet': adjustment += 0.2; break // Less liquidity during quiet hours
    }

    // Trading volume adjustment
    switch (conditions.tradingVolume) {
      case 'low': adjustment += 0.2; break
      case 'medium': adjustment += 0; break
      case 'high': adjustment -= 0.1; break
    }

    return Math.max(0, adjustment)
  }

  /**
   * Calculate time decay adjustment
   */
  private calculateTimeDecayAdjustment(quoteTimestamp: number, config: SlippageConfig): number {
    if (!config.timeDecay.enabled) return 0

    const ageSeconds = (Date.now() - quoteTimestamp) / 1000
    const ageMinutes = ageSeconds / 60

    // No adjustment for fresh quotes (< 30 seconds)
    if (ageSeconds < 30) return 0

    const decayAdjustment = Math.min(
      ageMinutes * config.timeDecay.decayRate,
      config.timeDecay.maxDecay
    )

    return decayAdjustment
  }

  /**
   * Calculate MEV risk adjustment
   */
  private calculateMEVAdjustment(mevRisk: 'low' | 'medium' | 'high', config: SlippageConfig): number {
    if (!config.mevAdjustment.enabled) return 0

    switch (mevRisk) {
      case 'low': return config.mevAdjustment.lowRisk
      case 'medium': return config.mevAdjustment.mediumRisk
      case 'high': return config.mevAdjustment.highRisk
      default: return 0
    }
  }

  /**
   * Calculate volatility adjustment
   */
  private calculateVolatilityAdjustment(conditions: MarketCondition, config: SlippageConfig): number {
    if (!config.volatilityAdjustment.enabled) return 0

    const volatility = conditions.volatility24h

    if (volatility < 5) return config.volatilityAdjustment.low
    if (volatility < 20) return config.volatilityAdjustment.medium
    return config.volatilityAdjustment.high
  }

  /**
   * Get current market conditions
   */
  private async getMarketConditions(inputMint: string, outputMint: string): Promise<MarketCondition> {
    const cacheKey = `market_conditions:${inputMint}:${outputMint}`
    
    // Try cache first
    let conditions = this.marketConditionCache.get(cacheKey)
    if (conditions) return conditions

    try {
      // Get from Redis cache
      const cached = await RedisService.getJSON<MarketCondition>(cacheKey)
      if (cached) {
        this.marketConditionCache.set(cacheKey, cached)
        return cached
      }

      // Generate market conditions (in real implementation, this would call external APIs)
      conditions = await this.fetchMarketConditions(inputMint, outputMint)
      
      // Cache for 1 minute
      await RedisService.setJSON(cacheKey, conditions, 60)
      this.marketConditionCache.set(cacheKey, conditions)
      
      return conditions

    } catch (error) {
      logger.debug('Failed to get market conditions, using defaults:', error)
      
      // Return conservative defaults
      return {
        volatility24h: 15, // Conservative 15%
        liquidityDepth: 'moderate',
        networkCongestion: 'medium',
        mevRisk: 'medium',
        timeOfDay: 'normal',
        tradingVolume: 'medium'
      }
    }
  }

  /**
   * Fetch market conditions from external sources
   */
  private async fetchMarketConditions(inputMint: string, outputMint: string): Promise<MarketCondition> {
    // In a real implementation, this would:
    // 1. Call price APIs for volatility data
    // 2. Check network congestion via RPC calls
    // 3. Analyze trading volume from DEX data
    // 4. Determine time-based patterns
    
    // For now, return simulated data
    const hour = new Date().getHours()
    let timeOfDay: 'peak' | 'normal' | 'quiet'
    if ((hour >= 8 && hour <= 11) || (hour >= 14 && hour <= 17)) {
      timeOfDay = 'peak'
    } else if (hour >= 22 || hour <= 6) {
      timeOfDay = 'quiet'
    } else {
      timeOfDay = 'normal'
    }

    return {
      volatility24h: Math.random() * 30 + 5, // 5-35% volatility
      liquidityDepth: ['shallow', 'moderate', 'deep', 'very_deep'][Math.floor(Math.random() * 4)] as any,
      networkCongestion: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as any,
      mevRisk: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as any,
      timeOfDay,
      tradingVolume: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as any
    }
  }

  /**
   * Get historical slippage data
   */
  private async getHistoricalSlippageData(inputMint: string, outputMint: string, amount: number): Promise<{
    averageSlippage: number
    distribution: number[]
  }> {
    const cacheKey = `historical_slippage:${inputMint}:${outputMint}:${Math.floor(amount / 1000000)}`
    
    try {
      const cached = await RedisService.getJSON<{ averageSlippage: number; distribution: number[] }>(cacheKey)
      if (cached) return cached

      // In real implementation, query historical data
      // For now, return simulated data
      const data = {
        averageSlippage: 0.3 + Math.random() * 0.4, // 0.3-0.7%
        distribution: Array.from({ length: 100 }, () => Math.random() * 2) // 0-2% distribution
      }

      // Cache for 1 hour
      await RedisService.setJSON(cacheKey, data, 3600)
      return data

    } catch (error) {
      logger.debug('Failed to get historical slippage data:', error)
      return {
        averageSlippage: 0.5,
        distribution: [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
      }
    }
  }

  /**
   * Determine risk level based on slippage and conditions
   */
  private determineRiskLevel(slippage: number, conditions: MarketCondition): 'low' | 'medium' | 'high' | 'extreme' {
    if (slippage < 0.5) return 'low'
    if (slippage < 1.5) return 'medium'
    if (slippage < 3.0) return 'high'
    return 'extreme'
  }

  /**
   * Generate recommendation based on analysis
   */
  private generateRecommendation(
    slippage: number, 
    riskLevel: string, 
    conditions: MarketCondition
  ): 'proceed' | 'increase_slippage' | 'reduce_trade_size' | 'wait_for_better_conditions' {
    if (riskLevel === 'extreme' || slippage > 4.0) {
      return 'wait_for_better_conditions'
    }
    
    if (riskLevel === 'high' || conditions.volatility24h > 25) {
      return 'reduce_trade_size'
    }
    
    if (riskLevel === 'medium' && conditions.liquidityDepth === 'shallow') {
      return 'increase_slippage'
    }
    
    return 'proceed'
  }

  /**
   * Calculate confidence in slippage estimate
   */
  private calculateConfidence(
    conditions: MarketCondition, 
    historicalData: { averageSlippage: number; distribution: number[] },
    calculatedSlippage: number
  ): number {
    let confidence = 0.8 // Base confidence

    // Reduce confidence for extreme conditions
    if (conditions.volatility24h > 30) confidence -= 0.2
    if (conditions.networkCongestion === 'high') confidence -= 0.1
    if (conditions.liquidityDepth === 'shallow') confidence -= 0.15

    // Increase confidence if slippage is close to historical average
    const deviation = Math.abs(calculatedSlippage - historicalData.averageSlippage)
    if (deviation < 0.2) confidence += 0.1
    else if (deviation > 1.0) confidence -= 0.2

    return Math.max(0.1, Math.min(1.0, confidence))
  }

  /**
   * Calculate percentile of slippage vs historical distribution
   */
  private calculatePercentile(slippage: number, distribution: number[]): number {
    const sortedDistribution = distribution.sort((a, b) => a - b)
    const index = sortedDistribution.findIndex(val => val >= slippage)
    
    if (index === -1) return 100 // Higher than all historical values
    return (index / sortedDistribution.length) * 100
  }

  /**
   * Get slippage configuration for user
   */
  private async getSlippageConfig(userId?: string): Promise<SlippageConfig> {
    if (!userId) return this.defaultConfig

    // Check memory cache
    if (this.userConfigs.has(userId)) {
      return this.userConfigs.get(userId)!
    }

    try {
      // Try Redis cache
      const cacheKey = `slippage_config:${userId}`
      const cached = await RedisService.getJSON<SlippageConfig>(cacheKey)
      
      if (cached) {
        this.userConfigs.set(userId, cached)
        return cached
      }

      // In real implementation, would query database for user preferences
      // For now, return default config
      return this.defaultConfig

    } catch (error) {
      logger.debug('Failed to get user slippage config:', error)
      return this.defaultConfig
    }
  }

  /**
   * Update user slippage configuration
   */
  public async updateUserConfig(userId: string, config: Partial<SlippageConfig>): Promise<void> {
    try {
      const currentConfig = await this.getSlippageConfig(userId)
      const updatedConfig = { ...currentConfig, ...config }

      // Validate configuration
      this.validateSlippageConfig(updatedConfig)

      // Update memory cache
      this.userConfigs.set(userId, updatedConfig)

      // Update Redis cache
      const cacheKey = `slippage_config:${userId}`
      await RedisService.setJSON(cacheKey, updatedConfig, 3600 * 24) // 24 hours

      logger.info('User slippage configuration updated', { userId })

    } catch (error) {
      logger.error('Failed to update user slippage config:', error)
      throw new AppError('Failed to update slippage configuration', 500, 'CONFIG_UPDATE_FAILED')
    }
  }

  /**
   * Validate slippage configuration
   */
  private validateSlippageConfig(config: SlippageConfig): void {
    if (config.userDefined < 0.1 || config.userDefined > 5.0) {
      throw new AppError('User slippage must be between 0.1% and 5.0%', 400, 'INVALID_SLIPPAGE_CONFIG')
    }

    if (config.emergencyMax < config.userDefined || config.emergencyMax > 10.0) {
      throw new AppError('Emergency max must be >= user slippage and <= 10%', 400, 'INVALID_EMERGENCY_CONFIG')
    }

    if (config.timeDecay.maxDecay > 5.0) {
      throw new AppError('Time decay max cannot exceed 5%', 400, 'INVALID_TIME_DECAY_CONFIG')
    }
  }

  /**
   * Get current default configuration
   */
  public getDefaultConfig(): SlippageConfig {
    return { ...this.defaultConfig }
  }

  /**
   * Health check
   */
  public async healthCheck(): Promise<boolean> {
    try {
      // Test basic functionality
      await this.calculateOptimalSlippage({
        inputMint: 'So11111111111111111111111111111111111111112',
        outputMint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
        amount: 1000000,
        userSlippage: 0.5
      })

      return true
    } catch (error) {
      logger.error('Slippage protection service health check failed:', error)
      return false
    }
  }
}

// Export singleton instance
export const SlippageProtectionService = SlippageProtectionService.getInstance()