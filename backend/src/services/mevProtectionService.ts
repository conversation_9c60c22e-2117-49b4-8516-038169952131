import { Connection, PublicKey, TransactionMessage, VersionedTransaction } from '@solana/web3.js'
import { EventEmitter } from 'events'
import { logger } from '@/utils/logger'
import { RedisService } from '@/services/redis'
import { HeliusRPC, getConnection } from '@/services/heliusRPC'
import { AppError } from '@/middleware/errorHandler'

export interface MEVProtectionConfig {
  enabled: boolean
  maxProtectionLevel: 'basic' | 'advanced' | 'maximum'
  priorityFee: {
    strategy: 'static' | 'dynamic' | 'competitive'
    baseFeeLamports: number
    maxFeeLamports: number
    congestionMultiplier: number
    mevRiskMultiplier: number
    urgencyBonus: number
  }
  sandwichDetection: {
    enabled: boolean
    mempoolAnalysisDepth: number
    similarTradeThreshold: number
    timeWindowMs: number
    suspiciousPatternThreshold: number
  }
  frontrunningProtection: {
    enabled: boolean
    slippageBuffer: number
    routeObfuscation: boolean
    delayRandomization: {
      enabled: boolean
      minDelayMs: number
      maxDelayMs: number
    }
  }
  jitLiquidityProtection: {
    enabled: boolean
    detectionSensitivity: number
    liquidityChangeThreshold: number
    timeframeMs: number
  }
  bundleSubmission: {
    enabled: boolean
    maxBundleSize: number
    priorityLevels: string[]
    flashloanDetection: boolean
  }
}

export interface MEVAnalysisResult {
  riskLevel: 'none' | 'low' | 'medium' | 'high' | 'critical'
  riskScore: number // 0-100
  detectedThreats: MEVThreat[]
  recommendedFee: number
  protectionStrategy: 'proceed' | 'increase_fee' | 'delay' | 'cancel' | 'obfuscate_route'
  analysis: {
    sandwich: SandwichAnalysis
    frontrunning: FrontrunningAnalysis
    jitLiquidity: JITLiquidityAnalysis
    generalMEV: GeneralMEVAnalysis
  }
  networkConditions: {
    congestion: 'low' | 'medium' | 'high'
    avgPriorityFee: number
    mempoolPressure: number
    blockUtilization: number
  }
  confidence: number // 0-1
  recommendations: string[]
  timestamp: number
}

interface MEVThreat {
  type: 'sandwich' | 'frontrun' | 'backrun' | 'jit_liquidity' | 'arbitrage' | 'liquidation'
  severity: 'low' | 'medium' | 'high' | 'critical'
  confidence: number
  description: string
  detectedAt: number
  evidence: any
}

interface SandwichAnalysis {
  detected: boolean
  confidence: number
  suspiciousTransactions: Array<{
    signature: string
    similarity: number
    timing: number
    wallet: string
  }>
  riskFactors: string[]
}

interface FrontrunningAnalysis {
  detected: boolean
  confidence: number
  competingTransactions: number
  averageGasPrice: number
  recommendedGasIncrease: number
}

interface JITLiquidityAnalysis {
  detected: boolean
  confidence: number
  liquidityChange: number
  suspiciousProviders: string[]
  timing: number
}

interface GeneralMEVAnalysis {
  arbitrageOpportunity: number
  liquidationRisk: number
  unusualActivity: boolean
  suspiciousContracts: string[]
}

class MEVProtectionService extends EventEmitter {
  private static instance: MEVProtectionService
  private connection: Connection
  private config: MEVProtectionConfig
  private mempoolCache: Map<string, any> = new Map()
  private threatDatabase: Map<string, MEVThreat[]> = new Map()
  private networkMetrics: {
    lastUpdate: number
    avgPriorityFee: number
    congestion: 'low' | 'medium' | 'high'
    mempoolSize: number
    blockUtilization: number
  }

  private constructor() {
    super()
    this.connection = getConnection()
    
    this.config = {
      enabled: true,
      maxProtectionLevel: 'maximum',
      priorityFee: {
        strategy: 'dynamic',
        baseFeeLamports: 10000, // 0.00001 SOL
        maxFeeLamports: 100000, // 0.0001 SOL
        congestionMultiplier: 2.0,
        mevRiskMultiplier: 3.0,
        urgencyBonus: 1.5
      },
      sandwichDetection: {
        enabled: true,
        mempoolAnalysisDepth: 50,
        similarTradeThreshold: 0.85,
        timeWindowMs: 5000,
        suspiciousPatternThreshold: 0.7
      },
      frontrunningProtection: {
        enabled: true,
        slippageBuffer: 0.2, // 0.2% additional buffer
        routeObfuscation: true,
        delayRandomization: {
          enabled: true,
          minDelayMs: 100,
          maxDelayMs: 2000
        }
      },
      jitLiquidityProtection: {
        enabled: true,
        detectionSensitivity: 0.8,
        liquidityChangeThreshold: 0.15, // 15% change threshold
        timeframeMs: 10000
      },
      bundleSubmission: {
        enabled: false, // Requires special RPC support
        maxBundleSize: 3,
        priorityLevels: ['low', 'medium', 'high', 'urgent'],
        flashloanDetection: true
      }
    }

    this.networkMetrics = {
      lastUpdate: 0,
      avgPriorityFee: 10000,
      congestion: 'medium',
      mempoolSize: 0,
      blockUtilization: 50
    }

    // Update network metrics periodically
    setInterval(() => this.updateNetworkMetrics(), 15000) // Every 15s
    
    // Clean old mempool data
    setInterval(() => this.cleanMempoolCache(), 60000) // Every minute
  }

  public static getInstance(): MEVProtectionService {
    if (!MEVProtectionService.instance) {
      MEVProtectionService.instance = new MEVProtectionService()
    }
    return MEVProtectionService.instance
  }

  /**
   * Comprehensive MEV analysis and protection
   */
  public async analyzeMEVRisk(params: {
    inputMint: string
    outputMint: string
    amount: number
    route: any[]
    userPublicKey: string
    urgency?: 'low' | 'medium' | 'high'
  }): Promise<MEVAnalysisResult> {
    try {
      if (!this.config.enabled) {
        return this.createBasicResult('none', 0, [])
      }

      logger.debug('Starting comprehensive MEV analysis', {
        tokenPair: `${params.inputMint.slice(0, 8)}.../${params.outputMint.slice(0, 8)}...`,
        amount: params.amount,
        urgency: params.urgency
      })

      const startTime = Date.now()

      // Update network conditions
      await this.updateNetworkMetrics()

      // Parallel analysis execution
      const [
        sandwichAnalysis,
        frontrunningAnalysis,
        jitLiquidityAnalysis,
        generalMEVAnalysis
      ] = await Promise.all([
        this.analyzeSandwichRisk(params),
        this.analyzeFrontrunningRisk(params),
        this.analyzeJITLiquidityRisk(params),
        this.analyzeGeneralMEVRisk(params)
      ])

      // Aggregate threats
      const detectedThreats: MEVThreat[] = []
      
      if (sandwichAnalysis.detected) {
        detectedThreats.push({
          type: 'sandwich',
          severity: this.calculateThreatSeverity(sandwichAnalysis.confidence),
          confidence: sandwichAnalysis.confidence,
          description: `Sandwich attack detected with ${sandwichAnalysis.suspiciousTransactions.length} suspicious transactions`,
          detectedAt: Date.now(),
          evidence: sandwichAnalysis.suspiciousTransactions
        })
      }

      if (frontrunningAnalysis.detected) {
        detectedThreats.push({
          type: 'frontrun',
          severity: this.calculateThreatSeverity(frontrunningAnalysis.confidence),
          confidence: frontrunningAnalysis.confidence,
          description: `Frontrunning risk with ${frontrunningAnalysis.competingTransactions} competing transactions`,
          detectedAt: Date.now(),
          evidence: { competingTransactions: frontrunningAnalysis.competingTransactions }
        })
      }

      if (jitLiquidityAnalysis.detected) {
        detectedThreats.push({
          type: 'jit_liquidity',
          severity: this.calculateThreatSeverity(jitLiquidityAnalysis.confidence),
          confidence: jitLiquidityAnalysis.confidence,
          description: `JIT liquidity manipulation detected (${jitLiquidityAnalysis.liquidityChange}% change)`,
          detectedAt: Date.now(),
          evidence: { liquidityChange: jitLiquidityAnalysis.liquidityChange }
        })
      }

      // Calculate overall risk
      const riskScore = this.calculateOverallRisk({
        sandwich: sandwichAnalysis,
        frontrunning: frontrunningAnalysis,
        jitLiquidity: jitLiquidityAnalysis,
        general: generalMEVAnalysis
      }, params.amount)

      const riskLevel = this.determineRiskLevel(riskScore)
      const recommendedFee = this.calculateOptimalPriorityFee(riskScore, params.urgency)
      const protectionStrategy = this.determineProtectionStrategy(riskLevel, detectedThreats)
      
      // Generate recommendations
      const recommendations = this.generateRecommendations(riskLevel, detectedThreats, params)
      
      // Calculate confidence in analysis
      const confidence = this.calculateAnalysisConfidence(
        sandwichAnalysis,
        frontrunningAnalysis,
        jitLiquidityAnalysis,
        this.networkMetrics
      )

      const result: MEVAnalysisResult = {
        riskLevel,
        riskScore,
        detectedThreats,
        recommendedFee,
        protectionStrategy,
        analysis: {
          sandwich: sandwichAnalysis,
          frontrunning: frontrunningAnalysis,
          jitLiquidity: jitLiquidityAnalysis,
          generalMEV: generalMEVAnalysis
        },
        networkConditions: {
          congestion: this.networkMetrics.congestion,
          avgPriorityFee: this.networkMetrics.avgPriorityFee,
          mempoolPressure: this.calculateMempoolPressure(),
          blockUtilization: this.networkMetrics.blockUtilization
        },
        confidence,
        recommendations,
        timestamp: Date.now()
      }

      // Store threat data for learning
      this.storeThreatData(params.inputMint + params.outputMint, detectedThreats)

      logger.debug('MEV analysis completed', {
        riskLevel,
        riskScore,
        threatsDetected: detectedThreats.length,
        recommendedFee,
        analysisTime: Date.now() - startTime
      })

      this.emit('mevAnalysisCompleted', result)
      return result

    } catch (error) {
      logger.error('MEV analysis failed:', error)
      throw new AppError('MEV analysis failed', 500, 'MEV_ANALYSIS_FAILED')
    }
  }

  /**
   * Analyze sandwich attack risk
   */
  private async analyzeSandwichRisk(params: {
    inputMint: string
    outputMint: string
    amount: number
    route: any[]
  }): Promise<SandwichAnalysis> {
    if (!this.config.sandwichDetection.enabled) {
      return { detected: false, confidence: 0, suspiciousTransactions: [], riskFactors: [] }
    }

    try {
      // Simulate mempool analysis (in production, would use actual mempool data)
      const mempoolTransactions = await this.getMempoolTransactions(
        params.inputMint,
        params.outputMint,
        this.config.sandwichDetection.mempoolAnalysisDepth
      )

      const suspiciousTransactions = []
      const riskFactors = []

      // Analyze transaction patterns
      for (const tx of mempoolTransactions) {
        const similarity = this.calculateTransactionSimilarity(tx, params)
        
        if (similarity > this.config.sandwichDetection.similarTradeThreshold) {
          suspiciousTransactions.push({
            signature: tx.signature,
            similarity,
            timing: tx.timestamp,
            wallet: tx.wallet
          })
        }
      }

      // Check for sandwich patterns
      if (suspiciousTransactions.length >= 2) {
        riskFactors.push('Multiple similar transactions detected')
      }

      // Check timing patterns
      const timingPattern = this.analyzeTimingPatterns(suspiciousTransactions)
      if (timingPattern.suspicious) {
        riskFactors.push('Suspicious timing pattern detected')
      }

      // Calculate detection confidence
      const confidence = Math.min(
        (suspiciousTransactions.length / 5) * 0.4 +
        (riskFactors.length / 3) * 0.6,
        1.0
      )

      const detected = confidence > this.config.sandwichDetection.suspiciousPatternThreshold

      return {
        detected,
        confidence,
        suspiciousTransactions,
        riskFactors
      }

    } catch (error) {
      logger.debug('Sandwich analysis failed:', error)
      return { detected: false, confidence: 0, suspiciousTransactions: [], riskFactors: ['Analysis failed'] }
    }
  }

  /**
   * Analyze frontrunning risk
   */
  private async analyzeFrontrunningRisk(params: {
    inputMint: string
    outputMint: string
    amount: number
  }): Promise<FrontrunningAnalysis> {
    if (!this.config.frontrunningProtection.enabled) {
      return { detected: false, confidence: 0, competingTransactions: 0, averageGasPrice: 0, recommendedGasIncrease: 0 }
    }

    try {
      // Analyze competing transactions
      const competingTxs = await this.getCompetingTransactions(params.inputMint, params.outputMint)
      const averageGasPrice = this.calculateAverageGasPrice(competingTxs)
      
      // Check for unusual activity
      const baselineActivity = await this.getBaselineActivity(params.inputMint, params.outputMint)
      const activityIncrease = competingTxs.length / Math.max(baselineActivity, 1)
      
      const confidence = Math.min(activityIncrease / 3, 1.0)
      const recommendedGasIncrease = Math.max(averageGasPrice * 1.2, this.config.priorityFee.baseFeeLamports)

      return {
        detected: confidence > 0.6,
        confidence,
        competingTransactions: competingTxs.length,
        averageGasPrice,
        recommendedGasIncrease
      }

    } catch (error) {
      logger.debug('Frontrunning analysis failed:', error)
      return { detected: false, confidence: 0, competingTransactions: 0, averageGasPrice: 0, recommendedGasIncrease: 0 }
    }
  }

  /**
   * Analyze JIT liquidity manipulation risk
   */
  private async analyzeJITLiquidityRisk(params: {
    inputMint: string
    outputMint: string
    route: any[]
  }): Promise<JITLiquidityAnalysis> {
    if (!this.config.jitLiquidityProtection.enabled) {
      return { detected: false, confidence: 0, liquidityChange: 0, suspiciousProviders: [], timing: 0 }
    }

    try {
      // Analyze recent liquidity changes
      const liquidityHistory = await this.getLiquidityHistory(params.inputMint, params.outputMint)
      const recentLiquidityChange = this.calculateLiquidityChange(liquidityHistory)
      
      // Detect suspicious providers
      const suspiciousProviders = this.detectSuspiciousProviders(liquidityHistory)
      
      const confidence = Math.abs(recentLiquidityChange) > this.config.jitLiquidityProtection.liquidityChangeThreshold
        ? Math.min(Math.abs(recentLiquidityChange) * 2, 1.0)
        : 0

      return {
        detected: confidence > this.config.jitLiquidityProtection.detectionSensitivity,
        confidence,
        liquidityChange: recentLiquidityChange,
        suspiciousProviders,
        timing: Date.now()
      }

    } catch (error) {
      logger.debug('JIT liquidity analysis failed:', error)
      return { detected: false, confidence: 0, liquidityChange: 0, suspiciousProviders: [], timing: 0 }
    }
  }

  /**
   * Analyze general MEV opportunities and risks
   */
  private async analyzeGeneralMEVRisk(params: {
    inputMint: string
    outputMint: string
    amount: number
  }): Promise<GeneralMEVAnalysis> {
    try {
      // Check for arbitrage opportunities
      const arbitrageOpportunity = await this.calculateArbitrageOpportunity(params.inputMint, params.outputMint)
      
      // Check liquidation risk
      const liquidationRisk = await this.assessLiquidationRisk(params.inputMint, params.outputMint, params.amount)
      
      // Detect unusual activity
      const unusualActivity = await this.detectUnusualActivity(params.inputMint, params.outputMint)
      
      // Check suspicious contracts
      const suspiciousContracts = await this.detectSuspiciousContracts(params.inputMint, params.outputMint)

      return {
        arbitrageOpportunity,
        liquidationRisk,
        unusualActivity,
        suspiciousContracts
      }

    } catch (error) {
      logger.debug('General MEV analysis failed:', error)
      return {
        arbitrageOpportunity: 0,
        liquidationRisk: 0,
        unusualActivity: false,
        suspiciousContracts: []
      }
    }
  }

  /**
   * Calculate optimal priority fee based on risk and urgency
   */
  private calculateOptimalPriorityFee(riskScore: number, urgency: 'low' | 'medium' | 'high' = 'medium'): number {
    let baseFee = this.config.priorityFee.baseFeeLamports

    // Apply congestion multiplier
    if (this.networkMetrics.congestion === 'high') {
      baseFee *= this.config.priorityFee.congestionMultiplier
    }

    // Apply MEV risk multiplier
    const riskMultiplier = 1 + (riskScore / 100) * (this.config.priorityFee.mevRiskMultiplier - 1)
    baseFee *= riskMultiplier

    // Apply urgency bonus
    const urgencyMultipliers = { low: 1.0, medium: 1.2, high: 1.5 }
    baseFee *= urgencyMultipliers[urgency] || 1.0

    // Cap at maximum
    return Math.min(baseFee, this.config.priorityFee.maxFeeLamports)
  }

  /**
   * Calculate overall MEV risk score
   */
  private calculateOverallRisk(analyses: {
    sandwich: SandwichAnalysis
    frontrunning: FrontrunningAnalysis
    jitLiquidity: JITLiquidityAnalysis
    general: GeneralMEVAnalysis
  }, amount: number): number {
    let riskScore = 0

    // Sandwich risk (30% weight)
    if (analyses.sandwich.detected) {
      riskScore += analyses.sandwich.confidence * 30
    }

    // Frontrunning risk (25% weight)
    if (analyses.frontrunning.detected) {
      riskScore += analyses.frontrunning.confidence * 25
    }

    // JIT liquidity risk (20% weight)
    if (analyses.jitLiquidity.detected) {
      riskScore += analyses.jitLiquidity.confidence * 20
    }

    // General MEV risk (25% weight)
    const generalRisk = Math.max(
      analyses.general.arbitrageOpportunity * 0.4,
      analyses.general.liquidationRisk * 0.3,
      analyses.general.unusualActivity ? 0.7 : 0,
      analyses.general.suspiciousContracts.length * 0.1
    )
    riskScore += generalRisk * 25

    // Amount-based adjustment (larger amounts = higher risk)
    const amountMultiplier = Math.min(1 + Math.log10(amount / 1000000) * 0.1, 1.5)
    riskScore *= amountMultiplier

    return Math.min(Math.max(riskScore, 0), 100)
  }

  /**
   * Determine risk level from score
   */
  private determineRiskLevel(riskScore: number): 'none' | 'low' | 'medium' | 'high' | 'critical' {
    if (riskScore < 10) return 'none'
    if (riskScore < 30) return 'low'
    if (riskScore < 60) return 'medium'
    if (riskScore < 85) return 'high'
    return 'critical'
  }

  /**
   * Determine protection strategy
   */
  private determineProtectionStrategy(
    riskLevel: string,
    threats: MEVThreat[]
  ): 'proceed' | 'increase_fee' | 'delay' | 'cancel' | 'obfuscate_route' {
    if (riskLevel === 'critical') return 'cancel'
    if (riskLevel === 'high') return 'obfuscate_route'
    if (riskLevel === 'medium') return 'increase_fee'
    if (threats.length > 0) return 'delay'
    return 'proceed'
  }

  /**
   * Generate recommendations based on analysis
   */
  private generateRecommendations(
    riskLevel: string,
    threats: MEVThreat[],
    params: any
  ): string[] {
    const recommendations = []

    if (riskLevel === 'critical') {
      recommendations.push('Cancel transaction - critical MEV risk detected')
    } else if (riskLevel === 'high') {
      recommendations.push('Use alternative routing to avoid MEV')
      recommendations.push('Consider splitting transaction into smaller amounts')
    } else if (riskLevel === 'medium') {
      recommendations.push('Increase priority fee for faster execution')
      recommendations.push('Monitor for unusual market activity')
    }

    if (threats.some(t => t.type === 'sandwich')) {
      recommendations.push('Increase slippage tolerance or split trade')
    }

    if (threats.some(t => t.type === 'frontrun')) {
      recommendations.push('Use higher priority fee to outbid frontrunners')
    }

    if (this.networkMetrics.congestion === 'high') {
      recommendations.push('Consider waiting for lower network congestion')
    }

    return recommendations
  }

  // Helper methods (simplified implementations for demo)
  private async getMempoolTransactions(inputMint: string, outputMint: string, depth: number): Promise<any[]> {
    // In production, would query actual mempool data
    return Array.from({ length: Math.floor(Math.random() * depth) }, (_, i) => ({
      signature: `tx_${i}_${Date.now()}`,
      timestamp: Date.now() - Math.random() * 10000,
      wallet: `wallet_${i}`,
      inputMint,
      outputMint,
      amount: Math.random() * 1000000
    }))
  }

  private calculateTransactionSimilarity(tx: any, params: any): number {
    // Simplified similarity calculation
    const amountSimilarity = 1 - Math.abs(tx.amount - params.amount) / Math.max(tx.amount, params.amount)
    const tokenSimilarity = (tx.inputMint === params.inputMint && tx.outputMint === params.outputMint) ? 1 : 0
    return (amountSimilarity * 0.6 + tokenSimilarity * 0.4)
  }

  private analyzeTimingPatterns(transactions: any[]): { suspicious: boolean } {
    if (transactions.length < 2) return { suspicious: false }
    
    const timings = transactions.map(tx => tx.timing).sort()
    const avgInterval = timings.reduce((sum, timing, i) => {
      if (i === 0) return sum
      return sum + (timing - timings[i - 1])
    }, 0) / (timings.length - 1)
    
    return { suspicious: avgInterval < 1000 } // Suspicious if < 1 second apart
  }

  private async getCompetingTransactions(inputMint: string, outputMint: string): Promise<any[]> {
    // Simplified competing transaction detection
    return Array.from({ length: Math.floor(Math.random() * 10) }, (_, i) => ({
      id: i,
      gasPrice: 10000 + Math.random() * 50000
    }))
  }

  private calculateAverageGasPrice(transactions: any[]): number {
    if (transactions.length === 0) return this.config.priorityFee.baseFeeLamports
    return transactions.reduce((sum, tx) => sum + tx.gasPrice, 0) / transactions.length
  }

  private async getBaselineActivity(inputMint: string, outputMint: string): Promise<number> {
    // Return baseline transaction activity for this pair
    return 3 // Simplified baseline
  }

  private async getLiquidityHistory(inputMint: string, outputMint: string): Promise<any[]> {
    // Simplified liquidity history
    return Array.from({ length: 10 }, (_, i) => ({
      timestamp: Date.now() - i * 60000,
      liquidity: 1000000 + Math.random() * 200000,
      provider: `provider_${i}`
    }))
  }

  private calculateLiquidityChange(history: any[]): number {
    if (history.length < 2) return 0
    const recent = history[0].liquidity
    const baseline = history.slice(-5).reduce((sum, h) => sum + h.liquidity, 0) / 5
    return (recent - baseline) / baseline
  }

  private detectSuspiciousProviders(history: any[]): string[] {
    // Detect providers with unusual patterns
    return history.filter(h => Math.random() > 0.8).map(h => h.provider)
  }

  private async calculateArbitrageOpportunity(inputMint: string, outputMint: string): Promise<number> {
    return Math.random() * 0.1 // 0-10% arbitrage opportunity
  }

  private async assessLiquidationRisk(inputMint: string, outputMint: string, amount: number): Promise<number> {
    return Math.random() * 0.05 // 0-5% liquidation risk
  }

  private async detectUnusualActivity(inputMint: string, outputMint: string): Promise<boolean> {
    return Math.random() > 0.8 // 20% chance of unusual activity
  }

  private async detectSuspiciousContracts(inputMint: string, outputMint: string): Promise<string[]> {
    return Math.random() > 0.9 ? [`suspicious_contract_${Date.now()}`] : []
  }

  private calculateThreatSeverity(confidence: number): 'low' | 'medium' | 'high' | 'critical' {
    if (confidence < 0.3) return 'low'
    if (confidence < 0.6) return 'medium'
    if (confidence < 0.85) return 'high'
    return 'critical'
  }

  private calculateAnalysisConfidence(
    sandwich: SandwichAnalysis,
    frontrunning: FrontrunningAnalysis,
    jitLiquidity: JITLiquidityAnalysis,
    networkMetrics: any
  ): number {
    // Base confidence
    let confidence = 0.7

    // Adjust based on data quality
    const dataQuality = Math.min(
      (networkMetrics.lastUpdate > Date.now() - 60000 ? 1 : 0.5) * 0.3 +
      (sandwich.suspiciousTransactions.length > 0 ? 1 : 0.8) * 0.3 +
      (frontrunning.competingTransactions > 0 ? 1 : 0.8) * 0.2 +
      (jitLiquidity.liquidityChange !== 0 ? 1 : 0.8) * 0.2,
      1.0
    )

    return Math.max(confidence * dataQuality, 0.1)
  }

  private calculateMempoolPressure(): number {
    return Math.min(this.networkMetrics.mempoolSize / 1000, 100)
  }

  private createBasicResult(riskLevel: any, riskScore: number, threats: MEVThreat[]): MEVAnalysisResult {
    return {
      riskLevel,
      riskScore,
      detectedThreats: threats,
      recommendedFee: this.config.priorityFee.baseFeeLamports,
      protectionStrategy: 'proceed',
      analysis: {
        sandwich: { detected: false, confidence: 0, suspiciousTransactions: [], riskFactors: [] },
        frontrunning: { detected: false, confidence: 0, competingTransactions: 0, averageGasPrice: 0, recommendedGasIncrease: 0 },
        jitLiquidity: { detected: false, confidence: 0, liquidityChange: 0, suspiciousProviders: [], timing: 0 },
        generalMEV: { arbitrageOpportunity: 0, liquidationRisk: 0, unusualActivity: false, suspiciousContracts: [] }
      },
      networkConditions: {
        congestion: this.networkMetrics.congestion,
        avgPriorityFee: this.networkMetrics.avgPriorityFee,
        mempoolPressure: 0,
        blockUtilization: this.networkMetrics.blockUtilization
      },
      confidence: 0.5,
      recommendations: [],
      timestamp: Date.now()
    }
  }

  private storeThreatData(tokenPair: string, threats: MEVThreat[]): void {
    this.threatDatabase.set(tokenPair, threats)
  }

  private async updateNetworkMetrics(): Promise<void> {
    try {
      // In production, would query actual network metrics
      const now = Date.now()
      
      // Simulate network conditions
      const hour = new Date().getHours()
      let congestion: 'low' | 'medium' | 'high'
      let avgFee: number
      let utilization: number

      if ((hour >= 8 && hour <= 11) || (hour >= 14 && hour <= 17)) {
        congestion = 'high'
        avgFee = 25000
        utilization = 85
      } else if (hour >= 22 || hour <= 6) {
        congestion = 'low'
        avgFee = 8000
        utilization = 35
      } else {
        congestion = 'medium'
        avgFee = 15000
        utilization = 60
      }

      this.networkMetrics = {
        lastUpdate: now,
        avgPriorityFee: avgFee,
        congestion,
        mempoolSize: Math.floor(Math.random() * 1000),
        blockUtilization: utilization
      }

    } catch (error) {
      logger.debug('Failed to update network metrics:', error)
    }
  }

  private cleanMempoolCache(): void {
    const cutoff = Date.now() - 300000 // 5 minutes
    for (const [key, value] of this.mempoolCache.entries()) {
      if (value.timestamp < cutoff) {
        this.mempoolCache.delete(key)
      }
    }
  }

  /**
   * Get current configuration
   */
  public getConfig(): MEVProtectionConfig {
    return { ...this.config }
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<MEVProtectionConfig>): void {
    this.config = { ...this.config, ...newConfig }
    logger.info('MEV protection configuration updated')
  }

  /**
   * Get current network metrics
   */
  public getNetworkMetrics(): typeof this.networkMetrics {
    return { ...this.networkMetrics }
  }

  /**
   * Health check
   */
  public async healthCheck(): Promise<boolean> {
    try {
      // Test basic analysis with mock data
      await this.analyzeMEVRisk({
        inputMint: 'So11111111111111111111111111111111111111112',
        outputMint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
        amount: 1000000,
        route: [],
        userPublicKey: 'test'
      })

      // Check if network metrics are fresh
      return Date.now() - this.networkMetrics.lastUpdate < 120000 // 2 minutes
    } catch (error) {
      logger.error('MEV protection service health check failed:', error)
      return false
    }
  }

  /**
   * Shutdown cleanup
   */
  public shutdown(): void {
    this.mempoolCache.clear()
    this.threatDatabase.clear()
    this.removeAllListeners()
    logger.info('MEV protection service shutdown completed')
  }
}

// Export singleton instance
export const MEVProtectionService = MEVProtectionService.getInstance()