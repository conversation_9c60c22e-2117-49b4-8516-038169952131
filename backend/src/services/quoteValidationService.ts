import { logger } from '@/utils/logger'
import { RedisService } from '@/services/redis'
import { AppError } from '@/middleware/errorHandler'
import type { Quote } from '@memetrader-pro/shared'
import { EnhancedJupiterService } from '@/services/enhancedJupiterService'

export interface QuoteValidationConfig {
  priceImpact: {
    warningThreshold: number      // 2% - show warning
    errorThreshold: number        // 5% - reject quote
    maxAllowed: number           // 10% - absolute maximum
  }
  routeComplexity: {
    maxHops: number              // 4 - maximum route hops
    preferredHops: number        // 2 - preferred route hops
    directRouteBonus: number     // 0.1% - bonus for direct routes
  }
  liquidityDepth: {
    minLiquidity: number         // $1000 - minimum liquidity required
    warningThreshold: number     // $10000 - show warning below this
    preferredThreshold: number   // $50000 - preferred liquidity level
  }
  marketMaker: {
    trustedMakers: string[]      // List of trusted market maker addresses
    blacklistedMakers: string[]  // List of blacklisted addresses
    reputationWeight: number     // 0.2 - weight of reputation in scoring
  }
  volatility: {
    maxVolatility: number        // 50% - maximum 24h volatility allowed
    warningThreshold: number     // 20% - show warning above this
    timeWindow: number           // 24h - volatility calculation window
  }
  freshness: {
    maxAge: number               // 10s - maximum quote age before refresh
    warningAge: number           // 5s - show warning for stale quotes
  }
}

export interface ValidationResult {
  isValid: boolean
  severity: 'info' | 'warning' | 'error' | 'critical'
  score: number                  // 0-100, higher is better
  warnings: string[]
  errors: string[]
  details: {
    priceImpactAnalysis: PriceImpactAnalysis
    routeAnalysis: RouteAnalysis
    liquidityAnalysis: LiquidityAnalysis
    marketMakerAnalysis: MarketMakerAnalysis
    volatilityAnalysis: VolatilityAnalysis
    freshnessAnalysis: FreshnessAnalysis
  }
  recommendation: 'execute' | 'proceed_with_caution' | 'reject' | 'retry_later'
  estimatedSlippage: number
  executionProbability: number   // 0-1, probability of successful execution
}

interface PriceImpactAnalysis {
  impact: number
  severity: 'low' | 'medium' | 'high' | 'extreme'
  recommendation: string
  historicalComparison: number
}

interface RouteAnalysis {
  hops: number
  complexity: 'simple' | 'moderate' | 'complex' | 'extreme'
  exchanges: string[]
  poolInfo: any[]
  riskFactors: string[]
  alternativeRoutes: number
}

interface LiquidityAnalysis {
  totalLiquidity: number
  depth: 'shallow' | 'moderate' | 'deep' | 'very_deep'
  distribution: any
  marketImpact: number
  liquidityRisk: 'low' | 'medium' | 'high'
}

interface MarketMakerAnalysis {
  makers: string[]
  reputation: number
  trustScore: number
  riskFlags: string[]
  verifiedMakers: number
}

interface VolatilityAnalysis {
  current24h: number
  trend: 'increasing' | 'decreasing' | 'stable'
  risk: 'low' | 'medium' | 'high' | 'extreme'
  recommendation: string
}

interface FreshnessAnalysis {
  ageSeconds: number
  freshness: 'fresh' | 'acceptable' | 'stale' | 'expired'
  shouldRefresh: boolean
}

class QuoteValidationService {
  private static instance: QuoteValidationService
  private config: QuoteValidationConfig
  private validationCache: Map<string, ValidationResult> = new Map()

  private constructor() {
    this.config = {
      priceImpact: {
        warningThreshold: 2.0,    // 2%
        errorThreshold: 5.0,      // 5%
        maxAllowed: 10.0          // 10%
      },
      routeComplexity: {
        maxHops: 4,
        preferredHops: 2,
        directRouteBonus: 0.1
      },
      liquidityDepth: {
        minLiquidity: 1000,       // $1,000
        warningThreshold: 10000,  // $10,000
        preferredThreshold: 50000 // $50,000
      },
      marketMaker: {
        trustedMakers: [
          // Add known good market makers
          'Jupiter', 'Orca', 'Raydium', 'Serum'
        ],
        blacklistedMakers: [
          // Add known bad market makers
        ],
        reputationWeight: 0.2
      },
      volatility: {
        maxVolatility: 50.0,      // 50%
        warningThreshold: 20.0,   // 20%
        timeWindow: 24 * 60 * 60 * 1000 // 24 hours
      },
      freshness: {
        maxAge: 10,               // 10 seconds
        warningAge: 5             // 5 seconds
      }
    }
  }

  public static getInstance(): QuoteValidationService {
    if (!QuoteValidationService.instance) {
      QuoteValidationService.instance = new QuoteValidationService()
    }
    return QuoteValidationService.instance
  }

  /**
   * Comprehensive quote validation with detailed analysis
   */
  public async validateQuote(
    quote: Quote,
    params: {
      inputMint: string
      outputMint: string
      amount: number
      slippageBps: number
      userPublicKey?: string
    },
    quoteTimestamp?: number
  ): Promise<ValidationResult> {
    try {
      const cacheKey = `validation:${JSON.stringify({ quote: quote.inAmount + quote.outAmount, params })}`
      
      // Check cache first
      const cached = this.validationCache.get(cacheKey)
      if (cached && Date.now() - quoteTimestamp! < 5000) {
        return cached
      }

      logger.debug('Starting comprehensive quote validation')

      // Perform all validation analyses in parallel
      const [
        priceImpactAnalysis,
        routeAnalysis,
        liquidityAnalysis,
        marketMakerAnalysis,
        volatilityAnalysis,
        freshnessAnalysis
      ] = await Promise.all([
        this.analyzePriceImpact(quote, params),
        this.analyzeRoute(quote),
        this.analyzeLiquidity(quote, params),
        this.analyzeMarketMakers(quote),
        this.analyzeVolatility(params.inputMint, params.outputMint),
        this.analyzeFreshness(quoteTimestamp)
      ])

      // Calculate overall validation result
      const result = this.calculateValidationResult({
        priceImpactAnalysis,
        routeAnalysis,
        liquidityAnalysis,
        marketMakerAnalysis,
        volatilityAnalysis,
        freshnessAnalysis
      })

      // Cache result for a brief period
      this.validationCache.set(cacheKey, result)
      setTimeout(() => this.validationCache.delete(cacheKey), 5000)

      logger.debug('Quote validation completed', { 
        score: result.score, 
        recommendation: result.recommendation,
        warnings: result.warnings.length,
        errors: result.errors.length
      })

      return result

    } catch (error) {
      logger.error('Quote validation failed:', error)
      throw new AppError('Quote validation failed', 500, 'VALIDATION_FAILED')
    }
  }

  /**
   * Analyze price impact and historical context
   */
  private async analyzePriceImpact(quote: Quote, params: any): Promise<PriceImpactAnalysis> {
    const impact = quote.priceImpactPct
    
    let severity: 'low' | 'medium' | 'high' | 'extreme'
    if (impact < 0.5) severity = 'low'
    else if (impact < 2.0) severity = 'medium'
    else if (impact < 5.0) severity = 'high'
    else severity = 'extreme'

    // Get historical price impact for similar trades
    const historicalImpact = await this.getHistoricalPriceImpact(
      params.inputMint, 
      params.outputMint, 
      params.amount
    )

    let recommendation: string
    if (impact <= this.config.priceImpact.warningThreshold) {
      recommendation = 'Acceptable price impact for trade size'
    } else if (impact <= this.config.priceImpact.errorThreshold) {
      recommendation = 'High price impact - consider reducing trade size'
    } else {
      recommendation = 'Extreme price impact - strongly recommend reducing trade size or splitting trade'
    }

    return {
      impact,
      severity,
      recommendation,
      historicalComparison: historicalImpact
    }
  }

  /**
   * Analyze route complexity and risk factors
   */
  private async analyzeRoute(quote: Quote): Promise<RouteAnalysis> {
    const routePlan = quote.routePlan || []
    const hops = routePlan.length
    
    let complexity: 'simple' | 'moderate' | 'complex' | 'extreme'
    if (hops <= 1) complexity = 'simple'
    else if (hops <= 2) complexity = 'moderate'
    else if (hops <= 4) complexity = 'complex'
    else complexity = 'extreme'

    const exchanges = routePlan.map((hop: any) => hop.swapInfo?.label || 'Unknown')
    const uniqueExchanges = [...new Set(exchanges)]
    
    const riskFactors: string[] = []
    if (hops > this.config.routeComplexity.maxHops) {
      riskFactors.push(`Route too complex (${hops} hops > ${this.config.routeComplexity.maxHops})`)
    }
    if (uniqueExchanges.length > 3) {
      riskFactors.push(`Too many different exchanges (${uniqueExchanges.length})`)
    }

    return {
      hops,
      complexity,
      exchanges: uniqueExchanges,
      poolInfo: routePlan.map((hop: any) => ({
        exchange: hop.swapInfo?.label,
        feePercent: hop.swapInfo?.feeAmount ? parseFloat(hop.swapInfo.feeAmount) / 10000 : 0
      })),
      riskFactors,
      alternativeRoutes: 0 // Would need additional API calls to determine this
    }
  }

  /**
   * Analyze liquidity depth and market impact
   */
  private async analyzeLiquidity(quote: Quote, params: any): Promise<LiquidityAnalysis> {
    // Estimate liquidity based on price impact and trade size
    const priceImpact = quote.priceImpactPct / 100
    const tradeSize = params.amount
    
    // Rough estimation: if price impact is X% for trade size Y, 
    // then total liquidity is approximately Y / X * 100
    const estimatedLiquidity = priceImpact > 0 ? (tradeSize / priceImpact) * 100 : tradeSize * 100

    let depth: 'shallow' | 'moderate' | 'deep' | 'very_deep'
    if (estimatedLiquidity < this.config.liquidityDepth.minLiquidity) depth = 'shallow'
    else if (estimatedLiquidity < this.config.liquidityDepth.warningThreshold) depth = 'moderate'
    else if (estimatedLiquidity < this.config.liquidityDepth.preferredThreshold) depth = 'deep'
    else depth = 'very_deep'

    let liquidityRisk: 'low' | 'medium' | 'high'
    if (depth === 'very_deep' || depth === 'deep') liquidityRisk = 'low'
    else if (depth === 'moderate') liquidityRisk = 'medium'
    else liquidityRisk = 'high'

    return {
      totalLiquidity: estimatedLiquidity,
      depth,
      distribution: {}, // Would need additional data sources
      marketImpact: priceImpact,
      liquidityRisk
    }
  }

  /**
   * Analyze market makers and reputation
   */
  private async analyzeMarketMakers(quote: Quote): Promise<MarketMakerAnalysis> {
    const routePlan = quote.routePlan || []
    const makers = routePlan.map((hop: any) => hop.swapInfo?.label || 'Unknown')
    
    const trustedCount = makers.filter(maker => 
      this.config.marketMaker.trustedMakers.includes(maker)
    ).length
    
    const blacklistedCount = makers.filter(maker => 
      this.config.marketMaker.blacklistedMakers.includes(maker)
    ).length

    const reputation = trustedCount / Math.max(makers.length, 1) * 100
    const trustScore = Math.max(0, reputation - (blacklistedCount * 25))

    const riskFlags: string[] = []
    if (blacklistedCount > 0) {
      riskFlags.push(`${blacklistedCount} blacklisted market makers detected`)
    }
    if (trustedCount === 0) {
      riskFlags.push('No trusted market makers in route')
    }

    return {
      makers: [...new Set(makers)],
      reputation,
      trustScore,
      riskFlags,
      verifiedMakers: trustedCount
    }
  }

  /**
   * Analyze token volatility
   */
  private async analyzeVolatility(inputMint: string, outputMint: string): Promise<VolatilityAnalysis> {
    try {
      // Get volatility data from cache or external API
      const cacheKey = `volatility:${inputMint}:${outputMint}`
      let volatilityData = await RedisService.getJSON<any>(cacheKey)
      
      if (!volatilityData) {
        // In a real implementation, this would call price APIs to get historical data
        volatilityData = {
          current24h: Math.random() * 30, // Mock data for now
          trend: ['increasing', 'decreasing', 'stable'][Math.floor(Math.random() * 3)]
        }
        
        // Cache for 5 minutes
        await RedisService.setJSON(cacheKey, volatilityData, 300)
      }

      let risk: 'low' | 'medium' | 'high' | 'extreme'
      if (volatilityData.current24h < 5) risk = 'low'
      else if (volatilityData.current24h < this.config.volatility.warningThreshold) risk = 'medium'
      else if (volatilityData.current24h < this.config.volatility.maxVolatility) risk = 'high'
      else risk = 'extreme'

      let recommendation: string
      if (risk === 'low' || risk === 'medium') {
        recommendation = 'Volatility within acceptable range'
      } else if (risk === 'high') {
        recommendation = 'High volatility detected - consider tighter slippage'
      } else {
        recommendation = 'Extreme volatility - recommend postponing trade'
      }

      return {
        current24h: volatilityData.current24h,
        trend: volatilityData.trend,
        risk,
        recommendation
      }
    } catch (error) {
      logger.debug('Volatility analysis failed, using conservative defaults:', error)
      return {
        current24h: 15, // Conservative default
        trend: 'stable',
        risk: 'medium',
        recommendation: 'Volatility data unavailable - proceed with caution'
      }
    }
  }

  /**
   * Analyze quote freshness
   */
  private analyzeFreshness(quoteTimestamp?: number): FreshnessAnalysis {
    if (!quoteTimestamp) {
      return {
        ageSeconds: 0,
        freshness: 'fresh',
        shouldRefresh: false
      }
    }

    const ageSeconds = (Date.now() - quoteTimestamp) / 1000

    let freshness: 'fresh' | 'acceptable' | 'stale' | 'expired'
    if (ageSeconds < 2) freshness = 'fresh'
    else if (ageSeconds < this.config.freshness.warningAge) freshness = 'acceptable'
    else if (ageSeconds < this.config.freshness.maxAge) freshness = 'stale'
    else freshness = 'expired'

    return {
      ageSeconds,
      freshness,
      shouldRefresh: ageSeconds >= this.config.freshness.warningAge
    }
  }

  /**
   * Calculate overall validation result
   */
  private calculateValidationResult(analyses: {
    priceImpactAnalysis: PriceImpactAnalysis
    routeAnalysis: RouteAnalysis
    liquidityAnalysis: LiquidityAnalysis
    marketMakerAnalysis: MarketMakerAnalysis
    volatilityAnalysis: VolatilityAnalysis
    freshnessAnalysis: FreshnessAnalysis
  }): ValidationResult {
    const warnings: string[] = []
    const errors: string[] = []
    let score = 100

    // Price impact scoring
    if (analyses.priceImpactAnalysis.impact > this.config.priceImpact.errorThreshold) {
      errors.push(`High price impact: ${analyses.priceImpactAnalysis.impact.toFixed(2)}%`)
      score -= 30
    } else if (analyses.priceImpactAnalysis.impact > this.config.priceImpact.warningThreshold) {
      warnings.push(`Moderate price impact: ${analyses.priceImpactAnalysis.impact.toFixed(2)}%`)
      score -= 10
    }

    // Route complexity scoring
    if (analyses.routeAnalysis.hops > this.config.routeComplexity.maxHops) {
      errors.push(`Route too complex: ${analyses.routeAnalysis.hops} hops`)
      score -= 20
    } else if (analyses.routeAnalysis.hops > this.config.routeComplexity.preferredHops) {
      warnings.push(`Complex route: ${analyses.routeAnalysis.hops} hops`)
      score -= 5
    }

    // Liquidity scoring
    if (analyses.liquidityAnalysis.liquidityRisk === 'high') {
      warnings.push('Low liquidity detected')
      score -= 15
    }

    // Market maker scoring
    if (analyses.marketMakerAnalysis.riskFlags.length > 0) {
      analyses.marketMakerAnalysis.riskFlags.forEach(flag => warnings.push(flag))
      score -= 10 * analyses.marketMakerAnalysis.riskFlags.length
    }

    // Volatility scoring
    if (analyses.volatilityAnalysis.risk === 'extreme') {
      errors.push('Extreme volatility detected')
      score -= 25
    } else if (analyses.volatilityAnalysis.risk === 'high') {
      warnings.push('High volatility detected')
      score -= 10
    }

    // Freshness scoring
    if (analyses.freshnessAnalysis.freshness === 'expired') {
      errors.push('Quote expired')
      score -= 20
    } else if (analyses.freshnessAnalysis.shouldRefresh) {
      warnings.push('Quote becoming stale')
      score -= 5
    }

    // Determine overall result
    score = Math.max(0, score)
    const isValid = errors.length === 0 && score >= 60
    
    let severity: 'info' | 'warning' | 'error' | 'critical'
    let recommendation: 'execute' | 'proceed_with_caution' | 'reject' | 'retry_later'

    if (errors.length > 0 || score < 40) {
      severity = 'critical'
      recommendation = 'reject'
    } else if (score < 60) {
      severity = 'error'
      recommendation = 'retry_later'
    } else if (warnings.length > 0 || score < 80) {
      severity = 'warning'
      recommendation = 'proceed_with_caution'
    } else {
      severity = 'info'
      recommendation = 'execute'
    }

    // Calculate execution probability based on all factors
    const executionProbability = Math.min(1, score / 100 * 
      (1 - analyses.priceImpactAnalysis.impact / 100) *
      (analyses.liquidityAnalysis.liquidityRisk === 'low' ? 1 : 0.8) *
      (analyses.marketMakerAnalysis.trustScore / 100)
    )

    // Estimate slippage based on various factors
    const estimatedSlippage = analyses.priceImpactAnalysis.impact * 1.2 + // Add buffer
      (analyses.routeAnalysis.hops - 1) * 0.1 + // Route complexity penalty
      (analyses.volatilityAnalysis.current24h / 100) * 0.5 // Volatility penalty

    return {
      isValid,
      severity,
      score,
      warnings,
      errors,
      details: analyses,
      recommendation,
      estimatedSlippage,
      executionProbability
    }
  }

  /**
   * Get historical price impact for similar trades
   */
  private async getHistoricalPriceImpact(
    inputMint: string, 
    outputMint: string, 
    amount: number
  ): Promise<number> {
    try {
      const cacheKey = `historical_impact:${inputMint}:${outputMint}:${Math.floor(amount / 1000000)}`
      const cached = await RedisService.getJSON<number>(cacheKey)
      
      if (cached !== null) {
        return cached
      }

      // In a real implementation, this would query historical data
      // For now, return a mock value based on trade size
      const historicalImpact = Math.min(10, (amount / 1000000) * 0.5)
      
      // Cache for 1 hour
      await RedisService.setJSON(cacheKey, historicalImpact, 3600)
      
      return historicalImpact
    } catch (error) {
      logger.debug('Failed to get historical price impact:', error)
      return 1.0 // Conservative default
    }
  }

  /**
   * Update validation configuration
   */
  public updateConfig(newConfig: Partial<QuoteValidationConfig>): void {
    this.config = { ...this.config, ...newConfig }
    logger.info('Quote validation configuration updated')
  }

  /**
   * Get current validation configuration
   */
  public getConfig(): QuoteValidationConfig {
    return { ...this.config }
  }

  /**
   * Health check
   */
  public async healthCheck(): Promise<boolean> {
    try {
      // Test basic functionality with a mock quote
      const mockQuote: Quote = {
        inputMint: 'So11111111111111111111111111111111111111112',
        outputMint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
        inAmount: '1000000',
        outAmount: '100000',
        priceImpactPct: 0.5,
        routePlan: [],
        fees: { jupiterFee: 0, priorityFee: 0, networkFee: 0, total: 0 }
      }

      await this.validateQuote(mockQuote, {
        inputMint: 'So11111111111111111111111111111111111111112',
        outputMint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
        amount: 1000000,
        slippageBps: 50
      }, Date.now())

      return true
    } catch (error) {
      logger.error('Quote validation service health check failed:', error)
      return false
    }
  }
}

// Export singleton instance
const quoteValidationServiceInstance = QuoteValidationService.getInstance()
export { quoteValidationServiceInstance as QuoteValidationService }