import { EventEmitter } from 'events'
import { logger } from '@/utils/logger'
import { DatabaseService } from '@/services/database'
import { RedisService } from '@/services/redis'
import { TradingService } from '@/services/tradingService'
import { PriceService } from '@/services/priceService'
import { PortfolioService } from '@/services/portfolioService'
import { TransactionRecordingService } from '@/services/transactionRecordingService'
import { AppError } from '@/middleware/errorHandler'
import type { PresetType } from '@memetrader-pro/shared'

export interface ExitStrategyConfig {
  enabled: boolean
  monitoringInterval: number // milliseconds
  executionDelay: number // milliseconds between checks and execution
  maxConcurrentExecutions: number
  riskManagementEnabled: boolean
  emergencyStopEnabled: boolean
  backtestingEnabled: boolean
  paperTradingMode: boolean
}

export interface ExitStrategy {
  id: string
  userId: string
  positionId: string
  name: string
  type: 'STOP_LOSS' | 'TAKE_PROFIT' | 'TRAILING_STOP' | 'MOON_BAG' | 'LADDER' | 'TIME_BASED' | 'COMPOSITE'
  
  // Strategy parameters
  stopLoss?: StopLossConfig
  takeProfits?: TakeProfitConfig[]
  trailingStop?: TrailingStopConfig
  moonBag?: MoonBagConfig
  ladder?: LadderConfig
  timeBased?: TimeBasedConfig
  
  // Execution settings
  executionMode: 'AUTOMATIC' | 'ALERT_ONLY' | 'MANUAL_CONFIRM'
  slippageTolerance: number
  presetToUse: PresetType
  mevProtection: boolean
  
  // Risk management
  maxLossPercent?: number
  minProfitPercent?: number
  cooldownPeriod?: number // milliseconds
  maxExecutionsPerDay?: number
  
  // Status and tracking
  status: 'ACTIVE' | 'PAUSED' | 'COMPLETED' | 'CANCELLED' | 'ERROR'
  executionState: ExitStrategyState
  
  // Metadata
  createdAt: Date
  updatedAt: Date
  lastExecuted?: Date
  executionCount: number
  totalRealized: number
}

export interface StopLossConfig {
  enabled: boolean
  type: 'FIXED_PRICE' | 'PERCENTAGE' | 'ATR_BASED' | 'SUPPORT_LEVEL'
  triggerPrice?: number
  triggerPercent?: number
  atrMultiplier?: number
  supportLevel?: number
  
  // Advanced features
  timeDelay?: number // milliseconds
  volumeConfirmation?: boolean
  partialExecution?: boolean
  partialPercent?: number
}

export interface TakeProfitConfig {
  id: string
  enabled: boolean
  type: 'FIXED_PRICE' | 'PERCENTAGE' | 'RISK_REWARD_RATIO' | 'RESISTANCE_LEVEL'
  triggerPrice?: number
  triggerPercent?: number
  riskRewardRatio?: number
  resistanceLevel?: number
  
  // Execution details
  sellPercent: number // percentage of position to sell
  executed: boolean
  executedAt?: Date
  executedPrice?: number
  
  // Advanced features
  timeWindow?: { start: number, end: number } // hours of day
  volumeThreshold?: number
  priceMovementConfirmation?: boolean
}

export interface TrailingStopConfig {
  enabled: boolean
  type: 'PERCENTAGE' | 'ATR_BASED' | 'FIXED_AMOUNT'
  trailPercent?: number
  trailAmount?: number
  atrMultiplier?: number
  
  // Current state
  highWaterMark: number
  currentStopPrice: number
  
  // Advanced features
  activationPrice?: number // only start trailing after this price
  minProfit?: number // minimum profit before activation
  accelerated?: boolean // increase trail rate with profit
}

export interface MoonBagConfig {
  enabled: boolean
  reservePercent: number // percentage to keep as moon bag
  triggerConditions: {
    minProfitPercent: number
    priceMultiple?: number
    timeHolding?: number // milliseconds
    volumeSpike?: boolean
  }
  
  // Moon bag rules
  neverSell: boolean
  sellRules?: {
    priceTargets: number[]
    sellPercentages: number[]
  }
}

export interface LadderConfig {
  enabled: boolean
  type: 'PROFIT_LADDER' | 'LOSS_LADDER'
  steps: LadderStep[]
  resetOnReversal: boolean
}

export interface LadderStep {
  triggerPercent: number
  sellPercent: number
  executed: boolean
  executedAt?: Date
  executedPrice?: number
}

export interface TimeBasedConfig {
  enabled: boolean
  type: 'SCHEDULED_EXIT' | 'HOLDING_PERIOD' | 'MARKET_HOURS'
  scheduledTime?: Date
  holdingPeriod?: number // milliseconds
  marketHours?: {
    exitBeforeClose: boolean
    minutesBeforeClose: number
    weekendsOnly: boolean
  }
}

export interface ExitStrategyState {
  currentPrice: number
  entryPrice: number
  currentPnL: number
  currentPnLPercent: number
  highWaterMark: number
  lowWaterMark: number
  
  // Execution tracking
  lastCheckTime: Date
  nextCheckTime: Date
  executionHistory: StrategyExecution[]
  
  // Alert tracking
  pendingAlerts: StrategyAlert[]
  alertHistory: StrategyAlert[]
  
  // Risk metrics
  riskScore: number
  volatility: number
  correlation: number
}

export interface StrategyExecution {
  id: string
  timestamp: Date
  type: 'STOP_LOSS' | 'TAKE_PROFIT' | 'TRAILING_STOP' | 'MOON_BAG' | 'LADDER' | 'TIME_BASED'
  trigger: string
  
  // Execution details
  price: number
  quantity: number
  percentage: number
  transactionHash?: string
  
  // Results
  success: boolean
  error?: string
  realizedPnL: number
  remainingQuantity: number
  
  // Market conditions
  marketConditions: {
    volume: number
    volatility: number
    spread: number
    liquidity: number
  }
}

export interface StrategyAlert {
  id: string
  timestamp: Date
  type: 'EXECUTION_TRIGGERED' | 'RISK_WARNING' | 'MARKET_CONDITION' | 'SYSTEM_ERROR'
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  title: string
  message: string
  data: any
  acknowledged: boolean
}

export interface BacktestResult {
  strategy: ExitStrategy
  period: { start: Date, end: Date }
  performance: {
    totalTrades: number
    winRate: number
    avgWin: number
    avgLoss: number
    maxDrawdown: number
    sharpeRatio: number
    totalReturn: number
    totalReturnPercent: number
  }
  trades: BacktestTrade[]
}

export interface BacktestTrade {
  timestamp: Date
  type: string
  price: number
  quantity: number
  pnl: number
  reason: string
}

class ExitStrategyService extends EventEmitter {
  private static instance: ExitStrategyService
  private config: ExitStrategyConfig
  private activeStrategies: Map<string, ExitStrategy> = new Map()
  private executionQueue: Map<string, StrategyExecution> = new Map()
  private monitoringInterval: NodeJS.Timeout | null = null
  private isExecuting = false

  private constructor() {
    super()
    
    this.config = {
      enabled: true,
      monitoringInterval: 5000, // 5 seconds
      executionDelay: 1000, // 1 second
      maxConcurrentExecutions: 10,
      riskManagementEnabled: true,
      emergencyStopEnabled: true,
      backtestingEnabled: true,
      paperTradingMode: false
    }

    this.initializeService()
  }

  public static getInstance(): ExitStrategyService {
    if (!ExitStrategyService.instance) {
      ExitStrategyService.instance = new ExitStrategyService()
    }
    return ExitStrategyService.instance
  }

  /**
   * Create a new exit strategy
   */
  public async createExitStrategy(strategyData: Omit<ExitStrategy, 'id' | 'createdAt' | 'updatedAt' | 'executionState' | 'executionCount' | 'totalRealized'>): Promise<ExitStrategy> {
    try {
      // Validate strategy configuration
      this.validateStrategyConfig(strategyData)

      // Create strategy in database
      const dbStrategy = await DatabaseService.client.exitStrategy.create({
        data: {
          userId: strategyData.userId,
          positionId: strategyData.positionId,
          type: strategyData.type,
          stopLoss: strategyData.stopLoss as any,
          profitTargets: strategyData.takeProfits as any,
          moonBag: strategyData.moonBag as any,
          customName: strategyData.name,
          locked: false,
          executionState: {
            currentPrice: 0,
            entryPrice: 0,
            currentPnL: 0,
            currentPnLPercent: 0,
            highWaterMark: 0,
            lowWaterMark: 0,
            lastCheckTime: new Date(),
            nextCheckTime: new Date(Date.now() + this.config.monitoringInterval),
            executionHistory: [],
            pendingAlerts: [],
            alertHistory: [],
            riskScore: 0,
            volatility: 0,
            correlation: 0
          } as any
        }
      })

      // Convert to our format
      const strategy: ExitStrategy = {
        id: dbStrategy.id,
        userId: dbStrategy.userId,
        positionId: dbStrategy.positionId,
        name: dbStrategy.customName || 'Unnamed Strategy',
        type: dbStrategy.type as any,
        stopLoss: dbStrategy.stopLoss as any,
        takeProfits: dbStrategy.profitTargets as any,
        moonBag: dbStrategy.moonBag as any,
        executionMode: 'AUTOMATIC',
        slippageTolerance: 2.0,
        presetToUse: 'DEFAULT',
        mevProtection: true,
        status: 'ACTIVE',
        executionState: dbStrategy.executionState as any,
        createdAt: dbStrategy.createdAt,
        updatedAt: dbStrategy.updatedAt,
        executionCount: 0,
        totalRealized: 0,
        ...strategyData
      }

      // Add to active strategies
      this.activeStrategies.set(strategy.id, strategy)

      // Initialize strategy state
      await this.initializeStrategyState(strategy)

      logger.info('Exit strategy created', {
        strategyId: strategy.id,
        type: strategy.type,
        userId: strategy.userId,
        positionId: strategy.positionId
      })

      this.emit('strategyCreated', strategy)
      return strategy

    } catch (error) {
      logger.error('Failed to create exit strategy:', error)
      throw new AppError('Exit strategy creation failed', 500, 'STRATEGY_CREATION_FAILED')
    }
  }

  /**
   * Update an existing exit strategy
   */
  public async updateExitStrategy(strategyId: string, updates: Partial<ExitStrategy>): Promise<ExitStrategy> {
    try {
      const strategy = this.activeStrategies.get(strategyId)
      if (!strategy) {
        throw new AppError('Strategy not found', 404, 'STRATEGY_NOT_FOUND')
      }

      // Validate updates
      if (updates.stopLoss || updates.takeProfits || updates.moonBag) {
        this.validateStrategyConfig({ ...strategy, ...updates })
      }

      // Update database
      await DatabaseService.client.exitStrategy.update({
        where: { id: strategyId },
        data: {
          type: updates.type || strategy.type,
          stopLoss: updates.stopLoss as any || strategy.stopLoss,
          profitTargets: updates.takeProfits as any || strategy.takeProfits,
          moonBag: updates.moonBag as any || strategy.moonBag,
          customName: updates.name || strategy.name,
          updatedAt: new Date()
        }
      })

      // Update in-memory strategy
      const updatedStrategy = { ...strategy, ...updates, updatedAt: new Date() }
      this.activeStrategies.set(strategyId, updatedStrategy)

      logger.info('Exit strategy updated', { strategyId, updates: Object.keys(updates) })
      this.emit('strategyUpdated', updatedStrategy)

      return updatedStrategy

    } catch (error) {
      logger.error('Failed to update exit strategy:', error)
      throw new AppError('Exit strategy update failed', 500, 'STRATEGY_UPDATE_FAILED')
    }
  }

  /**
   * Delete an exit strategy
   */
  public async deleteExitStrategy(strategyId: string, userId: string): Promise<void> {
    try {
      const strategy = this.activeStrategies.get(strategyId)
      if (!strategy || strategy.userId !== userId) {
        throw new AppError('Strategy not found', 404, 'STRATEGY_NOT_FOUND')
      }

      // Remove from database
      await DatabaseService.client.exitStrategy.delete({
        where: { id: strategyId }
      })

      // Remove from active strategies
      this.activeStrategies.delete(strategyId)

      logger.info('Exit strategy deleted', { strategyId, userId })
      this.emit('strategyDeleted', { strategyId, userId })

    } catch (error) {
      logger.error('Failed to delete exit strategy:', error)
      throw new AppError('Exit strategy deletion failed', 500, 'STRATEGY_DELETION_FAILED')
    }
  }

  /**
   * Get all strategies for a user
   */
  public async getUserStrategies(userId: string): Promise<ExitStrategy[]> {
    try {
      const strategies = Array.from(this.activeStrategies.values())
        .filter(strategy => strategy.userId === userId)

      return strategies

    } catch (error) {
      logger.error('Failed to get user strategies:', error)
      throw new AppError('Failed to retrieve strategies', 500, 'STRATEGY_RETRIEVAL_FAILED')
    }
  }

  /**
   * Get specific strategy details
   */
  public async getStrategy(strategyId: string, userId: string): Promise<ExitStrategy | null> {
    try {
      const strategy = this.activeStrategies.get(strategyId)
      
      if (!strategy || strategy.userId !== userId) {
        return null
      }

      return strategy

    } catch (error) {
      logger.error('Failed to get strategy:', error)
      return null
    }
  }

  /**
   * Execute strategy monitoring cycle
   */
  public async executeMonitoringCycle(): Promise<void> {
    if (this.isExecuting || !this.config.enabled) {
      return
    }

    this.isExecuting = true

    try {
      const activeStrategies = Array.from(this.activeStrategies.values())
        .filter(strategy => strategy.status === 'ACTIVE')

      logger.debug('Monitoring cycle started', { strategiesCount: activeStrategies.length })

      // Process strategies in parallel with concurrency limit
      const chunks = this.chunkArray(activeStrategies, this.config.maxConcurrentExecutions)
      
      for (const chunk of chunks) {
        await Promise.all(chunk.map(strategy => this.monitorStrategy(strategy)))
      }

      logger.debug('Monitoring cycle completed')

    } catch (error) {
      logger.error('Monitoring cycle failed:', error)
    } finally {
      this.isExecuting = false
    }
  }

  /**
   * Monitor individual strategy
   */
  private async monitorStrategy(strategy: ExitStrategy): Promise<void> {
    try {
      // Get current position data
      const position = await PortfolioService.getPositionDetails(strategy.positionId, strategy.userId)
      if (!position) {
        logger.warn('Position not found for strategy', { strategyId: strategy.id })
        return
      }

      // Update strategy state
      await this.updateStrategyState(strategy, position)

      // Check execution conditions
      const executions = await this.checkExecutionConditions(strategy, position)

      // Execute triggered strategies
      for (const execution of executions) {
        await this.executeStrategy(strategy, execution)
      }

    } catch (error) {
      logger.error('Strategy monitoring failed:', error, { strategyId: strategy.id })
      
      // Create error alert
      await this.createAlert(strategy, {
        type: 'SYSTEM_ERROR',
        severity: 'HIGH',
        title: 'Strategy Monitoring Error',
        message: `Error monitoring strategy: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: { error: error instanceof Error ? error.stack : error }
      })
    }
  }

  /**
   * Check if strategy execution conditions are met
   */
  private async checkExecutionConditions(strategy: ExitStrategy, position: any): Promise<StrategyExecution[]> {
    const executions: StrategyExecution[] = []
    const state = strategy.executionState

    // Check stop loss conditions
    if (strategy.stopLoss?.enabled) {
      const stopLossExecution = this.checkStopLossConditions(strategy, position, state)
      if (stopLossExecution) executions.push(stopLossExecution)
    }

    // Check take profit conditions
    if (strategy.takeProfits) {
      const takeProfitExecutions = this.checkTakeProfitConditions(strategy, position, state)
      executions.push(...takeProfitExecutions)
    }

    // Check trailing stop conditions
    if (strategy.trailingStop?.enabled) {
      const trailingStopExecution = this.checkTrailingStopConditions(strategy, position, state)
      if (trailingStopExecution) executions.push(trailingStopExecution)
    }

    // Check moon bag conditions
    if (strategy.moonBag?.enabled) {
      const moonBagExecution = this.checkMoonBagConditions(strategy, position, state)
      if (moonBagExecution) executions.push(moonBagExecution)
    }

    // Check ladder conditions
    if (strategy.ladder?.enabled) {
      const ladderExecutions = this.checkLadderConditions(strategy, position, state)
      executions.push(...ladderExecutions)
    }

    // Check time-based conditions
    if (strategy.timeBased?.enabled) {
      const timeBasedExecution = this.checkTimeBasedConditions(strategy, position, state)
      if (timeBasedExecution) executions.push(timeBasedExecution)
    }

    return executions
  }

  /**
   * Check stop loss conditions
   */
  private checkStopLossConditions(strategy: ExitStrategy, position: any, state: ExitStrategyState): StrategyExecution | null {
    const stopLoss = strategy.stopLoss!
    const currentPrice = position.currentPrice
    let triggered = false
    let triggerReason = ''

    switch (stopLoss.type) {
      case 'FIXED_PRICE':
        if (stopLoss.triggerPrice && currentPrice <= stopLoss.triggerPrice) {
          triggered = true
          triggerReason = `Price ${currentPrice} <= Stop Loss ${stopLoss.triggerPrice}`
        }
        break

      case 'PERCENTAGE':
        if (stopLoss.triggerPercent) {
          const lossPercent = ((position.entryPrice - currentPrice) / position.entryPrice) * 100
          if (lossPercent >= stopLoss.triggerPercent) {
            triggered = true
            triggerReason = `Loss ${lossPercent.toFixed(2)}% >= Stop Loss ${stopLoss.triggerPercent}%`
          }
        }
        break

      case 'ATR_BASED':
        // Would implement ATR-based stop loss calculation
        break

      case 'SUPPORT_LEVEL':
        if (stopLoss.supportLevel && currentPrice <= stopLoss.supportLevel) {
          triggered = true
          triggerReason = `Price ${currentPrice} broke support ${stopLoss.supportLevel}`
        }
        break
    }

    if (triggered) {
      return {
        id: `sl_${Date.now()}`,
        timestamp: new Date(),
        type: 'STOP_LOSS',
        trigger: triggerReason,
        price: currentPrice,
        quantity: stopLoss.partialExecution ? 
          position.quantity * (stopLoss.partialPercent || 100) / 100 : 
          position.quantity,
        percentage: stopLoss.partialExecution ? (stopLoss.partialPercent || 100) : 100,
        success: false,
        realizedPnL: 0,
        remainingQuantity: 0,
        marketConditions: {
          volume: 0, // Would get from market data
          volatility: state.volatility,
          spread: 0,
          liquidity: 0
        }
      }
    }

    return null
  }

  /**
   * Check take profit conditions
   */
  private checkTakeProfitConditions(strategy: ExitStrategy, position: any, state: ExitStrategyState): StrategyExecution[] {
    const executions: StrategyExecution[] = []
    const currentPrice = position.currentPrice

    if (!strategy.takeProfits) return executions

    for (const takeProfit of strategy.takeProfits) {
      if (!takeProfit.enabled || takeProfit.executed) continue

      let triggered = false
      let triggerReason = ''

      switch (takeProfit.type) {
        case 'FIXED_PRICE':
          if (takeProfit.triggerPrice && currentPrice >= takeProfit.triggerPrice) {
            triggered = true
            triggerReason = `Price ${currentPrice} >= Take Profit ${takeProfit.triggerPrice}`
          }
          break

        case 'PERCENTAGE':
          if (takeProfit.triggerPercent) {
            const profitPercent = ((currentPrice - position.entryPrice) / position.entryPrice) * 100
            if (profitPercent >= takeProfit.triggerPercent) {
              triggered = true
              triggerReason = `Profit ${profitPercent.toFixed(2)}% >= Take Profit ${takeProfit.triggerPercent}%`
            }
          }
          break

        case 'RISK_REWARD_RATIO':
          if (takeProfit.riskRewardRatio) {
            const profit = currentPrice - position.entryPrice
            const stopLossRisk = strategy.stopLoss?.triggerPrice ? 
              position.entryPrice - strategy.stopLoss.triggerPrice : 
              position.entryPrice * 0.1 // Default 10% risk
            
            const currentRR = profit / stopLossRisk
            if (currentRR >= takeProfit.riskRewardRatio) {
              triggered = true
              triggerReason = `Risk/Reward ${currentRR.toFixed(2)} >= Target ${takeProfit.riskRewardRatio}`
            }
          }
          break
      }

      if (triggered) {
        executions.push({
          id: `tp_${takeProfit.id}_${Date.now()}`,
          timestamp: new Date(),
          type: 'TAKE_PROFIT',
          trigger: triggerReason,
          price: currentPrice,
          quantity: position.quantity * takeProfit.sellPercent / 100,
          percentage: takeProfit.sellPercent,
          success: false,
          realizedPnL: 0,
          remainingQuantity: 0,
          marketConditions: {
            volume: 0,
            volatility: state.volatility,
            spread: 0,
            liquidity: 0
          }
        })
      }
    }

    return executions
  }

  /**
   * Check trailing stop conditions
   */
  private checkTrailingStopConditions(strategy: ExitStrategy, position: any, state: ExitStrategyState): StrategyExecution | null {
    const trailingStop = strategy.trailingStop!
    const currentPrice = position.currentPrice

    // Update high water mark
    if (currentPrice > trailingStop.highWaterMark) {
      trailingStop.highWaterMark = currentPrice
      
      // Update stop price based on trailing type
      switch (trailingStop.type) {
        case 'PERCENTAGE':
          trailingStop.currentStopPrice = currentPrice * (1 - (trailingStop.trailPercent || 0.05))
          break
        case 'FIXED_AMOUNT':
          trailingStop.currentStopPrice = currentPrice - (trailingStop.trailAmount || 0)
          break
        case 'ATR_BASED':
          // Would implement ATR-based trailing stop
          break
      }
    }

    // Check if trailing stop is triggered
    if (currentPrice <= trailingStop.currentStopPrice) {
      return {
        id: `ts_${Date.now()}`,
        timestamp: new Date(),
        type: 'TRAILING_STOP',
        trigger: `Price ${currentPrice} <= Trailing Stop ${trailingStop.currentStopPrice}`,
        price: currentPrice,
        quantity: position.quantity,
        percentage: 100,
        success: false,
        realizedPnL: 0,
        remainingQuantity: 0,
        marketConditions: {
          volume: 0,
          volatility: state.volatility,
          spread: 0,
          liquidity: 0
        }
      }
    }

    return null
  }

  /**
   * Check moon bag conditions
   */
  private checkMoonBagConditions(strategy: ExitStrategy, position: any, state: ExitStrategyState): StrategyExecution | null {
    const moonBag = strategy.moonBag!
    const currentPrice = position.currentPrice
    const profitPercent = ((currentPrice - position.entryPrice) / position.entryPrice) * 100

    // Check if moon bag conditions are met
    if (profitPercent >= moonBag.triggerConditions.minProfitPercent) {
      const sellPercent = 100 - moonBag.reservePercent
      
      return {
        id: `mb_${Date.now()}`,
        timestamp: new Date(),
        type: 'MOON_BAG',
        trigger: `Profit ${profitPercent.toFixed(2)}% >= Moon Bag Trigger ${moonBag.triggerConditions.minProfitPercent}%`,
        price: currentPrice,
        quantity: position.quantity * sellPercent / 100,
        percentage: sellPercent,
        success: false,
        realizedPnL: 0,
        remainingQuantity: 0,
        marketConditions: {
          volume: 0,
          volatility: state.volatility,
          spread: 0,
          liquidity: 0
        }
      }
    }

    return null
  }

  /**
   * Check ladder conditions
   */
  private checkLadderConditions(strategy: ExitStrategy, position: any, state: ExitStrategyState): StrategyExecution[] {
    const executions: StrategyExecution[] = []
    const ladder = strategy.ladder!
    const currentPrice = position.currentPrice
    const profitPercent = ((currentPrice - position.entryPrice) / position.entryPrice) * 100

    for (const step of ladder.steps) {
      if (step.executed) continue

      let triggered = false
      if (ladder.type === 'PROFIT_LADDER' && profitPercent >= step.triggerPercent) {
        triggered = true
      } else if (ladder.type === 'LOSS_LADDER' && profitPercent <= -Math.abs(step.triggerPercent)) {
        triggered = true
      }

      if (triggered) {
        executions.push({
          id: `ladder_${step.triggerPercent}_${Date.now()}`,
          timestamp: new Date(),
          type: 'LADDER',
          trigger: `${ladder.type} step at ${step.triggerPercent}%`,
          price: currentPrice,
          quantity: position.quantity * step.sellPercent / 100,
          percentage: step.sellPercent,
          success: false,
          realizedPnL: 0,
          remainingQuantity: 0,
          marketConditions: {
            volume: 0,
            volatility: state.volatility,
            spread: 0,
            liquidity: 0
          }
        })
      }
    }

    return executions
  }

  /**
   * Check time-based conditions
   */
  private checkTimeBasedConditions(strategy: ExitStrategy, position: any, state: ExitStrategyState): StrategyExecution | null {
    const timeBased = strategy.timeBased!
    const now = new Date()

    let triggered = false
    let triggerReason = ''

    switch (timeBased.type) {
      case 'SCHEDULED_EXIT':
        if (timeBased.scheduledTime && now >= timeBased.scheduledTime) {
          triggered = true
          triggerReason = `Scheduled exit time reached: ${timeBased.scheduledTime}`
        }
        break

      case 'HOLDING_PERIOD':
        if (timeBased.holdingPeriod) {
          const holdingTime = now.getTime() - position.entryTimestamp.getTime()
          if (holdingTime >= timeBased.holdingPeriod) {
            triggered = true
            triggerReason = `Holding period ${timeBased.holdingPeriod}ms reached`
          }
        }
        break

      case 'MARKET_HOURS':
        // Would implement market hours logic
        break
    }

    if (triggered) {
      return {
        id: `tb_${Date.now()}`,
        timestamp: new Date(),
        type: 'TIME_BASED',
        trigger: triggerReason,
        price: position.currentPrice,
        quantity: position.quantity,
        percentage: 100,
        success: false,
        realizedPnL: 0,
        remainingQuantity: 0,
        marketConditions: {
          volume: 0,
          volatility: state.volatility,
          spread: 0,
          liquidity: 0
        }
      }
    }

    return null
  }

  /**
   * Execute strategy
   */
  private async executeStrategy(strategy: ExitStrategy, execution: StrategyExecution): Promise<void> {
    try {
      if (this.config.paperTradingMode) {
        // Paper trading simulation
        execution.success = true
        execution.realizedPnL = (execution.price - strategy.executionState.entryPrice) * execution.quantity
        logger.info('Paper trade executed', { strategyId: strategy.id, execution })
        return
      }

      if (strategy.executionMode === 'ALERT_ONLY') {
        // Create alert instead of executing
        await this.createAlert(strategy, {
          type: 'EXECUTION_TRIGGERED',
          severity: 'MEDIUM',
          title: `${execution.type} Triggered`,
          message: execution.trigger,
          data: execution
        })
        return
      }

      // Execute actual trade
      const tradeResult = await TradingService.executeTrade(
        {
          tokenIn: 'TOKEN_ADDRESS', // Would get from position
          tokenOut: 'So11111111111111111111111111111111111111112', // SOL
          amount: execution.quantity,
          slippage: strategy.slippageTolerance,
          preset: strategy.presetToUse,
          strategyId: strategy.id
        },
        'USER_WALLET', // Would get from user
        strategy.userId
      )

      if (tradeResult.success) {
        execution.success = true
        execution.transactionHash = tradeResult.transactionHash
        execution.realizedPnL = (execution.price - strategy.executionState.entryPrice) * execution.quantity
        
        // Update strategy state
        strategy.executionState.executionHistory.push(execution)
        strategy.executionCount++
        strategy.totalRealized += execution.realizedPnL
        strategy.lastExecuted = new Date()

        // Mark take profit steps as executed
        if (execution.type === 'TAKE_PROFIT' && strategy.takeProfits) {
          const takeProfit = strategy.takeProfits.find(tp => execution.id.includes(tp.id))
          if (takeProfit) {
            takeProfit.executed = true
            takeProfit.executedAt = new Date()
            takeProfit.executedPrice = execution.price
          }
        }

        // Mark ladder steps as executed
        if (execution.type === 'LADDER' && strategy.ladder) {
          const step = strategy.ladder.steps.find(s => execution.id.includes(s.triggerPercent.toString()))
          if (step) {
            step.executed = true
            step.executedAt = new Date()
            step.executedPrice = execution.price
          }
        }

        // Update database
        await DatabaseService.client.exitStrategy.update({
          where: { id: strategy.id },
          data: {
            executionState: strategy.executionState as any,
            lastUpdate: new Date()
          }
        })

        logger.info('Strategy executed successfully', {
          strategyId: strategy.id,
          executionType: execution.type,
          transactionHash: execution.transactionHash,
          realizedPnL: execution.realizedPnL
        })

        this.emit('strategyExecuted', { strategy, execution })

      } else {
        execution.success = false
        execution.error = tradeResult.error

        logger.error('Strategy execution failed', {
          strategyId: strategy.id,
          executionType: execution.type,
          error: tradeResult.error
        })

        await this.createAlert(strategy, {
          type: 'SYSTEM_ERROR',
          severity: 'HIGH',
          title: 'Strategy Execution Failed',
          message: `Failed to execute ${execution.type}: ${tradeResult.error}`,
          data: execution
        })
      }

    } catch (error) {
      execution.success = false
      execution.error = error instanceof Error ? error.message : 'Unknown error'
      
      logger.error('Strategy execution error:', error, { strategyId: strategy.id })
      
      await this.createAlert(strategy, {
        type: 'SYSTEM_ERROR',
        severity: 'CRITICAL',
        title: 'Strategy Execution Error',
        message: `Critical error executing ${execution.type}: ${execution.error}`,
        data: { execution, error: error instanceof Error ? error.stack : error }
      })
    }
  }

  /**
   * Initialize strategy state
   */
  private async initializeStrategyState(strategy: ExitStrategy): Promise<void> {
    try {
      const position = await PortfolioService.getPositionDetails(strategy.positionId, strategy.userId)
      if (!position) return

      strategy.executionState = {
        currentPrice: position.currentPrice,
        entryPrice: position.averageEntryPrice,
        currentPnL: position.totalPnL,
        currentPnLPercent: position.totalPnLPercent,
        highWaterMark: position.currentPrice,
        lowWaterMark: position.currentPrice,
        lastCheckTime: new Date(),
        nextCheckTime: new Date(Date.now() + this.config.monitoringInterval),
        executionHistory: [],
        pendingAlerts: [],
        alertHistory: [],
        riskScore: 0,
        volatility: 0,
        correlation: 0
      }

      // Initialize trailing stop
      if (strategy.trailingStop?.enabled) {
        strategy.trailingStop.highWaterMark = position.currentPrice
        strategy.trailingStop.currentStopPrice = position.currentPrice * 0.95 // Default 5% trail
      }

    } catch (error) {
      logger.error('Failed to initialize strategy state:', error)
    }
  }

  /**
   * Update strategy state
   */
  private async updateStrategyState(strategy: ExitStrategy, position: any): Promise<void> {
    const state = strategy.executionState
    
    state.currentPrice = position.currentPrice
    state.currentPnL = position.totalPnL
    state.currentPnLPercent = position.totalPnLPercent
    state.lastCheckTime = new Date()
    state.nextCheckTime = new Date(Date.now() + this.config.monitoringInterval)
    
    // Update high/low water marks
    state.highWaterMark = Math.max(state.highWaterMark, position.currentPrice)
    state.lowWaterMark = Math.min(state.lowWaterMark, position.currentPrice)

    // Update risk metrics (simplified)
    state.riskScore = this.calculateRiskScore(position)
    state.volatility = this.calculateVolatility(position)
  }

  /**
   * Create strategy alert
   */
  private async createAlert(strategy: ExitStrategy, alertData: Omit<StrategyAlert, 'id' | 'timestamp' | 'acknowledged'>): Promise<void> {
    const alert: StrategyAlert = {
      id: `alert_${Date.now()}`,
      timestamp: new Date(),
      acknowledged: false,
      ...alertData
    }

    strategy.executionState.pendingAlerts.push(alert)
    strategy.executionState.alertHistory.push(alert)

    // Broadcast alert
    await RedisService.publishJSON('strategy_alert', {
      userId: strategy.userId,
      strategyId: strategy.id,
      alert
    })

    this.emit('strategyAlert', { strategy, alert })
  }

  /**
   * Validate strategy configuration
   */
  private validateStrategyConfig(strategy: any): void {
    if (!strategy.userId || !strategy.positionId) {
      throw new Error('User ID and Position ID are required')
    }

    if (!strategy.type) {
      throw new Error('Strategy type is required')
    }

    // Validate specific configurations
    if (strategy.stopLoss?.enabled && !strategy.stopLoss.triggerPrice && !strategy.stopLoss.triggerPercent) {
      throw new Error('Stop loss requires trigger price or percentage')
    }

    if (strategy.takeProfits?.length) {
      for (const tp of strategy.takeProfits) {
        if (!tp.triggerPrice && !tp.triggerPercent) {
          throw new Error('Take profit requires trigger price or percentage')
        }
        if (tp.sellPercent <= 0 || tp.sellPercent > 100) {
          throw new Error('Take profit sell percentage must be between 0 and 100')
        }
      }
    }
  }

  /**
   * Initialize service
   */
  private initializeService(): void {
    // Load active strategies from database
    this.loadActiveStrategies()

    // Start monitoring
    if (this.config.enabled) {
      this.monitoringInterval = setInterval(() => {
        this.executeMonitoringCycle()
      }, this.config.monitoringInterval)
    }
  }

  /**
   * Load active strategies from database
   */
  private async loadActiveStrategies(): Promise<void> {
    try {
      const dbStrategies = await DatabaseService.client.exitStrategy.findMany({
        where: { locked: false }
      })

      for (const dbStrategy of dbStrategies) {
        const strategy: ExitStrategy = {
          id: dbStrategy.id,
          userId: dbStrategy.userId,
          positionId: dbStrategy.positionId || '',
          name: dbStrategy.customName || 'Unnamed Strategy',
          type: dbStrategy.type as any,
          stopLoss: dbStrategy.stopLoss as any,
          takeProfits: dbStrategy.profitTargets as any,
          moonBag: dbStrategy.moonBag as any,
          executionMode: 'AUTOMATIC',
          slippageTolerance: 2.0,
          presetToUse: 'DEFAULT',
          mevProtection: true,
          status: 'ACTIVE',
          executionState: dbStrategy.executionState as any || {
            currentPrice: 0,
            entryPrice: 0,
            currentPnL: 0,
            currentPnLPercent: 0,
            highWaterMark: 0,
            lowWaterMark: 0,
            lastCheckTime: new Date(),
            nextCheckTime: new Date(),
            executionHistory: [],
            pendingAlerts: [],
            alertHistory: [],
            riskScore: 0,
            volatility: 0,
            correlation: 0
          },
          createdAt: dbStrategy.createdAt,
          updatedAt: dbStrategy.updatedAt,
          executionCount: 0,
          totalRealized: 0
        }

        this.activeStrategies.set(strategy.id, strategy)
      }

      logger.info('Loaded active exit strategies', { count: this.activeStrategies.size })

    } catch (error) {
      logger.error('Failed to load active strategies:', error)
    }
  }

  /**
   * Helper methods
   */
  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks = []
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size))
    }
    return chunks
  }

  private calculateRiskScore(position: any): number {
    // Simplified risk score calculation
    const pnlPercent = Math.abs(position.totalPnLPercent)
    return Math.min(pnlPercent * 2, 100)
  }

  private calculateVolatility(position: any): number {
    // Simplified volatility calculation
    return Math.random() * 50 // Would calculate from price history
  }

  /**
   * Get current configuration
   */
  public getConfig(): ExitStrategyConfig {
    return { ...this.config }
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<ExitStrategyConfig>): void {
    this.config = { ...this.config, ...newConfig }
    
    // Restart monitoring if interval changed
    if (newConfig.monitoringInterval) {
      if (this.monitoringInterval) {
        clearInterval(this.monitoringInterval)
      }
      this.initializeService()
    }
    
    logger.info('Exit strategy service configuration updated')
  }

  /**
   * Health check
   */
  public async healthCheck(): Promise<boolean> {
    try {
      // Test database connectivity
      await DatabaseService.client.exitStrategy.count({ take: 1 })
      
      // Check service state
      const activeCount = this.activeStrategies.size
      const executionQueueSize = this.executionQueue.size
      
      // Consider healthy if not overloaded
      return activeCount < 10000 && executionQueueSize < 1000
    } catch (error) {
      logger.error('Exit strategy service health check failed:', error)
      return false
    }
  }

  /**
   * Shutdown cleanup
   */
  public shutdown(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = null
    }

    this.activeStrategies.clear()
    this.executionQueue.clear()
    this.removeAllListeners()
    
    logger.info('Exit strategy service shutdown completed')
  }
}

// Export singleton instance
const exitStrategyServiceInstance = ExitStrategyService.getInstance()
export { exitStrategyServiceInstance as ExitStrategyService }