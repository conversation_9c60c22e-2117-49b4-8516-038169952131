import { Connection, Commitment, ConnectionConfig } from '@solana/web3.js'
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios'
import { EventEmitter } from 'events'
import { config } from '@/config/environment'
import { logger } from '@/utils/logger'
import { RedisService } from '@/services/redis'

interface RPCEndpoint {
  id: string
  url: string
  priority: number // Lower = higher priority
  weight: number // For load balancing
  maxRPS: number // Rate limiting
  timeout: number
  retryAttempts: number
  healthy: boolean
  lastHealthCheck: number
  errorCount: number
  totalRequests: number
  successfulRequests: number
  avgResponseTime: number
  lastError?: string
}

interface RPCRequest {
  method: string
  params: any[]
  id?: string | number
  timeout?: number
  retryAttempts?: number
  skipFailover?: boolean
}

interface RPCResponse {
  jsonrpc: string
  id: string | number
  result?: any
  error?: {
    code: number
    message: string
    data?: any
  }
}

interface HealthCheckResult {
  endpoint: RPCEndpoint
  responseTime: number
  success: boolean
  error?: string
  blockHeight?: number
  version?: string
}

interface ConnectionPoolEntry {
  connection: Connection
  lastUsed: number
  inUse: boolean
  requests: number
}

class HeliusRPCService extends EventEmitter {
  private static instance: HeliusRPCService
  private endpoints: Map<string, RPCEndpoint> = new Map()
  private connectionPool: Map<string, ConnectionPoolEntry> = new Map()
  private axiosInstances: Map<string, AxiosInstance> = new Map()
  private healthCheckInterval: NodeJS.Timeout | null = null
  private rateLimiters: Map<string, { tokens: number; lastRefill: number }> = new Map()
  private circuitBreakers: Map<string, { isOpen: boolean; failureCount: number; lastFailure: number }> = new Map()
  private currentEndpointIndex = 0
  private requestQueue: Array<{ request: RPCRequest; resolve: Function; reject: Function }> = []
  private processingQueue = false

  // Configuration
  private readonly HEALTH_CHECK_INTERVAL = 30000 // 30 seconds
  private readonly POOL_SIZE_PER_ENDPOINT = 5
  private readonly CONNECTION_TIMEOUT = 10000
  private readonly CIRCUIT_BREAKER_THRESHOLD = 5
  private readonly CIRCUIT_BREAKER_TIMEOUT = 60000 // 1 minute
  private readonly MAX_QUEUE_SIZE = 1000

  private constructor() {
    super()
    this.setMaxListeners(0)
    this.initializeEndpoints()
    this.startHealthChecking()
  }

  public static getInstance(): HeliusRPCService {
    if (!HeliusRPCService.instance) {
      HeliusRPCService.instance = new HeliusRPCService()
    }
    return HeliusRPCService.instance
  }

  /**
   * Initialize RPC endpoints with different priorities and configurations
   */
  private initializeEndpoints(): void {
    const heliusApiKey = config.helius.apiKey
    
    // Primary Helius endpoints (Free tier: 10 RPS for RPC)
    this.addEndpoint({
      id: 'helius-mainnet-primary',
      url: `https://mainnet.helius-rpc.com/?api-key=${heliusApiKey}`,
      priority: 1,
      weight: 100,
      maxRPS: 8, // Conservative limit for free tier (10 RPS max)
      timeout: 5000,
      retryAttempts: 3,
      healthy: true,
      lastHealthCheck: 0,
      errorCount: 0,
      totalRequests: 0,
      successfulRequests: 0,
      avgResponseTime: 0
    })

    this.addEndpoint({
      id: 'helius-mainnet-secondary',
      url: `https://rpc.helius.xyz/?api-key=${heliusApiKey}`,
      priority: 2,
      weight: 80,
      maxRPS: 5, // Secondary endpoint with lower limit
      timeout: 6000,
      retryAttempts: 3,
      healthy: true,
      lastHealthCheck: 0,
      errorCount: 0,
      totalRequests: 0,
      successfulRequests: 0,
      avgResponseTime: 0
    })

    // Backup endpoints (public)
    this.addEndpoint({
      id: 'solana-mainnet-public',
      url: 'https://api.mainnet-beta.solana.com',
      priority: 10,
      weight: 30,
      maxRPS: 20,
      timeout: 8000,
      retryAttempts: 2,
      healthy: true,
      lastHealthCheck: 0,
      errorCount: 0,
      totalRequests: 0,
      successfulRequests: 0,
      avgResponseTime: 0
    })

    this.addEndpoint({
      id: 'quicknode-mainnet',
      url: 'https://solana-mainnet.g.alchemy.com/v2/demo', // Demo endpoint
      priority: 11,
      weight: 20,
      maxRPS: 10,
      timeout: 10000,
      retryAttempts: 2,
      healthy: true,
      lastHealthCheck: 0,
      errorCount: 0,
      totalRequests: 0,
      successfulRequests: 0,
      avgResponseTime: 0
    })

    logger.info(`Initialized ${this.endpoints.size} RPC endpoints`, {
      endpoints: Array.from(this.endpoints.values()).map(e => ({
        id: e.id,
        priority: e.priority,
        maxRPS: e.maxRPS
      }))
    })

    // Initialize connection pools and axios instances
    this.initializeConnectionPools()
  }

  /**
   * Add a new RPC endpoint
   */
  public addEndpoint(endpoint: RPCEndpoint): void {
    this.endpoints.set(endpoint.id, endpoint)
    this.initializeRateLimiter(endpoint.id, endpoint.maxRPS)
    this.initializeCircuitBreaker(endpoint.id)
    this.initializeAxiosInstance(endpoint)
    this.initializeConnectionPool(endpoint)
  }

  /**
   * Remove an RPC endpoint
   */
  public removeEndpoint(endpointId: string): void {
    this.endpoints.delete(endpointId)
    this.rateLimiters.delete(endpointId)
    this.circuitBreakers.delete(endpointId)
    this.axiosInstances.delete(endpointId)
    this.connectionPool.delete(endpointId)
  }

  /**
   * Initialize connection pools for each endpoint
   */
  private initializeConnectionPools(): void {
    for (const endpoint of this.endpoints.values()) {
      this.initializeConnectionPool(endpoint)
      this.initializeAxiosInstance(endpoint)
    }
  }

  private initializeConnectionPool(endpoint: RPCEndpoint): void {
    const config: ConnectionConfig = {
      commitment: 'confirmed',
      wsEndpoint: endpoint.url.replace('https://', 'wss://').replace('http://', 'ws://'),
      httpHeaders: endpoint.url.includes('helius') ? {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      } : undefined
    }

    const connection = new Connection(endpoint.url, config)
    
    this.connectionPool.set(endpoint.id, {
      connection,
      lastUsed: Date.now(),
      inUse: false,
      requests: 0
    })
  }

  private initializeAxiosInstance(endpoint: RPCEndpoint): void {
    const instance = axios.create({
      baseURL: endpoint.url,
      timeout: endpoint.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    })

    // Add request interceptor for logging
    instance.interceptors.request.use(
      (config) => {
        const startTime = Date.now()
        config.metadata = { startTime }
        return config
      },
      (error) => Promise.reject(error)
    )

    // Add response interceptor for metrics
    instance.interceptors.response.use(
      (response) => {
        const endTime = Date.now()
        const startTime = response.config.metadata?.startTime || endTime
        const responseTime = endTime - startTime
        
        this.updateEndpointMetrics(endpoint.id, true, responseTime)
        return response
      },
      (error) => {
        const endTime = Date.now()
        const startTime = error.config?.metadata?.startTime || endTime
        const responseTime = endTime - startTime
        
        this.updateEndpointMetrics(endpoint.id, false, responseTime, error.message)
        return Promise.reject(error)
      }
    )

    this.axiosInstances.set(endpoint.id, instance)
  }

  private initializeRateLimiter(endpointId: string, maxRPS: number): void {
    this.rateLimiters.set(endpointId, {
      tokens: maxRPS,
      lastRefill: Date.now()
    })
  }

  private initializeCircuitBreaker(endpointId: string): void {
    this.circuitBreakers.set(endpointId, {
      isOpen: false,
      failureCount: 0,
      lastFailure: 0
    })
  }

  /**
   * Get the best available connection based on health and load balancing
   */
  public getConnection(preference?: string): Connection {
    const endpoint = this.selectBestEndpoint(preference)
    if (!endpoint) {
      throw new Error('No healthy RPC endpoints available')
    }

    const poolEntry = this.connectionPool.get(endpoint.id)
    if (!poolEntry) {
      throw new Error(`No connection pool for endpoint ${endpoint.id}`)
    }

    poolEntry.lastUsed = Date.now()
    poolEntry.requests++
    
    return poolEntry.connection
  }

  /**
   * Make an RPC request with automatic failover
   */
  public async request(rpcRequest: RPCRequest): Promise<any> {
    if (this.requestQueue.length >= this.MAX_QUEUE_SIZE) {
      throw new Error('Request queue is full')
    }

    return new Promise((resolve, reject) => {
      this.requestQueue.push({ request: rpcRequest, resolve, reject })
      this.processRequestQueue()
    })
  }

  private async processRequestQueue(): Promise<void> {
    if (this.processingQueue || this.requestQueue.length === 0) {
      return
    }

    this.processingQueue = true

    try {
      while (this.requestQueue.length > 0) {
        const { request, resolve, reject } = this.requestQueue.shift()!
        
        try {
          const result = await this.executeRequest(request)
          resolve(result)
        } catch (error) {
          reject(error)
        }
      }
    } finally {
      this.processingQueue = false
    }
  }

  private async executeRequest(rpcRequest: RPCRequest, excludeEndpoints: Set<string> = new Set()): Promise<any> {
    const endpoint = this.selectBestEndpoint(undefined, excludeEndpoints)
    
    if (!endpoint) {
      throw new Error('No healthy RPC endpoints available')
    }

    // Check rate limiting
    if (!this.checkRateLimit(endpoint.id)) {
      // If rate limited, try next endpoint
      excludeEndpoints.add(endpoint.id)
      return this.executeRequest(rpcRequest, excludeEndpoints)
    }

    // Check circuit breaker
    if (this.isCircuitBreakerOpen(endpoint.id)) {
      excludeEndpoints.add(endpoint.id)
      return this.executeRequest(rpcRequest, excludeEndpoints)
    }

    const axios = this.axiosInstances.get(endpoint.id)!
    const startTime = Date.now()

    try {
      const requestPayload = {
        jsonrpc: '2.0',
        id: rpcRequest.id || Date.now(),
        method: rpcRequest.method,
        params: rpcRequest.params
      }

      const config: AxiosRequestConfig = {
        timeout: rpcRequest.timeout || endpoint.timeout
      }

      const response = await axios.post('/', requestPayload, config)
      const rpcResponse: RPCResponse = response.data

      if (rpcResponse.error) {
        throw new Error(`RPC Error: ${rpcResponse.error.message} (Code: ${rpcResponse.error.code})`)
      }

      // Record successful request
      this.recordSuccessfulRequest(endpoint.id, Date.now() - startTime)
      
      return rpcResponse.result

    } catch (error) {
      this.recordFailedRequest(endpoint.id, error instanceof Error ? error.message : 'Unknown error')
      
      // If this endpoint failed and we have retries left, try another endpoint
      if (!rpcRequest.skipFailover && (rpcRequest.retryAttempts || endpoint.retryAttempts) > 0) {
        excludeEndpoints.add(endpoint.id)
        
        const retryRequest = {
          ...rpcRequest,
          retryAttempts: (rpcRequest.retryAttempts || endpoint.retryAttempts) - 1
        }
        
        return this.executeRequest(retryRequest, excludeEndpoints)
      }
      
      throw error
    }
  }

  /**
   * Select the best endpoint based on health, priority, and load balancing
   */
  private selectBestEndpoint(preference?: string, exclude: Set<string> = new Set()): RPCEndpoint | null {
    let availableEndpoints = Array.from(this.endpoints.values())
      .filter(e => e.healthy && !exclude.has(e.id) && !this.isCircuitBreakerOpen(e.id))

    if (availableEndpoints.length === 0) {
      // If no healthy endpoints, try the least recently failed ones
      availableEndpoints = Array.from(this.endpoints.values())
        .filter(e => !exclude.has(e.id))
        .sort((a, b) => a.lastHealthCheck - b.lastHealthCheck)
        .slice(0, 2) // Try 2 least recently failed
    }

    if (availableEndpoints.length === 0) {
      return null
    }

    // If preference specified and available, use it
    if (preference) {
      const preferred = availableEndpoints.find(e => e.id === preference)
      if (preferred) {
        return preferred
      }
    }

    // Sort by priority (lower = higher priority)
    availableEndpoints.sort((a, b) => {
      if (a.priority !== b.priority) {
        return a.priority - b.priority
      }
      // If same priority, use weighted round robin
      return b.weight - a.weight
    })

    // Simple round-robin among endpoints of the same priority
    const highestPriority = availableEndpoints[0].priority
    const samePriorityEndpoints = availableEndpoints.filter(e => e.priority === highestPriority)
    
    const selected = samePriorityEndpoints[this.currentEndpointIndex % samePriorityEndpoints.length]
    this.currentEndpointIndex++
    
    return selected
  }

  /**
   * Check if rate limiting allows the request
   */
  private checkRateLimit(endpointId: string): boolean {
    const limiter = this.rateLimiters.get(endpointId)
    const endpoint = this.endpoints.get(endpointId)
    
    if (!limiter || !endpoint) {
      return false
    }

    const now = Date.now()
    const timePassed = now - limiter.lastRefill
    const tokensToAdd = Math.floor(timePassed / 1000) * endpoint.maxRPS / 60 // Tokens per second

    limiter.tokens = Math.min(endpoint.maxRPS, limiter.tokens + tokensToAdd)
    limiter.lastRefill = now

    if (limiter.tokens >= 1) {
      limiter.tokens--
      return true
    }

    return false
  }

  /**
   * Check if circuit breaker is open for an endpoint
   */
  private isCircuitBreakerOpen(endpointId: string): boolean {
    const breaker = this.circuitBreakers.get(endpointId)
    if (!breaker) {
      return false
    }

    if (breaker.isOpen) {
      // Check if timeout has passed
      if (Date.now() - breaker.lastFailure > this.CIRCUIT_BREAKER_TIMEOUT) {
        breaker.isOpen = false
        breaker.failureCount = 0
      }
    }

    return breaker.isOpen
  }

  /**
   * Record successful request
   */
  private recordSuccessfulRequest(endpointId: string, responseTime: number): void {
    const endpoint = this.endpoints.get(endpointId)
    const breaker = this.circuitBreakers.get(endpointId)
    
    if (endpoint) {
      endpoint.totalRequests++
      endpoint.successfulRequests++
      endpoint.avgResponseTime = (endpoint.avgResponseTime + responseTime) / 2
      endpoint.healthy = true
      endpoint.errorCount = Math.max(0, endpoint.errorCount - 1)
    }

    if (breaker) {
      breaker.failureCount = Math.max(0, breaker.failureCount - 1)
    }
  }

  /**
   * Record failed request
   */
  private recordFailedRequest(endpointId: string, error: string): void {
    const endpoint = this.endpoints.get(endpointId)
    const breaker = this.circuitBreakers.get(endpointId)
    
    if (endpoint) {
      endpoint.totalRequests++
      endpoint.errorCount++
      endpoint.lastError = error

      // Mark unhealthy if error rate is too high
      if (endpoint.totalRequests > 10 && endpoint.errorCount / endpoint.totalRequests > 0.5) {
        endpoint.healthy = false
      }
    }

    if (breaker) {
      breaker.failureCount++
      breaker.lastFailure = Date.now()
      
      if (breaker.failureCount >= this.CIRCUIT_BREAKER_THRESHOLD) {
        breaker.isOpen = true
        logger.warn(`Circuit breaker opened for endpoint ${endpointId}`)
      }
    }
  }

  private updateEndpointMetrics(endpointId: string, success: boolean, responseTime: number, error?: string): void {
    if (success) {
      this.recordSuccessfulRequest(endpointId, responseTime)
    } else {
      this.recordFailedRequest(endpointId, error || 'Unknown error')
    }
  }

  /**
   * Start health checking for all endpoints
   */
  private startHealthChecking(): void {
    this.healthCheckInterval = setInterval(() => {
      this.performHealthChecks()
    }, this.HEALTH_CHECK_INTERVAL)

    // Perform initial health check
    this.performHealthChecks()
  }

  /**
   * Perform health checks on all endpoints
   */
  private async performHealthChecks(): Promise<void> {
    const healthPromises = Array.from(this.endpoints.values()).map(endpoint =>
      this.checkEndpointHealth(endpoint).catch(error => ({
        endpoint,
        responseTime: 0,
        success: false,
        error: error instanceof Error ? error.message : 'Health check failed'
      }))
    )

    const results = await Promise.all(healthPromises)
    
    for (const result of results) {
      this.updateEndpointHealth(result)
    }

    // Emit health check results
    this.emit('healthCheck', results)
    
    // Cache health status in Redis
    await this.cacheHealthStatus(results)
  }

  private async checkEndpointHealth(endpoint: RPCEndpoint): Promise<HealthCheckResult> {
    const startTime = Date.now()
    const axios = this.axiosInstances.get(endpoint.id)!

    try {
      // Simple health check: get version
      const response = await axios.post('/', {
        jsonrpc: '2.0',
        id: 1,
        method: 'getVersion',
        params: []
      }, { timeout: 5000 })

      const responseTime = Date.now() - startTime
      
      if (response.data.error) {
        throw new Error(response.data.error.message)
      }

      // Try to get block height as well
      const blockHeightResponse = await axios.post('/', {
        jsonrpc: '2.0',
        id: 2,
        method: 'getSlot',
        params: []
      }, { timeout: 3000 })

      return {
        endpoint,
        responseTime,
        success: true,
        blockHeight: blockHeightResponse.data.result || 0,
        version: response.data.result?.['solana-core'] || 'unknown'
      }
    } catch (error) {
      return {
        endpoint,
        responseTime: Date.now() - startTime,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  private updateEndpointHealth(result: HealthCheckResult): void {
    const endpoint = result.endpoint
    endpoint.lastHealthCheck = Date.now()
    
    if (result.success) {
      endpoint.healthy = true
      endpoint.avgResponseTime = (endpoint.avgResponseTime + result.responseTime) / 2
      endpoint.errorCount = Math.max(0, endpoint.errorCount - 1)
      delete endpoint.lastError
    } else {
      endpoint.healthy = false
      endpoint.errorCount++
      endpoint.lastError = result.error
    }
  }

  private async cacheHealthStatus(results: HealthCheckResult[]): Promise<void> {
    try {
      const healthStatus = {
        timestamp: Date.now(),
        endpoints: results.map(r => ({
          id: r.endpoint.id,
          healthy: r.success,
          responseTime: r.responseTime,
          error: r.error,
          blockHeight: r.blockHeight,
          version: r.version
        }))
      }

      await RedisService.setJSON('rpc:health_status', healthStatus, 60) // Cache for 1 minute
    } catch (error) {
      logger.error('Failed to cache health status:', error)
    }
  }

  /**
   * Get current status of all endpoints
   */
  public getEndpointStatus(): Array<RPCEndpoint & { circuitBreakerOpen: boolean }> {
    return Array.from(this.endpoints.values()).map(endpoint => ({
      ...endpoint,
      circuitBreakerOpen: this.isCircuitBreakerOpen(endpoint.id)
    }))
  }

  /**
   * Get health summary
   */
  public getHealthSummary(): {
    totalEndpoints: number
    healthyEndpoints: number
    totalRequests: number
    successfulRequests: number
    avgResponseTime: number
  } {
    const endpoints = Array.from(this.endpoints.values())
    const healthyEndpoints = endpoints.filter(e => e.healthy).length
    const totalRequests = endpoints.reduce((sum, e) => sum + e.totalRequests, 0)
    const successfulRequests = endpoints.reduce((sum, e) => sum + e.successfulRequests, 0)
    const avgResponseTime = endpoints.reduce((sum, e) => sum + e.avgResponseTime, 0) / endpoints.length

    return {
      totalEndpoints: endpoints.length,
      healthyEndpoints,
      totalRequests,
      successfulRequests,
      avgResponseTime
    }
  }

  /**
   * Shutdown the service
   */
  public shutdown(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval)
      this.healthCheckInterval = null
    }

    // Close all connections
    for (const poolEntry of this.connectionPool.values()) {
      // Connection cleanup if needed
    }

    this.emit('shutdown')
    logger.info('Helius RPC service shutdown completed')
  }
}

// Export singleton instance
export const HeliusRPC = HeliusRPCService.getInstance()

// Convenience exports for direct usage
export const getConnection = () => HeliusRPC.getConnection()
export const rpcRequest = (method: string, params: any[] = []) => HeliusRPC.request({ method, params })