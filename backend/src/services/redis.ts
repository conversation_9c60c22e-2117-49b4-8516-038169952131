import Redis from 'ioredis'
import { config } from '@/config/environment'
import { logger } from '@/utils/logger'

class RedisServiceClass {
  private static instance: RedisServiceClass
  private client: Redis | null = null
  private subscriber: Redis | null = null
  private publisher: Redis | null = null

  private constructor() {}

  public static getInstance(): RedisServiceClass {
    if (!RedisServiceClass.instance) {
      RedisServiceClass.instance = new RedisServiceClass()
    }
    return RedisServiceClass.instance
  }

  public async initialize(): Promise<void> {
    try {
      // Main Redis client
      this.client = new Redis(config.redis.url, {
        retryDelayOnFailover: 100,
        enableReadyCheck: false,
        maxRetriesPerRequest: null,
        lazyConnect: true,
      })

      // Subscriber client (for pub/sub)
      this.subscriber = new Redis(config.redis.url, {
        retryDelayOnFailover: 100,
        enableReadyCheck: false,
        maxRetriesPerRequest: null,
        lazyConnect: true,
      })

      // Publisher client (for pub/sub)
      this.publisher = new Redis(config.redis.url, {
        retryDelayOnFailover: 100,
        enableReadyCheck: false,
        maxRetriesPerRequest: null,
        lazyConnect: true,
      })

      // Set up event listeners
      this.setupEventListeners()

      // Connect to Redis
      await Promise.all([
        this.client.connect(),
        this.subscriber.connect(),
        this.publisher.connect(),
      ])

      logger.info('Redis connections established successfully')
    } catch (error) {
      logger.error('Failed to initialize Redis connections:', error)
      throw error
    }
  }

  private setupEventListeners(): void {
    if (!this.client || !this.subscriber || !this.publisher) return

    // Main client events
    this.client.on('connect', () => {
      logger.info('Redis main client connected')
    })

    this.client.on('error', (error) => {
      logger.error('Redis main client error:', error)
    })

    this.client.on('close', () => {
      logger.warn('Redis main client connection closed')
    })

    // Subscriber events
    this.subscriber.on('connect', () => {
      logger.info('Redis subscriber connected')
    })

    this.subscriber.on('error', (error) => {
      logger.error('Redis subscriber error:', error)
    })

    // Publisher events
    this.publisher.on('connect', () => {
      logger.info('Redis publisher connected')
    })

    this.publisher.on('error', (error) => {
      logger.error('Redis publisher error:', error)
    })
  }

  public async disconnect(): Promise<void> {
    try {
      if (this.client) {
        await this.client.quit()
        this.client = null
      }

      if (this.subscriber) {
        await this.subscriber.quit()
        this.subscriber = null
      }

      if (this.publisher) {
        await this.publisher.quit()
        this.publisher = null
      }

      logger.info('Redis connections closed')
    } catch (error) {
      logger.error('Error closing Redis connections:', error)
    }
  }

  public get mainClient(): Redis {
    if (!this.client) {
      throw new Error('Redis not initialized. Call initialize() first.')
    }
    return this.client
  }

  public get sub(): Redis {
    if (!this.subscriber) {
      throw new Error('Redis subscriber not initialized. Call initialize() first.')
    }
    return this.subscriber
  }

  public get pub(): Redis {
    if (!this.publisher) {
      throw new Error('Redis publisher not initialized. Call initialize() first.')
    }
    return this.publisher
  }

  // Cache methods
  public async get(key: string): Promise<string | null> {
    return this.mainClient.get(key)
  }

  public async set(key: string, value: string, ttl?: number): Promise<void> {
    if (ttl) {
      await this.mainClient.setex(key, ttl, value)
    } else {
      await this.mainClient.set(key, value)
    }
  }

  public async del(key: string): Promise<number> {
    return this.mainClient.del(key)
  }

  public async exists(key: string): Promise<number> {
    return this.mainClient.exists(key)
  }

  public async expire(key: string, seconds: number): Promise<number> {
    return this.mainClient.expire(key, seconds)
  }

  // JSON methods
  public async getJSON<T>(key: string): Promise<T | null> {
    const value = await this.get(key)
    return value ? JSON.parse(value) : null
  }

  public async setJSON<T>(key: string, value: T, ttl?: number): Promise<void> {
    await this.set(key, JSON.stringify(value), ttl)
  }

  // Hash methods
  public async hget(key: string, field: string): Promise<string | null> {
    return this.mainClient.hget(key, field)
  }

  public async hset(key: string, field: string, value: string): Promise<number> {
    return this.mainClient.hset(key, field, value)
  }

  public async hgetall(key: string): Promise<Record<string, string>> {
    return this.mainClient.hgetall(key)
  }

  public async hdel(key: string, ...fields: string[]): Promise<number> {
    return this.mainClient.hdel(key, ...fields)
  }

  // List methods
  public async lpush(key: string, ...values: string[]): Promise<number> {
    return this.mainClient.lpush(key, ...values)
  }

  public async rpush(key: string, ...values: string[]): Promise<number> {
    return this.mainClient.rpush(key, ...values)
  }

  public async lpop(key: string): Promise<string | null> {
    return this.mainClient.lpop(key)
  }

  public async rpop(key: string): Promise<string | null> {
    return this.mainClient.rpop(key)
  }

  public async lrange(key: string, start: number, stop: number): Promise<string[]> {
    return this.mainClient.lrange(key, start, stop)
  }

  // Set methods
  public async sadd(key: string, ...members: string[]): Promise<number> {
    return this.mainClient.sadd(key, ...members)
  }

  public async srem(key: string, ...members: string[]): Promise<number> {
    return this.mainClient.srem(key, ...members)
  }

  public async smembers(key: string): Promise<string[]> {
    return this.mainClient.smembers(key)
  }

  public async sismember(key: string, member: string): Promise<number> {
    return this.mainClient.sismember(key, member)
  }

  // Pub/Sub methods
  public async publish(channel: string, message: string): Promise<number> {
    return this.pub.publish(channel, message)
  }

  public async publishJSON<T>(channel: string, data: T): Promise<number> {
    return this.publish(channel, JSON.stringify(data))
  }

  public async subscribe(channel: string, callback: (message: string) => void): Promise<void> {
    await this.sub.subscribe(channel)
    this.sub.on('message', (receivedChannel, message) => {
      if (receivedChannel === channel) {
        callback(message)
      }
    })
  }

  public async unsubscribe(channel: string): Promise<void> {
    await this.sub.unsubscribe(channel)
  }

  // Pattern subscription
  public async psubscribe(pattern: string, callback: (channel: string, message: string) => void): Promise<void> {
    await this.sub.psubscribe(pattern)
    this.sub.on('pmessage', (receivedPattern, channel, message) => {
      if (receivedPattern === pattern) {
        callback(channel, message)
      }
    })
  }

  // Health check
  public async healthCheck(): Promise<boolean> {
    try {
      const result = await this.mainClient.ping()
      return result === 'PONG'
    } catch (error) {
      logger.error('Redis health check failed:', error)
      return false
    }
  }

  // Session management
  public async setSession(sessionId: string, data: any, ttl: number = 86400): Promise<void> {
    await this.setJSON(`session:${sessionId}`, data, ttl)
  }

  public async getSession<T>(sessionId: string): Promise<T | null> {
    return this.getJSON<T>(`session:${sessionId}`)
  }

  public async deleteSession(sessionId: string): Promise<number> {
    return this.del(`session:${sessionId}`)
  }

  // Rate limiting
  public async checkRateLimit(key: string, limit: number, window: number): Promise<{ allowed: boolean; remaining: number; resetTime: number }> {
    const current = await this.mainClient.incr(key)
    
    if (current === 1) {
      await this.mainClient.expire(key, window)
    }
    
    const ttl = await this.mainClient.ttl(key)
    const resetTime = Date.now() + (ttl * 1000)
    
    return {
      allowed: current <= limit,
      remaining: Math.max(0, limit - current),
      resetTime,
    }
  }
}

// Export singleton instance
export const RedisService = RedisServiceClass.getInstance()
