import WebSocket from 'ws'
import { EventEmitter } from 'events'
import { logger } from '@/utils/logger'
import { config } from '@/config/environment'
import { RedisService } from '@/services/redis'

interface HeliusWebSocketMessage {
  jsonrpc: string
  method: string
  params: {
    result: {
      context: { slot: number }
      value: any
    }
    subscription: number
  }
}

interface PriceUpdate {
  token: string
  price: number
  change24h: number
  volume24h: number
  timestamp: number
  slot: number
}

interface AccountUpdate {
  account: string
  data: any
  executable: boolean
  lamports: number
  owner: string
  rentEpoch: number
  timestamp: number
}

interface TransactionUpdate {
  signature: string
  err: any
  meta: any
  transaction: any
  timestamp: number
  slot: number
}

class HeliusWebSocketService extends EventEmitter {
  private ws: WebSocket | null = null
  private reconnectInterval: NodeJS.Timeout | null = null
  private heartbeatInterval: NodeJS.Timeout | null = null
  private subscriptions: Map<number, { type: string; address?: string; userId?: string }> = new Map()
  private nextSubscriptionId = 1
  private isConnected = false
  private reconnectAttempts = 0
  private maxReconnectAttempts = 10
  private reconnectDelay = 1000
  private heartbeatIntervalMs = 30000

  constructor() {
    super()
    this.setMaxListeners(0) // Remove limit for event listeners
  }

  /**
   * Initialize WebSocket connection to Helius
   */
  public async connect(): Promise<void> {
    try {
      const wsUrl = config.helius.wsUrl
      if (!wsUrl) {
        throw new Error('Helius WebSocket URL not configured')
      }

      logger.info('Connecting to Helius WebSocket...', { url: wsUrl })
      
      this.ws = new WebSocket(wsUrl)
      
      this.ws.on('open', this.handleOpen.bind(this))
      this.ws.on('message', this.handleMessage.bind(this))
      this.ws.on('close', this.handleClose.bind(this))
      this.ws.on('error', this.handleError.bind(this))
      this.ws.on('pong', this.handlePong.bind(this))

    } catch (error) {
      logger.error('Failed to connect to Helius WebSocket:', error)
      this.scheduleReconnect()
      throw error
    }
  }

  /**
   * Disconnect from WebSocket
   */
  public disconnect(): void {
    logger.info('Disconnecting from Helius WebSocket')
    
    this.isConnected = false
    this.clearIntervals()
    
    if (this.ws) {
      this.ws.removeAllListeners()
      this.ws.close(1000, 'Intentional disconnect')
      this.ws = null
    }
    
    this.subscriptions.clear()
    this.emit('disconnected')
  }

  /**
   * Subscribe to token price updates
   */
  public async subscribeToTokenPrices(tokens: string[], userId?: string): Promise<number[]> {
    if (!this.isConnected) {
      throw new Error('WebSocket not connected')
    }

    const subscriptionIds: number[] = []

    for (const token of tokens) {
      const subscriptionId = this.nextSubscriptionId++
      
      // For token prices, we'll subscribe to account changes for the token mint
      const message = {
        jsonrpc: '2.0',
        id: subscriptionId,
        method: 'accountSubscribe',
        params: [
          token,
          {
            encoding: 'base64',
            commitment: 'processed'
          }
        ]
      }

      this.ws!.send(JSON.stringify(message))
      this.subscriptions.set(subscriptionId, { type: 'tokenPrice', address: token, userId })
      subscriptionIds.push(subscriptionId)
      
      logger.debug('Subscribed to token price updates', { token, subscriptionId })
    }

    return subscriptionIds
  }

  /**
   * Subscribe to account changes (for position monitoring)
   */
  public async subscribeToAccount(account: string, userId: string): Promise<number> {
    if (!this.isConnected) {
      throw new Error('WebSocket not connected')
    }

    const subscriptionId = this.nextSubscriptionId++
    
    const message = {
      jsonrpc: '2.0',
      id: subscriptionId,
      method: 'accountSubscribe',
      params: [
        account,
        {
          encoding: 'base64+zstd',
          commitment: 'confirmed'
        }
      ]
    }

    this.ws!.send(JSON.stringify(message))
    this.subscriptions.set(subscriptionId, { type: 'account', address: account, userId })
    
    logger.debug('Subscribed to account changes', { account, subscriptionId, userId })
    
    return subscriptionId
  }

  /**
   * Subscribe to transaction confirmations
   */
  public async subscribeToTransaction(signature: string, userId: string): Promise<number> {
    if (!this.isConnected) {
      throw new Error('WebSocket not connected')
    }

    const subscriptionId = this.nextSubscriptionId++
    
    const message = {
      jsonrpc: '2.0',
      id: subscriptionId,
      method: 'signatureSubscribe',
      params: [
        signature,
        {
          commitment: 'confirmed'
        }
      ]
    }

    this.ws!.send(JSON.stringify(message))
    this.subscriptions.set(subscriptionId, { type: 'transaction', address: signature, userId })
    
    logger.debug('Subscribed to transaction confirmation', { signature, subscriptionId, userId })
    
    return subscriptionId
  }

  /**
   * Unsubscribe from updates
   */
  public async unsubscribe(subscriptionId: number): Promise<void> {
    if (!this.isConnected || !this.subscriptions.has(subscriptionId)) {
      return
    }

    const subscription = this.subscriptions.get(subscriptionId)!
    let method: string

    switch (subscription.type) {
      case 'tokenPrice':
      case 'account':
        method = 'accountUnsubscribe'
        break
      case 'transaction':
        method = 'signatureUnsubscribe'
        break
      default:
        logger.warn('Unknown subscription type for unsubscribe', { subscriptionId, type: subscription.type })
        return
    }

    const message = {
      jsonrpc: '2.0',
      id: Date.now(),
      method,
      params: [subscriptionId]
    }

    this.ws!.send(JSON.stringify(message))
    this.subscriptions.delete(subscriptionId)
    
    logger.debug('Unsubscribed from updates', { subscriptionId, type: subscription.type })
  }

  /**
   * Get current connection status
   */
  public getConnectionStatus(): { connected: boolean; subscriptions: number; reconnectAttempts: number } {
    return {
      connected: this.isConnected,
      subscriptions: this.subscriptions.size,
      reconnectAttempts: this.reconnectAttempts
    }
  }

  private handleOpen(): void {
    logger.info('Connected to Helius WebSocket')
    this.isConnected = true
    this.reconnectAttempts = 0
    this.startHeartbeat()
    this.emit('connected')
  }

  private handleMessage(data: WebSocket.Data): void {
    try {
      const message: HeliusWebSocketMessage = JSON.parse(data.toString())
      
      // Handle subscription responses
      if (message.method === 'accountNotification') {
        this.handleAccountNotification(message)
      } else if (message.method === 'signatureNotification') {
        this.handleTransactionNotification(message)
      } else if (message.jsonrpc && message.params) {
        // Handle other types of notifications
        logger.debug('Received WebSocket message', { method: message.method })
      }
    } catch (error) {
      logger.error('Failed to parse WebSocket message:', error)
    }
  }

  private handleAccountNotification(message: HeliusWebSocketMessage): void {
    const subscriptionId = message.params.subscription
    const subscription = this.subscriptions.get(subscriptionId)
    
    if (!subscription) {
      logger.warn('Received notification for unknown subscription', { subscriptionId })
      return
    }

    const accountData = message.params.result.value
    const slot = message.params.result.context.slot

    if (subscription.type === 'tokenPrice') {
      // Process token price update
      this.processTokenPriceUpdate(subscription.address!, accountData, slot, subscription.userId)
    } else if (subscription.type === 'account') {
      // Process account update
      this.processAccountUpdate(subscription.address!, accountData, subscription.userId!)
    }
  }

  private handleTransactionNotification(message: HeliusWebSocketMessage): void {
    const subscriptionId = message.params.subscription
    const subscription = this.subscriptions.get(subscriptionId)
    
    if (!subscription) {
      logger.warn('Received transaction notification for unknown subscription', { subscriptionId })
      return
    }

    const transactionData = message.params.result.value
    this.processTransactionUpdate(subscription.address!, transactionData, subscription.userId!)
  }

  private async processTokenPriceUpdate(token: string, accountData: any, slot: number, userId?: string): Promise<void> {
    try {
      // Extract price information from account data
      // This is simplified - actual implementation would need to parse mint account data
      const priceUpdate: PriceUpdate = {
        token,
        price: 0, // TODO: Parse actual price from account data
        change24h: 0, // TODO: Calculate 24h change
        volume24h: 0, // TODO: Get volume data
        timestamp: Date.now(),
        slot
      }

      // Cache the price update
      await RedisService.setJSON(`price:${token}`, priceUpdate, 300) // Cache for 5 minutes

      // Emit price update event
      this.emit('priceUpdate', priceUpdate)

      // Publish to Redis for other services
      await RedisService.publishJSON('price_update', priceUpdate)

      logger.debug('Processed token price update', { token, slot })
    } catch (error) {
      logger.error('Failed to process token price update:', error)
    }
  }

  private async processAccountUpdate(account: string, accountData: any, userId: string): Promise<void> {
    try {
      const accountUpdate: AccountUpdate = {
        account,
        data: accountData.data,
        executable: accountData.executable,
        lamports: accountData.lamports,
        owner: accountData.owner,
        rentEpoch: accountData.rentEpoch,
        timestamp: Date.now()
      }

      // Emit account update event
      this.emit('accountUpdate', accountUpdate)

      // Publish to Redis for position monitoring service
      await RedisService.publishJSON('account_update', { ...accountUpdate, userId })

      logger.debug('Processed account update', { account, userId })
    } catch (error) {
      logger.error('Failed to process account update:', error)
    }
  }

  private async processTransactionUpdate(signature: string, transactionData: any, userId: string): Promise<void> {
    try {
      const transactionUpdate: TransactionUpdate = {
        signature,
        err: transactionData.err,
        meta: transactionData.meta,
        transaction: transactionData.transaction,
        timestamp: Date.now(),
        slot: transactionData.slot || 0
      }

      // Emit transaction update event
      this.emit('transactionUpdate', transactionUpdate)

      // Publish to Redis for frontend updates
      await RedisService.publishJSON('transaction_update', { ...transactionUpdate, userId })

      logger.info('Transaction confirmed', { signature, userId, success: !transactionData.err })
    } catch (error) {
      logger.error('Failed to process transaction update:', error)
    }
  }

  private handleClose(code: number, reason: string): void {
    logger.warn('Helius WebSocket closed', { code, reason })
    this.isConnected = false
    this.clearIntervals()
    this.emit('disconnected', { code, reason })
    
    // Only reconnect if it wasn't an intentional disconnect
    if (code !== 1000) {
      this.scheduleReconnect()
    }
  }

  private handleError(error: Error): void {
    logger.error('Helius WebSocket error:', error)
    this.emit('error', error)
  }

  private handlePong(): void {
    logger.debug('Received pong from Helius WebSocket')
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.ping()
      }
    }, this.heartbeatIntervalMs)
  }

  private clearIntervals(): void {
    if (this.reconnectInterval) {
      clearInterval(this.reconnectInterval)
      this.reconnectInterval = null
    }
    
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }

  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.error('Max reconnection attempts reached for Helius WebSocket')
      this.emit('maxReconnectAttemptsReached')
      return
    }

    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts), 30000) // Max 30 seconds
    this.reconnectAttempts++

    logger.info(`Scheduling Helius WebSocket reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
    
    this.reconnectInterval = setTimeout(() => {
      this.connect().catch(error => {
        logger.error('Reconnection attempt failed:', error)
      })
    }, delay)
  }

  /**
   * Health check for the service
   */
  public healthCheck(): { healthy: boolean; connected: boolean; subscriptions: number } {
    return {
      healthy: this.isConnected && this.ws?.readyState === WebSocket.OPEN,
      connected: this.isConnected,
      subscriptions: this.subscriptions.size
    }
  }
}

// Export singleton instance
export const heliusWebSocketService = new HeliusWebSocketService()

// Auto-connect on import in production
if (process.env.NODE_ENV === 'production') {
  heliusWebSocketService.connect().catch(error => {
    logger.error('Failed to auto-connect to Helius WebSocket:', error)
  })
}