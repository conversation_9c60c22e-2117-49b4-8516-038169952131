import axios, { AxiosInstance } from 'axios'
import { EventEmitter } from 'events'
import { logger } from '@/utils/logger'
import { RedisService } from '@/services/redis'
import { AppError } from '@/middleware/errorHandler'
import type { Quote, TransactionFees } from '@memetrader-pro/shared'

interface JupiterQuoteResponse {
  inputMint: string
  outputMint: string
  inAmount: string
  outAmount: string
  otherAmountThreshold: string
  swapMode: string
  slippageBps: number
  platformFee: any
  priceImpactPct: string
  routePlan: any[]
  contextSlot: number
  timeTaken: number
}

interface JupiterSwapResponse {
  swapTransaction: string
  lastValidBlockHeight: number
  prioritizationFeeLamports: number
}

interface JupiterEndpoint {
  baseUrl: string
  name: string
  priority: number
  healthStatus: 'healthy' | 'degraded' | 'unhealthy'
  lastHealthCheck: number
  failureCount: number
  avgResponseTime: number
  circuitBreakerOpen: boolean
}

interface QuoteRequest {
  inputMint: string
  outputMint: string
  amount: number
  slippageBps?: number
  userPublicKey?: string
  maxAccounts?: number
  onlyDirectRoutes?: boolean
  restrictIntermediateTokens?: boolean
  dynamicSlippage?: boolean
  platformFeeBps?: number
  feeAccount?: string
  asLegacyTransaction?: boolean
}

interface SwapRequest {
  quoteResponse: any
  userPublicKey: string
  payer?: string // Custom payer for transaction fees and rent
  wrapAndUnwrapSol?: boolean
  useSharedAccounts?: boolean
  feeAccount?: string
  trackingAccount?: string
  prioritizationFeeLamports?: number | 'auto' | {
    priorityLevelWithMaxLamports?: {
      maxLamports: number
      global?: boolean
      priorityLevel: 'medium' | 'high' | 'veryHigh'
    }
    jitoTipLamports?: number
  }
  asLegacyTransaction?: boolean
  destinationTokenAccount?: string // Public key of token account to receive output
  dynamicComputeUnitLimit?: boolean
  skipUserAccountsRpcCalls?: boolean
  dynamicSlippage?: boolean
  computeUnitPriceMicroLamports?: number
  blockhashSlotsToExpiry?: number
}

interface RetryConfig {
  maxRetries: number
  baseDelay: number
  maxDelay: number
  backoffFactor: number
  retryCondition: (error: any) => boolean
}

interface CircuitBreakerConfig {
  failureThreshold: number
  recoveryTimeout: number
  monitoringPeriod: number
}

class EnhancedJupiterService extends EventEmitter {
  private endpoints: JupiterEndpoint[]
  private axiosInstances: Map<string, AxiosInstance>
  private requestQueue: Map<string, Promise<any>>
  private retryConfig: RetryConfig
  private circuitBreakerConfig: CircuitBreakerConfig
  private healthCheckInterval: NodeJS.Timeout | null = null
  private metrics: {
    totalRequests: number
    successfulRequests: number
    failedRequests: number
    avgResponseTime: number
    circuitBreakerTrips: number
  }

  constructor() {
    super()

            // Initialize Jupiter endpoints based on actual documentation
    // Lite API v1: https://lite-api.jup.ag/swap/v1 (Free tier)
    // V6 API: https://quote-api.jup.ag/v6 (Legacy fallback)
    this.endpoints = [
      {
        baseUrl: 'https://lite-api.jup.ag/swap/v1',
        name: 'jupiter-lite-v1',
        priority: 1,
        healthStatus: 'healthy',
        lastHealthCheck: 0,
        failureCount: 0,
        avgResponseTime: 0,
        circuitBreakerOpen: false
      },
      {
        baseUrl: 'https://quote-api.jup.ag',
        name: 'jupiter-v6-fallback',
        priority: 2,
        healthStatus: 'healthy',
        lastHealthCheck: 0,
        failureCount: 0,
        avgResponseTime: 0,
        circuitBreakerOpen: false
      }
    ]

    // Initialize axios instances for each endpoint
    this.axiosInstances = new Map()
    this.setupAxiosInstances()

    // Initialize request queue for deduplication
    this.requestQueue = new Map()

    // Configure retry strategy
    this.retryConfig = {
      maxRetries: 3,
      baseDelay: 100, // 100ms
      maxDelay: 10000, // 10s
      backoffFactor: 2,
      retryCondition: (error: any) => {
        if (!error.response) return true // Network errors
        const status = error.response.status
        return status >= 500 || status === 429 || status === 408 // Server errors, rate limits, timeouts
      }
    }

    // Configure circuit breaker
    this.circuitBreakerConfig = {
      failureThreshold: 5,
      recoveryTimeout: 30000, // 30s
      monitoringPeriod: 60000 // 1 minute
    }

    // Initialize metrics
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      avgResponseTime: 0,
      circuitBreakerTrips: 0
    }

    // Start health monitoring
    this.startHealthMonitoring()
  }

  /**
   * Setup axios instances for each endpoint with proper configuration
   */
  private setupAxiosInstances(): void {
    this.endpoints.forEach(endpoint => {
      const instance = axios.create({
        baseURL: endpoint.baseUrl,
        timeout: 15000, // 15s timeout
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'User-Agent': 'MemeTrader-Pro/1.0'
        }
      })

      // Request interceptor for metrics
      instance.interceptors.request.use(
        (config: any) => {
          config.metadata = { startTime: Date.now() }
          return config
        },
        (error) => Promise.reject(error)
      )

      // Response interceptor for metrics and error handling
      instance.interceptors.response.use(
        (response: any) => {
          const duration = Date.now() - (response.config.metadata?.startTime || Date.now())
          this.updateEndpointMetrics(endpoint.name, true, duration)
          return response
        },
        (error: any) => {
          const duration = error.config?.metadata ? Date.now() - error.config.metadata.startTime : 0
          this.updateEndpointMetrics(endpoint.name, false, duration)
          return Promise.reject(error)
        }
      )

      this.axiosInstances.set(endpoint.name, instance)
    })
  }

  /**
   * Get healthy endpoints sorted by priority
   */
  private getHealthyEndpoints(): JupiterEndpoint[] {
    return this.endpoints
      .filter(endpoint => endpoint.healthStatus !== 'unhealthy' && !endpoint.circuitBreakerOpen)
      .sort((a, b) => a.priority - b.priority)
  }

  /**
   * Execute request with intelligent retry and failover
   */
  private async executeWithRetry<T>(
    operation: (endpoint: JupiterEndpoint) => Promise<T>,
    operationName: string
  ): Promise<T> {
    const healthyEndpoints = this.getHealthyEndpoints()

    if (healthyEndpoints.length === 0) {
      throw new AppError('All Jupiter endpoints are unhealthy', 503, 'JUPITER_UNAVAILABLE')
    }

    let lastError: any

    // Try each healthy endpoint
    for (const endpoint of healthyEndpoints) {
      try {
        logger.debug(`Attempting ${operationName} with endpoint: ${endpoint.name}`)

        const result = await this.retryOperation(
          () => operation(endpoint),
          endpoint,
          operationName
        )

        return result
      } catch (error) {
        lastError = error
        logger.warn(`${operationName} failed on endpoint ${endpoint.name}:`, error)

        // Check if circuit breaker should open
        this.evaluateCircuitBreaker(endpoint, error)
      }
    }

    // All endpoints failed
    this.metrics.failedRequests++
    throw lastError || new AppError('All Jupiter endpoints failed', 503, 'JUPITER_ALL_FAILED')
  }

  /**
   * Retry operation with exponential backoff
   */
  private async retryOperation<T>(
    operation: () => Promise<T>,
    endpoint: JupiterEndpoint,
    operationName: string
  ): Promise<T> {
    let lastError: any

    for (let attempt = 0; attempt <= this.retryConfig.maxRetries; attempt++) {
      try {
        const result = await operation()

        // Reset failure count on success
        endpoint.failureCount = Math.max(0, endpoint.failureCount - 1)
        this.metrics.successfulRequests++

        return result
      } catch (error) {
        lastError = error
        endpoint.failureCount++

        // Don't retry if it's the last attempt or not retryable
        if (attempt === this.retryConfig.maxRetries || !this.retryConfig.retryCondition(error)) {
          break
        }

        // Calculate delay with exponential backoff and jitter
        const delay = Math.min(
          this.retryConfig.baseDelay * Math.pow(this.retryConfig.backoffFactor, attempt),
          this.retryConfig.maxDelay
        )
        const jitter = Math.random() * 0.1 * delay // ±10% jitter
        const finalDelay = delay + jitter

        logger.debug(`Retrying ${operationName} in ${finalDelay}ms (attempt ${attempt + 1}/${this.retryConfig.maxRetries})`)
        await this.sleep(finalDelay)
      }
    }

    throw lastError
  }

  /**
   * Evaluate if circuit breaker should open
   */
  private evaluateCircuitBreaker(endpoint: JupiterEndpoint, error: any): void {
    if (endpoint.failureCount >= this.circuitBreakerConfig.failureThreshold) {
      endpoint.circuitBreakerOpen = true
      endpoint.healthStatus = 'unhealthy'
      this.metrics.circuitBreakerTrips++

      logger.warn(`Circuit breaker opened for endpoint: ${endpoint.name}`)
      this.emit('circuitBreakerOpen', endpoint.name, error)

      // Schedule recovery
      setTimeout(() => {
        endpoint.circuitBreakerOpen = false
        endpoint.failureCount = 0
        endpoint.healthStatus = 'degraded'
        logger.info(`Circuit breaker recovery attempted for endpoint: ${endpoint.name}`)
      }, this.circuitBreakerConfig.recoveryTimeout)
    }
  }

  /**
   * Update endpoint metrics
   */
  private updateEndpointMetrics(endpointName: string, success: boolean, responseTime: number): void {
    const endpoint = this.endpoints.find(e => e.name === endpointName)
    if (!endpoint) return

    // Update average response time
    endpoint.avgResponseTime = endpoint.avgResponseTime === 0
      ? responseTime
      : (endpoint.avgResponseTime + responseTime) / 2

    // Update global metrics
    this.metrics.totalRequests++
    if (success) {
      this.metrics.successfulRequests++
    } else {
      this.metrics.failedRequests++
    }

    this.metrics.avgResponseTime = this.metrics.avgResponseTime === 0
      ? responseTime
      : (this.metrics.avgResponseTime + responseTime) / 2
  }

  /**
   * Start health monitoring for all endpoints
   */
  private startHealthMonitoring(): void {
    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthChecks()
    }, 30000) // Every 30 seconds
  }

  /**
   * Perform health checks on all endpoints
   */
  private async performHealthChecks(): Promise<void> {
    const healthCheckPromises = this.endpoints.map(async (endpoint) => {
      try {
        const instance = this.axiosInstances.get(endpoint.name)
        if (!instance) return

        const startTime = Date.now()

        // Health check based on endpoint type
        if (endpoint.name.includes('lite-v1')) {
          // Lite API: https://lite-api.jup.ag/quote
          await instance.get('/quote', {
            params: {
              inputMint: 'So11111111111111111111111111111111111111112', // SOL
              outputMint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
              amount: '**********', // 1 SOL in lamports
              slippageBps: 50
            },
            timeout: 5000
          })
        } else if (endpoint.name.includes('v6')) {
          // V6 API: https://quote-api.jup.ag/v6/quote
          await instance.get('/v6/quote', {
            params: {
              inputMint: 'So11111111111111111111111111111111111111112',
              outputMint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
              amount: '**********',
              slippageBps: 50
            },
            timeout: 5000
          })
        } else {
          // Generic health check
          await instance.head('/', { timeout: 5000 })
        }
        const responseTime = Date.now() - startTime

        endpoint.healthStatus = responseTime < 1000 ? 'healthy' : 'degraded'
        endpoint.lastHealthCheck = Date.now()
        endpoint.avgResponseTime = responseTime

      } catch (error) {
        endpoint.healthStatus = 'unhealthy'
        endpoint.lastHealthCheck = Date.now()
        logger.debug(`Health check failed for endpoint ${endpoint.name}:`, error)
      }
    })

    await Promise.allSettled(healthCheckPromises)
  }

  /**
   * Get quote with enhanced reliability using latest Jupiter v1 API
   */
  public async getQuote(params: QuoteRequest): Promise<Quote> {
    this.metrics.totalRequests++

    // Create cache key for request deduplication
    const cacheKey = `quote:${JSON.stringify({
      inputMint: params.inputMint,
      outputMint: params.outputMint,
      amount: params.amount,
      slippageBps: params.slippageBps || 50,
      dynamicSlippage: params.dynamicSlippage || false
    })}`

    // Check if same request is already in progress
    if (this.requestQueue.has(cacheKey)) {
      logger.debug('Deduplicating quote request')
      return this.requestQueue.get(cacheKey)!
    }

    // Check cache first
    try {
      const cached = await RedisService.getJSON<Quote>(`jupiter:${cacheKey}`)
      if (cached) {
        logger.debug('Returning cached quote')
        return cached
      }
    } catch (error) {
      logger.debug('Cache miss for quote request')
    }

    const quotePromise = this.executeWithRetry(
      async (endpoint) => {
        const instance = this.axiosInstances.get(endpoint.name)!

        // Build request parameters based on API endpoint
        let requestParams: any = {
          inputMint: params.inputMint,
          outputMint: params.outputMint,
          amount: Math.floor(params.amount).toString(),
          slippageBps: params.slippageBps || 50,
          onlyDirectRoutes: params.onlyDirectRoutes || false,
          restrictIntermediateTokens: params.restrictIntermediateTokens !== false,
          maxAccounts: params.maxAccounts || 64
        }

        // All endpoints currently use V6 API format
        // Determine request path based on endpoint type
        let requestUrl: string
        if (endpoint.name.includes('lite-v1')) {
          // Lite API: https://lite-api.jup.ag/quote
          requestUrl = '/quote'
        } else {
          // V6 API: https://quote-api.jup.ag/v6/quote
          requestUrl = '/v6/quote'
        }

        // Add advanced parameters for v1 endpoints (lite and pro)
        if (endpoint.name.includes('lite') || endpoint.name.includes('pro')) {
          requestParams = {
            ...requestParams,
            swapMode: 'ExactIn',
            asLegacyTransaction: params.asLegacyTransaction || false
          }

          // Add user public key for better routing
          if (params.userPublicKey) {
            requestParams.userPublicKey = params.userPublicKey
          }

          // Add platform fee for referral programs
          if (params.platformFeeBps) {
            requestParams.platformFeeBps = params.platformFeeBps
          }
        }

        const response = await instance.get(requestUrl, {
          params: requestParams
        })

        const jupiterQuote: JupiterQuoteResponse = response.data

        // Transform to our Quote format
        const quote: Quote = {
          inputMint: jupiterQuote.inputMint,
          outputMint: jupiterQuote.outputMint,
          inAmount: jupiterQuote.inAmount,
          outAmount: jupiterQuote.outAmount,
          priceImpactPct: parseFloat(jupiterQuote.priceImpactPct) || 0,
          routePlan: jupiterQuote.routePlan,
          fees: this.calculateFees(jupiterQuote)
        }

        // Cache the result for 5 seconds
        try {
          await RedisService.setJSON(`jupiter:${cacheKey}`, quote, 5)
        } catch (error) {
          logger.debug('Failed to cache quote:', error)
        }

        return quote
      },
      'getQuote'
    )

    // Store promise in queue
    this.requestQueue.set(cacheKey, quotePromise)

    try {
      const result = await quotePromise
      return result
    } finally {
      // Remove from queue when done
      this.requestQueue.delete(cacheKey)
    }
  }

  /**
   * Get swap transaction with enhanced reliability using latest Jupiter v1 API
   */
  public async getSwapTransaction(params: SwapRequest): Promise<JupiterSwapResponse> {
    this.metrics.totalRequests++

    return this.executeWithRetry(
      async (endpoint) => {
        const instance = this.axiosInstances.get(endpoint.name)!

        // All endpoints currently use V6 API format
        // Determine request path based on endpoint type
        let requestUrl: string
        if (endpoint.name.includes('lite-v1')) {
          // Lite API: https://lite-api.jup.ag/swap
          requestUrl = '/swap'
        } else {
          // V6 API: https://quote-api.jup.ag/v6/swap
          requestUrl = '/v6/swap'
        }
        const requestBody: any = {
          quoteResponse: params.quoteResponse,
          userPublicKey: params.userPublicKey,
          wrapAndUnwrapSol: params.wrapAndUnwrapSol !== false,
          asLegacyTransaction: params.asLegacyTransaction || false,
          dynamicComputeUnitLimit: params.dynamicComputeUnitLimit !== false,
          prioritizationFeeLamports: params.prioritizationFeeLamports || 'auto'
        }

        // Add optional parameters from Jupiter Swap API documentation
        if (params.payer) {
          requestBody.payer = params.payer
        }

        if (params.useSharedAccounts !== false) {
          requestBody.useSharedAccounts = true
        }

        if (params.feeAccount) {
          requestBody.feeAccount = params.feeAccount
        }

        if (params.trackingAccount) {
          requestBody.trackingAccount = params.trackingAccount
        }

        if (params.destinationTokenAccount) {
          requestBody.destinationTokenAccount = params.destinationTokenAccount
        }

        if (params.skipUserAccountsRpcCalls !== undefined) {
          requestBody.skipUserAccountsRpcCalls = params.skipUserAccountsRpcCalls
        }

        if (params.dynamicSlippage !== undefined) {
          requestBody.dynamicSlippage = params.dynamicSlippage
        }

        if (params.computeUnitPriceMicroLamports) {
          requestBody.computeUnitPriceMicroLamports = params.computeUnitPriceMicroLamports
        }

        if (params.blockhashSlotsToExpiry) {
          requestBody.blockhashSlotsToExpiry = params.blockhashSlotsToExpiry
        }

        const response = await instance.post(requestUrl, requestBody)

        return response.data
      },
      'getSwapTransaction'
    )
  }

  /**
   * Calculate transaction fees
   */
  private calculateFees(quote: JupiterQuoteResponse): TransactionFees {
    const baseNetworkFee = 0.000005 // 5000 lamports base fee
    const priorityFeeEstimate = 0.0001 // Estimated priority fee

    // Jupiter platform fee (usually 0.1% for swaps)
    const jupiterFeeRate = 0.001
    const jupiterFee = (parseFloat(quote.inAmount) * jupiterFeeRate) / Math.pow(10, 9)

    return {
      jupiterFee,
      priorityFee: priorityFeeEstimate,
      networkFee: baseNetworkFee,
      total: jupiterFee + priorityFeeEstimate + baseNetworkFee
    }
  }

  /**
   * Get service health status
   */
  public getHealthStatus(): {
    status: 'healthy' | 'degraded' | 'unhealthy'
    endpoints: any[]
    metrics: any
  } {
    const healthyCount = this.endpoints.filter(e => e.healthStatus === 'healthy').length
    const totalCount = this.endpoints.length

    let status: 'healthy' | 'degraded' | 'unhealthy'
    if (healthyCount === totalCount) {
      status = 'healthy'
    } else if (healthyCount > 0) {
      status = 'degraded'
    } else {
      status = 'unhealthy'
    }

    return {
      status,
      endpoints: this.endpoints.map(e => ({
        name: e.name,
        status: e.healthStatus,
        avgResponseTime: e.avgResponseTime,
        failureCount: e.failureCount,
        circuitBreakerOpen: e.circuitBreakerOpen
      })),
      metrics: {
        ...this.metrics,
        successRate: this.metrics.totalRequests > 0
          ? (this.metrics.successfulRequests / this.metrics.totalRequests * 100).toFixed(2) + '%'
          : '0%'
      }
    }
  }

  /**
   * Health check method for monitoring integration
   */
  public async healthCheck(): Promise<boolean> {
    try {
      const healthyEndpoints = this.getHealthyEndpoints()
      return healthyEndpoints.length > 0
    } catch (error) {
      logger.error('Jupiter service health check failed:', error)
      return false
    }
  }

  /**
   * Utility method for sleep
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Cleanup method
   */
  public shutdown(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval)
      this.healthCheckInterval = null
    }

    this.removeAllListeners()
    logger.info('Enhanced Jupiter service shutdown completed')
  }
}

// Export singleton instance
export const jupiterService = new EnhancedJupiterService()
export { EnhancedJupiterService as EnhancedJupiterServiceClass }
