import { EventEmitter } from 'events'
import { DatabaseService } from '@/services/database'
import { RedisService } from '@/services/redis'
import { TradingService } from '@/services/tradingService'
import { HeliusWebSocketService } from '@/services/heliusWebSocket'
import { logger, logTrading } from '@/utils/logger'
import { AppError } from '@/middleware/errorHandler'
import type { 
  Position,
  ExitStrategy,
  ProfitTarget,
  StopLossConfig,
  MoonBagConfig,
  TradeParams
} from '@memetrader-pro/shared'
import { PositionStatus, StrategyExecutionState, PresetType } from '@memetrader-pro/shared'

interface PositionUpdate {
  positionId: string
  userId: string
  currentPrice: number
  pnl: number
  pnlPercentage: number
  marketValue: number
  timestamp: number
}

interface StrategyTrigger {
  strategyId: string
  positionId: string
  userId: string
  triggerType: 'STOP_LOSS' | 'PROFIT_TARGET' | 'TRAILING_STOP' | 'MOON_BAG'
  triggerPrice: number
  currentPrice: number
  sellAmount: number
  sellPercentage: number
  isEmergency: boolean
}

class PositionMonitorService extends EventEmitter {
  private activePositions: Map<string, Position> = new Map()
  private activeStrategies: Map<string, ExitStrategy> = new Map()
  private priceSubscriptions: Map<string, Set<string>> = new Map() // token -> positionIds
  private monitoringInterval: NodeJS.Timeout | null = null
  private isRunning = false
  private readonly monitoringIntervalMs = 500 // 500ms for real-time monitoring

  constructor() {
    super()
    // Redis subscriptions will be initialized when service starts
  }

  /**
   * Start position monitoring service
   */
  public async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Position monitor already running')
      return
    }

    try {
      logger.info('Starting position monitoring service...')

      // Initialize Redis subscriptions first
      this.initializeRedisSubscriptions()

      // Load active positions and strategies from database
      await this.loadActivePositions()
      await this.loadActiveStrategies()

      // Subscribe to price updates
      await this.subscribeToTokenPrices()

      // Start monitoring loop
      this.startMonitoring()

      this.isRunning = true
      logger.info('Position monitoring service started', {
        activePositions: this.activePositions.size,
        activeStrategies: this.activeStrategies.size
      })

    } catch (error) {
      logger.error('Failed to start position monitoring service:', error)
      throw error
    }
  }

  /**
   * Stop position monitoring service
   */
  public async stop(): Promise<void> {
    if (!this.isRunning) {
      return
    }

    logger.info('Stopping position monitoring service...')

    this.isRunning = false

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = null
    }

    // Unsubscribe from price feeds
    await this.unsubscribeFromTokenPrices()

    this.activePositions.clear()
    this.activeStrategies.clear()
    this.priceSubscriptions.clear()

    logger.info('Position monitoring service stopped')
  }

  /**
   * Add position to monitoring
   */
  public async addPosition(position: Position, strategy?: ExitStrategy): Promise<void> {
    try {
      this.activePositions.set(position.id, position)

      if (strategy) {
        this.activeStrategies.set(strategy.id, strategy)
        
        // Update strategy state to active
        await DatabaseService.client.exitStrategy.update({
          where: { id: strategy.id },
          data: {
            executionState: StrategyExecutionState.ACTIVE,
            lastTriggered: new Date()
          }
        })
      }

      // Subscribe to token price updates
      const tokenPositions = this.priceSubscriptions.get(position.tokenAddress) || new Set()
      tokenPositions.add(position.id)
      this.priceSubscriptions.set(position.tokenAddress, tokenPositions)

      if (tokenPositions.size === 1) {
        // First position for this token, subscribe to price updates
        await HeliusWebSocketService.subscribeToTokenPrices([position.tokenAddress], position.userId)
      }

      logTrading('Position added to monitoring', position.userId, {
        positionId: position.id,
        token: position.tokenSymbol,
        entryPrice: position.entryPrice,
        strategyId: strategy?.id
      })

    } catch (error) {
      logger.error('Failed to add position to monitoring:', error)
      throw error
    }
  }

  /**
   * Remove position from monitoring
   */
  public async removePosition(positionId: string): Promise<void> {
    try {
      const position = this.activePositions.get(positionId)
      if (!position) {
        return
      }

      // Remove from active positions
      this.activePositions.delete(positionId)

      // Update price subscriptions
      const tokenPositions = this.priceSubscriptions.get(position.tokenAddress)
      if (tokenPositions) {
        tokenPositions.delete(positionId)
        
        if (tokenPositions.size === 0) {
          // No more positions for this token, can unsubscribe
          this.priceSubscriptions.delete(position.tokenAddress)
          // TODO: Unsubscribe from price updates for this token
        }
      }

      // Deactivate associated strategy
      if (position.strategyId) {
        const strategy = this.activeStrategies.get(position.strategyId)
        if (strategy) {
          await DatabaseService.client.exitStrategy.update({
            where: { id: position.strategyId },
            data: {
              executionState: StrategyExecutionState.COMPLETED
            }
          })
          this.activeStrategies.delete(position.strategyId)
        }
      }

      logTrading('Position removed from monitoring', position.userId, { positionId })

    } catch (error) {
      logger.error('Failed to remove position from monitoring:', error)
    }
  }

  /**
   * Update position with new price data
   */
  public async updatePositionPrice(tokenAddress: string, currentPrice: number): Promise<void> {
    const tokenPositions = this.priceSubscriptions.get(tokenAddress)
    if (!tokenPositions) {
      return
    }

    const updates: PositionUpdate[] = []

    for (const positionId of tokenPositions) {
      const position = this.activePositions.get(positionId)
      if (!position) {
        continue
      }

      // Calculate P&L
      const pnl = (currentPrice - position.entryPrice) * position.quantity
      const pnlPercentage = ((currentPrice - position.entryPrice) / position.entryPrice) * 100
      const marketValue = currentPrice * position.quantity

      // Update position in memory
      position.currentPrice = currentPrice
      position.pnl = pnl
      position.pnlPercentage = pnlPercentage

      const positionUpdate: PositionUpdate = {
        positionId: position.id,
        userId: position.userId,
        currentPrice,
        pnl,
        pnlPercentage,
        marketValue,
        timestamp: Date.now()
      }

      updates.push(positionUpdate)

      // Check exit strategy triggers
      if (position.strategyId) {
        await this.checkStrategyTriggers(position, currentPrice)
      }
    }

    // Batch update database
    if (updates.length > 0) {
      await this.batchUpdatePositions(updates)
      
      // Publish updates to Redis for real-time frontend updates
      for (const update of updates) {
        await RedisService.publishJSON('position_update', update)
      }
    }
  }

  /**
   * Check if any exit strategy triggers should fire
   */
  private async checkStrategyTriggers(position: Position, currentPrice: number): Promise<void> {
    const strategy = this.activeStrategies.get(position.strategyId!)
    if (!strategy || strategy.executionState !== StrategyExecutionState.ACTIVE) {
      return
    }

    const triggers: StrategyTrigger[] = []

    // Check stop loss
    if (strategy.stopLoss && strategy.stopLoss.percentage > 0) {
      const stopLossPrice = position.entryPrice * (1 - strategy.stopLoss.percentage / 100)
      
      if (currentPrice <= stopLossPrice) {
        triggers.push({
          strategyId: strategy.id,
          positionId: position.id,
          userId: position.userId,
          triggerType: 'STOP_LOSS',
          triggerPrice: stopLossPrice,
          currentPrice,
          sellAmount: position.quantity,
          sellPercentage: 100,
          isEmergency: true
        })
      }
    }

    // Check profit targets
    for (const target of strategy.profitTargets) {
      const targetPrice = position.entryPrice * (1 + target.percentage / 100)
      
      if (currentPrice >= targetPrice) {
        // Check if this target has already been triggered
        const targetTriggered = await this.isTargetTriggered(strategy.id, target.percentage)
        
        if (!targetTriggered) {
          const sellAmount = position.quantity * (target.sellPercentage / 100)
          
          triggers.push({
            strategyId: strategy.id,
            positionId: position.id,
            userId: position.userId,
            triggerType: 'PROFIT_TARGET',
            triggerPrice: targetPrice,
            currentPrice,
            sellAmount,
            sellPercentage: target.sellPercentage,
            isEmergency: false
          })
        }
      }
    }

    // Check moon bag exit
    if (strategy.moonBag?.enabled && strategy.moonBag.exitTarget > 0) {
      const moonBagExitPrice = position.entryPrice * (1 + strategy.moonBag.exitTarget / 100)
      
      if (currentPrice >= moonBagExitPrice) {
        const moonBagTriggered = await this.isMoonBagTriggered(strategy.id)
        
        if (!moonBagTriggered) {
          // Calculate remaining moon bag amount
          const moonBagAmount = await this.calculateMoonBagAmount(position.id)
          
          if (moonBagAmount > 0) {
            triggers.push({
              strategyId: strategy.id,
              positionId: position.id,
              userId: position.userId,
              triggerType: 'MOON_BAG',
              triggerPrice: moonBagExitPrice,
              currentPrice,
              sellAmount: moonBagAmount,
              sellPercentage: (moonBagAmount / position.quantity) * 100,
              isEmergency: false
            })
          }
        }
      }
    }

    // Execute triggers
    for (const trigger of triggers) {
      await this.executeTrigger(trigger)
    }
  }

  /**
   * Execute a strategy trigger
   */
  private async executeTrigger(trigger: StrategyTrigger): Promise<void> {
    try {
      logTrading('Executing strategy trigger', trigger.userId, {
        strategyId: trigger.strategyId,
        triggerType: trigger.triggerType,
        triggerPrice: trigger.triggerPrice,
        currentPrice: trigger.currentPrice,
        sellAmount: trigger.sellAmount
      })

      const position = this.activePositions.get(trigger.positionId)
      if (!position) {
        throw new Error('Position not found for trigger execution')
      }

      // Prepare trade parameters for sell order
      const tradeParams: TradeParams = {
        tokenIn: position.tokenAddress,
        tokenOut: 'So11111111111111111111111111111111111111112', // SOL
        amount: trigger.sellAmount,
        slippage: trigger.isEmergency ? 10 : 1, // Higher slippage for emergency exits
        preset: position.presetUsed,
        strategyId: trigger.strategyId
      }

      // Execute the sell order
      const result = await TradingService.executeTrade(
        tradeParams,
        position.userId, // This should be wallet address, need to get from user
        trigger.userId
      )

      if (result.success) {
        // Update position quantity
        position.quantity -= trigger.sellAmount
        
        // Update database
        await DatabaseService.client.position.update({
          where: { id: trigger.positionId },
          data: {
            quantity: position.quantity,
            status: position.quantity <= 0 ? PositionStatus.CLOSED : PositionStatus.PARTIAL
          }
        })

        // Record trigger execution
        await this.recordTriggerExecution(trigger, result.transactionHash!)

        // If position is fully closed, remove from monitoring
        if (position.quantity <= 0) {
          await this.removePosition(trigger.positionId)
        }

        // Publish trigger execution to Redis
        await RedisService.publishJSON('strategy_trigger', {
          ...trigger,
          transactionHash: result.transactionHash,
          executedAt: new Date().toISOString()
        })

        logTrading('Strategy trigger executed successfully', trigger.userId, {
          triggerType: trigger.triggerType,
          transactionHash: result.transactionHash,
          sellAmount: trigger.sellAmount
        })

      } else {
        logger.error('Failed to execute strategy trigger', {
          trigger,
          error: result.error
        })
      }

    } catch (error) {
      logger.error('Error executing strategy trigger:', error)
      
      // TODO: Implement retry logic or alert mechanism
    }
  }

  /**
   * Load active positions from database
   */
  private async loadActivePositions(): Promise<void> {
    try {
      const positions = await DatabaseService.client.position.findMany({
        where: {
          status: {
            in: [PositionStatus.ACTIVE, PositionStatus.PARTIAL]
          }
        },
        include: {
          strategy: true
        }
      })

      for (const position of positions) {
        const positionData: Position = {
          id: position.id,
          userId: position.userId,
          tokenAddress: position.tokenAddress,
          tokenSymbol: position.tokenSymbol,
          entryPrice: parseFloat(position.entryPrice.toString()),
          currentPrice: parseFloat(position.currentPrice.toString()),
          quantity: parseFloat(position.quantity.toString()),
          entryTimestamp: position.entryTimestamp,
          strategyId: position.strategyId || undefined,
          presetUsed: position.presetUsed,
          riskLevel: position.riskLevel,
          status: position.status,
          pnl: parseFloat(position.pnl.toString()),
          pnlPercentage: position.pnlPercent,
          age: Date.now() - position.entryTimestamp.getTime()
        }

        this.activePositions.set(position.id, positionData)

        // Group by token for price subscriptions
        const tokenPositions = this.priceSubscriptions.get(position.tokenAddress) || new Set()
        tokenPositions.add(position.id)
        this.priceSubscriptions.set(position.tokenAddress, tokenPositions)
      }

      logger.info(`Loaded ${positions.length} active positions`)
    } catch (error) {
      logger.error('Failed to load active positions:', error)
      throw error
    }
  }

  /**
   * Load active exit strategies from database
   */
  private async loadActiveStrategies(): Promise<void> {
    try {
      const strategies = await DatabaseService.client.exitStrategy.findMany({
        where: {
          executionState: StrategyExecutionState.ACTIVE
        }
      })

      for (const strategy of strategies) {
        const strategyData: ExitStrategy = {
          id: strategy.id,
          userId: strategy.userId,
          positionId: strategy.positionId || undefined,
          type: strategy.type,
          stopLoss: strategy.stopLoss as StopLossConfig,
          profitTargets: strategy.profitTargets as ProfitTarget[],
          moonBag: strategy.moonBag as MoonBagConfig,
          locked: strategy.locked,
          customName: strategy.customName || undefined,
          executionState: strategy.executionState,
          lastUpdate: strategy.updatedAt
        }

        this.activeStrategies.set(strategy.id, strategyData)
      }

      logger.info(`Loaded ${strategies.length} active strategies`)
    } catch (error) {
      logger.error('Failed to load active strategies:', error)
      throw error
    }
  }

  /**
   * Subscribe to token price updates
   */
  private async subscribeToTokenPrices(): Promise<void> {
    const uniqueTokens = Array.from(this.priceSubscriptions.keys())
    
    if (uniqueTokens.length > 0) {
      try {
        await HeliusWebSocketService.subscribeToTokenPrices(uniqueTokens)
        logger.info(`Subscribed to price updates for ${uniqueTokens.length} tokens`)
      } catch (error) {
        logger.error('Failed to subscribe to token prices:', error)
      }
    }
  }

  /**
   * Unsubscribe from token price updates
   */
  private async unsubscribeFromTokenPrices(): Promise<void> {
    // TODO: Implement unsubscribe logic when HeliusWebSocketService supports it
  }

  /**
   * Initialize Redis subscriptions for external events
   */
  private initializeRedisSubscriptions(): void {
    // Subscribe to price updates from Helius WebSocket service
    RedisService.subscribe('price_update', (data) => {
      if (data.token) {
        this.updatePositionPrice(data.token, data.price)
      }
    })

    // Subscribe to account updates for position monitoring
    RedisService.subscribe('account_update', (data) => {
      // TODO: Process account updates for position tracking
    })
  }

  /**
   * Start monitoring loop
   */
  private startMonitoring(): void {
    this.monitoringInterval = setInterval(async () => {
      if (!this.isRunning) {
        return
      }

      try {
        // Perform periodic checks and cleanup
        await this.performPeriodicChecks()
      } catch (error) {
        logger.error('Error in monitoring loop:', error)
      }
    }, this.monitoringIntervalMs)
  }

  /**
   * Perform periodic checks and maintenance
   */
  private async performPeriodicChecks(): Promise<void> {
    // Check for stale positions
    const stalePositions = Array.from(this.activePositions.values()).filter(
      position => Date.now() - position.entryTimestamp.getTime() > 24 * 60 * 60 * 1000 // 24 hours
    )

    if (stalePositions.length > 0) {
      logger.debug(`Found ${stalePositions.length} positions older than 24 hours`)
    }

    // TODO: Implement additional periodic checks
    // - Strategy performance analysis
    // - Risk limit monitoring
    // - Position correlation checks
  }

  /**
   * Batch update positions in database
   */
  private async batchUpdatePositions(updates: PositionUpdate[]): Promise<void> {
    try {
      const updatePromises = updates.map(update =>
        DatabaseService.client.position.update({
          where: { id: update.positionId },
          data: {
            currentPrice: update.currentPrice,
            pnl: update.pnl,
            pnlPercent: update.pnlPercentage,
            updatedAt: new Date()
          }
        })
      )

      await Promise.all(updatePromises)
    } catch (error) {
      logger.error('Failed to batch update positions:', error)
    }
  }

  /**
   * Check if profit target has been triggered
   */
  private async isTargetTriggered(strategyId: string, targetPercentage: number): Promise<boolean> {
    // TODO: Check database for triggered targets
    return false
  }

  /**
   * Check if moon bag has been triggered
   */
  private async isMoonBagTriggered(strategyId: string): Promise<boolean> {
    // TODO: Check database for moon bag trigger
    return false
  }

  /**
   * Calculate remaining moon bag amount
   */
  private async calculateMoonBagAmount(positionId: string): Promise<number> {
    // TODO: Calculate based on executed targets and moon bag percentage
    return 0
  }

  /**
   * Record trigger execution in database
   */
  private async recordTriggerExecution(trigger: StrategyTrigger, transactionHash: string): Promise<void> {
    try {
      // TODO: Create trigger execution record in database
      logger.debug('Recorded trigger execution', {
        strategyId: trigger.strategyId,
        triggerType: trigger.triggerType,
        transactionHash
      })
    } catch (error) {
      logger.error('Failed to record trigger execution:', error)
    }
  }

  /**
   * Get monitoring statistics
   */
  public getStats(): {
    isRunning: boolean
    activePositions: number
    activeStrategies: number
    trackedTokens: number
  } {
    return {
      isRunning: this.isRunning,
      activePositions: this.activePositions.size,
      activeStrategies: this.activeStrategies.size,
      trackedTokens: this.priceSubscriptions.size
    }
  }

  /**
   * Health check for the service
   */
  public healthCheck(): { healthy: boolean; stats: any } {
    const stats = this.getStats()
    return {
      healthy: this.isRunning,
      stats
    }
  }
}

// Export singleton instance factory (lazy initialization)
let positionMonitorServiceInstance: PositionMonitorService | null = null

export const positionMonitorService = {
  getInstance(): PositionMonitorService {
    if (!positionMonitorServiceInstance) {
      positionMonitorServiceInstance = new PositionMonitorService()
    }
    return positionMonitorServiceInstance
  },
  
  async start(): Promise<void> {
    return this.getInstance().start()
  },
  
  async stop(): Promise<void> {
    return this.getInstance().stop()
  }
}