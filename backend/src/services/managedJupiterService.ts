/**
 * Managed Jupiter Service
 * 
 * Provides rate-limited, optimized access to Jupiter APIs with intelligent
 * endpoint routing and request management.
 */

import { logger } from '@/utils/logger'
import { rateLimitManager } from '@/services/rateLimitManager'
import { RedisService } from '@/services/redis'

export interface JupiterQuoteRequest {
  inputMint: string
  outputMint: string
  amount: string
  slippageBps?: number
  feeBps?: number
  onlyDirectRoutes?: boolean
  asLegacyTransaction?: boolean
  restrictIntermediateTokens?: boolean
  dynamicSlippage?: boolean
  maxAccounts?: number
  minimizeSlippage?: boolean
  prioritizationFeeLamports?: number
}

export interface JupiterSwapRequest {
  quoteResponse: any
  userPublicKey: string
  wrapAndUnwrapSol?: boolean
  useSharedAccounts?: boolean
  feeAccount?: string
  dynamicComputeUnitLimit?: boolean
  dynamicSlippage?: boolean
  prioritizationFeeLamports?: {
    priorityLevelWithMaxLamports?: {
      maxLamports: number
      global?: boolean
      priorityLevel?: string
    }
  }
}

export interface JupiterEndpoint {
  baseUrl: string
  name: string
  priority: number
  isHealthy: boolean
  lastHealthCheck: number
  responseTime: number
  successRate: number
  requestCount: number
  errorCount: number
}

class ManagedJupiterService {
  private endpoints: JupiterEndpoint[] = [
    {
      baseUrl: 'https://lite-api.jup.ag/swap/v1',
      name: 'jupiter_lite_primary',
      priority: 1,
      isHealthy: true,
      lastHealthCheck: 0,
      responseTime: 0,
      successRate: 1.0,
      requestCount: 0,
      errorCount: 0
    },
    {
      baseUrl: 'https://api.jup.ag/swap/v1',
      name: 'jupiter_pro_primary',
      priority: 2,
      isHealthy: true,
      lastHealthCheck: 0,
      responseTime: 0,
      successRate: 1.0,
      requestCount: 0,
      errorCount: 0
    }
  ]

  private quoteCache: Map<string, { data: any, timestamp: number }> = new Map()
  private readonly QUOTE_CACHE_TTL = 10000 // 10 seconds for quotes

  constructor() {
    // Set up rate limit manager request handler
    rateLimitManager.on('executeRequest', this.handleRateLimitedRequest.bind(this))
    
    // Start health monitoring
    this.startHealthMonitoring()
    
    // Start cache cleanup
    this.startCacheCleanup()
  }

  /**
   * Get quote with intelligent routing and caching
   */
  public async getQuote(
    request: JupiterQuoteRequest,
    priority: 'low' | 'medium' | 'high' | 'critical' = 'high'
  ): Promise<any> {
    // Check cache first
    const cacheKey = this.getQuoteCacheKey(request)
    const cached = this.getFromCache(cacheKey)
    
    if (cached) {
      logger.debug('Using cached Jupiter quote', { 
        inputMint: request.inputMint.substring(0, 8),
        outputMint: request.outputMint.substring(0, 8),
        amount: request.amount
      })
      return cached
    }

    // Add optimization parameters
    const optimizedRequest: JupiterQuoteRequest = {
      ...request,
      restrictIntermediateTokens: request.restrictIntermediateTokens ?? true,
      dynamicSlippage: request.dynamicSlippage ?? true,
      maxAccounts: request.maxAccounts ?? 64,
      minimizeSlippage: request.minimizeSlippage ?? true
    }

    try {
      const result = await rateLimitManager.queueRequest(
        'jupiter_quote',
        'quote',
        optimizedRequest,
        priority
      )

      // Cache the result
      this.setCache(cacheKey, result)

      logger.info('Jupiter quote obtained', { 
        inputMint: request.inputMint.substring(0, 8),
        outputMint: request.outputMint.substring(0, 8),
        amount: request.amount,
        outAmount: result?.outAmount,
        priceImpact: result?.priceImpactPct
      })

      return result

    } catch (error) {
      logger.error('Jupiter quote failed', { 
        inputMint: request.inputMint,
        outputMint: request.outputMint,
        amount: request.amount,
        error: error instanceof Error ? error.message : 'Unknown error' 
      })
      throw error
    }
  }

  /**
   * Get swap transaction with intelligent priority fee integration
   */
  public async getSwapTransaction(
    request: JupiterSwapRequest,
    priority: 'low' | 'medium' | 'high' | 'critical' = 'high'
  ): Promise<any> {
    // Add optimization parameters
    const optimizedRequest: JupiterSwapRequest = {
      ...request,
      wrapAndUnwrapSol: request.wrapAndUnwrapSol ?? true,
      useSharedAccounts: request.useSharedAccounts ?? true,
      dynamicComputeUnitLimit: request.dynamicComputeUnitLimit ?? true,
      dynamicSlippage: request.dynamicSlippage ?? true
    }

    try {
      const result = await rateLimitManager.queueRequest(
        'jupiter_swap',
        'swap',
        optimizedRequest,
        priority
      )

      logger.info('Jupiter swap transaction obtained', { 
        userPublicKey: request.userPublicKey.substring(0, 8),
        swapTransaction: !!result?.swapTransaction
      })

      return result

    } catch (error) {
      logger.error('Jupiter swap transaction failed', { 
        userPublicKey: request.userPublicKey,
        error: error instanceof Error ? error.message : 'Unknown error' 
      })
      throw error
    }
  }

  /**
   * Get multiple quotes for comparison
   */
  public async getMultipleQuotes(
    requests: JupiterQuoteRequest[],
    priority: 'low' | 'medium' | 'high' | 'critical' = 'medium'
  ): Promise<any[]> {
    if (requests.length === 0) {
      return []
    }

    // Check cache for all requests
    const results: (any | null)[] = []
    const uncachedRequests: { index: number, request: JupiterQuoteRequest }[] = []

    requests.forEach((request, index) => {
      const cacheKey = this.getQuoteCacheKey(request)
      const cached = this.getFromCache(cacheKey)
      
      if (cached) {
        results[index] = cached
      } else {
        results[index] = null
        uncachedRequests.push({ index, request })
      }
    })

    // Get uncached quotes
    if (uncachedRequests.length > 0) {
      const promises = uncachedRequests.map(({ request }) => 
        this.getQuote(request, priority).catch(error => {
          logger.error('Quote failed in batch', { error: error.message })
          return null
        })
      )

      const uncachedResults = await Promise.all(promises)

      // Merge results
      uncachedRequests.forEach(({ index }, i) => {
        results[index] = uncachedResults[i]
      })
    }

    return results
  }

  /**
   * Get route map for a token pair
   */
  public async getRouteMap(
    inputMint: string,
    outputMint: string,
    onlyDirectRoutes: boolean = false,
    priority: 'low' | 'medium' | 'high' | 'critical' = 'low'
  ): Promise<any> {
    try {
      const result = await rateLimitManager.queueRequest(
        'jupiter_quote',
        'route-map',
        { inputMint, outputMint, onlyDirectRoutes },
        priority
      )

      return result

    } catch (error) {
      logger.error('Jupiter route map failed', { 
        inputMint, 
        outputMint,
        error: error instanceof Error ? error.message : 'Unknown error' 
      })
      throw error
    }
  }

  /**
   * Handle rate-limited requests
   */
  private async handleRateLimitedRequest(event: {
    apiType: string
    request: any
    resolve: (value: any) => void
    reject: (error: Error) => void
  }): Promise<void> {
    const { apiType, request, resolve, reject } = event

    try {
      if (!apiType.startsWith('jupiter')) {
        return // Not our request
      }

      let result: any

      if (request.endpoint === 'quote') {
        result = await this.executeQuoteRequest(request.data)
      } else if (request.endpoint === 'swap') {
        result = await this.executeSwapRequest(request.data)
      } else if (request.endpoint === 'route-map') {
        result = await this.executeRouteMapRequest(request.data)
      } else {
        throw new Error(`Unknown Jupiter endpoint: ${request.endpoint}`)
      }

      resolve(result)

    } catch (error) {
      reject(error instanceof Error ? error : new Error('Request execution failed'))
    }
  }

  /**
   * Execute quote request with endpoint failover
   */
  private async executeQuoteRequest(request: JupiterQuoteRequest): Promise<any> {
    const healthyEndpoints = this.getHealthyEndpoints()
    
    for (const endpoint of healthyEndpoints) {
      try {
        const startTime = Date.now()
        
        // Build query parameters
        const params = new URLSearchParams()
        params.append('inputMint', request.inputMint)
        params.append('outputMint', request.outputMint)
        params.append('amount', request.amount)
        
        if (request.slippageBps) params.append('slippageBps', request.slippageBps.toString())
        if (request.feeBps) params.append('feeBps', request.feeBps.toString())
        if (request.onlyDirectRoutes) params.append('onlyDirectRoutes', 'true')
        if (request.restrictIntermediateTokens) params.append('restrictIntermediateTokens', 'true')
        if (request.dynamicSlippage) params.append('dynamicSlippage', 'true')
        if (request.maxAccounts) params.append('maxAccounts', request.maxAccounts.toString())
        if (request.minimizeSlippage) params.append('minimizeSlippage', 'true')

        const url = `${endpoint.baseUrl}/quote?${params.toString()}`
        
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          }
        })

        const responseTime = Date.now() - startTime

        if (!response.ok) {
          throw new Error(`Jupiter quote failed: ${response.status} ${response.statusText}`)
        }

        const result = await response.json()
        
        // Update endpoint metrics
        this.updateEndpointMetrics(endpoint, true, responseTime)
        
        return result

      } catch (error) {
        logger.warn('Jupiter endpoint failed, trying next', { 
          endpoint: endpoint.name,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
        
        this.updateEndpointMetrics(endpoint, false, 0)
        
        // Continue to next endpoint
        continue
      }
    }

    throw new Error('All Jupiter endpoints failed for quote request')
  }

  /**
   * Execute swap request
   */
  private async executeSwapRequest(request: JupiterSwapRequest): Promise<any> {
    const healthyEndpoints = this.getHealthyEndpoints()
    
    for (const endpoint of healthyEndpoints) {
      try {
        const startTime = Date.now()
        
        const url = `${endpoint.baseUrl}/swap`
        
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: JSON.stringify(request)
        })

        const responseTime = Date.now() - startTime

        if (!response.ok) {
          const errorText = await response.text()
          throw new Error(`Jupiter swap failed: ${response.status} ${errorText}`)
        }

        const result = await response.json()
        
        // Update endpoint metrics
        this.updateEndpointMetrics(endpoint, true, responseTime)
        
        return result

      } catch (error) {
        logger.warn('Jupiter endpoint failed, trying next', { 
          endpoint: endpoint.name,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
        
        this.updateEndpointMetrics(endpoint, false, 0)
        
        // Continue to next endpoint
        continue
      }
    }

    throw new Error('All Jupiter endpoints failed for swap request')
  }

  /**
   * Execute route map request
   */
  private async executeRouteMapRequest(request: any): Promise<any> {
    const healthyEndpoints = this.getHealthyEndpoints()
    
    for (const endpoint of healthyEndpoints) {
      try {
        const params = new URLSearchParams()
        if (request.inputMint) params.append('inputMint', request.inputMint)
        if (request.outputMint) params.append('outputMint', request.outputMint)
        if (request.onlyDirectRoutes) params.append('onlyDirectRoutes', 'true')

        const url = `${endpoint.baseUrl}/route-map?${params.toString()}`
        
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          }
        })

        if (!response.ok) {
          throw new Error(`Jupiter route map failed: ${response.status}`)
        }

        const result = await response.json()
        this.updateEndpointMetrics(endpoint, true, 0)
        return result

      } catch (error) {
        this.updateEndpointMetrics(endpoint, false, 0)
        continue
      }
    }

    throw new Error('All Jupiter endpoints failed for route map request')
  }

  /**
   * Get healthy endpoints sorted by priority and performance
   */
  private getHealthyEndpoints(): JupiterEndpoint[] {
    return this.endpoints
      .filter(endpoint => endpoint.isHealthy)
      .sort((a, b) => {
        // Sort by success rate first, then by response time
        if (a.successRate !== b.successRate) {
          return b.successRate - a.successRate
        }
        return a.responseTime - b.responseTime
      })
  }

  /**
   * Update endpoint metrics
   */
  private updateEndpointMetrics(endpoint: JupiterEndpoint, success: boolean, responseTime: number): void {
    endpoint.requestCount++
    
    if (success) {
      endpoint.responseTime = (endpoint.responseTime + responseTime) / 2
    } else {
      endpoint.errorCount++
    }
    
    endpoint.successRate = (endpoint.requestCount - endpoint.errorCount) / endpoint.requestCount
    endpoint.isHealthy = endpoint.successRate > 0.8 && endpoint.errorCount < 10
  }

  /**
   * Start health monitoring
   */
  private startHealthMonitoring(): void {
    setInterval(async () => {
      for (const endpoint of this.endpoints) {
        try {
          const startTime = Date.now()
          const response = await fetch(`${endpoint.baseUrl}/health`, {
            method: 'GET',
            timeout: 5000
          })
          
          const responseTime = Date.now() - startTime
          endpoint.lastHealthCheck = Date.now()
          
          if (response.ok) {
            endpoint.isHealthy = true
            endpoint.responseTime = (endpoint.responseTime + responseTime) / 2
          } else {
            endpoint.isHealthy = false
          }
          
        } catch (error) {
          endpoint.isHealthy = false
          endpoint.lastHealthCheck = Date.now()
        }
      }
      
      logger.debug('Jupiter endpoint health check completed', {
        healthyEndpoints: this.endpoints.filter(e => e.isHealthy).length,
        totalEndpoints: this.endpoints.length
      })
      
    }, 60000) // Check every minute
  }

  /**
   * Cache management
   */
  private getQuoteCacheKey(request: JupiterQuoteRequest): string {
    const key = `${request.inputMint}-${request.outputMint}-${request.amount}-${request.slippageBps || 0}`
    return `jupiter:quote:${key}`
  }

  private getFromCache(key: string): any | null {
    const cached = this.quoteCache.get(key)
    if (!cached) return null

    if (Date.now() > cached.timestamp + this.QUOTE_CACHE_TTL) {
      this.quoteCache.delete(key)
      return null
    }

    return cached.data
  }

  private setCache(key: string, data: any): void {
    this.quoteCache.set(key, {
      data,
      timestamp: Date.now()
    })
  }

  /**
   * Start cache cleanup
   */
  private startCacheCleanup(): void {
    setInterval(() => {
      const now = Date.now()
      for (const [key, cached] of this.quoteCache.entries()) {
        if (now > cached.timestamp + this.QUOTE_CACHE_TTL) {
          this.quoteCache.delete(key)
        }
      }
    }, 30000) // Clean every 30 seconds
  }

  /**
   * Get service statistics
   */
  public getStats(): {
    endpoints: JupiterEndpoint[]
    cacheSize: number
    rateLimitStats: any
  } {
    return {
      endpoints: this.endpoints.map(e => ({ ...e })),
      cacheSize: this.quoteCache.size,
      rateLimitStats: rateLimitManager.getStats()
    }
  }

  /**
   * Health check
   */
  public async healthCheck(): Promise<boolean> {
    const healthyEndpoints = this.getHealthyEndpoints()
    return healthyEndpoints.length > 0
  }

  /**
   * Clear cache
   */
  public clearCache(): void {
    this.quoteCache.clear()
    logger.info('Jupiter service cache cleared')
  }
}

// Export singleton instance
export const managedJupiterService = new ManagedJupiterService()
export { ManagedJupiterService }