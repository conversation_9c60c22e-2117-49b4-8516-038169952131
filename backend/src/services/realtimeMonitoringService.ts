import { EventEmitter } from 'events'
import { logger } from '@/utils/logger'
import { RedisService } from '@/services/redis'
import { DatabaseService } from '@/services/database'
import { heliusLaserStreamService } from '@/services/heliusLaserStreamService'

export interface UserPortfolioMonitoring {
  userId: string
  walletAddresses: string[]
  tokenAddresses: string[]
  subscriptionIds: string[]
  isActive: boolean
  createdAt: Date
}

export interface PortfolioUpdate {
  userId: string
  type: 'balance' | 'transaction' | 'price' | 'position'
  data: any
  timestamp: number
}

class RealtimeMonitoringService extends EventEmitter {
  private userMonitoring: Map<string, UserPortfolioMonitoring> = new Map()
  private isInitialized: boolean = false

  constructor() {
    super()
  }

  /**
   * Initialize the real-time monitoring service
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return
    }

    try {
      // Set up LaserStream event handlers
      heliusLaserStreamService.on('transactionUpdate', this.handleTransactionUpdate.bind(this))
      heliusLaserStreamService.on('accountUpdate', this.handleAccountUpdate.bind(this))
      
      // Load existing user monitoring from database
      await this.loadExistingMonitoring()
      
      this.isInitialized = true
      logger.info('Real-time monitoring service initialized')
    } catch (error) {
      logger.error('Failed to initialize real-time monitoring service:', error)
      throw error
    }
  }

  /**
   * Start monitoring for a user's portfolio
   */
  public async startUserMonitoring(userId: string, walletAddresses: string[]): Promise<void> {
    try {
      // Stop existing monitoring if any
      await this.stopUserMonitoring(userId)

      // Get user's token addresses from positions
      const tokenAddresses = await this.getUserTokenAddresses(userId)
      const allAddresses = [...walletAddresses, ...tokenAddresses]

      if (allAddresses.length === 0) {
        logger.warn('No addresses to monitor for user:', { userId })
        return
      }

      // Subscribe to account updates for all addresses
      const subscriptionId = await heliusLaserStreamService.subscribeToAccounts(allAddresses, userId)

      // Create monitoring record
      const monitoring: UserPortfolioMonitoring = {
        userId,
        walletAddresses,
        tokenAddresses,
        subscriptionIds: [subscriptionId],
        isActive: true,
        createdAt: new Date()
      }

      this.userMonitoring.set(userId, monitoring)

      // Save to database for persistence
      await this.saveMonitoringToDatabase(monitoring)

      logger.info('Started real-time monitoring for user', { 
        userId, 
        addresses: allAddresses.length,
        subscriptionId 
      })

    } catch (error) {
      logger.error('Failed to start user monitoring:', error)
      throw error
    }
  }

  /**
   * Stop monitoring for a user
   */
  public async stopUserMonitoring(userId: string): Promise<void> {
    try {
      const monitoring = this.userMonitoring.get(userId)
      
      if (!monitoring) {
        return
      }

      // Unsubscribe from all LaserStream subscriptions
      for (const subscriptionId of monitoring.subscriptionIds) {
        await heliusLaserStreamService.unsubscribe(subscriptionId)
      }

      // Remove from memory
      this.userMonitoring.delete(userId)

      // Update database
      await this.removeMonitoringFromDatabase(userId)

      logger.info('Stopped real-time monitoring for user', { userId })

    } catch (error) {
      logger.error('Failed to stop user monitoring:', error)
    }
  }

  /**
   * Add transaction monitoring for specific signatures
   */
  public async addTransactionMonitoring(userId: string, signatures: string[]): Promise<void> {
    try {
      const subscriptionId = await heliusLaserStreamService.subscribeToTransactions(signatures, userId)
      
      // Update user monitoring record
      const monitoring = this.userMonitoring.get(userId)
      if (monitoring) {
        monitoring.subscriptionIds.push(subscriptionId)
        await this.saveMonitoringToDatabase(monitoring)
      }

      logger.info('Added transaction monitoring', { 
        userId, 
        signatures: signatures.length,
        subscriptionId 
      })

    } catch (error) {
      logger.error('Failed to add transaction monitoring:', error)
      throw error
    }
  }

  /**
   * Handle transaction updates from LaserStream
   */
  private async handleTransactionUpdate(update: any): Promise<void> {
    try {
      // Find users interested in this transaction
      const interestedUsers = await this.findUsersInterestedInTransaction(update.signature)

      for (const userId of interestedUsers) {
        const portfolioUpdate: PortfolioUpdate = {
          userId,
          type: 'transaction',
          data: {
            signature: update.signature,
            status: update.confirmationStatus,
            slot: update.slot,
            blockTime: update.blockTime,
            err: update.err
          },
          timestamp: Date.now()
        }

        // Emit event
        this.emit('portfolioUpdate', portfolioUpdate)

        // Publish to Redis for real-time client updates
        await RedisService.publishJSON(`user:${userId}:portfolio_update`, portfolioUpdate)

        logger.debug('Transaction update sent to user', { 
          userId, 
          signature: update.signature,
          status: update.confirmationStatus 
        })
      }

    } catch (error) {
      logger.error('Error handling transaction update:', error)
    }
  }

  /**
   * Handle account updates from LaserStream
   */
  private async handleAccountUpdate(update: any): Promise<void> {
    try {
      // Find users interested in this account
      const interestedUsers = await this.findUsersInterestedInAccount(update.account)

      for (const userId of interestedUsers) {
        const portfolioUpdate: PortfolioUpdate = {
          userId,
          type: 'balance',
          data: {
            account: update.account,
            lamports: update.lamports,
            slot: update.slot,
            owner: update.owner
          },
          timestamp: Date.now()
        }

        // Emit event
        this.emit('portfolioUpdate', portfolioUpdate)

        // Publish to Redis for real-time client updates
        await RedisService.publishJSON(`user:${userId}:portfolio_update`, portfolioUpdate)

        // Update cached balance if it's a significant change
        if (await this.isSignificantBalanceChange(update.account, update.lamports)) {
          await RedisService.setJSON(`balance:${update.account}`, {
            lamports: update.lamports,
            slot: update.slot,
            timestamp: Date.now()
          }, 60) // 1 minute cache
        }

        logger.debug('Account update sent to user', { 
          userId, 
          account: update.account,
          lamports: update.lamports 
        })
      }

    } catch (error) {
      logger.error('Error handling account update:', error)
    }
  }

  /**
   * Find users interested in a specific transaction
   */
  private async findUsersInterestedInTransaction(signature: string): Promise<string[]> {
    try {
      // Check if this transaction is associated with any user positions
      const positions = await DatabaseService.client.position.findMany({
        where: {
          OR: [
            { entryTransactionHash: signature },
            { exitTransactionHash: signature }
          ]
        },
        select: { userId: true }
      })

      return positions.map(p => p.userId)

    } catch (error) {
      logger.error('Error finding users interested in transaction:', error)
      return []
    }
  }

  /**
   * Find users interested in a specific account
   */
  private async findUsersInterestedInAccount(account: string): Promise<string[]> {
    try {
      const interestedUsers: string[] = []

      // Check if this account belongs to any monitored wallets or tokens
      for (const [userId, monitoring] of this.userMonitoring.entries()) {
        if (monitoring.walletAddresses.includes(account) || 
            monitoring.tokenAddresses.includes(account)) {
          interestedUsers.push(userId)
        }
      }

      return interestedUsers

    } catch (error) {
      logger.error('Error finding users interested in account:', error)
      return []
    }
  }

  /**
   * Get user's token addresses from positions
   */
  private async getUserTokenAddresses(userId: string): Promise<string[]> {
    try {
      const positions = await DatabaseService.client.position.findMany({
        where: { 
          userId,
          status: 'ACTIVE'
        },
        select: { tokenAddress: true }
      })

      return positions.map(p => p.tokenAddress)

    } catch (error) {
      logger.error('Error getting user token addresses:', error)
      return []
    }
  }

  /**
   * Check if balance change is significant enough to notify
   */
  private async isSignificantBalanceChange(account: string, newLamports: number): Promise<boolean> {
    try {
      const cached = await RedisService.getJSON<{ lamports: number }>(`balance:${account}`)
      
      if (!cached) {
        return true // First time seeing this balance
      }

      const changePercent = Math.abs((newLamports - cached.lamports) / cached.lamports)
      return changePercent > 0.01 // 1% change threshold

    } catch (error) {
      return true // Assume significant if we can't determine
    }
  }

  /**
   * Load existing monitoring from database
   */
  private async loadExistingMonitoring(): Promise<void> {
    try {
      // This would load from a monitoring table if it exists
      // For now, we'll start fresh each time
      logger.info('Loading existing monitoring configurations (placeholder)')

    } catch (error) {
      logger.error('Error loading existing monitoring:', error)
    }
  }

  /**
   * Save monitoring configuration to database
   */
  private async saveMonitoringToDatabase(monitoring: UserPortfolioMonitoring): Promise<void> {
    try {
      // Save monitoring configuration to database for persistence
      // This would require a new table for monitoring configurations
      logger.debug('Saving monitoring configuration to database', { userId: monitoring.userId })

    } catch (error) {
      logger.error('Error saving monitoring to database:', error)
    }
  }

  /**
   * Remove monitoring configuration from database
   */
  private async removeMonitoringFromDatabase(userId: string): Promise<void> {
    try {
      // Remove monitoring configuration from database
      logger.debug('Removing monitoring configuration from database', { userId })

    } catch (error) {
      logger.error('Error removing monitoring from database:', error)
    }
  }

  /**
   * Get monitoring status for a user
   */
  public getUserMonitoringStatus(userId: string): UserPortfolioMonitoring | null {
    return this.userMonitoring.get(userId) || null
  }

  /**
   * Get overall service status
   */
  public getServiceStatus(): {
    isInitialized: boolean
    activeUsers: number
    totalSubscriptions: number
    laserStreamStatus: any
  } {
    let totalSubscriptions = 0
    for (const monitoring of this.userMonitoring.values()) {
      totalSubscriptions += monitoring.subscriptionIds.length
    }

    return {
      isInitialized: this.isInitialized,
      activeUsers: this.userMonitoring.size,
      totalSubscriptions,
      laserStreamStatus: heliusLaserStreamService.getStatus()
    }
  }

  /**
   * Health check
   */
  public async healthCheck(): Promise<boolean> {
    try {
      return this.isInitialized && await heliusLaserStreamService.healthCheck()
    } catch (error) {
      logger.error('Real-time monitoring service health check failed:', error)
      return false
    }
  }
}

// Export singleton instance
export const realtimeMonitoringService = new RealtimeMonitoringService()
export { RealtimeMonitoringService }