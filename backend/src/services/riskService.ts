import { config } from '@/config/environment'
import { logger, logRisk } from '@/utils/logger'
import { DatabaseService } from '@/services/database'
import { RedisService } from '@/services/redis'
import { PriceService } from '@/services/priceService'
import { AppError } from '@/middleware/errorHandler'
import type { 
  Position, 
  TokenMarketData, 
  RiskParameters,
  PerformanceMetrics
} from '@memetrader-pro/shared'
import { RiskLevel, PositionStatus, PresetType } from '@memetrader-pro/shared'

interface PortfolioRiskMetrics {
  totalValue: number
  totalValueUsd: number
  exposurePercentage: number
  riskScore: number
  maxDrawdown: number
  concentration: number
  correlationRisk: number
  liquidityRisk: number
  volatilityScore: number
}

interface PositionRiskAnalysis {
  positionId: string
  riskLevel: RiskLevel
  riskScore: number
  volatilityRisk: number
  liquidityRisk: number
  concentrationRisk: number
  timeRisk: number
  recommendations: string[]
}

interface PositionSizeCalculation {
  recommendedSize: number
  maxAllowedSize: number
  riskAdjustedSize: number
  riskLevel: RiskLevel
  reasoning: string[]
  warnings: string[]
}

interface RiskLimits {
  maxPositionSize: number // % of portfolio
  maxDailyLoss: number // % of portfolio
  maxDrawdown: number // % of portfolio
  maxConcentration: number // % in single token
  maxCorrelatedExposure: number // % in correlated tokens
  volatilityThreshold: number // Max acceptable volatility
}

class RiskServiceClass {
  private static instance: RiskServiceClass
  private defaultRiskLimits: RiskLimits = {
    maxPositionSize: 10, // 10% max per position
    maxDailyLoss: 5, // 5% max daily loss
    maxDrawdown: 20, // 20% max drawdown
    maxConcentration: 25, // 25% max in single token
    maxCorrelatedExposure: 40, // 40% max in correlated assets
    volatilityThreshold: 100 // 100% daily volatility threshold
  }

  private constructor() {}

  public static getInstance(): RiskServiceClass {
    if (!RiskServiceClass.instance) {
      RiskServiceClass.instance = new RiskServiceClass()
    }
    return RiskServiceClass.instance
  }

  /**
   * Calculate optimal position size based on risk parameters
   */
  public async calculatePositionSize(
    userId: string,
    tokenAddress: string,
    riskTolerance: RiskLevel,
    portfolioValue: number,
    customRiskParams?: Partial<RiskParameters>
  ): Promise<PositionSizeCalculation> {
    try {
      logRisk('Calculating position size', userId, {
        tokenAddress,
        riskTolerance,
        portfolioValue
      })

      // 1. Get user's risk parameters
      const userRiskParams = await this.getUserRiskParameters(userId)
      const riskParams = { ...userRiskParams, ...customRiskParams }

      // 2. Get token market data and volatility
      const marketData = await PriceService.getTokenMarketData(tokenAddress)
      if (!marketData) {
        throw new AppError('Unable to get market data for risk calculation', 400, 'MARKET_DATA_UNAVAILABLE')
      }

      // 3. Calculate volatility-based risk
      const volatility = await this.calculateTokenVolatility(tokenAddress)
      const liquidityRisk = await this.calculateLiquidityRisk(tokenAddress, marketData)

      // 4. Get current portfolio exposure
      const currentExposure = await this.getCurrentPortfolioExposure(userId)
      const tokenExposure = await this.getTokenExposure(userId, tokenAddress)

      // 5. Calculate base position size based on risk tolerance
      let baseSize = this.getBaseSizeForRiskLevel(riskTolerance, portfolioValue)

      // 6. Apply risk adjustments
      const volatilityAdjustment = Math.max(0.1, Math.min(1, 50 / volatility)) // Reduce size for high volatility
      const liquidityAdjustment = Math.max(0.1, Math.min(1, marketData.liquidity || 0) / 1000000) // Reduce for low liquidity
      const exposureAdjustment = Math.max(0.1, 1 - (currentExposure.exposurePercentage / 100))

      // 7. Calculate adjusted sizes
      const riskAdjustedSize = baseSize * volatilityAdjustment * liquidityAdjustment * exposureAdjustment
      const maxAllowedSize = Math.min(
        portfolioValue * (riskParams.maxPositionSize / 100),
        portfolioValue * (this.defaultRiskLimits.maxPositionSize / 100)
      )

      const recommendedSize = Math.min(riskAdjustedSize, maxAllowedSize)

      // 8. Generate warnings and recommendations
      const warnings: string[] = []
      const reasoning: string[] = []

      if (volatility > this.defaultRiskLimits.volatilityThreshold) {
        warnings.push(`High volatility detected (${volatility.toFixed(1)}%)`)
        reasoning.push('Position size reduced due to high volatility')
      }

      if ((marketData.liquidity || 0) < 100000) {
        warnings.push('Low liquidity may impact execution')
        reasoning.push('Position size reduced due to liquidity concerns')
      }

      if (currentExposure.exposurePercentage > 80) {
        warnings.push('Portfolio exposure is high')
        reasoning.push('Position size reduced due to existing exposure')
      }

      if (tokenExposure > riskParams.maxPositionSize) {
        warnings.push('Would exceed maximum position size limit')
      }

      reasoning.push(`Base size calculated for ${riskTolerance} risk tolerance`)
      reasoning.push(`Adjusted for volatility (${(volatilityAdjustment * 100).toFixed(0)}%)`)
      reasoning.push(`Adjusted for liquidity (${(liquidityAdjustment * 100).toFixed(0)}%)`)

      const result: PositionSizeCalculation = {
        recommendedSize,
        maxAllowedSize,
        riskAdjustedSize,
        riskLevel: riskTolerance,
        reasoning,
        warnings
      }

      logRisk('Position size calculated', userId, result)

      return result

    } catch (error) {
      logRisk('Position size calculation failed', userId, {
        tokenAddress,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      throw error
    }
  }

  /**
   * Analyze risk for a specific position
   */
  public async analyzePositionRisk(positionId: string): Promise<PositionRiskAnalysis> {
    try {
      const position = await DatabaseService.client.position.findUnique({
        where: { id: positionId },
        include: { user: true }
      })

      if (!position) {
        throw new AppError('Position not found', 404, 'POSITION_NOT_FOUND')
      }

      logRisk('Analyzing position risk', position.userId, { positionId })

      // 1. Get market data
      const marketData = await PriceService.getTokenMarketData(position.tokenAddress)
      if (!marketData) {
        throw new AppError('Market data unavailable for risk analysis', 400, 'MARKET_DATA_UNAVAILABLE')
      }

      // 2. Calculate various risk components
      const volatilityRisk = await this.calculateVolatilityRisk(position.tokenAddress)
      const liquidityRisk = await this.calculateLiquidityRisk(position.tokenAddress, marketData)
      const concentrationRisk = await this.calculateConcentrationRisk(position.userId, position.tokenAddress)
      const timeRisk = this.calculateTimeRisk(position.entryTimestamp)

      // 3. Calculate overall risk score (weighted average)
      const riskScore = (
        volatilityRisk * 0.3 +
        liquidityRisk * 0.25 +
        concentrationRisk * 0.25 +
        timeRisk * 0.2
      )

      // 4. Determine risk level
      let riskLevel: RiskLevel
      if (riskScore < 30) riskLevel = RiskLevel.LOW
      else if (riskScore < 60) riskLevel = RiskLevel.MEDIUM
      else if (riskScore < 80) riskLevel = RiskLevel.HIGH
      else riskLevel = RiskLevel.EXTREME

      // 5. Generate recommendations
      const recommendations: string[] = []

      if (volatilityRisk > 70) {
        recommendations.push('Consider reducing position size due to high volatility')
      }

      if (liquidityRisk > 60) {
        recommendations.push('Monitor liquidity closely for exit opportunities')
      }

      if (concentrationRisk > 50) {
        recommendations.push('Position represents high portfolio concentration')
      }

      if (timeRisk > 40) {
        recommendations.push('Consider implementing time-based exit strategy')
      }

      if (position.pnlPercent < -10) {
        recommendations.push('Position showing significant loss - review exit strategy')
      }

      const analysis: PositionRiskAnalysis = {
        positionId,
        riskLevel,
        riskScore,
        volatilityRisk,
        liquidityRisk,
        concentrationRisk,
        timeRisk,
        recommendations
      }

      logRisk('Position risk analysis completed', position.userId, analysis)

      return analysis

    } catch (error) {
      logRisk('Position risk analysis failed', undefined, {
        positionId,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      throw error
    }
  }

  /**
   * Calculate comprehensive portfolio risk metrics
   */
  public async calculatePortfolioRisk(userId: string): Promise<PortfolioRiskMetrics> {
    try {
      logRisk('Calculating portfolio risk', userId)

      // 1. Get all active positions
      const positions = await DatabaseService.client.position.findMany({
        where: {
          userId,
          status: PositionStatus.ACTIVE
        }
      })

      if (positions.length === 0) {
        return {
          totalValue: 0,
          totalValueUsd: 0,
          exposurePercentage: 0,
          riskScore: 0,
          maxDrawdown: 0,
          concentration: 0,
          correlationRisk: 0,
          liquidityRisk: 0,
          volatilityScore: 0
        }
      }

      // 2. Get current market data for all positions
      const tokenAddresses = positions.map(p => p.tokenAddress)
      const marketDataMap = await PriceService.getMultipleTokenPrices(tokenAddresses)

      // 3. Calculate total portfolio value
      let totalValue = 0
      let totalValueUsd = 0
      const positionValues: { [key: string]: number } = {}

      for (const position of positions) {
        const marketData = marketDataMap.get(position.tokenAddress)
        if (marketData) {
          const currentValue = parseFloat(position.quantity.toString()) * marketData.price
          positionValues[position.tokenAddress] = currentValue
          totalValue += currentValue
          totalValueUsd += currentValue // Assuming price is in USD equivalent
        }
      }

      // 4. Calculate concentration risk
      const maxPositionValue = Math.max(...Object.values(positionValues))
      const concentration = totalValue > 0 ? (maxPositionValue / totalValue) * 100 : 0

      // 5. Calculate portfolio-level metrics
      const volatilityScore = await this.calculatePortfolioVolatility(positions, marketDataMap)
      const liquidityRisk = await this.calculatePortfolioLiquidityRisk(positions, marketDataMap)
      const correlationRisk = await this.calculateCorrelationRisk(positions)

      // 6. Get historical performance for drawdown calculation
      const maxDrawdown = await this.calculateMaxDrawdown(userId)

      // 7. Calculate overall risk score
      const riskScore = (
        volatilityScore * 0.3 +
        liquidityRisk * 0.25 +
        correlationRisk * 0.2 +
        concentration * 0.15 +
        (maxDrawdown > 0 ? Math.min(maxDrawdown / 20 * 100, 100) : 0) * 0.1
      )

      // 8. Calculate exposure percentage (vs ideal portfolio size)
      const idealPortfolioSize = totalValue * 1.2 // Assume 20% cash buffer is ideal
      const exposurePercentage = totalValue > 0 ? Math.min((totalValue / idealPortfolioSize) * 100, 100) : 0

      const metrics: PortfolioRiskMetrics = {
        totalValue,
        totalValueUsd,
        exposurePercentage,
        riskScore,
        maxDrawdown,
        concentration,
        correlationRisk,
        liquidityRisk,
        volatilityScore
      }

      // Cache results for 1 minute
      await RedisService.setJSON(`portfolio_risk:${userId}`, metrics, 60)

      logRisk('Portfolio risk calculated', userId, metrics)

      return metrics

    } catch (error) {
      logRisk('Portfolio risk calculation failed', userId, {
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      throw new AppError('Failed to calculate portfolio risk', 500, 'RISK_CALCULATION_FAILED')
    }
  }

  /**
   * Check if a new position would violate risk limits
   */
  public async validateNewPosition(
    userId: string,
    tokenAddress: string,
    positionSize: number,
    currentPortfolioValue: number
  ): Promise<{ allowed: boolean; violations: string[]; warnings: string[] }> {
    try {
      const violations: string[] = []
      const warnings: string[] = []

      // 1. Get user risk parameters
      const userRiskParams = await this.getUserRiskParameters(userId)
      const riskLimits = { ...this.defaultRiskLimits, ...userRiskParams }

      // 2. Check position size limit
      const positionPercentage = (positionSize / currentPortfolioValue) * 100
      if (positionPercentage > riskLimits.maxPositionSize) {
        violations.push(`Position size (${positionPercentage.toFixed(1)}%) exceeds maximum allowed (${riskLimits.maxPositionSize}%)`)
      }

      // 3. Check concentration limits
      const currentTokenExposure = await this.getTokenExposure(userId, tokenAddress)
      const newTokenExposure = currentTokenExposure + positionPercentage
      if (newTokenExposure > riskLimits.maxConcentration) {
        violations.push(`Total token exposure (${newTokenExposure.toFixed(1)}%) would exceed concentration limit (${riskLimits.maxConcentration}%)`)
      }

      // 4. Check portfolio exposure
      const portfolioRisk = await this.calculatePortfolioRisk(userId)
      if (portfolioRisk.exposurePercentage > 90) {
        warnings.push('Portfolio exposure is very high')
      }

      // 5. Check daily loss limits (simplified)
      const dailyPnl = await this.getDailyPnL(userId)
      if (dailyPnl < -riskLimits.maxDailyLoss) {
        warnings.push('Daily loss limit has been reached')
      }

      // 6. Check volatility concerns
      const volatility = await this.calculateTokenVolatility(tokenAddress)
      if (volatility > riskLimits.volatilityThreshold) {
        warnings.push(`Token has high volatility (${volatility.toFixed(1)}%)`)
      }

      return {
        allowed: violations.length === 0,
        violations,
        warnings
      }

    } catch (error) {
      logRisk('Position validation failed', userId, {
        tokenAddress,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      
      return {
        allowed: false,
        violations: ['Risk validation failed - unable to verify safety'],
        warnings: []
      }
    }
  }

  /**
   * Get base position size for risk level
   */
  private getBaseSizeForRiskLevel(riskLevel: RiskLevel, portfolioValue: number): number {
    const percentages = {
      [RiskLevel.LOW]: 0.5, // 0.5% of portfolio
      [RiskLevel.MEDIUM]: 2.0, // 2% of portfolio
      [RiskLevel.HIGH]: 5.0, // 5% of portfolio
      [RiskLevel.EXTREME]: 10.0 // 10% of portfolio
    }

    return portfolioValue * (percentages[riskLevel] / 100)
  }

  /**
   * Calculate token volatility
   */
  private async calculateTokenVolatility(tokenAddress: string): Promise<number> {
    try {
      // Get historical price data
      const priceHistory = await DatabaseService.client.priceHistory.findMany({
        where: {
          tokenAddress,
          timestamp: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
          }
        },
        orderBy: { timestamp: 'asc' }
      })

      if (priceHistory.length < 2) {
        return 50 // Default volatility for new tokens
      }

      // Calculate price changes
      const priceChanges: number[] = []
      for (let i = 1; i < priceHistory.length; i++) {
        const currentPrice = parseFloat(priceHistory[i].price.toString())
        const previousPrice = parseFloat(priceHistory[i - 1].price.toString())
        const change = ((currentPrice - previousPrice) / previousPrice) * 100
        priceChanges.push(Math.abs(change))
      }

      // Calculate average volatility
      const avgVolatility = priceChanges.reduce((sum, change) => sum + change, 0) / priceChanges.length

      return avgVolatility

    } catch (error) {
      logRisk('Volatility calculation failed', undefined, {
        tokenAddress,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      return 50 // Default volatility
    }
  }

  /**
   * Calculate liquidity risk score
   */
  private async calculateLiquidityRisk(tokenAddress: string, marketData: TokenMarketData): Promise<number> {
    const liquidity = marketData.liquidity || 0
    const volume24h = marketData.volume24h || 0

    // Risk score based on liquidity and volume
    let riskScore = 0

    if (liquidity < 10000) riskScore += 40
    else if (liquidity < 50000) riskScore += 30
    else if (liquidity < 100000) riskScore += 20
    else if (liquidity < 500000) riskScore += 10

    if (volume24h < 1000) riskScore += 30
    else if (volume24h < 10000) riskScore += 20
    else if (volume24h < 50000) riskScore += 10

    return Math.min(riskScore, 100)
  }

  /**
   * Calculate concentration risk for a token
   */
  private async calculateConcentrationRisk(userId: string, tokenAddress: string): Promise<number> {
    try {
      const positions = await DatabaseService.client.position.findMany({
        where: {
          userId,
          status: PositionStatus.ACTIVE
        }
      })

      if (positions.length === 0) return 0

      // Calculate total portfolio value and token exposure
      let totalValue = 0
      let tokenValue = 0

      for (const position of positions) {
        const positionValue = parseFloat(position.quantity.toString()) * parseFloat(position.currentPrice.toString())
        totalValue += positionValue

        if (position.tokenAddress === tokenAddress) {
          tokenValue += positionValue
        }
      }

      const concentration = totalValue > 0 ? (tokenValue / totalValue) * 100 : 0
      
      // Risk score based on concentration
      if (concentration > 50) return 100
      if (concentration > 30) return 80
      if (concentration > 20) return 60
      if (concentration > 10) return 40
      if (concentration > 5) return 20
      return 0

    } catch (error) {
      return 50 // Default moderate risk
    }
  }

  /**
   * Calculate time-based risk (holding period risk)
   */
  private calculateTimeRisk(entryTimestamp: Date): number {
    const holdingTime = Date.now() - entryTimestamp.getTime()
    const hoursHeld = holdingTime / (1000 * 60 * 60)

    // Risk increases with holding time for volatile assets
    if (hoursHeld > 168) return 80 // > 1 week
    if (hoursHeld > 72) return 60   // > 3 days
    if (hoursHeld > 24) return 40   // > 1 day
    if (hoursHeld > 4) return 20    // > 4 hours
    return 10 // Fresh position
  }

  /**
   * Get user's risk parameters
   */
  private async getUserRiskParameters(userId: string): Promise<RiskParameters> {
    try {
      const userPrefs = await DatabaseService.client.userPreferences.findUnique({
        where: { userId }
      })

      return {
        maxPositionSize: userPrefs?.maxPositionSize || this.defaultRiskLimits.maxPositionSize,
        maxDailyLoss: this.defaultRiskLimits.maxDailyLoss,
        maxDrawdown: this.defaultRiskLimits.maxDrawdown,
        correlationLimit: this.defaultRiskLimits.maxCorrelatedExposure / 100
      }
    } catch (error) {
      return {
        maxPositionSize: this.defaultRiskLimits.maxPositionSize,
        maxDailyLoss: this.defaultRiskLimits.maxDailyLoss,
        maxDrawdown: this.defaultRiskLimits.maxDrawdown,
        correlationLimit: this.defaultRiskLimits.maxCorrelatedExposure / 100
      }
    }
  }

  /**
   * Get current portfolio exposure
   */
  private async getCurrentPortfolioExposure(userId: string): Promise<{ exposurePercentage: number; totalValue: number }> {
    try {
      const cached = await RedisService.getJSON<{ exposurePercentage: number; totalValue: number }>(`portfolio_exposure:${userId}`)
      if (cached) return cached

      const portfolioRisk = await this.calculatePortfolioRisk(userId)
      const result = {
        exposurePercentage: portfolioRisk.exposurePercentage,
        totalValue: portfolioRisk.totalValue
      }

      await RedisService.setJSON(`portfolio_exposure:${userId}`, result, 30)
      return result
    } catch (error) {
      return { exposurePercentage: 0, totalValue: 0 }
    }
  }

  /**
   * Get token exposure percentage
   */
  private async getTokenExposure(userId: string, tokenAddress: string): Promise<number> {
    try {
      const positions = await DatabaseService.client.position.findMany({
        where: {
          userId,
          tokenAddress,
          status: PositionStatus.ACTIVE
        }
      })

      const totalValue = await this.getCurrentPortfolioExposure(userId)
      let tokenValue = 0

      for (const position of positions) {
        tokenValue += parseFloat(position.quantity.toString()) * parseFloat(position.currentPrice.toString())
      }

      return totalValue.totalValue > 0 ? (tokenValue / totalValue.totalValue) * 100 : 0
    } catch (error) {
      return 0
    }
  }

  /**
   * Calculate daily P&L
   */
  private async getDailyPnL(userId: string): Promise<number> {
    try {
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      const transactions = await DatabaseService.client.transaction.findMany({
        where: {
          userId,
          timestamp: { gte: today }
        }
      })

      // Simplified calculation - would need more sophisticated logic
      let totalPnl = 0
      for (const tx of transactions) {
        if (tx.type === 'SELL') {
          // Add profit/loss from sales
          totalPnl += parseFloat(tx.amountOut.toString()) - parseFloat(tx.amountIn.toString())
        }
      }

      return totalPnl
    } catch (error) {
      return 0
    }
  }

  /**
   * Calculate portfolio volatility
   */
  private async calculatePortfolioVolatility(
    positions: any[], 
    marketDataMap: Map<string, TokenMarketData>
  ): Promise<number> {
    if (positions.length === 0) return 0

    let weightedVolatility = 0
    let totalValue = 0

    for (const position of positions) {
      const marketData = marketDataMap.get(position.tokenAddress)
      if (marketData) {
        const positionValue = parseFloat(position.quantity.toString()) * marketData.price
        const volatility = await this.calculateTokenVolatility(position.tokenAddress)
        
        weightedVolatility += volatility * positionValue
        totalValue += positionValue
      }
    }

    return totalValue > 0 ? weightedVolatility / totalValue : 0
  }

  /**
   * Calculate portfolio liquidity risk
   */
  private async calculatePortfolioLiquidityRisk(
    positions: any[], 
    marketDataMap: Map<string, TokenMarketData>
  ): Promise<number> {
    if (positions.length === 0) return 0

    let weightedRisk = 0
    let totalValue = 0

    for (const position of positions) {
      const marketData = marketDataMap.get(position.tokenAddress)
      if (marketData) {
        const positionValue = parseFloat(position.quantity.toString()) * marketData.price
        const liquidityRisk = await this.calculateLiquidityRisk(position.tokenAddress, marketData)
        
        weightedRisk += liquidityRisk * positionValue
        totalValue += positionValue
      }
    }

    return totalValue > 0 ? weightedRisk / totalValue : 0
  }

  /**
   * Calculate correlation risk (simplified)
   */
  private async calculateCorrelationRisk(positions: any[]): Promise<number> {
    // Simplified correlation calculation
    // In production, you'd analyze price correlations between assets
    const uniqueTokens = new Set(positions.map(p => p.tokenAddress))
    const totalPositions = positions.length
    
    if (totalPositions <= uniqueTokens.size) return 0 // No overlapping positions
    
    const overlapRatio = (totalPositions - uniqueTokens.size) / totalPositions
    return Math.min(overlapRatio * 100, 100)
  }

  /**
   * Calculate maximum drawdown
   */
  private async calculateMaxDrawdown(userId: string): Promise<number> {
    try {
      // Get portfolio value history for the last 30 days
      // This is simplified - you'd need a proper portfolio value tracking system
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      
      const transactions = await DatabaseService.client.transaction.findMany({
        where: {
          userId,
          timestamp: { gte: thirtyDaysAgo }
        },
        orderBy: { timestamp: 'asc' }
      })

      if (transactions.length === 0) return 0

      // Calculate daily portfolio values (simplified)
      const dailyValues: number[] = []
      let runningValue = 10000 // Assume starting value

      for (const tx of transactions) {
        if (tx.type === 'BUY') {
          runningValue -= parseFloat(tx.amountIn.toString())
        } else if (tx.type === 'SELL') {
          runningValue += parseFloat(tx.amountOut.toString())
        }
        dailyValues.push(runningValue)
      }

      // Calculate max drawdown
      let peak = dailyValues[0]
      let maxDrawdown = 0

      for (const value of dailyValues) {
        if (value > peak) {
          peak = value
        } else {
          const drawdown = ((peak - value) / peak) * 100
          maxDrawdown = Math.max(maxDrawdown, drawdown)
        }
      }

      return maxDrawdown

    } catch (error) {
      return 0
    }
  }

  /**
   * Health check for risk service
   */
  public async healthCheck(): Promise<boolean> {
    try {
      // Test database connectivity
      await DatabaseService.client.user.findFirst()
      
      // Test Redis connectivity
      await RedisService.mainClient.ping()

      return true
    } catch (error) {
      logger.error('Risk service health check failed:', error)
      return false
    }
  }
}

// Export singleton instance
export const RiskService = RiskServiceClass.getInstance()