import { DatabaseService } from '@/services/database'
import { RedisService } from '@/services/redis'
import type { TradingPreset } from '@memetrader-pro/shared'
import { PresetType } from '@memetrader-pro/shared'

/**
 * Get trading presets (implementation for TradingService)
 */
export async function getTradingPresets(): Promise<TradingPreset[]> {
  try {
    // Try cache first
    const cached = await RedisService.getJSON<TradingPreset[]>('trading:presets')
    if (cached) {
      return cached
    }

    // Get from database
    const presets = await DatabaseService.client.tradingPreset.findMany({
      orderBy: { name: 'asc' }
    })

    const tradingPresets: TradingPreset[] = presets.map(preset => ({
      id: preset.id,
      name: preset.name,
      priorityFee: parseFloat(preset.priorityFee.toString()),
      slippageLimit: preset.slippageLimit,
      mevProtectionLevel: preset.mevProtectionLevel,
      brideAmount: preset.brideAmount ? parseFloat(preset.brideAmount.toString()) : undefined,
      locked: preset.locked,
      buySettings: preset.buySettings as any,
      sellSettings: preset.sellSettings as any
    }))

    // Cache for 5 minutes
    await RedisService.setJSON('trading:presets', tradingPresets, 300)

    return tradingPresets
  } catch (error) {
    throw new Error(`Failed to get trading presets: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Update trading preset
 */
export async function updateTradingPreset(id: string, presetData: Partial<TradingPreset>): Promise<void> {
  try {
    await DatabaseService.client.tradingPreset.update({
      where: { id },
      data: {
        priorityFee: presetData.priorityFee,
        slippageLimit: presetData.slippageLimit,
        mevProtectionLevel: presetData.mevProtectionLevel,
        brideAmount: presetData.brideAmount,
        buySettings: presetData.buySettings,
        sellSettings: presetData.sellSettings,
        updatedAt: new Date()
      }
    })

    // Clear cache
    await RedisService.del('trading:presets')
  } catch (error) {
    throw new Error(`Failed to update trading preset: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Get user strategies (implementation for StrategyService)
 */
export async function getUserStrategies(userId: string, options: { activeOnly?: boolean } = {}) {
  try {
    const where: any = { userId }
    if (options.activeOnly) {
      where.executionState = 'ACTIVE'
    }

    const strategies = await DatabaseService.client.exitStrategy.findMany({
      where,
      include: {
        positions: {
          select: {
            id: true,
            tokenSymbol: true,
            status: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    return strategies
  } catch (error) {
    throw new Error(`Failed to get user strategies: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Get custom strategies
 */
export async function getCustomStrategies(userId: string) {
  try {
    const customStrategies = await DatabaseService.client.customStrategy.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' }
    })

    return customStrategies
  } catch (error) {
    throw new Error(`Failed to get custom strategies: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}