import { Connection, PublicKey, Transaction, VersionedTransaction, Keypair } from '@solana/web3.js'
import bs58 from 'bs58'
import { config } from '@/config/environment'
import { logger, logTrading } from '@/utils/logger'
import { DatabaseService } from '@/services/database'
import { RedisService } from '@/services/redis'
import { PositionMonitorService } from '@/services/positionMonitor'
import { HeliusWebSocketService } from '@/services/heliusWebSocket'
import { HeliusRPC, getConnection, rpcRequest } from '@/services/heliusRPC'
import { managedJupiterService } from '@/services/managedJupiterService'
import { managedHeliusService } from '@/services/managedHeliusService'
import { rateLimitManager } from '@/services/rateLimitManager'
import { QuoteValidationService } from '@/services/quoteValidationService'
import { SlippageProtectionService } from '@/services/slippageProtectionService'
import { TransactionConfirmationService } from '@/services/transactionConfirmationService'
import { MEVProtectionService } from '@/services/mevProtectionService'
import { WalletValidationService } from '@/services/walletValidationService'
import { TransactionRecordingService } from '@/services/transactionRecordingService'
import { heliusLaserStreamService } from '@/services/heliusLaserStreamService'
import { AppError } from '@/middleware/errorHandler'
import type { 
  TradeParams, 
  Quote, 
  TradeResult, 
  SimulationParams, 
  SimulationResult,
  TransactionFees,
  TradingPreset,
  ExecutionResult
} from '@memetrader-pro/shared'
import { PresetType, MEVProtectionLevel } from '@memetrader-pro/shared'



class TradingServiceClass {
  private static instance: TradingServiceClass
  private connection: Connection
  private walletKeypair: Keypair | null = null

  private constructor() {
    // Use Helius RPC with failover for better performance and reliability
    this.connection = getConnection()
    this.initializeWallet()
    this.initializeLaserStream()
  }

  /**
   * Initialize LaserStream for real-time monitoring
   */
  private async initializeLaserStream(): Promise<void> {
    try {
      // Connect to LaserStream
      await heliusLaserStreamService.connect()
      
      // Set up event handlers for real-time updates
      heliusLaserStreamService.on('transactionUpdate', (update) => {
        // Broadcast transaction updates to clients
        RedisService.publishJSON('realtime_transaction_update', {
          signature: update.signature,
          status: update.confirmationStatus,
          slot: update.slot,
          timestamp: Date.now()
        }).catch(error => {
          logger.error('Failed to broadcast transaction update:', error)
        })
      })
      
      heliusLaserStreamService.on('error', (error) => {
        logger.error('LaserStream error:', error)
      })
      
      heliusLaserStreamService.on('disconnected', (event) => {
        logger.warn('LaserStream disconnected:', event)
      })
      
      logger.info('LaserStream initialized successfully')
    } catch (error) {
      logger.error('Failed to initialize LaserStream:', error)
    }
  }

  /**
   * Initialize wallet keypair from environment
   */
  private initializeWallet(): void {
    try {
      const privateKey = config.wallet.privateKey
      if (privateKey) {
        const secretKey = bs58.decode(privateKey)
        this.walletKeypair = Keypair.fromSecretKey(secretKey)
        
        // Validate that the public key matches the configured address
        const derivedAddress = this.walletKeypair.publicKey.toBase58()
        if (derivedAddress !== config.wallet.address) {
          throw new Error(`Wallet private key does not match configured address. Expected: ${config.wallet.address}, Got: ${derivedAddress}`)
        }
        
        logger.info('Trading wallet initialized', { 
          publicKey: derivedAddress
        })
      } else {
        throw new Error('WALLET_PRIVATE_KEY is required for live trading')
      }
    } catch (error) {
      logger.error('Failed to initialize trading wallet:', error)
      throw error
    }
  }

  public static getInstance(): TradingServiceClass {
    if (!TradingServiceClass.instance) {
      TradingServiceClass.instance = new TradingServiceClass()
    }
    return TradingServiceClass.instance
  }

  /**
   * Get validated quote with intelligent retry logic
   */
  public async getValidatedQuoteWithRetry(params: {
    inputMint: string
    outputMint: string
    amount: number
    slippageBps?: number
    userPublicKey?: string
    maxRetries?: number
  }): Promise<Quote & { validation?: any; retryAttempts?: number }> {
    const { maxRetries = 3 } = params
    let lastError: any
    let retryAttempts = 0

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      retryAttempts = attempt + 1
      
      try {
        logTrading('Attempting quote with validation', undefined, {
          attempt: retryAttempts,
          maxRetries,
          slippageBps: params.slippageBps
        })

        const quote = await this.getQuote(params)
        
        // If we get here, validation passed or was skipped
        return { ...quote, retryAttempts }
        
      } catch (error) {
        lastError = error
        
        if (error instanceof AppError && error.code === 'QUOTE_QUALITY_LOW' && attempt < maxRetries - 1) {
          // Try with adjusted parameters for next attempt
          const adjustedParams = this.adjustQuoteParameters(params, attempt + 1)
          
          logTrading('Quote validation failed, retrying with adjusted parameters', undefined, {
            attempt: retryAttempts,
            adjustedSlippage: adjustedParams.slippageBps,
            originalSlippage: params.slippageBps
          })
          
          // Update params for next iteration
          Object.assign(params, adjustedParams)
          
          // Wait before retry with exponential backoff
          const delay = Math.min(1000 * Math.pow(2, attempt), 5000) // Max 5s delay
          await this.sleep(delay)
          
          continue
        }
        
        // For validation failures or other errors, don't retry
        throw error
      }
    }

    throw lastError
  }

  /**
   * Adjust quote parameters for retry attempts
   */
  private adjustQuoteParameters(originalParams: {
    inputMint: string
    outputMint: string
    amount: number
    slippageBps?: number
    userPublicKey?: string
  }, attempt: number): typeof originalParams {
    const adjustedParams = { ...originalParams }
    
    // Increase slippage tolerance progressively
    const baseSlippage = originalParams.slippageBps || 50
    const slippageIncrease = attempt * 25 // Increase by 0.25% per attempt
    adjustedParams.slippageBps = Math.min(baseSlippage + slippageIncrease, 500) // Max 5%
    
    return adjustedParams
  }

  /**
   * Get validated quote from Jupiter API using enhanced service
   */
  public async getQuote(params: {
    inputMint: string
    outputMint: string
    amount: number
    slippageBps?: number
    userPublicKey?: string
    skipValidation?: boolean
  }): Promise<Quote & { validation?: any }> {
    try {
      const { inputMint, outputMint, amount, slippageBps = 50, userPublicKey, skipValidation = false } = params

      logTrading('Requesting Jupiter quote', undefined, {
        inputMint,
        outputMint,
        amount,
        slippageBps
      })

      const quoteTimestamp = Date.now()

      // Use managed Jupiter service with rate limiting, circuit breakers, and intelligent routing
      const quote = await managedJupiterService.getQuote({
        inputMint,
        outputMint,
        amount: amount.toString(),
        slippageBps,
        onlyDirectRoutes: false,
        restrictIntermediateTokens: true, // Enable for more stable routes
        dynamicSlippage: true, // Enable dynamic slippage optimization
        maxAccounts: 64, // Recommended default for better routing
        minimizeSlippage: true
      }, 'high') // High priority for trading quotes

      logTrading('Jupiter quote received', undefined, {
        inAmount: quote.inAmount,
        outAmount: quote.outAmount,
        priceImpact: quote.priceImpactPct,
        totalFees: quote.fees.total
      })

      // Perform comprehensive quote validation
      if (!skipValidation) {
        const validation = await QuoteValidationService.validateQuote(quote, {
          inputMint,
          outputMint,
          amount,
          slippageBps,
          userPublicKey
        }, quoteTimestamp)

        logTrading('Quote validation completed', undefined, {
          score: validation.score,
          recommendation: validation.recommendation,
          warnings: validation.warnings.length,
          errors: validation.errors.length,
          executionProbability: validation.executionProbability
        })

        // Handle validation results
        if (validation.recommendation === 'reject') {
          throw new AppError(
            `Quote validation failed: ${validation.errors.join(', ')}`,
            400,
            'QUOTE_VALIDATION_FAILED'
          )
        }

        if (validation.recommendation === 'retry_later') {
          throw new AppError(
            `Quote quality insufficient: ${validation.warnings.join(', ')}. Try again with different parameters.`,
            400,
            'QUOTE_QUALITY_LOW'
          )
        }

        // Return quote with validation data
        return {
          ...quote,
          validation: {
            score: validation.score,
            recommendation: validation.recommendation,
            warnings: validation.warnings,
            estimatedSlippage: validation.estimatedSlippage,
            executionProbability: validation.executionProbability,
            details: validation.details
          }
        }
      }

      return quote
    } catch (error) {
      logTrading('Jupiter quote failed', undefined, { error: error instanceof Error ? error.message : 'Unknown error' })
      
      // Enhanced error handling - the error from EnhancedJupiterService is already categorized
      if (error instanceof AppError) {
        throw error
      }
      
      throw new AppError('Failed to get quote from Jupiter', 500, 'QUOTE_FAILED')
    }
  }

  /**
   * Execute trade with MEV protection
   */
  public async executeTrade(
    params: TradeParams,
    walletPublicKey: string,
    userId: string
  ): Promise<TradeResult> {
    const startTime = Date.now()
    
    try {
      logTrading('Starting trade execution', userId, params)

      // 1. Validate wallet before executing trade
      const walletValidation = await WalletValidationService.validateWallet(walletPublicKey)
      
      if (!walletValidation.isValid) {
        const issues = walletValidation.issues.map(i => i.message).join(', ')
        throw new AppError(
          `Wallet validation failed: ${issues}`,
          400,
          'WALLET_VALIDATION_FAILED'
        )
      }

      if (walletValidation.securityLevel === 'critical') {
        throw new AppError(
          'Critical wallet security issues detected - trading disabled',
          400,
          'WALLET_SECURITY_CRITICAL'
        )
      }

      if (walletValidation.balance.status === 'critical') {
        throw new AppError(
          'Insufficient wallet balance for trading',
          400,
          'INSUFFICIENT_WALLET_BALANCE'
        )
      }

      logTrading('Wallet validation completed', userId, {
        validationScore: walletValidation.validationScore,
        securityLevel: walletValidation.securityLevel,
        balanceStatus: walletValidation.balance.status,
        issues: walletValidation.issues.length
      })

      // 2. Get trading preset configuration
      const preset = await this.getTradingPreset(params.preset)
      
      // 3. Enhanced MEV analysis with advanced protection
      let mevAnalysis: any = null
      if (preset.mevProtectionLevel !== MEVProtectionLevel.NONE) {
        mevAnalysis = await MEVProtectionService.analyzeMEVRisk({
          inputMint: params.tokenIn,
          outputMint: params.tokenOut,
          amount: params.amount,
          route: [], // Will be populated from quote
          userPublicKey: walletPublicKey,
          urgency: 'medium'
        })
        
        logTrading('Enhanced MEV analysis completed', userId, {
          riskLevel: mevAnalysis.riskLevel,
          riskScore: mevAnalysis.riskScore,
          threatsDetected: mevAnalysis.detectedThreats.length,
          recommendedFee: mevAnalysis.recommendedFee,
          protectionStrategy: mevAnalysis.protectionStrategy,
          confidence: mevAnalysis.confidence
        })

        // Handle critical MEV risks
        if (mevAnalysis.riskLevel === 'critical') {
          throw new AppError(
            `Critical MEV risk detected: ${mevAnalysis.recommendations.join(', ')}`,
            400,
            'MEV_RISK_CRITICAL'
          )
        }

        if (mevAnalysis.riskLevel === 'high' && preset.mevProtectionLevel === MEVProtectionLevel.MAXIMUM) {
          throw new AppError(
            `High MEV risk detected: ${mevAnalysis.recommendations.join(', ')}`,
            400,
            'MEV_RISK_HIGH'
          )
        }

        // Apply protection strategy
        if (mevAnalysis.protectionStrategy === 'delay') {
          const delayMs = Math.random() * 2000 + 500 // 0.5-2.5s random delay
          logTrading('Applying MEV protection delay', userId, { delayMs })
          await this.sleep(delayMs)
        }
      }

      // 4. Calculate optimal slippage with advanced protection
      const slippageAnalysis = await SlippageProtectionService.calculateOptimalSlippage({
        inputMint: params.tokenIn,
        outputMint: params.tokenOut,
        amount: params.amount,
        userSlippage: params.slippage,
        userId,
        mevRisk: mevAnalysis?.riskLevel?.toLowerCase() as any,
        quoteTimestamp: Date.now()
      })

      logTrading('Slippage protection analysis completed', userId, {
        finalSlippage: slippageAnalysis.finalSlippage,
        riskLevel: slippageAnalysis.riskLevel,
        recommendation: slippageAnalysis.recommendation,
        confidence: slippageAnalysis.confidence,
        reasoning: slippageAnalysis.reasoning
      })

      // Handle slippage recommendations
      if (slippageAnalysis.recommendation === 'wait_for_better_conditions') {
        throw new AppError(
          `Market conditions unfavorable: ${slippageAnalysis.reasoning.join(', ')}. Recommended slippage: ${slippageAnalysis.finalSlippage}%`,
          400,
          'MARKET_CONDITIONS_UNFAVORABLE'
        )
      }

      if (slippageAnalysis.recommendation === 'reduce_trade_size') {
        logTrading('High slippage detected - proceeding with warning', userId, {
          recommendedAction: 'reduce_trade_size',
          slippageRisk: slippageAnalysis.riskLevel
        })
      }

      // 5. Get validated quote with optimal slippage
      const quote = await this.getValidatedQuoteWithRetry({
        inputMint: params.tokenIn,
        outputMint: params.tokenOut,
        amount: params.amount,
        slippageBps: Math.floor(slippageAnalysis.finalSlippage * 100),
        userPublicKey: walletPublicKey,
        maxRetries: 3
      })

      // 6. Additional validation check if quote has validation data
      if (quote.validation && quote.validation.recommendation === 'proceed_with_caution') {
        logTrading('Quote validation warnings', userId, {
          warnings: quote.validation.warnings,
          score: quote.validation.score,
          executionProbability: quote.validation.executionProbability
        })
      }

      // 7. Execute swap transaction
      const swapResult = await this.executeSwap(quote, walletPublicKey, preset)

      // 8. Create position in database
      const position = await this.createPosition({
        userId,
        tokenAddress: params.tokenOut,
        tokenSymbol: 'TOKEN', // TODO: Get actual symbol
        entryPrice: parseFloat(quote.outAmount) / parseFloat(quote.inAmount),
        quantity: parseFloat(quote.outAmount),
        strategyId: params.strategyId,
        presetUsed: params.preset,
        transactionHash: swapResult.transactionHash!
      })

      // 9. Record comprehensive transaction in database with enhanced metadata
      await TransactionRecordingService.recordTransaction({
        userId,
        positionId: position.id,
        hash: swapResult.transactionHash!,
        type: 'SWAP',
        status: 'CONFIRMED',
        
        // Token information
        tokenIn: params.tokenIn,
        tokenOut: params.tokenOut,
        tokenInSymbol: 'SOL', // TODO: Get actual symbols from token metadata
        tokenOutSymbol: 'TOKEN',
        
        // Amounts and pricing
        amountIn: parseFloat(quote.inAmount),
        amountOut: parseFloat(quote.outAmount),
        amountInRaw: quote.inAmount,
        amountOutRaw: quote.outAmount,
        price: parseFloat(quote.outAmount) / parseFloat(quote.inAmount),
        priceImpact: quote.priceImpactPct || 0,
        
        // Fees and costs
        fees: quote.fees,
        priorityFee: priorityFeeRecommendation.recommendedFee,
        networkFee: quote.fees?.network || 0,
        
        // Trading context
        strategyId: params.strategyId,
        presetUsed: params.preset,
        slippageUsed: slippageAnalysis.finalSlippage,
        slippageActual: slippageAnalysis.finalSlippage, // Will be updated later when actual slippage is calculated
        mevProtected: mevAnalysis !== null,
        mevRiskLevel: mevAnalysis?.riskLevel || 'none',
        
        // Execution metadata
        executionTime: Date.now() - startTime,
        retryCount: quote.retryAttempts || 0,
        
        // Market conditions
        marketConditions: {
          networkCongestion: priorityFeeRecommendation.networkConditions.networkCongestion,
          avgPriorityFee: priorityFeeRecommendation.networkConditions.avgPriorityFee,
          priorityFeeConfidence: priorityFeeRecommendation.confidence,
          priorityFeeReasoning: priorityFeeRecommendation.reasoning,
          liquidityScore: 0, // TODO: Calculate from route data
          volatilityIndex: 0, // TODO: Calculate from price data
          timestamp: Date.now()
        },
        
        // Advanced analytics
        analytics: {
          routeComplexity: quote.routePlan?.length || 1,
          liquidityUtilization: 0, // TODO: Calculate
          timing: {
            quoteTime: 0, // TODO: Track from quote request
            validationTime: 0, // TODO: Track from validation
            executionTime: Date.now() - startTime,
            confirmationTime: 0 // Will be updated when confirmation completes
          },
          performance: {
            expectedSlippage: slippageAnalysis.finalSlippage,
            actualSlippage: slippageAnalysis.finalSlippage, // Will be updated
            priceDeviation: quote.priceImpactPct || 0
          }
        }
      })

      // 10. Add position to monitoring service
      if (params.strategyId && position) {
        const strategy = await DatabaseService.client.exitStrategy.findUnique({
          where: { id: params.strategyId }
        })
        
        if (strategy) {
          await PositionMonitorService.addPosition(position, {
            id: strategy.id,
            userId: strategy.userId,
            positionId: strategy.positionId || undefined,
            type: strategy.type,
            stopLoss: strategy.stopLoss as any,
            profitTargets: strategy.profitTargets as any,
            moonBag: strategy.moonBag as any,
            locked: strategy.locked,
            customName: strategy.customName || undefined,
            executionState: strategy.executionState,
            lastUpdate: strategy.updatedAt
          })
        }
      }

      // 11. Enhanced transaction confirmation monitoring with LaserStream
      if (swapResult.transactionHash) {
        // Subscribe to real-time transaction updates via LaserStream
        await heliusLaserStreamService.subscribeToTransactions([swapResult.transactionHash], userId)
        
        // Enhanced confirmation monitoring with real-time progress updates
        this.startEnhancedConfirmationMonitoring(swapResult.transactionHash, userId, position.id)
      }

      // 12. Publish real-time update
      await RedisService.publishJSON('trade_executed', {
        userId,
        positionId: position.id,
        transactionHash: swapResult.transactionHash,
        tokenIn: params.tokenIn,
        tokenOut: params.tokenOut,
        amount: params.amount,
        executionTime: Date.now() - startTime
      })

      const result: TradeResult = {
        success: true,
        transactionHash: swapResult.transactionHash,
        quote,
        executionTime: Date.now() - startTime
      }

      logTrading('Trade execution completed', userId, {
        hash: result.transactionHash,
        executionTime: result.executionTime
      })

      return result

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      logTrading('Trade execution failed', userId, { error: errorMessage })

      return {
        success: false,
        error: errorMessage,
        quote: null as any, // We'll handle this better in error cases
        executionTime: Date.now() - startTime
      }
    }
  }

  /**
   * Simulate trade execution
   */
  public async simulateTrade(params: SimulationParams): Promise<SimulationResult> {
    try {
      logTrading('Starting trade simulation', undefined, params)

      const preset = await this.getTradingPreset(params.preset)
      
      const quote = await this.getQuote({
        inputMint: params.tokenIn,
        outputMint: params.tokenOut,
        amount: params.amount,
        slippageBps: Math.floor(preset.slippageLimit * 100)
      })

      const warnings: string[] = []

      // Check for warnings
      if (quote.priceImpactPct > 2) {
        warnings.push('High price impact detected')
      }

      if (quote.routePlan.length > 3) {
        warnings.push('Complex routing may increase failure risk')
      }

      const result: SimulationResult = {
        success: true,
        estimatedOutput: parseFloat(quote.outAmount),
        priceImpact: quote.priceImpactPct,
        fees: quote.fees,
        route: quote.routePlan,
        warnings
      }

      logTrading('Trade simulation completed', undefined, {
        estimatedOutput: result.estimatedOutput,
        priceImpact: result.priceImpact
      })

      return result

    } catch (error) {
      logTrading('Trade simulation failed', undefined, { error: error instanceof Error ? error.message : 'Unknown error' })
      throw new AppError('Trade simulation failed', 500, 'SIMULATION_FAILED')
    }
  }


  /**
   * Execute swap transaction using Helius RPC
   */
  private async executeSwap(
    quote: Quote,
    walletPublicKey: string,
    preset: TradingPreset
  ): Promise<ExecutionResult> {
    try {
      // Get intelligent priority fee recommendation from managed Helius service
      const accountKeys = quote.routePlan?.map((route: any) => route.swapInfo?.ammKey).filter(Boolean).slice(0, 5) || ['So11111111111111111111111111111111111111112']
      const priorityFeeEstimate = await managedHeliusService.getPriorityFeeEstimate({
        accountKeys,
        options: {
          priorityLevel: preset.mevProtectionLevel === 'MAXIMUM' ? 'VeryHigh' : 'High',
          includeAllPriorityFeeLevels: true,
          recommended: true
        }
      }, 'high')
      
      // Create priority fee recommendation object for compatibility
      const priorityFeeRecommendation = {
        recommendedFee: priorityFeeEstimate.priorityFeeEstimate,
        priorityLevel: preset.mevProtectionLevel === 'MAXIMUM' ? 'VeryHigh' : 'High',
        networkConditions: {
          networkCongestion: priorityFeeEstimate.priorityFeeEstimate > 10000 ? 'high' : 'medium',
          avgPriorityFee: priorityFeeEstimate.priorityFeeEstimate
        },
        confidence: 0.8,
        reasoning: ['Smart priority fee calculated using Helius API']
      }

      logTrading('Smart priority fee calculated', userId, {
        recommendedFee: priorityFeeRecommendation.recommendedFee,
        priorityLevel: priorityFeeRecommendation.priorityLevel,
        networkCongestion: priorityFeeRecommendation.networkConditions.networkCongestion,
        confidence: priorityFeeRecommendation.confidence,
        reasoning: priorityFeeRecommendation.reasoning
      })

      // Get swap transaction from managed Jupiter service with intelligent priority fees
      const swapData = await managedJupiterService.getSwapTransaction({
        quoteResponse: quote,
        userPublicKey: walletPublicKey,
        wrapAndUnwrapSol: true,
        dynamicComputeUnitLimit: true,
        dynamicSlippage: true, // Enable dynamic slippage for better execution
        prioritizationFeeLamports: {
          priorityLevelWithMaxLamports: {
            maxLamports: priorityFeeRecommendation.recommendedFee,
            global: false, // Use local fee market for more accurate estimation
            priorityLevel: priorityFeeRecommendation.priorityLevel.toLowerCase() as any
          }
        },
        useSharedAccounts: true
      }, 'critical') // Critical priority for swap transactions

      if (!swapData.swapTransaction) {
        throw new Error('No swap transaction returned from Jupiter')
      }

      // Wallet should always be configured in live mode
      if (!this.walletKeypair) {
        throw new Error('Trading wallet not configured - cannot execute transaction')
      }

      // Deserialize the transaction
      const transactionBuffer = Buffer.from(swapData.swapTransaction, 'base64')
      const transaction = VersionedTransaction.deserialize(transactionBuffer)

      // Sign the transaction
      transaction.sign([this.walletKeypair])

      // Get latest blockhash for transaction confirmation
      const latestBlockhash = await this.connection.getLatestBlockhash('confirmed')

      // Send the transaction to Helius RPC
      const rawTransaction = transaction.serialize()
      const txid = await this.connection.sendRawTransaction(rawTransaction, {
        skipPreflight: true,
        maxRetries: 3,
        preflightCommitment: 'confirmed'
      })

      logTrading('Transaction sent to network', walletPublicKey, {
        transactionHash: txid,
        inputAmount: quote.inAmount,
        outputAmount: quote.outAmount
      })

      // Enhanced transaction confirmation with multi-level polling
      const confirmationResult = await TransactionConfirmationService.confirmTransaction(txid, {
        requireFinalized: false, // Only require 'confirmed' for faster execution
        onProgress: (progress) => {
          logTrading('Transaction confirmation progress', walletPublicKey, {
            transactionHash: txid,
            status: progress.status,
            level: progress.confirmationLevel,
            confirmations: progress.confirmations
          })
        },
        config: {
          commitmentLevels: {
            confirmed: {
              timeout: 45000, // 45s for trading transactions
              maxRetries: 8,
              pollInterval: 1500
            }
          },
          networkConditions: {
            adjustTimeouts: true,
            congestionMultiplier: 1.8,
            lowLatencyBonus: 0.7
          }
        }
      })

      if (confirmationResult.status === 'failed') {
        throw new Error(`Transaction confirmation failed: ${confirmationResult.error}`)
      }

      if (confirmationResult.status === 'timeout') {
        throw new Error(`Transaction confirmation timeout after ${confirmationResult.attempts.confirmed} attempts`)
      }

      logTrading('Transaction confirmed with enhanced monitoring', walletPublicKey, {
        transactionHash: txid,
        confirmationLevel: confirmationResult.confirmationLevel,
        slot: confirmationResult.slot,
        confirmations: confirmationResult.confirmations,
        totalTime: Date.now() - confirmationResult.timeline.submitted,
        networkCongestion: confirmationResult.networkMetrics.networkCongestion
      })

      return {
        success: true,
        transactionHash: txid,
        amountExecuted: parseFloat(quote.outAmount),
        priceExecuted: parseFloat(quote.outAmount) / parseFloat(quote.inAmount),
        fees: quote.fees
      }

    } catch (error) {
      logTrading('Swap execution failed', walletPublicKey, { 
        error: error instanceof Error ? error.message : 'Unknown error' 
      })
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        amountExecuted: 0,
        priceExecuted: 0,
        fees: quote.fees
      }
    }
  }

  /**
   * Get trading preset configuration
   */
  private async getTradingPreset(presetType: PresetType): Promise<TradingPreset> {
    try {
      // Try to get from cache first
      const cacheKey = `preset:${presetType}`
      const cached = await RedisService.getJSON<TradingPreset>(cacheKey)
      
      if (cached) {
        return cached
      }

      // Get from database
      const preset = await DatabaseService.client.tradingPreset.findUnique({
        where: { name: presetType }
      })

      if (!preset) {
        throw new AppError(`Trading preset ${presetType} not found`, 404, 'PRESET_NOT_FOUND')
      }

      const tradingPreset: TradingPreset = {
        id: preset.id,
        name: preset.name,
        priorityFee: parseFloat(preset.priorityFee.toString()),
        slippageLimit: preset.slippageLimit,
        mevProtectionLevel: preset.mevProtectionLevel,
        brideAmount: preset.brideAmount ? parseFloat(preset.brideAmount.toString()) : undefined,
        locked: preset.locked,
        buySettings: preset.buySettings as any,
        sellSettings: preset.sellSettings as any
      }

      // Cache for 5 minutes
      await RedisService.setJSON(cacheKey, tradingPreset, 300)

      return tradingPreset

    } catch (error) {
      logTrading('Failed to get trading preset', undefined, { preset: presetType, error: error instanceof Error ? error.message : 'Unknown error' })
      throw error
    }
  }


  /**
   * Create position in database
   */
  private async createPosition(params: {
    userId: string
    tokenAddress: string
    tokenSymbol: string
    entryPrice: number
    quantity: number
    strategyId?: string
    presetUsed: PresetType
    transactionHash: string
  }): Promise<any> {
    try {
      const position = await DatabaseService.client.position.create({
        data: {
          userId: params.userId,
          tokenAddress: params.tokenAddress,
          tokenSymbol: params.tokenSymbol,
          tokenName: params.tokenSymbol, // TODO: Get actual name
          entryPrice: params.entryPrice,
          currentPrice: params.entryPrice,
          quantity: params.quantity,
          entryTimestamp: new Date(),
          strategyId: params.strategyId,
          presetUsed: params.presetUsed,
          riskLevel: 'MEDIUM', // TODO: Calculate based on position size
          status: 'ACTIVE',
          pnl: 0,
          pnlPercent: 0
        }
      })

      logTrading('Position created', params.userId, {
        positionId: position.id,
        tokenSymbol: params.tokenSymbol,
        quantity: params.quantity,
        entryPrice: params.entryPrice
      })

      return position
    } catch (error) {
      logger.error('Failed to create position:', error)
      throw error
    }
  }



  /**
   * Get trading presets
   */
  public async getTradingPresets(): Promise<TradingPreset[]> {
    const { getTradingPresets } = await import('@/services/tradingHelpers')
    return getTradingPresets()
  }

  /**
   * Update trading preset
   */
  public async updateTradingPreset(id: string, presetData: Partial<TradingPreset>): Promise<void> {
    const { updateTradingPreset } = await import('@/services/tradingHelpers')
    return updateTradingPreset(id, presetData)
  }

  /**
   * Health check for trading service
   */
  public async healthCheck(): Promise<boolean> {
    try {
      // Test Jupiter API connectivity using managed service
      const jupiterHealthy = await managedJupiterService.healthCheck()
      
      if (!jupiterHealthy) {
        logger.warn('Jupiter service is unhealthy')
        return false
      }

      // Test quote validation service
      const validationHealthy = await QuoteValidationService.healthCheck()
      
      if (!validationHealthy) {
        logger.warn('Quote validation service is unhealthy')
        return false
      }

      // Test slippage protection service
      const slippageHealthy = await SlippageProtectionService.healthCheck()
      
      if (!slippageHealthy) {
        logger.warn('Slippage protection service is unhealthy')
        return false
      }

      // Test transaction confirmation service
      const confirmationHealthy = await TransactionConfirmationService.healthCheck()
      
      if (!confirmationHealthy) {
        logger.warn('Transaction confirmation service is unhealthy')
        return false
      }

      // Test MEV protection service
      const mevHealthy = await MEVProtectionService.healthCheck()
      
      if (!mevHealthy) {
        logger.warn('MEV protection service is unhealthy')
        return false
      }

      // Test wallet validation service
      const walletHealthy = await WalletValidationService.healthCheck()
      
      if (!walletHealthy) {
        logger.warn('Wallet validation service is unhealthy')
        return false
      }

      // Test Helius managed service
      const heliusHealthy = await managedHeliusService.healthCheck()
      
      if (!heliusHealthy) {
        logger.warn('Helius managed service is unhealthy')
        return false
      }

      // Test rate limit manager
      const rateLimitHealthy = await rateLimitManager.healthCheck()
      
      if (!rateLimitHealthy) {
        logger.warn('Rate limit manager is unhealthy')
        return false
      }

      // Test Helius LaserStream service
      const laserStreamHealthy = await heliusLaserStreamService.healthCheck()
      
      if (!laserStreamHealthy) {
        logger.warn('Helius LaserStream service is unhealthy')
        return false
      }

      // Test Solana RPC connectivity
      await this.connection.getLatestBlockhash()

      return true
    } catch (error) {
      logger.error('Trading service health check failed:', error)
      return false
    }
  }

  /**
   * Start enhanced confirmation monitoring with LaserStream real-time updates
   */
  private startEnhancedConfirmationMonitoring(
    transactionHash: string, 
    userId: string, 
    positionId: string
  ): void {
    // Start async confirmation monitoring without blocking
    TransactionConfirmationService.confirmTransaction(transactionHash, {
      userId,
      requireFinalized: true, // Monitor to finalization for complete lifecycle
      metadata: { positionId, userId },
      onProgress: async (progress) => {
        try {
          // Publish real-time confirmation updates
          await RedisService.publishJSON('transaction_confirmation_update', {
            userId,
            positionId,
            transactionHash,
            status: progress.status,
            confirmationLevel: progress.confirmationLevel,
            confirmations: progress.confirmations,
            slot: progress.slot,
            networkCongestion: progress.networkMetrics.networkCongestion,
            estimatedFinalizationTime: progress.networkMetrics.estimatedFinalizationTime,
            attempts: progress.attempts,
            timestamp: Date.now()
          })

          logTrading('Transaction confirmation progress broadcast', userId, {
            transactionHash,
            status: progress.status,
            level: progress.confirmationLevel,
            confirmations: progress.confirmations
          })

        } catch (error) {
          logger.error('Failed to broadcast confirmation update:', error)
        }
      }
    }).then(async (finalResult) => {
      try {
        // Final confirmation update
        await RedisService.publishJSON('transaction_finalized', {
          userId,
          positionId,
          transactionHash,
          status: finalResult.status,
          confirmationLevel: finalResult.confirmationLevel,
          slot: finalResult.slot,
          blockTime: finalResult.blockTime,
          totalConfirmationTime: Date.now() - finalResult.timeline.submitted,
          networkMetrics: finalResult.networkMetrics,
          retryHistory: finalResult.retryHistory,
          timestamp: Date.now()
        })

        logTrading('Transaction fully confirmed and finalized', userId, {
          transactionHash,
          finalStatus: finalResult.status,
          totalTime: Date.now() - finalResult.timeline.submitted,
          confirmations: finalResult.confirmations
        })

      } catch (error) {
        logger.error('Failed to broadcast final confirmation:', error)
      }
    }).catch(async (error) => {
      try {
        // Broadcast confirmation failure
        await RedisService.publishJSON('transaction_confirmation_failed', {
          userId,
          positionId,
          transactionHash,
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: Date.now()
        })

        logTrading('Transaction confirmation failed', userId, {
          transactionHash,
          error: error instanceof Error ? error.message : 'Unknown error'
        })

      } catch (broadcastError) {
        logger.error('Failed to broadcast confirmation failure:', broadcastError)
      }
    })
  }

  /**
   * Utility method for delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// Export singleton instance
export const TradingService = TradingServiceClass.getInstance()