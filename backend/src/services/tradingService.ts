import { Connection, PublicKey, Transaction, VersionedTransaction, Keypair } from '@solana/web3.js'
import axios from 'axios'
import bs58 from 'bs58'
import { config } from '@/config/environment'
import { logger, logTrading } from '@/utils/logger'
import { DatabaseService } from '@/services/database'
import { RedisService } from '@/services/redis'
import { PositionMonitorService } from '@/services/positionMonitor'
import { HeliusWebSocketService } from '@/services/heliusWebSocket'
import { AppError } from '@/middleware/errorHandler'
import type { 
  TradeParams, 
  Quote, 
  TradeResult, 
  SimulationParams, 
  SimulationResult,
  TransactionFees,
  TradingPreset,
  ExecutionResult
} from '@memetrader-pro/shared'
import { PresetType, MEVProtectionLevel } from '@memetrader-pro/shared'

interface JupiterQuoteResponse {
  inputMint: string
  outputMint: string
  inAmount: string
  outAmount: string
  priceImpactPct: number
  marketInfos: any[]
  routePlan: any[]
  otherAmountThreshold: string
  swapMode: string
  slippageBps: number
  platformFee: any
  contextSlot: number
  timeTaken: number
}

interface JupiterSwapResponse {
  swapTransaction: string
  lastValidBlockHeight: number
  prioritizationFeeLamports: number
}

interface MEVAnalysis {
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
  recommendation: string
  priceImpact: number
  liquidityDepth: number
  frontrunRisk: number
  backrunRisk: number
}

class TradingServiceClass {
  private static instance: TradingServiceClass
  private connection: Connection
  private jupiterApiUrl: string
  private walletKeypair: Keypair | null = null

  private constructor() {
    // Use Helius RPC for better performance and reliability
    this.connection = new Connection(config.solana.rpcUrl, { 
      commitment: 'confirmed',
      wsEndpoint: config.solana.wsUrl
    })
    this.jupiterApiUrl = config.jupiter.apiUrl
    this.initializeWallet()
  }

  /**
   * Initialize wallet keypair from environment
   */
  private initializeWallet(): void {
    try {
      const privateKey = config.wallet.privateKey
      if (privateKey) {
        const secretKey = bs58.decode(privateKey)
        this.walletKeypair = Keypair.fromSecretKey(secretKey)
        
        // Validate that the public key matches the configured address
        const derivedAddress = this.walletKeypair.publicKey.toBase58()
        if (derivedAddress !== config.wallet.address) {
          throw new Error(`Wallet private key does not match configured address. Expected: ${config.wallet.address}, Got: ${derivedAddress}`)
        }
        
        logger.info('Trading wallet initialized', { 
          publicKey: derivedAddress
        })
      } else {
        throw new Error('WALLET_PRIVATE_KEY is required for live trading')
      }
    } catch (error) {
      logger.error('Failed to initialize trading wallet:', error)
      throw error
    }
  }

  public static getInstance(): TradingServiceClass {
    if (!TradingServiceClass.instance) {
      TradingServiceClass.instance = new TradingServiceClass()
    }
    return TradingServiceClass.instance
  }

  /**
   * Get quote from Jupiter API
   */
  public async getQuote(params: {
    inputMint: string
    outputMint: string
    amount: number
    slippageBps?: number
    userPublicKey?: string
  }): Promise<Quote> {
    try {
      const { inputMint, outputMint, amount, slippageBps = 50, userPublicKey } = params

      logTrading('Requesting Jupiter quote', undefined, {
        inputMint,
        outputMint,
        amount,
        slippageBps
      })

      const response = await axios.get(`${this.jupiterApiUrl}/quote`, {
        params: {
          inputMint,
          outputMint,
          amount: Math.floor(amount).toString(),
          slippageBps,
          swapMode: 'ExactIn',
          onlyDirectRoutes: false,
          asLegacyTransaction: false,
          userPublicKey,
          restrictIntermediateTokens: true,
          maxAccounts: 28
        },
        timeout: 5000
      })

      const jupiterQuote: JupiterQuoteResponse = response.data

      // Calculate fees
      const fees = this.calculateFees(jupiterQuote, slippageBps)

      const quote: Quote = {
        inputMint: jupiterQuote.inputMint,
        outputMint: jupiterQuote.outputMint,
        inAmount: jupiterQuote.inAmount,
        outAmount: jupiterQuote.outAmount,
        priceImpactPct: jupiterQuote.priceImpactPct,
        routePlan: jupiterQuote.routePlan,
        fees
      }

      logTrading('Jupiter quote received', undefined, {
        inAmount: quote.inAmount,
        outAmount: quote.outAmount,
        priceImpact: quote.priceImpactPct,
        totalFees: fees.total
      })

      return quote
    } catch (error) {
      logTrading('Jupiter quote failed', undefined, { error: error instanceof Error ? error.message : 'Unknown error' })
      throw new AppError('Failed to get quote from Jupiter', 500, 'QUOTE_FAILED')
    }
  }

  /**
   * Execute trade with MEV protection
   */
  public async executeTrade(
    params: TradeParams,
    walletPublicKey: string,
    userId: string
  ): Promise<TradeResult> {
    const startTime = Date.now()
    
    try {
      logTrading('Starting trade execution', userId, params)

      // 1. Get trading preset configuration
      const preset = await this.getTradingPreset(params.preset)
      
      // 2. Perform MEV analysis if protection is enabled
      let mevAnalysis: MEVAnalysis | null = null
      if (preset.mevProtectionLevel !== MEVProtectionLevel.NONE) {
        mevAnalysis = await this.analyzeMEVRisk(params)
        
        if (mevAnalysis.riskLevel === 'HIGH' && preset.mevProtectionLevel === MEVProtectionLevel.MAXIMUM) {
          throw new AppError('MEV risk too high for execution', 400, 'MEV_RISK_HIGH')
        }
      }

      // 3. Get quote with optimal slippage
      const quote = await this.getQuote({
        inputMint: params.tokenIn,
        outputMint: params.tokenOut,
        amount: params.amount,
        slippageBps: Math.floor(params.slippage * 100),
        userPublicKey: walletPublicKey
      })

      // 4. Validate price impact
      if (quote.priceImpactPct > config.trading.maxSlippageBps / 100) {
        throw new AppError('Price impact exceeds maximum threshold', 400, 'PRICE_IMPACT_HIGH')
      }

      // 5. Execute swap transaction
      const swapResult = await this.executeSwap(quote, walletPublicKey, preset)

      // 6. Create position in database
      const position = await this.createPosition({
        userId,
        tokenAddress: params.tokenOut,
        tokenSymbol: 'TOKEN', // TODO: Get actual symbol
        entryPrice: parseFloat(quote.outAmount) / parseFloat(quote.inAmount),
        quantity: parseFloat(quote.outAmount),
        strategyId: params.strategyId,
        presetUsed: params.preset,
        transactionHash: swapResult.transactionHash!
      })

      // 7. Record transaction in database
      await this.recordTransaction({
        userId,
        hash: swapResult.transactionHash!,
        type: 'SWAP',
        tokenIn: params.tokenIn,
        tokenOut: params.tokenOut,
        amountIn: parseFloat(quote.inAmount),
        amountOut: parseFloat(quote.outAmount),
        price: parseFloat(quote.outAmount) / parseFloat(quote.inAmount),
        fees: quote.fees,
        presetUsed: params.preset,
        mevProtected: mevAnalysis !== null,
        slippageUsed: params.slippage,
        strategyId: params.strategyId,
        executionTime: Date.now() - startTime,
        positionId: position.id
      })

      // 8. Add position to monitoring service
      if (params.strategyId && position) {
        const strategy = await DatabaseService.client.exitStrategy.findUnique({
          where: { id: params.strategyId }
        })
        
        if (strategy) {
          await PositionMonitorService.addPosition(position, {
            id: strategy.id,
            userId: strategy.userId,
            positionId: strategy.positionId || undefined,
            type: strategy.type,
            stopLoss: strategy.stopLoss as any,
            profitTargets: strategy.profitTargets as any,
            moonBag: strategy.moonBag as any,
            locked: strategy.locked,
            customName: strategy.customName || undefined,
            executionState: strategy.executionState,
            lastUpdate: strategy.updatedAt
          })
        }
      }

      // 9. Subscribe to transaction confirmation
      if (swapResult.transactionHash) {
        await HeliusWebSocketService.subscribeToTransaction(swapResult.transactionHash, userId)
      }

      // 10. Publish real-time update
      await RedisService.publishJSON('trade_executed', {
        userId,
        positionId: position.id,
        transactionHash: swapResult.transactionHash,
        tokenIn: params.tokenIn,
        tokenOut: params.tokenOut,
        amount: params.amount,
        executionTime: Date.now() - startTime
      })

      const result: TradeResult = {
        success: true,
        transactionHash: swapResult.transactionHash,
        quote,
        executionTime: Date.now() - startTime
      }

      logTrading('Trade execution completed', userId, {
        hash: result.transactionHash,
        executionTime: result.executionTime
      })

      return result

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      logTrading('Trade execution failed', userId, { error: errorMessage })

      return {
        success: false,
        error: errorMessage,
        quote: null as any, // We'll handle this better in error cases
        executionTime: Date.now() - startTime
      }
    }
  }

  /**
   * Simulate trade execution
   */
  public async simulateTrade(params: SimulationParams): Promise<SimulationResult> {
    try {
      logTrading('Starting trade simulation', undefined, params)

      const preset = await this.getTradingPreset(params.preset)
      
      const quote = await this.getQuote({
        inputMint: params.tokenIn,
        outputMint: params.tokenOut,
        amount: params.amount,
        slippageBps: Math.floor(preset.slippageLimit * 100)
      })

      const warnings: string[] = []

      // Check for warnings
      if (quote.priceImpactPct > 2) {
        warnings.push('High price impact detected')
      }

      if (quote.routePlan.length > 3) {
        warnings.push('Complex routing may increase failure risk')
      }

      const result: SimulationResult = {
        success: true,
        estimatedOutput: parseFloat(quote.outAmount),
        priceImpact: quote.priceImpactPct,
        fees: quote.fees,
        route: quote.routePlan,
        warnings
      }

      logTrading('Trade simulation completed', undefined, {
        estimatedOutput: result.estimatedOutput,
        priceImpact: result.priceImpact
      })

      return result

    } catch (error) {
      logTrading('Trade simulation failed', undefined, { error: error instanceof Error ? error.message : 'Unknown error' })
      throw new AppError('Trade simulation failed', 500, 'SIMULATION_FAILED')
    }
  }

  /**
   * Analyze MEV risk for a trade
   */
  private async analyzeMEVRisk(params: TradeParams): Promise<MEVAnalysis> {
    try {
      // This is a simplified MEV analysis
      // In production, you would integrate with specialized MEV protection services
      
      const liquidityData = await this.getLiquidityData(params.tokenIn, params.tokenOut)
      const volumeData = await this.getVolumeData(params.tokenIn, params.tokenOut)
      
      // Calculate risk factors
      const liquidityRisk = params.amount / liquidityData.totalLiquidity
      const volumeRisk = params.amount / volumeData.volume24h
      const sizeRisk = params.amount > 1000 ? 0.8 : params.amount > 100 ? 0.5 : 0.2

      const totalRisk = (liquidityRisk * 0.4 + volumeRisk * 0.3 + sizeRisk * 0.3)

      let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
      let recommendation: string

      if (totalRisk < 0.3) {
        riskLevel = 'LOW'
        recommendation = 'Safe to execute with standard parameters'
      } else if (totalRisk < 0.7) {
        riskLevel = 'MEDIUM'
        recommendation = 'Consider reducing trade size or increasing slippage tolerance'
      } else {
        riskLevel = 'HIGH'
        recommendation = 'High MEV risk - consider splitting trade or waiting for better conditions'
      }

      return {
        riskLevel,
        recommendation,
        priceImpact: liquidityRisk * 100,
        liquidityDepth: liquidityData.totalLiquidity,
        frontrunRisk: Math.min(totalRisk * 1.2, 1),
        backrunRisk: Math.min(totalRisk * 0.8, 1)
      }

    } catch (error) {
      logTrading('MEV analysis failed', undefined, { error: error instanceof Error ? error.message : 'Unknown error' })
      
      // Return conservative analysis on failure
      return {
        riskLevel: 'MEDIUM',
        recommendation: 'MEV analysis unavailable - proceed with caution',
        priceImpact: 5,
        liquidityDepth: 0,
        frontrunRisk: 0.5,
        backrunRisk: 0.5
      }
    }
  }

  /**
   * Execute swap transaction using Helius RPC
   */
  private async executeSwap(
    quote: Quote,
    walletPublicKey: string,
    preset: TradingPreset
  ): Promise<ExecutionResult> {
    try {
      // Get swap transaction from Jupiter
      const swapResponse = await axios.post(`${this.jupiterApiUrl}/swap`, {
        quoteResponse: quote,
        userPublicKey: walletPublicKey,
        wrapAndUnwrapSol: true,
        dynamicComputeUnitLimit: true,
        prioritizationFeeLamports: 'auto',
        useSharedAccounts: true,
        feeAccount: undefined,
        trackingAccount: undefined,
        asLegacyTransaction: false
      })

      const swapData: JupiterSwapResponse = swapResponse.data

      if (!swapData.swapTransaction) {
        throw new Error('No swap transaction returned from Jupiter')
      }

      // Wallet should always be configured in live mode
      if (!this.walletKeypair) {
        throw new Error('Trading wallet not configured - cannot execute transaction')
      }

      // Deserialize the transaction
      const transactionBuffer = Buffer.from(swapData.swapTransaction, 'base64')
      const transaction = VersionedTransaction.deserialize(transactionBuffer)

      // Sign the transaction
      transaction.sign([this.walletKeypair])

      // Get latest blockhash for transaction confirmation
      const latestBlockhash = await this.connection.getLatestBlockhash('confirmed')

      // Send the transaction to Helius RPC
      const rawTransaction = transaction.serialize()
      const txid = await this.connection.sendRawTransaction(rawTransaction, {
        skipPreflight: true,
        maxRetries: 3,
        preflightCommitment: 'confirmed'
      })

      logTrading('Transaction sent to network', walletPublicKey, {
        transactionHash: txid,
        inputAmount: quote.inAmount,
        outputAmount: quote.outAmount
      })

      // Wait for confirmation using Helius RPC
      const confirmation = await this.connection.confirmTransaction({
        signature: txid,
        blockhash: latestBlockhash.blockhash,
        lastValidBlockHeight: latestBlockhash.lastValidBlockHeight
      }, 'confirmed')

      if (confirmation.value.err) {
        throw new Error(`Transaction failed: ${JSON.stringify(confirmation.value.err)}`)
      }

      logTrading('Transaction confirmed', walletPublicKey, {
        transactionHash: txid,
        slot: confirmation.context.slot
      })

      return {
        success: true,
        transactionHash: txid,
        amountExecuted: parseFloat(quote.outAmount),
        priceExecuted: parseFloat(quote.outAmount) / parseFloat(quote.inAmount),
        fees: quote.fees
      }

    } catch (error) {
      logTrading('Swap execution failed', walletPublicKey, { 
        error: error instanceof Error ? error.message : 'Unknown error' 
      })
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        amountExecuted: 0,
        priceExecuted: 0,
        fees: quote.fees
      }
    }
  }

  /**
   * Get trading preset configuration
   */
  private async getTradingPreset(presetType: PresetType): Promise<TradingPreset> {
    try {
      // Try to get from cache first
      const cacheKey = `preset:${presetType}`
      const cached = await RedisService.getJSON<TradingPreset>(cacheKey)
      
      if (cached) {
        return cached
      }

      // Get from database
      const preset = await DatabaseService.client.tradingPreset.findUnique({
        where: { name: presetType }
      })

      if (!preset) {
        throw new AppError(`Trading preset ${presetType} not found`, 404, 'PRESET_NOT_FOUND')
      }

      const tradingPreset: TradingPreset = {
        id: preset.id,
        name: preset.name,
        priorityFee: parseFloat(preset.priorityFee.toString()),
        slippageLimit: preset.slippageLimit,
        mevProtectionLevel: preset.mevProtectionLevel,
        brideAmount: preset.brideAmount ? parseFloat(preset.brideAmount.toString()) : undefined,
        locked: preset.locked,
        buySettings: preset.buySettings as any,
        sellSettings: preset.sellSettings as any
      }

      // Cache for 5 minutes
      await RedisService.setJSON(cacheKey, tradingPreset, 300)

      return tradingPreset

    } catch (error) {
      logTrading('Failed to get trading preset', undefined, { preset: presetType, error: error instanceof Error ? error.message : 'Unknown error' })
      throw error
    }
  }

  /**
   * Calculate transaction fees
   */
  private calculateFees(quote: JupiterQuoteResponse, slippageBps: number): TransactionFees {
    const baseNetworkFee = 0.000005 // 5000 lamports base fee
    const priorityFeeEstimate = 0.0001 // Estimated priority fee
    
    // Jupiter platform fee (usually 0.1% for swaps)
    const jupiterFeeRate = 0.001
    const jupiterFee = (parseFloat(quote.inAmount) * jupiterFeeRate) / Math.pow(10, 9)
    
    return {
      jupiterFee,
      priorityFee: priorityFeeEstimate,
      networkFee: baseNetworkFee,
      total: jupiterFee + priorityFeeEstimate + baseNetworkFee
    }
  }

  /**
   * Create position in database
   */
  private async createPosition(params: {
    userId: string
    tokenAddress: string
    tokenSymbol: string
    entryPrice: number
    quantity: number
    strategyId?: string
    presetUsed: PresetType
    transactionHash: string
  }): Promise<any> {
    try {
      const position = await DatabaseService.client.position.create({
        data: {
          userId: params.userId,
          tokenAddress: params.tokenAddress,
          tokenSymbol: params.tokenSymbol,
          tokenName: params.tokenSymbol, // TODO: Get actual name
          entryPrice: params.entryPrice,
          currentPrice: params.entryPrice,
          quantity: params.quantity,
          entryTimestamp: new Date(),
          strategyId: params.strategyId,
          presetUsed: params.presetUsed,
          riskLevel: 'MEDIUM', // TODO: Calculate based on position size
          status: 'ACTIVE',
          pnl: 0,
          pnlPercent: 0
        }
      })

      logTrading('Position created', params.userId, {
        positionId: position.id,
        tokenSymbol: params.tokenSymbol,
        quantity: params.quantity,
        entryPrice: params.entryPrice
      })

      return position
    } catch (error) {
      logger.error('Failed to create position:', error)
      throw error
    }
  }

  /**
   * Record transaction in database
   */
  private async recordTransaction(params: {
    userId: string
    hash: string
    type: 'BUY' | 'SELL' | 'SWAP' | 'TRANSFER'
    tokenIn: string
    tokenOut: string
    amountIn: number
    amountOut: number
    price: number
    fees: TransactionFees
    presetUsed: PresetType
    mevProtected: boolean
    slippageUsed: number
    strategyId?: string
    executionTime: number
    positionId?: string
  }): Promise<void> {
    try {
      await DatabaseService.client.transaction.create({
        data: {
          userId: params.userId,
          positionId: params.positionId,
          hash: params.hash,
          type: params.type,
          tokenIn: params.tokenIn,
          tokenOut: params.tokenOut,
          tokenInSymbol: 'SOL', // TODO: Get actual symbol
          tokenOutSymbol: 'TOKEN', // TODO: Get actual symbol
          amountIn: params.amountIn,
          amountOut: params.amountOut,
          price: params.price,
          fees: params.fees,
          strategyId: params.strategyId,
          presetUsed: params.presetUsed,
          mevProtected: params.mevProtected,
          slippageUsed: params.slippageUsed,
          executionTime: params.executionTime,
          confirmations: 0 // Will be updated by confirmation service
        }
      })

      logTrading('Transaction recorded', params.userId, { hash: params.hash })

    } catch (error) {
      logTrading('Failed to record transaction', params.userId, { 
        hash: params.hash, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      })
      // Don't throw here - transaction succeeded even if recording failed
    }
  }

  /**
   * Get liquidity data for tokens using Jupiter API
   */
  private async getLiquidityData(tokenA: string, tokenB: string): Promise<{ totalLiquidity: number }> {
    try {
      // Use Jupiter's route discovery to estimate liquidity
      const quote = await axios.get(`${this.jupiterApiUrl}/quote`, {
        params: {
          inputMint: tokenA,
          outputMint: tokenB,
          amount: '1000000000', // 1 token worth to test liquidity
          slippageBps: 50
        },
        timeout: 5000
      })

      // Estimate liquidity based on price impact and amount
      const priceImpact = quote.data.priceImpactPct || 0
      const estimatedLiquidity = priceImpact > 0 ? (1000000000 / priceImpact) * 100 : 1000000

      return { totalLiquidity: estimatedLiquidity }
    } catch (error) {
      logger.error('Failed to get liquidity data:', error)
      // Return conservative estimate on error
      return { totalLiquidity: 100000 }
    }
  }

  /**
   * Get volume data for tokens using Helius API
   */
  private async getVolumeData(tokenA: string, tokenB: string): Promise<{ volume24h: number }> {
    try {
      // Get token prices and volume from Jupiter/Birdeye APIs
      const response = await axios.get(`https://public-api.birdeye.so/defi/tokenlist`, {
        headers: {
          'X-API-KEY': 'your-birdeye-api-key' // Should be added to config
        },
        timeout: 5000
      })

      // Find volume data for the tokens
      const tokenAData = response.data.data?.tokens?.find((t: any) => t.address === tokenA)
      const tokenBData = response.data.data?.tokens?.find((t: any) => t.address === tokenB)

      const volumeA = tokenAData?.v24hUSD || 0
      const volumeB = tokenBData?.v24hUSD || 0

      return { volume24h: Math.max(volumeA, volumeB) }
    } catch (error) {
      logger.error('Failed to get volume data:', error)
      // Fallback to Jupiter price endpoint for basic volume estimation
      try {
        const priceResponse = await axios.get(`https://price.jup.ag/v4/price?ids=${tokenA},${tokenB}`)
        const priceData = priceResponse.data.data
        
        // Rough volume estimation based on price data
        const tokenAVolume = priceData[tokenA]?.mintSymbol ? 500000 : 50000
        const tokenBVolume = priceData[tokenB]?.mintSymbol ? 500000 : 50000
        
        return { volume24h: Math.max(tokenAVolume, tokenBVolume) }
      } catch {
        return { volume24h: 100000 }
      }
    }
  }

  /**
   * Get trading presets
   */
  public async getTradingPresets(): Promise<TradingPreset[]> {
    const { getTradingPresets } = await import('@/services/tradingHelpers')
    return getTradingPresets()
  }

  /**
   * Update trading preset
   */
  public async updateTradingPreset(id: string, presetData: Partial<TradingPreset>): Promise<void> {
    const { updateTradingPreset } = await import('@/services/tradingHelpers')
    return updateTradingPreset(id, presetData)
  }

  /**
   * Health check for trading service
   */
  public async healthCheck(): Promise<boolean> {
    try {
      // Test Jupiter API connectivity
      await axios.get(`${this.jupiterApiUrl}/quote`, {
        params: {
          inputMint: 'So11111111111111111111111111111111111111112', // SOL
          outputMint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
          amount: '1000000', // 1 SOL in lamports
          slippageBps: 50
        },
        timeout: 5000
      })

      // Test Solana RPC connectivity
      await this.connection.getLatestBlockhash()

      return true
    } catch (error) {
      logger.error('Trading service health check failed:', error)
      return false
    }
  }
}

// Export singleton instance
export const TradingService = TradingServiceClass.getInstance()