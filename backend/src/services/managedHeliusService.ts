/**
 * Managed Helius Service
 * 
 * Provides rate-limited, circuit-breaker protected access to Helius APIs
 * with intelligent request management and optimization.
 */

import { logger } from '@/utils/logger'
import { config } from '@/config/environment'
import { rateLimitManager } from '@/services/rateLimitManager'
import { RedisService } from '@/services/redis'

export interface HeliusRpcRequest {
  method: string
  params: any[]
  id?: string
}

export interface HeliusRpcResponse {
  jsonrpc: string
  id: string
  result?: any
  error?: {
    code: number
    message: string
    data?: any
  }
}

export interface PriorityFeeRequest {
  accountKeys: string[]
  options?: {
    priorityLevel?: 'Low' | 'Medium' | 'High' | 'VeryHigh'
    includeAllPriorityFeeLevels?: boolean
    recommended?: boolean
  }
}

export interface BatchRequest {
  requests: HeliusRpcRequest[]
  priority?: 'low' | 'medium' | 'high' | 'critical'
}

class ManagedHeliusService {
  private rpcUrl: string
  private apiKey: string
  private requestCache: Map<string, { data: any, timestamp: number, ttl: number }> = new Map()

  constructor() {
    this.rpcUrl = config.helius.rpcUrl || 'https://mainnet.helius-rpc.com'
    this.apiKey = config.helius.apiKey || ''

    if (!this.apiKey) {
      logger.error('Helius API key is required')
      throw new Error('Helius API key is required')
    }

    // Set up rate limit manager request handler
    rateLimitManager.on('executeRequest', this.handleRateLimitedRequest.bind(this))
    
    // Start cache cleanup
    this.startCacheCleanup()
  }

  /**
   * Make a single RPC call with rate limiting
   */
  public async rpcCall<T = any>(
    method: string,
    params: any[] = [],
    priority: 'low' | 'medium' | 'high' | 'critical' = 'medium',
    cacheTtl?: number
  ): Promise<T> {
    // Check cache first
    if (cacheTtl) {
      const cacheKey = this.getCacheKey(method, params)
      const cached = this.getFromCache(cacheKey)
      if (cached) {
        logger.debug('Cache hit for RPC call', { method, cacheKey })
        return cached
      }
    }

    const request: HeliusRpcRequest = {
      method,
      params,
      id: `rpc-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    }

    try {
      const result = await rateLimitManager.queueRequest<T>(
        'helius_rpc',
        this.rpcUrl,
        request,
        priority
      )

      // Cache if TTL specified
      if (cacheTtl && result) {
        const cacheKey = this.getCacheKey(method, params)
        this.setCache(cacheKey, result, cacheTtl)
      }

      return result

    } catch (error) {
      logger.error('RPC call failed', { 
        method, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      })
      throw error
    }
  }

  /**
   * Make multiple RPC calls in a batch
   */
  public async batchRpcCall<T = any>(
    requests: HeliusRpcRequest[],
    priority: 'low' | 'medium' | 'high' | 'critical' = 'medium'
  ): Promise<T[]> {
    if (requests.length === 0) {
      return []
    }

    if (requests.length === 1) {
      const result = await this.rpcCall(
        requests[0].method,
        requests[0].params,
        priority
      )
      return [result]
    }

    // Batch multiple requests
    const batchRequest = {
      requests,
      priority
    }

    try {
      return await rateLimitManager.queueRequest<T[]>(
        'helius_rpc',
        this.rpcUrl,
        batchRequest,
        priority
      )

    } catch (error) {
      logger.error('Batch RPC call failed', { 
        requestCount: requests.length,
        error: error instanceof Error ? error.message : 'Unknown error' 
      })
      throw error
    }
  }

  /**
   * Get priority fee estimate with intelligent caching
   */
  public async getPriorityFeeEstimate(
    request: PriorityFeeRequest,
    priority: 'low' | 'medium' | 'high' | 'critical' = 'high'
  ): Promise<any> {
    // Use shorter cache for priority fees since they change frequently
    const cacheKey = this.getCacheKey('getPriorityFeeEstimate', [request])
    const cached = this.getFromCache(cacheKey)
    
    if (cached) {
      logger.debug('Using cached priority fee estimate')
      return cached
    }

    try {
      const result = await rateLimitManager.queueRequest(
        'helius_priority_fee',
        this.rpcUrl,
        {
          method: 'getPriorityFeeEstimate',
          params: [request],
          id: `priority-fee-${Date.now()}`
        },
        priority
      )

      // Cache for 30 seconds
      this.setCache(cacheKey, result, 30000)
      return result

    } catch (error) {
      logger.error('Priority fee estimate failed', { 
        accountKeys: request.accountKeys?.length || 0,
        error: error instanceof Error ? error.message : 'Unknown error' 
      })
      throw error
    }
  }

  /**
   * Get multiple accounts with batching optimization
   */
  public async getMultipleAccounts(
    addresses: string[],
    commitment: 'processed' | 'confirmed' | 'finalized' = 'confirmed',
    priority: 'low' | 'medium' | 'high' | 'critical' = 'medium'
  ): Promise<any[]> {
    if (addresses.length === 0) {
      return []
    }

    // Split into batches of 100 (Solana limit)
    const batchSize = 100
    const batches: string[][] = []
    
    for (let i = 0; i < addresses.length; i += batchSize) {
      batches.push(addresses.slice(i, i + batchSize))
    }

    const results: any[] = []

    // Process batches with rate limiting
    for (const batch of batches) {
      try {
        const batchResult = await this.rpcCall(
          'getMultipleAccounts',
          [batch, { commitment, encoding: 'jsonParsed' }],
          priority,
          10000 // 10 second cache for account data
        )

        results.push(...(batchResult?.value || []))

      } catch (error) {
        logger.error('getMultipleAccounts batch failed', { 
          batchSize: batch.length,
          error: error instanceof Error ? error.message : 'Unknown error' 
        })
        
        // Fill with nulls for failed batch
        results.push(...new Array(batch.length).fill(null))
      }
    }

    return results
  }

  /**
   * Get token balances for multiple wallets efficiently
   */
  public async getTokenBalances(
    walletAddresses: string[],
    priority: 'low' | 'medium' | 'high' | 'critical' = 'medium'
  ): Promise<Record<string, any[]>> {
    const results: Record<string, any[]> = {}

    // Batch requests for efficiency
    const requests: HeliusRpcRequest[] = walletAddresses.map(address => ({
      method: 'getTokenAccountsByOwner',
      params: [
        address,
        { programId: 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA' },
        { encoding: 'jsonParsed' }
      ],
      id: `token-balance-${address}`
    }))

    try {
      const batchResults = await this.batchRpcCall(requests, priority)

      // Map results back to wallet addresses
      walletAddresses.forEach((address, index) => {
        results[address] = batchResults[index]?.value || []
      })

      return results

    } catch (error) {
      logger.error('getTokenBalances failed', { 
        walletCount: walletAddresses.length,
        error: error instanceof Error ? error.message : 'Unknown error' 
      })
      
      // Return empty results
      walletAddresses.forEach(address => {
        results[address] = []
      })
      
      return results
    }
  }

  /**
   * Get transaction history with pagination
   */
  public async getTransactionHistory(
    address: string,
    limit: number = 10,
    before?: string,
    priority: 'low' | 'medium' | 'high' | 'critical' = 'low'
  ): Promise<any[]> {
    const params: any[] = [
      address,
      {
        limit,
        commitment: 'confirmed'
      }
    ]

    if (before) {
      params[1].before = before
    }

    try {
      const result = await this.rpcCall(
        'getSignaturesForAddress',
        params,
        priority,
        60000 // 1 minute cache for transaction history
      )

      return result || []

    } catch (error) {
      logger.error('getTransactionHistory failed', { 
        address,
        limit,
        error: error instanceof Error ? error.message : 'Unknown error' 
      })
      return []
    }
  }

  /**
   * Handle rate-limited requests
   */
  private async handleRateLimitedRequest(event: {
    apiType: string
    request: any
    resolve: (value: any) => void
    reject: (error: Error) => void
  }): Promise<void> {
    const { apiType, request, resolve, reject } = event

    try {
      let result: any

      if (apiType.startsWith('helius')) {
        result = await this.executeHeliusRequest(request.data)
      } else {
        throw new Error(`Unsupported API type: ${apiType}`)
      }

      resolve(result)

    } catch (error) {
      reject(error instanceof Error ? error : new Error('Request execution failed'))
    }
  }

  /**
   * Execute Helius RPC request
   */
  private async executeHeliusRequest(requestData: any): Promise<any> {
    const isHeliusUrl = this.rpcUrl.includes('helius-rpc.com')
    const url = isHeliusUrl ? `${this.rpcUrl}/?api-key=${this.apiKey}` : this.rpcUrl

    // Handle batch requests
    if (requestData.requests && Array.isArray(requestData.requests)) {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData.requests)
      })

      if (!response.ok) {
        throw new Error(`Helius batch request failed: ${response.status} ${response.statusText}`)
      }

      const results = await response.json()
      return results.map((r: HeliusRpcResponse) => r.result)
    }

    // Handle single requests
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: requestData.id || `req-${Date.now()}`,
        method: requestData.method,
        params: requestData.params
      })
    })

    if (!response.ok) {
      throw new Error(`Helius request failed: ${response.status} ${response.statusText}`)
    }

    const data: HeliusRpcResponse = await response.json()

    if (data.error) {
      throw new Error(`Helius RPC error: ${data.error.message}`)
    }

    return data.result
  }

  /**
   * Cache management
   */
  private getCacheKey(method: string, params: any[]): string {
    return `helius:${method}:${JSON.stringify(params)}`
  }

  private getFromCache(key: string): any | null {
    const cached = this.requestCache.get(key)
    if (!cached) return null

    if (Date.now() > cached.timestamp + cached.ttl) {
      this.requestCache.delete(key)
      return null
    }

    return cached.data
  }

  private setCache(key: string, data: any, ttl: number): void {
    this.requestCache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }

  /**
   * Start cache cleanup interval
   */
  private startCacheCleanup(): void {
    setInterval(() => {
      const now = Date.now()
      for (const [key, cached] of this.requestCache.entries()) {
        if (now > cached.timestamp + cached.ttl) {
          this.requestCache.delete(key)
        }
      }
    }, 60000) // Clean every minute
  }

  /**
   * Get service statistics
   */
  public getStats(): {
    cacheSize: number
    cacheHitRate: number
    rateLimitStats: any
  } {
    return {
      cacheSize: this.requestCache.size,
      cacheHitRate: 0, // Would need to track hits/misses
      rateLimitStats: rateLimitManager.getStats()
    }
  }

  /**
   * Health check
   */
  public async healthCheck(): Promise<boolean> {
    try {
      // Test a simple RPC call
      await this.rpcCall('getSlot', [], 'low', 5000)
      return true
    } catch (error) {
      logger.error('Managed Helius service health check failed:', error)
      return false
    }
  }

  /**
   * Clear cache
   */
  public clearCache(): void {
    this.requestCache.clear()
    logger.info('Helius service cache cleared')
  }
}

// Export singleton instance
export const managedHeliusService = new ManagedHeliusService()
export { ManagedHeliusService }