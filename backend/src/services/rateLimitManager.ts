/**
 * Rate Limit Manager
 * 
 * Manages API rate limits for Helius and Jupiter APIs to ensure we stay within
 * free tier limits and optimize request patterns.
 */

import { EventEmitter } from 'events'
import { logger } from '@/utils/logger'
import { RedisService } from '@/services/redis'

export interface RateLimitConfig {
  name: string
  requestsPerSecond: number
  requestsPerMinute: number
  requestsPerHour: number
  burstLimit: number
  queueMaxSize: number
  retryAttempts: number
  retryDelay: number
}

export interface QueuedRequest {
  id: string
  timestamp: number
  priority: 'low' | 'medium' | 'high' | 'critical'
  endpoint: string
  resolve: (value: any) => void
  reject: (error: Error) => void
  data: any
  attempt: number
}

// Default configurations for different APIs
export const API_RATE_LIMITS: Record<string, RateLimitConfig> = {
  helius_rpc: {
    name: 'Helius RPC',
    requestsPerSecond: 10,
    requestsPerMinute: 100,
    requestsPerHour: 1000,
    burstLimit: 20,
    queueMaxSize: 500,
    retryAttempts: 3,
    retryDelay: 1000
  },
  helius_priority_fee: {
    name: 'Helius Priority Fee',
    requestsPerSecond: 5,
    requestsPerMinute: 50,
    requestsPerHour: 500,
    burstLimit: 10,
    queueMaxSize: 100,
    retryAttempts: 3,
    retryDelay: 2000
  },
  jupiter_quote: {
    name: 'Jupiter Quote',
    requestsPerSecond: 20,
    requestsPerMinute: 600,
    requestsPerHour: 10000,
    burstLimit: 50,
    queueMaxSize: 1000,
    retryAttempts: 3,
    retryDelay: 500
  },
  jupiter_swap: {
    name: 'Jupiter Swap',
    requestsPerSecond: 10,
    requestsPerMinute: 300,
    requestsPerHour: 5000,
    burstLimit: 25,
    queueMaxSize: 500,
    retryAttempts: 3,
    retryDelay: 1000
  }
}

class RateLimitManager extends EventEmitter {
  private requestQueues: Map<string, QueuedRequest[]> = new Map()
  private requestCounts: Map<string, { 
    perSecond: number[], 
    perMinute: number[], 
    perHour: number[] 
  }> = new Map()
  private isProcessing: Map<string, boolean> = new Map()
  private circuitBreakers: Map<string, {
    isOpen: boolean
    failureCount: number
    lastFailureTime: number
    nextAttemptTime: number
  }> = new Map()

  constructor() {
    super()
    this.startCleanupInterval()
  }

  /**
   * Queue a request with rate limiting
   */
  public async queueRequest<T>(
    apiType: keyof typeof API_RATE_LIMITS,
    endpoint: string,
    requestData: any,
    priority: 'low' | 'medium' | 'high' | 'critical' = 'medium'
  ): Promise<T> {
    const config = API_RATE_LIMITS[apiType]
    if (!config) {
      throw new Error(`Unknown API type: ${apiType}`)
    }

    // Check circuit breaker
    if (this.isCircuitBreakerOpen(apiType)) {
      throw new Error(`Circuit breaker is open for ${config.name}`)
    }

    return new Promise<T>((resolve, reject) => {
      const requestId = `${apiType}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      
      const queuedRequest: QueuedRequest = {
        id: requestId,
        timestamp: Date.now(),
        priority,
        endpoint,
        resolve,
        reject,
        data: requestData,
        attempt: 0
      }

      // Initialize queue if it doesn't exist
      if (!this.requestQueues.has(apiType)) {
        this.requestQueues.set(apiType, [])
      }

      const queue = this.requestQueues.get(apiType)!
      
      // Check queue size limit
      if (queue.length >= config.queueMaxSize) {
        reject(new Error(`Request queue is full for ${config.name}`))
        return
      }

      // Insert by priority
      this.insertByPriority(queue, queuedRequest)
      
      logger.debug('Request queued', { 
        apiType, 
        requestId, 
        queueSize: queue.length,
        priority 
      })

      // Start processing if not already running
      if (!this.isProcessing.get(apiType)) {
        this.processQueue(apiType)
      }
    })
  }

  /**
   * Process the request queue for a specific API
   */
  private async processQueue(apiType: string): Promise<void> {
    const config = API_RATE_LIMITS[apiType]
    if (!config) return

    this.isProcessing.set(apiType, true)

    try {
      while (true) {
        const queue = this.requestQueues.get(apiType)
        if (!queue || queue.length === 0) {
          break
        }

        // Check if we can make a request
        if (!this.canMakeRequest(apiType)) {
          // Wait before checking again
          await this.sleep(100)
          continue
        }

        const request = queue.shift()!
        
        try {
          // Execute the request
          const result = await this.executeRequest(apiType, request)
          
          // Record successful request
          this.recordRequest(apiType)
          this.recordSuccess(apiType)
          
          request.resolve(result)
          
          logger.debug('Request processed successfully', { 
            apiType, 
            requestId: request.id,
            attempt: request.attempt 
          })

        } catch (error) {
          logger.error('Request failed', { 
            apiType, 
            requestId: request.id,
            attempt: request.attempt,
            error: error instanceof Error ? error.message : 'Unknown error'
          })

          // Record failure
          this.recordFailure(apiType)

          // Retry if attempts remaining
          if (request.attempt < config.retryAttempts) {
            request.attempt++
            
            // Wait before retry
            await this.sleep(config.retryDelay * request.attempt)
            
            // Re-queue the request
            this.insertByPriority(queue, request)
            
            logger.info('Request queued for retry', { 
              apiType, 
              requestId: request.id,
              attempt: request.attempt 
            })
            
          } else {
            request.reject(error instanceof Error ? error : new Error('Request failed'))
          }
        }

        // Small delay between requests to prevent overwhelming
        await this.sleep(50)
      }

    } finally {
      this.isProcessing.set(apiType, false)
    }
  }

  /**
   * Execute a request (to be overridden by specific implementations)
   */
  private async executeRequest(apiType: string, request: QueuedRequest): Promise<any> {
    // This is a placeholder - specific implementations should override this
    // For now, we'll emit an event that the specific services can listen to
    
    return new Promise((resolve, reject) => {
      this.emit('executeRequest', {
        apiType,
        request,
        resolve,
        reject
      })
    })
  }

  /**
   * Check if we can make a request based on rate limits
   */
  private canMakeRequest(apiType: string): boolean {
    const config = API_RATE_LIMITS[apiType]
    if (!config) return false

    const counts = this.getRequestCounts(apiType)
    const now = Date.now()

    // Check per-second limit
    const secondCounts = counts.perSecond.filter(timestamp => now - timestamp < 1000)
    if (secondCounts.length >= config.requestsPerSecond) {
      return false
    }

    // Check per-minute limit
    const minuteCounts = counts.perMinute.filter(timestamp => now - timestamp < 60000)
    if (minuteCounts.length >= config.requestsPerMinute) {
      return false
    }

    // Check per-hour limit
    const hourCounts = counts.perHour.filter(timestamp => now - timestamp < 3600000)
    if (hourCounts.length >= config.requestsPerHour) {
      return false
    }

    return true
  }

  /**
   * Record a successful request
   */
  private recordRequest(apiType: string): void {
    const counts = this.getRequestCounts(apiType)
    const now = Date.now()

    counts.perSecond.push(now)
    counts.perMinute.push(now)
    counts.perHour.push(now)

    // Keep arrays from growing too large
    if (counts.perSecond.length > 100) {
      counts.perSecond = counts.perSecond.slice(-50)
    }
    if (counts.perMinute.length > 1000) {
      counts.perMinute = counts.perMinute.slice(-500)
    }
    if (counts.perHour.length > 10000) {
      counts.perHour = counts.perHour.slice(-5000)
    }
  }

  /**
   * Get request counts for an API type
   */
  private getRequestCounts(apiType: string) {
    if (!this.requestCounts.has(apiType)) {
      this.requestCounts.set(apiType, {
        perSecond: [],
        perMinute: [],
        perHour: []
      })
    }
    return this.requestCounts.get(apiType)!
  }

  /**
   * Insert request by priority
   */
  private insertByPriority(queue: QueuedRequest[], request: QueuedRequest): void {
    const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 }
    
    const insertIndex = queue.findIndex(r => 
      priorityOrder[r.priority] > priorityOrder[request.priority]
    )
    
    if (insertIndex === -1) {
      queue.push(request)
    } else {
      queue.splice(insertIndex, 0, request)
    }
  }

  /**
   * Circuit breaker management
   */
  private isCircuitBreakerOpen(apiType: string): boolean {
    const breaker = this.circuitBreakers.get(apiType)
    if (!breaker) return false

    if (breaker.isOpen) {
      // Check if we should try again
      if (Date.now() > breaker.nextAttemptTime) {
        breaker.isOpen = false
        breaker.failureCount = 0
        logger.info('Circuit breaker reset', { apiType })
        return false
      }
      return true
    }

    return false
  }

  /**
   * Record API success
   */
  private recordSuccess(apiType: string): void {
    const breaker = this.getCircuitBreaker(apiType)
    breaker.failureCount = Math.max(0, breaker.failureCount - 1)
  }

  /**
   * Record API failure
   */
  private recordFailure(apiType: string): void {
    const breaker = this.getCircuitBreaker(apiType)
    breaker.failureCount++
    breaker.lastFailureTime = Date.now()

    // Open circuit breaker if too many failures
    if (breaker.failureCount >= 5) {
      breaker.isOpen = true
      breaker.nextAttemptTime = Date.now() + (30000 * Math.min(breaker.failureCount - 4, 5)) // 30s to 2.5min
      
      logger.warn('Circuit breaker opened', { 
        apiType, 
        failureCount: breaker.failureCount,
        nextAttemptTime: new Date(breaker.nextAttemptTime).toISOString()
      })
    }
  }

  /**
   * Get circuit breaker for API type
   */
  private getCircuitBreaker(apiType: string) {
    if (!this.circuitBreakers.has(apiType)) {
      this.circuitBreakers.set(apiType, {
        isOpen: false,
        failureCount: 0,
        lastFailureTime: 0,
        nextAttemptTime: 0
      })
    }
    return this.circuitBreakers.get(apiType)!
  }

  /**
   * Get statistics for monitoring
   */
  public getStats(): Record<string, any> {
    const stats: Record<string, any> = {}

    for (const [apiType, config] of Object.entries(API_RATE_LIMITS)) {
      const queue = this.requestQueues.get(apiType) || []
      const counts = this.getRequestCounts(apiType)
      const breaker = this.circuitBreakers.get(apiType)
      const now = Date.now()

      stats[apiType] = {
        name: config.name,
        queueSize: queue.length,
        isProcessing: this.isProcessing.get(apiType) || false,
        requestCounts: {
          lastSecond: counts.perSecond.filter(t => now - t < 1000).length,
          lastMinute: counts.perMinute.filter(t => now - t < 60000).length,
          lastHour: counts.perHour.filter(t => now - t < 3600000).length
        },
        limits: {
          perSecond: config.requestsPerSecond,
          perMinute: config.requestsPerMinute,
          perHour: config.requestsPerHour
        },
        circuitBreaker: {
          isOpen: breaker?.isOpen || false,
          failureCount: breaker?.failureCount || 0,
          nextAttemptTime: breaker?.nextAttemptTime || 0
        }
      }
    }

    return stats
  }

  /**
   * Health check
   */
  public async healthCheck(): Promise<boolean> {
    try {
      // Check if any circuit breakers are open
      const openBreakers = Array.from(this.circuitBreakers.values())
        .filter(breaker => breaker.isOpen)

      if (openBreakers.length > 0) {
        logger.warn('Some circuit breakers are open', { 
          openBreakers: openBreakers.length 
        })
      }

      // Check queue sizes
      const totalQueueSize = Array.from(this.requestQueues.values())
        .reduce((total, queue) => total + queue.length, 0)

      if (totalQueueSize > 1000) {
        logger.warn('High queue sizes detected', { totalQueueSize })
      }

      return true

    } catch (error) {
      logger.error('Rate limit manager health check failed:', error)
      return false
    }
  }

  /**
   * Utility sleep function
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Start cleanup interval
   */
  private startCleanupInterval(): void {
    setInterval(() => {
      const now = Date.now()
      
      // Clean old request counts
      for (const counts of this.requestCounts.values()) {
        counts.perSecond = counts.perSecond.filter(t => now - t < 2000)
        counts.perMinute = counts.perMinute.filter(t => now - t < 120000)
        counts.perHour = counts.perHour.filter(t => now - t < 7200000)
      }
      
      // Clean old queued requests (older than 5 minutes)
      for (const queue of this.requestQueues.values()) {
        const expiredRequests = queue.filter(req => now - req.timestamp > 300000)
        expiredRequests.forEach(req => {
          req.reject(new Error('Request expired in queue'))
        })
        queue.splice(0, queue.length, ...queue.filter(req => now - req.timestamp <= 300000))
      }
      
    }, 60000) // Clean every minute
  }
}

// Export singleton instance
export const rateLimitManager = new RateLimitManager()
export { RateLimitManager }