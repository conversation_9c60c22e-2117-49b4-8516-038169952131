import { Connection } from '@solana/web3.js'
import { logger } from '@/utils/logger'
import { HeliusRPC, getConnection } from '@/services/heliusRPC'
import { RedisService } from '@/services/redis'
import { AppError } from '@/middleware/errorHandler'

export interface PriorityFeeEstimate {
  priorityFeeEstimate: number
  priorityFeeLevels?: {
    min: number
    low: number
    medium: number
    high: number
    veryHigh: number
    unsafeMax: number
  }
  networkMetrics?: {
    avgPriorityFee: number
    networkCongestion: 'low' | 'medium' | 'high' | 'extreme'
    recommendedLevel: 'low' | 'medium' | 'high' | 'veryHigh'
    confidence: number
  }
}

export interface PriorityFeeRequest {
  accountKeys: string[]
  options?: {
    priorityLevel?: 'Low' | 'Medium' | 'High' | 'VeryHigh'
    includeAllPriorityFeeLevels?: boolean
    recommended?: boolean
    lookbackSlots?: number
    includeVote?: boolean
    accountMetadata?: boolean
  }
}

export interface NetworkConditions {
  avgBlockTime: number
  networkCongestion: 'low' | 'medium' | 'high' | 'extreme'
  avgPriorityFee: number
  successRate: number
  queueLength: number
  timestamp: number
}

class HeliusPriorityFeeService {
  private connection: Connection
  private readonly CACHE_TTL = 10 // 10 seconds cache for priority fees
  private readonly NETWORK_CACHE_TTL = 30 // 30 seconds cache for network conditions
  
  // Rate limiting for DAS API (2 requests/s for free tier)
  private dasApiTokens: number = 2
  private dasApiLastRefill: number = Date.now()
  private readonly DAS_API_MAX_RPS = 2 // Free tier limit

  constructor() {
    this.connection = getConnection()
  }

  /**
   * Get priority fee estimate using Helius API
   */
  public async getPriorityFeeEstimate(request: PriorityFeeRequest): Promise<PriorityFeeEstimate> {
    try {
      // Create cache key for the request
      const cacheKey = `helius:priority_fee:${JSON.stringify({
        accounts: request.accountKeys.sort(),
        options: request.options
      })}`

      // Check cache first
      try {
        const cached = await RedisService.getJSON<PriorityFeeEstimate>(cacheKey)
        if (cached) {
          logger.debug('Returning cached priority fee estimate')
          return cached
        }
      } catch (error) {
        logger.debug('Cache miss for priority fee estimate')
      }

      // Check DAS API rate limiting before making request
      await this.checkDasApiRateLimit()

      // Prepare the request body
      const requestBody = {
        jsonrpc: '2.0',
        id: `priority-fee-${Date.now()}`,
        method: 'getPriorityFeeEstimate',
        params: [
          {
            accountKeys: request.accountKeys,
            options: {
              priorityLevel: request.options?.priorityLevel || 'High',
              includeAllPriorityFeeLevels: request.options?.includeAllPriorityFeeLevels || false,
              recommended: request.options?.recommended || true,
              lookbackSlots: request.options?.lookbackSlots || 150,
              includeVote: request.options?.includeVote || false,
              accountMetadata: request.options?.accountMetadata || false
            }
          }
        ]
      }

      // Make the request to Helius RPC
      const response = await fetch(this.connection.rpcEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      })

      if (!response.ok) {
        throw new Error(`Helius RPC request failed: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()

      if (data.error) {
        throw new Error(`Helius API error: ${data.error.message}`)
      }

      // Transform the response
      const estimate: PriorityFeeEstimate = {
        priorityFeeEstimate: data.result.priorityFeeEstimate,
        priorityFeeLevels: data.result.priorityFeeLevels,
        networkMetrics: await this.analyzeNetworkConditions(data.result)
      }

      // Cache the result
      try {
        await RedisService.setJSON(cacheKey, estimate, this.CACHE_TTL)
      } catch (error) {
        logger.debug('Failed to cache priority fee estimate:', error)
      }

      logger.debug('Priority fee estimate retrieved', {
        estimate: estimate.priorityFeeEstimate,
        level: request.options?.priorityLevel,
        networkCongestion: estimate.networkMetrics?.networkCongestion
      })

      return estimate

    } catch (error) {
      logger.error('Failed to get priority fee estimate:', error)
      throw new AppError('Failed to estimate priority fees', 500, 'PRIORITY_FEE_ESTIMATION_FAILED')
    }
  }

  /**
   * Get priority fee estimate for specific priority level
   */
  public async getPriorityFeeForLevel(
    accountKeys: string[],
    priorityLevel: 'Low' | 'Medium' | 'High' | 'VeryHigh' = 'High'
  ): Promise<number> {
    const estimate = await this.getPriorityFeeEstimate({
      accountKeys,
      options: {
        priorityLevel,
        recommended: true
      }
    })

    return estimate.priorityFeeEstimate
  }

  /**
   * Get all priority fee levels at once
   */
  public async getAllPriorityLevels(accountKeys: string[]): Promise<PriorityFeeEstimate> {
    return this.getPriorityFeeEstimate({
      accountKeys,
      options: {
        includeAllPriorityFeeLevels: true,
        recommended: true
      }
    })
  }

  /**
   * Get smart priority fee recommendation based on trading context
   */
  public async getSmartPriorityFee(params: {
    accountKeys: string[]
    urgency: 'low' | 'medium' | 'high' | 'critical'
    maxFeeWillingness: number // in lamports
    tradeSize: number // for context
    mevRisk?: 'low' | 'medium' | 'high' | 'critical'
  }): Promise<{
    recommendedFee: number
    priorityLevel: 'Low' | 'Medium' | 'High' | 'VeryHigh'
    reasoning: string[]
    confidence: number
    networkConditions: NetworkConditions
  }> {
    try {
      // Get all priority levels
      const estimate = await this.getAllPriorityLevels(params.accountKeys)
      const networkConditions = await this.getNetworkConditions()

      // Determine base priority level based on urgency and network conditions
      let basePriorityLevel: 'Low' | 'Medium' | 'High' | 'VeryHigh' = 'Medium'
      let reasoning: string[] = []

      // Adjust based on urgency
      switch (params.urgency) {
        case 'critical':
          basePriorityLevel = 'VeryHigh'
          reasoning.push('Critical urgency requires highest priority')
          break
        case 'high':
          basePriorityLevel = 'High'
          reasoning.push('High urgency trading scenario')
          break
        case 'medium':
          basePriorityLevel = 'Medium'
          reasoning.push('Standard trading priority')
          break
        case 'low':
          basePriorityLevel = 'Low'
          reasoning.push('Low urgency allows for lower fees')
          break
      }

      // Adjust based on network congestion
      if (networkConditions.networkCongestion === 'extreme') {
        basePriorityLevel = 'VeryHigh'
        reasoning.push('Extreme network congestion detected')
      } else if (networkConditions.networkCongestion === 'high') {
        if (basePriorityLevel === 'Low') basePriorityLevel = 'Medium'
        if (basePriorityLevel === 'Medium') basePriorityLevel = 'High'
        reasoning.push('High network congestion')
      }

      // Adjust based on MEV risk
      if (params.mevRisk === 'critical' || params.mevRisk === 'high') {
        basePriorityLevel = 'VeryHigh'
        reasoning.push('High MEV risk requires maximum priority')
      }

      // Get the recommended fee for the determined level
      let recommendedFee = estimate.priorityFeeLevels?.[basePriorityLevel.toLowerCase() as keyof typeof estimate.priorityFeeLevels] || estimate.priorityFeeEstimate

      // Check against max willingness to pay
      if (recommendedFee > params.maxFeeWillingness) {
        // Find the highest level we can afford
        const levels = estimate.priorityFeeLevels
        if (levels) {
          if (params.maxFeeWillingness >= levels.high) {
            basePriorityLevel = 'High'
            recommendedFee = levels.high
            reasoning.push('Adjusted to High level due to fee constraints')
          } else if (params.maxFeeWillingness >= levels.medium) {
            basePriorityLevel = 'Medium'
            recommendedFee = levels.medium
            reasoning.push('Adjusted to Medium level due to fee constraints')
          } else if (params.maxFeeWillingness >= levels.low) {
            basePriorityLevel = 'Low'
            recommendedFee = levels.low
            reasoning.push('Adjusted to Low level due to fee constraints')
          } else {
            reasoning.push('Warning: Recommended fee exceeds maximum willingness to pay')
          }
        }
      }

      // Calculate confidence score
      let confidence = 0.8 // Base confidence
      
      if (networkConditions.successRate > 0.9) confidence += 0.1
      if (networkConditions.networkCongestion === 'low') confidence += 0.1
      if (estimate.networkMetrics?.confidence) confidence = Math.max(confidence, estimate.networkMetrics.confidence)

      return {
        recommendedFee,
        priorityLevel: basePriorityLevel,
        reasoning,
        confidence: Math.min(confidence, 1.0),
        networkConditions
      }

    } catch (error) {
      logger.error('Failed to get smart priority fee recommendation:', error)
      throw new AppError('Failed to get smart priority fee recommendation', 500, 'SMART_PRIORITY_FEE_FAILED')
    }
  }

  /**
   * Analyze network conditions from priority fee data
   */
  private async analyzeNetworkConditions(priorityFeeData: any): Promise<PriorityFeeEstimate['networkMetrics']> {
    try {
      const networkConditions = await this.getNetworkConditions()
      
      // Determine network congestion level
      let congestionLevel: 'low' | 'medium' | 'high' | 'extreme' = 'medium'
      
      if (networkConditions.avgPriorityFee < 1000) {
        congestionLevel = 'low'
      } else if (networkConditions.avgPriorityFee < 10000) {
        congestionLevel = 'medium'
      } else if (networkConditions.avgPriorityFee < 100000) {
        congestionLevel = 'high'
      } else {
        congestionLevel = 'extreme'
      }

      // Determine recommended level
      let recommendedLevel: 'low' | 'medium' | 'high' | 'veryHigh' = 'medium'
      
      if (congestionLevel === 'extreme') {
        recommendedLevel = 'veryHigh'
      } else if (congestionLevel === 'high') {
        recommendedLevel = 'high'
      } else if (congestionLevel === 'low') {
        recommendedLevel = 'low'
      }

      return {
        avgPriorityFee: networkConditions.avgPriorityFee,
        networkCongestion: congestionLevel,
        recommendedLevel,
        confidence: networkConditions.successRate
      }

    } catch (error) {
      logger.debug('Failed to analyze network conditions:', error)
      return {
        avgPriorityFee: 5000,
        networkCongestion: 'medium',
        recommendedLevel: 'medium',
        confidence: 0.7
      }
    }
  }

  /**
   * Get current network conditions
   */
  public async getNetworkConditions(): Promise<NetworkConditions> {
    try {
      const cacheKey = 'helius:network_conditions'
      
      // Check cache first
      try {
        const cached = await RedisService.getJSON<NetworkConditions>(cacheKey)
        if (cached && (Date.now() - cached.timestamp) < (this.NETWORK_CACHE_TTL * 1000)) {
          return cached
        }
      } catch (error) {
        logger.debug('Cache miss for network conditions')
      }

      // Get recent prioritization fees
      const response = await fetch(this.connection.rpcEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: 'network-conditions',
          method: 'getRecentPrioritizationFees',
          params: [
            [] // Empty array for global fees
          ]
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to get network conditions: ${response.statusText}`)
      }

      const data = await response.json()
      
      if (data.error) {
        throw new Error(`Network conditions API error: ${data.error.message}`)
      }

      const fees = data.result || []
      
      // Calculate metrics
      const avgPriorityFee = fees.length > 0 
        ? fees.reduce((sum: number, fee: any) => sum + fee.prioritizationFee, 0) / fees.length
        : 5000

      const successRate = 0.85 // TODO: Get actual success rate from monitoring

      let networkCongestion: 'low' | 'medium' | 'high' | 'extreme'
      if (avgPriorityFee < 1000) {
        networkCongestion = 'low'
      } else if (avgPriorityFee < 10000) {
        networkCongestion = 'medium'
      } else if (avgPriorityFee < 100000) {
        networkCongestion = 'high'
      } else {
        networkCongestion = 'extreme'
      }

      const conditions: NetworkConditions = {
        avgBlockTime: 400, // ~400ms average on Solana
        networkCongestion,
        avgPriorityFee,
        successRate,
        queueLength: fees.length,
        timestamp: Date.now()
      }

      // Cache the conditions
      try {
        await RedisService.setJSON(cacheKey, conditions, this.NETWORK_CACHE_TTL)
      } catch (error) {
        logger.debug('Failed to cache network conditions:', error)
      }

      return conditions

    } catch (error) {
      logger.debug('Failed to get network conditions, using defaults:', error)
      
      // Return default conditions
      return {
        avgBlockTime: 400,
        networkCongestion: 'medium',
        avgPriorityFee: 5000,
        successRate: 0.8,
        queueLength: 0,
        timestamp: Date.now()
      }
    }
  }

  /**
   * Rate limiting for DAS API calls (2 requests/s for free tier)
   */
  private async checkDasApiRateLimit(): Promise<void> {
    const now = Date.now()
    
    // Refill tokens based on time elapsed
    const timeElapsed = now - this.dasApiLastRefill
    const tokensToAdd = Math.floor(timeElapsed / 1000) * this.DAS_API_MAX_RPS
    
    if (tokensToAdd > 0) {
      this.dasApiTokens = Math.min(this.DAS_API_MAX_RPS, this.dasApiTokens + tokensToAdd)
      this.dasApiLastRefill = now
    }
    
    // If no tokens available, wait
    if (this.dasApiTokens < 1) {
      const waitTime = Math.ceil((1 - this.dasApiTokens) / this.DAS_API_MAX_RPS * 1000)
      logger.debug(`DAS API rate limit hit, waiting ${waitTime}ms`)
      await new Promise(resolve => setTimeout(resolve, waitTime))
      await this.checkDasApiRateLimit() // Recursive check after waiting
      return
    }
    
    // Consume a token
    this.dasApiTokens -= 1
  }

  /**
   * Health check for the service
   */
  public async healthCheck(): Promise<boolean> {
    try {
      const testAccounts = ['So11111111111111111111111111111111111111112'] // SOL mint
      const estimate = await this.getPriorityFeeEstimate({
        accountKeys: testAccounts,
        options: { priorityLevel: 'Medium' }
      })
      
      return estimate.priorityFeeEstimate > 0
    } catch (error) {
      logger.error('Helius priority fee service health check failed:', error)
      return false
    }
  }
}

// Export singleton instance
export const heliusPriorityFeeService = new HeliusPriorityFeeService()
export { HeliusPriorityFeeService }