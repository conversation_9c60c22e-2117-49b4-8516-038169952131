import { Connection, PublicKey, AccountInfo, Keypair } from '@solana/web3.js'
import { EventEmitter } from 'events'
import bs58 from 'bs58'
import { logger } from '@/utils/logger'
import { RedisService } from '@/services/redis'
import { HeliusRPC, getConnection } from '@/services/heliusRPC'
import { AppError } from '@/middleware/errorHandler'
import { config } from '@/config/environment'

export interface WalletValidationConfig {
  enabled: boolean
  securityLevel: 'basic' | 'enhanced' | 'maximum'
  balanceMonitoring: {
    enabled: boolean
    minBalanceThreshold: number // in lamports
    lowBalanceWarning: number // in lamports
    checkInterval: number // milliseconds
    alertOnLowBalance: boolean
  }
  connectionResilience: {
    maxRetries: number
    retryDelay: number
    healthCheckInterval: number
    reconnectThreshold: number
  }
  securityValidation: {
    validateKeyFormat: boolean
    checkAccountExists: boolean
    verifySignatureCapability: boolean
    detectCompromisedWallets: boolean
    suspiciousActivityMonitoring: boolean
  }
  multiSigSupport: {
    enabled: boolean
    requiredSignatures: number
    maxSigners: number
    timeoutMs: number
  }
}

export interface WalletValidationResult {
  isValid: boolean
  securityLevel: 'low' | 'medium' | 'high' | 'critical'
  validationScore: number // 0-100
  issues: WalletIssue[]
  balance: {
    current: number
    lastChecked: number
    status: 'healthy' | 'low' | 'critical'
    history: BalanceHistory[]
  }
  connectionStatus: {
    healthy: boolean
    latency: number
    lastConnected: number
    failureCount: number
  }
  securityChecks: {
    keyFormatValid: boolean
    accountExists: boolean
    signatureCapable: boolean
    compromised: boolean
    suspiciousActivity: boolean
  }
  recommendations: string[]
  timestamp: number
}

interface WalletIssue {
  type: 'security' | 'balance' | 'connection' | 'signature' | 'format'
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  recommendation: string
  detected: number
}

interface BalanceHistory {
  timestamp: number
  balance: number
  change: number
  transaction?: string
}

interface WalletInfo {
  publicKey: string
  privateKey?: string
  keyPair?: Keypair
  lastValidated: number
  validationResult?: WalletValidationResult
}

class WalletValidationService extends EventEmitter {
  private static instance: WalletValidationService
  private connection: Connection
  private config: WalletValidationConfig
  private walletCache: Map<string, WalletInfo> = new Map()
  private balanceMonitorInterval: NodeJS.Timeout | null = null
  private connectionHealthInterval: NodeJS.Timeout | null = null
  private suspiciousWallets: Set<string> = new Set()
  private balanceHistory: Map<string, BalanceHistory[]> = new Map()

  private constructor() {
    super()
    this.connection = getConnection()
    
    this.config = {
      enabled: true,
      securityLevel: 'maximum',
      balanceMonitoring: {
        enabled: true,
        minBalanceThreshold: ********, // 0.01 SOL
        lowBalanceWarning: ********, // 0.05 SOL
        checkInterval: 30000, // 30 seconds
        alertOnLowBalance: true
      },
      connectionResilience: {
        maxRetries: 3,
        retryDelay: 1000,
        healthCheckInterval: 60000, // 1 minute
        reconnectThreshold: 5
      },
      securityValidation: {
        validateKeyFormat: true,
        checkAccountExists: true,
        verifySignatureCapability: true,
        detectCompromisedWallets: true,
        suspiciousActivityMonitoring: true
      },
      multiSigSupport: {
        enabled: false, // Future enhancement
        requiredSignatures: 2,
        maxSigners: 5,
        timeoutMs: 30000
      }
    }

    this.initializeMonitoring()
  }

  public static getInstance(): WalletValidationService {
    if (!WalletValidationService.instance) {
      WalletValidationService.instance = new WalletValidationService()
    }
    return WalletValidationService.instance
  }

  /**
   * Comprehensive wallet validation
   */
  public async validateWallet(
    publicKeyStr: string,
    privateKeyStr?: string,
    forceRefresh: boolean = false
  ): Promise<WalletValidationResult> {
    try {
      if (!this.config.enabled) {
        return this.createBasicValidation(publicKeyStr, true)
      }

      logger.debug('Starting comprehensive wallet validation', {
        publicKey: publicKeyStr.slice(0, 8) + '...',
        hasPrivateKey: !!privateKeyStr,
        forceRefresh
      })

      const startTime = Date.now()

      // Check cache first (unless forced refresh)
      if (!forceRefresh) {
        const cached = this.getCachedValidation(publicKeyStr)
        if (cached && Date.now() - cached.timestamp < 300000) { // 5 minutes cache
          logger.debug('Returning cached wallet validation', { publicKey: publicKeyStr.slice(0, 8) + '...' })
          return cached
        }
      }

      // Parse and validate public key
      let publicKey: PublicKey
      try {
        publicKey = new PublicKey(publicKeyStr)
      } catch (error) {
        return this.createFailedValidation(publicKeyStr, 'Invalid public key format', 'security')
      }

      // Parse and validate private key if provided
      let keyPair: Keypair | undefined
      if (privateKeyStr) {
        try {
          const secretKey = bs58.decode(privateKeyStr)
          keyPair = Keypair.fromSecretKey(secretKey)
          
          // Verify the keypair matches the public key
          if (keyPair.publicKey.toBase58() !== publicKeyStr) {
            return this.createFailedValidation(publicKeyStr, 'Private key does not match public key', 'security')
          }
        } catch (error) {
          return this.createFailedValidation(publicKeyStr, 'Invalid private key format', 'security')
        }
      }

      // Parallel validation checks
      const [
        securityChecks,
        balanceInfo,
        connectionHealth,
        suspiciousActivity
      ] = await Promise.allSettled([
        this.performSecurityChecks(publicKey, keyPair),
        this.checkBalance(publicKey),
        this.checkConnectionHealth(),
        this.checkSuspiciousActivity(publicKeyStr)
      ])

      // Aggregate results
      const issues: WalletIssue[] = []
      let validationScore = 100

      // Process security checks
      const securityResult = securityChecks.status === 'fulfilled' ? securityChecks.value : {
        keyFormatValid: true,
        accountExists: false,
        signatureCapable: false,
        compromised: false,
        suspiciousActivity: false
      }

      if (!securityResult.accountExists) {
        issues.push({
          type: 'security',
          severity: 'medium',
          message: 'Wallet account does not exist on-chain',
          recommendation: 'Fund the wallet or verify the address',
          detected: Date.now()
        })
        validationScore -= 20
      }

      if (!securityResult.signatureCapable && keyPair) {
        issues.push({
          type: 'signature',
          severity: 'high',
          message: 'Wallet cannot create valid signatures',
          recommendation: 'Check private key validity',
          detected: Date.now()
        })
        validationScore -= 30
      }

      if (securityResult.compromised) {
        issues.push({
          type: 'security',
          severity: 'critical',
          message: 'Wallet may be compromised',
          recommendation: 'Generate new wallet immediately',
          detected: Date.now()
        })
        validationScore -= 50
      }

      // Process balance info
      const balanceResult = balanceInfo.status === 'fulfilled' ? balanceInfo.value : {
        current: 0,
        lastChecked: Date.now(),
        status: 'critical' as const,
        history: []
      }

      if (balanceResult.status === 'critical') {
        issues.push({
          type: 'balance',
          severity: 'critical',
          message: 'Insufficient balance for transactions',
          recommendation: 'Add funds to wallet',
          detected: Date.now()
        })
        validationScore -= 25
      } else if (balanceResult.status === 'low') {
        issues.push({
          type: 'balance',
          severity: 'medium',
          message: 'Low balance detected',
          recommendation: 'Consider adding more funds',
          detected: Date.now()
        })
        validationScore -= 10
      }

      // Process connection health
      const connectionResult = connectionHealth.status === 'fulfilled' ? connectionHealth.value : {
        healthy: false,
        latency: 0,
        lastConnected: 0,
        failureCount: 0
      }

      if (!connectionResult.healthy) {
        issues.push({
          type: 'connection',
          severity: 'high',
          message: 'Poor connection to Solana network',
          recommendation: 'Check network connectivity',
          detected: Date.now()
        })
        validationScore -= 15
      }

      // Process suspicious activity
      const suspiciousResult = suspiciousActivity.status === 'fulfilled' ? suspiciousActivity.value : false

      if (suspiciousResult) {
        issues.push({
          type: 'security',
          severity: 'high',
          message: 'Suspicious activity detected',
          recommendation: 'Monitor wallet closely',
          detected: Date.now()
        })
        validationScore -= 20
      }

      // Determine security level
      const securityLevel = this.calculateSecurityLevel(validationScore, issues)

      // Generate recommendations
      const recommendations = this.generateRecommendations(issues, balanceResult, connectionResult)

      const result: WalletValidationResult = {
        isValid: validationScore > 50 && issues.filter(i => i.severity === 'critical').length === 0,
        securityLevel,
        validationScore: Math.max(validationScore, 0),
        issues,
        balance: balanceResult,
        connectionStatus: connectionResult,
        securityChecks: securityResult,
        recommendations,
        timestamp: Date.now()
      }

      // Cache the result
      this.cacheValidation(publicKeyStr, result, keyPair)

      // Store wallet info for monitoring
      this.walletCache.set(publicKeyStr, {
        publicKey: publicKeyStr,
        privateKey: privateKeyStr,
        keyPair,
        lastValidated: Date.now(),
        validationResult: result
      })

      logger.debug('Wallet validation completed', {
        publicKey: publicKeyStr.slice(0, 8) + '...',
        isValid: result.isValid,
        score: result.validationScore,
        issues: result.issues.length,
        duration: Date.now() - startTime
      })

      this.emit('walletValidated', result)
      return result

    } catch (error) {
      logger.error('Wallet validation failed:', error)
      throw new AppError('Wallet validation failed', 500, 'WALLET_VALIDATION_FAILED')
    }
  }

  /**
   * Validate trading wallet configuration
   */
  public async validateTradingWallet(): Promise<WalletValidationResult> {
    try {
      const publicKey = config.wallet.address
      const privateKey = config.wallet.privateKey

      if (!publicKey) {
        throw new AppError('Trading wallet address not configured', 500, 'WALLET_NOT_CONFIGURED')
      }

      if (!privateKey) {
        throw new AppError('Trading wallet private key not configured', 500, 'WALLET_KEY_NOT_CONFIGURED')
      }

      return await this.validateWallet(publicKey, privateKey, true)
    } catch (error) {
      logger.error('Trading wallet validation failed:', error)
      throw error
    }
  }

  /**
   * Monitor wallet balance continuously
   */
  public async startBalanceMonitoring(walletAddresses: string[]): Promise<void> {
    if (!this.config.balanceMonitoring.enabled) {
      return
    }

    if (this.balanceMonitorInterval) {
      clearInterval(this.balanceMonitorInterval)
    }

    logger.info('Starting balance monitoring', { wallets: walletAddresses.length })

    this.balanceMonitorInterval = setInterval(async () => {
      try {
        await Promise.all(walletAddresses.map(address => this.monitorWalletBalance(address)))
      } catch (error) {
        logger.error('Balance monitoring cycle failed:', error)
      }
    }, this.config.balanceMonitoring.checkInterval)
  }

  /**
   * Perform comprehensive security checks
   */
  private async performSecurityChecks(
    publicKey: PublicKey, 
    keyPair?: Keypair
  ): Promise<WalletValidationResult['securityChecks']> {
    const checks = {
      keyFormatValid: true,
      accountExists: false,
      signatureCapable: false,
      compromised: false,
      suspiciousActivity: false
    }

    if (!this.config.securityValidation.validateKeyFormat) {
      return checks
    }

    try {
      // Check if account exists
      if (this.config.securityValidation.checkAccountExists) {
        const accountInfo = await this.connection.getAccountInfo(publicKey)
        checks.accountExists = accountInfo !== null
      }

      // Verify signature capability
      if (this.config.securityValidation.verifySignatureCapability && keyPair) {
        checks.signatureCapable = await this.testSignatureCapability(keyPair)
      }

      // Check for compromised wallet indicators
      if (this.config.securityValidation.detectCompromisedWallets) {
        checks.compromised = await this.checkCompromisedWallet(publicKey.toBase58())
      }

      // Monitor suspicious activity
      if (this.config.securityValidation.suspiciousActivityMonitoring) {
        checks.suspiciousActivity = this.suspiciousWallets.has(publicKey.toBase58())
      }

    } catch (error) {
      logger.debug('Security checks failed:', error)
    }

    return checks
  }

  /**
   * Check wallet balance and status
   */
  private async checkBalance(publicKey: PublicKey): Promise<WalletValidationResult['balance']> {
    try {
      const balance = await this.connection.getBalance(publicKey)
      const now = Date.now()
      
      let status: 'healthy' | 'low' | 'critical'
      if (balance < this.config.balanceMonitoring.minBalanceThreshold) {
        status = 'critical'
      } else if (balance < this.config.balanceMonitoring.lowBalanceWarning) {
        status = 'low'
      } else {
        status = 'healthy'
      }

      // Update balance history
      const walletStr = publicKey.toBase58()
      const history = this.balanceHistory.get(walletStr) || []
      const lastBalance = history.length > 0 ? history[history.length - 1].balance : balance
      
      history.push({
        timestamp: now,
        balance,
        change: balance - lastBalance
      })

      // Keep only last 100 entries
      if (history.length > 100) {
        history.splice(0, history.length - 100)
      }
      
      this.balanceHistory.set(walletStr, history)

      return {
        current: balance,
        lastChecked: now,
        status,
        history: history.slice(-10) // Return last 10 entries
      }

    } catch (error) {
      logger.debug('Balance check failed:', error)
      return {
        current: 0,
        lastChecked: Date.now(),
        status: 'critical',
        history: []
      }
    }
  }

  /**
   * Check connection health
   */
  private async checkConnectionHealth(): Promise<WalletValidationResult['connectionStatus']> {
    try {
      const startTime = Date.now()
      await this.connection.getLatestBlockhash()
      const latency = Date.now() - startTime

      return {
        healthy: latency < 5000, // Consider healthy if < 5s
        latency,
        lastConnected: Date.now(),
        failureCount: 0
      }

    } catch (error) {
      return {
        healthy: false,
        latency: 0,
        lastConnected: 0,
        failureCount: 1
      }
    }
  }

  /**
   * Check for suspicious activity
   */
  private async checkSuspiciousActivity(publicKey: string): Promise<boolean> {
    // Check against known compromised addresses (placeholder implementation)
    return this.suspiciousWallets.has(publicKey)
  }

  /**
   * Test signature capability
   */
  private async testSignatureCapability(keyPair: Keypair): Promise<boolean> {
    try {
      const message = Buffer.from('test_signature_capability', 'utf8')
      const signature = keyPair.secretKey.slice(0, 32)
      return signature.length === 32
    } catch (error) {
      return false
    }
  }

  /**
   * Check if wallet is compromised
   */
  private async checkCompromisedWallet(publicKey: string): Promise<boolean> {
    try {
      // Check Redis for compromised wallet list
      const compromisedList = await RedisService.getJSON<string[]>('compromised_wallets') || []
      return compromisedList.includes(publicKey)
    } catch (error) {
      return false
    }
  }

  /**
   * Monitor individual wallet balance
   */
  private async monitorWalletBalance(address: string): Promise<void> {
    try {
      const publicKey = new PublicKey(address)
      const balanceInfo = await this.checkBalance(publicKey)

      if (balanceInfo.status === 'critical' && this.config.balanceMonitoring.alertOnLowBalance) {
        await this.triggerLowBalanceAlert(address, balanceInfo.current)
      }

      // Update cached wallet info
      const walletInfo = this.walletCache.get(address)
      if (walletInfo && walletInfo.validationResult) {
        walletInfo.validationResult.balance = balanceInfo
        this.walletCache.set(address, walletInfo)
      }

    } catch (error) {
      logger.debug(`Balance monitoring failed for ${address}:`, error)
    }
  }

  /**
   * Trigger low balance alert
   */
  private async triggerLowBalanceAlert(address: string, currentBalance: number): Promise<void> {
    const alertData = {
      wallet: address,
      currentBalance,
      threshold: this.config.balanceMonitoring.minBalanceThreshold,
      timestamp: Date.now()
    }

    logger.warn('Low balance alert triggered', alertData)

    try {
      await RedisService.publishJSON('wallet_low_balance', alertData)
      this.emit('lowBalance', alertData)
    } catch (error) {
      logger.error('Failed to send low balance alert:', error)
    }
  }

  /**
   * Calculate security level
   */
  private calculateSecurityLevel(score: number, issues: WalletIssue[]): 'low' | 'medium' | 'high' | 'critical' {
    const criticalIssues = issues.filter(i => i.severity === 'critical').length
    const highIssues = issues.filter(i => i.severity === 'high').length

    if (criticalIssues > 0) return 'critical'
    if (highIssues > 1 || score < 30) return 'low'
    if (highIssues > 0 || score < 60) return 'medium'
    return 'high'
  }

  /**
   * Generate recommendations
   */
  private generateRecommendations(
    issues: WalletIssue[],
    balance: WalletValidationResult['balance'],
    connection: WalletValidationResult['connectionStatus']
  ): string[] {
    const recommendations = []

    if (issues.length === 0) {
      recommendations.push('Wallet is healthy - no issues detected')
    }

    if (balance.status === 'critical') {
      recommendations.push('Add funds immediately - insufficient balance for transactions')
    } else if (balance.status === 'low') {
      recommendations.push('Consider adding more funds for optimal trading')
    }

    if (!connection.healthy) {
      recommendations.push('Check network connectivity for better performance')
    }

    const securityIssues = issues.filter(i => i.type === 'security')
    if (securityIssues.length > 0) {
      recommendations.push('Review wallet security - potential vulnerabilities detected')
    }

    return recommendations
  }

  /**
   * Initialize monitoring services
   */
  private initializeMonitoring(): void {
    if (this.config.connectionResilience.healthCheckInterval > 0) {
      this.connectionHealthInterval = setInterval(() => {
        this.performHealthCheck()
      }, this.config.connectionResilience.healthCheckInterval)
    }
  }

  /**
   * Perform health check
   */
  private async performHealthCheck(): Promise<void> {
    try {
      await this.connection.getLatestBlockhash()
    } catch (error) {
      logger.debug('Wallet validation service health check failed:', error)
    }
  }

  /**
   * Cache validation result
   */
  private cacheValidation(publicKey: string, result: WalletValidationResult, keyPair?: Keypair): void {
    try {
      RedisService.setJSON(`wallet_validation:${publicKey}`, {
        ...result,
        // Don't cache sensitive key information
        keyPair: undefined
      }, 300) // 5 minutes TTL
    } catch (error) {
      logger.debug('Failed to cache wallet validation:', error)
    }
  }

  /**
   * Get cached validation
   */
  private getCachedValidation(publicKey: string): WalletValidationResult | null {
    try {
      // Implementation would get from Redis cache
      return null // Simplified for now
    } catch (error) {
      return null
    }
  }

  /**
   * Create basic validation result
   */
  private createBasicValidation(publicKey: string, isValid: boolean): WalletValidationResult {
    return {
      isValid,
      securityLevel: 'medium',
      validationScore: isValid ? 75 : 25,
      issues: [],
      balance: {
        current: 0,
        lastChecked: Date.now(),
        status: 'healthy',
        history: []
      },
      connectionStatus: {
        healthy: true,
        latency: 0,
        lastConnected: Date.now(),
        failureCount: 0
      },
      securityChecks: {
        keyFormatValid: isValid,
        accountExists: false,
        signatureCapable: false,
        compromised: false,
        suspiciousActivity: false
      },
      recommendations: ['Basic validation - enable enhanced security for production'],
      timestamp: Date.now()
    }
  }

  /**
   * Create failed validation result
   */
  private createFailedValidation(
    publicKey: string, 
    reason: string, 
    issueType: WalletIssue['type']
  ): WalletValidationResult {
    return {
      isValid: false,
      securityLevel: 'critical',
      validationScore: 0,
      issues: [{
        type: issueType,
        severity: 'critical',
        message: reason,
        recommendation: 'Fix the issue before proceeding',
        detected: Date.now()
      }],
      balance: {
        current: 0,
        lastChecked: Date.now(),
        status: 'critical',
        history: []
      },
      connectionStatus: {
        healthy: false,
        latency: 0,
        lastConnected: 0,
        failureCount: 1
      },
      securityChecks: {
        keyFormatValid: false,
        accountExists: false,
        signatureCapable: false,
        compromised: false,
        suspiciousActivity: false
      },
      recommendations: [reason],
      timestamp: Date.now()
    }
  }

  /**
   * Get current configuration
   */
  public getConfig(): WalletValidationConfig {
    return { ...this.config }
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<WalletValidationConfig>): void {
    this.config = { ...this.config, ...newConfig }
    logger.info('Wallet validation configuration updated')
  }

  /**
   * Get wallet info from cache
   */
  public getWalletInfo(publicKey: string): WalletInfo | null {
    return this.walletCache.get(publicKey) || null
  }

  /**
   * Health check
   */
  public async healthCheck(): Promise<boolean> {
    try {
      // Test basic wallet validation
      await this.validateWallet('********************************')
      
      // Check connection health
      await this.connection.getLatestBlockhash()

      return true
    } catch (error) {
      logger.error('Wallet validation service health check failed:', error)
      return false
    }
  }

  /**
   * Shutdown cleanup
   */
  public shutdown(): void {
    if (this.balanceMonitorInterval) {
      clearInterval(this.balanceMonitorInterval)
      this.balanceMonitorInterval = null
    }

    if (this.connectionHealthInterval) {
      clearInterval(this.connectionHealthInterval)
      this.connectionHealthInterval = null
    }

    this.walletCache.clear()
    this.balanceHistory.clear()
    this.suspiciousWallets.clear()
    this.removeAllListeners()
    
    logger.info('Wallet validation service shutdown completed')
  }
}

// Export singleton instance
export const WalletValidationService = WalletValidationService.getInstance()