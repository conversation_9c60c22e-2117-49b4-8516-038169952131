import { Connection, PublicKey, LAMPORTS_PER_SOL, AccountInfo, ParsedAccountData } from '@solana/web3.js'
import { getAssociatedTokenAddress, TOKEN_PROGRAM_ID } from '@solana/spl-token'
import { config } from '@/config/environment'
import { logger } from '@/utils/logger'
import { RedisService } from '@/services/redis'
import axios from 'axios'

interface TokenBalance {
  address: string
  symbol: string
  name: string
  decimals: number
  balance: number
  uiAmount: number
  valueUSD: number
  logoURI?: string
}

interface WalletBalance {
  solBalance: number
  tokenBalances: TokenBalance[]
  totalValueUSD: number
  lastUpdated: number
}

interface TokenMetadata {
  address: string
  symbol: string
  name: string
  decimals: number
  logoURI?: string
  coingeckoId?: string
  verified: boolean
}

class WalletServiceClass {
  private static instance: WalletServiceClass
  private connection: Connection
  private walletAddress: PublicKey
  
  private constructor() {
    this.connection = new Connection(config.solana.rpcUrl, { 
      commitment: 'confirmed',
      wsEndpoint: config.solana.wsUrl
    })
    this.walletAddress = new PublicKey(config.wallet.address)
  }

  public static getInstance(): WalletServiceClass {
    if (!WalletServiceClass.instance) {
      WalletServiceClass.instance = new WalletServiceClass()
    }
    return WalletServiceClass.instance
  }

  /**
   * Get SOL balance for the trading wallet
   */
  public async getSOLBalance(): Promise<number> {
    try {
      const balance = await this.connection.getBalance(this.walletAddress)
      return balance / LAMPORTS_PER_SOL
    } catch (error) {
      logger.error('Failed to get SOL balance:', error)
      throw new Error('Failed to fetch SOL balance')
    }
  }

  /**
   * Get all token balances for the trading wallet
   */
  public async getTokenBalances(minValueUSD: number = 0): Promise<TokenBalance[]> {
    try {
      // Get all token accounts
      const tokenAccounts = await this.connection.getParsedTokenAccountsByOwner(
        this.walletAddress,
        { programId: TOKEN_PROGRAM_ID }
      )

      const tokenBalances: TokenBalance[] = []
      
      // Get token prices for USD value calculation
      const tokenPrices = await this.getTokenPrices()

      for (const accountInfo of tokenAccounts.value) {
        const parsedInfo = accountInfo.account.data.parsed.info
        const mintAddress = parsedInfo.mint
        const balance = parsedInfo.tokenAmount.uiAmount
        
        if (balance === 0) continue // Skip empty accounts

        // Get token metadata
        const metadata = await this.getTokenMetadata(mintAddress)
        if (!metadata) continue

        // Calculate USD value
        const price = tokenPrices[mintAddress] || 0
        const valueUSD = balance * price

        // Apply minimum value filter
        if (valueUSD < minValueUSD) continue

        tokenBalances.push({
          address: mintAddress,
          symbol: metadata.symbol,
          name: metadata.name,
          decimals: metadata.decimals,
          balance: parsedInfo.tokenAmount.amount,
          uiAmount: balance,
          valueUSD,
          logoURI: metadata.logoURI
        })
      }

      // Sort by USD value descending
      return tokenBalances.sort((a, b) => b.valueUSD - a.valueUSD)
    } catch (error) {
      logger.error('Failed to get token balances:', error)
      throw new Error('Failed to fetch token balances')
    }
  }

  /**
   * Get complete wallet balance including SOL and all tokens
   */
  public async getWalletBalance(minValueUSD: number = 0.5): Promise<WalletBalance> {
    try {
      const cacheKey = `wallet_balance:${this.walletAddress.toBase58()}`
      
      // Try to get from cache first (cache for 30 seconds)
      const cached = await RedisService.getJSON<WalletBalance>(cacheKey)
      if (cached && Date.now() - cached.lastUpdated < 30000) {
        return cached
      }

      const [solBalance, tokenBalances] = await Promise.all([
        this.getSOLBalance(),
        this.getTokenBalances(minValueUSD)
      ])

      // Get SOL price for USD calculation
      const solPrice = await this.getSOLPrice()
      const solValueUSD = solBalance * solPrice

      const totalValueUSD = solValueUSD + tokenBalances.reduce((sum, token) => sum + token.valueUSD, 0)

      const walletBalance: WalletBalance = {
        solBalance,
        tokenBalances,
        totalValueUSD,
        lastUpdated: Date.now()
      }

      // Cache the result
      await RedisService.setJSON(cacheKey, walletBalance, 30)

      return walletBalance
    } catch (error) {
      logger.error('Failed to get wallet balance:', error)
      throw new Error('Failed to fetch wallet balance')
    }
  }

  /**
   * Get specific token balance
   */
  public async getTokenBalance(tokenAddress: string): Promise<number> {
    try {
      const mintPublicKey = new PublicKey(tokenAddress)
      const tokenAccount = await getAssociatedTokenAddress(mintPublicKey, this.walletAddress)
      
      const accountInfo = await this.connection.getParsedAccountInfo(tokenAccount)
      if (!accountInfo.value) return 0

      const parsedData = accountInfo.value.data as ParsedAccountData
      return parsedData.parsed.info.tokenAmount.uiAmount || 0
    } catch (error) {
      // Token account doesn't exist or other error
      return 0
    }
  }

  /**
   * Get token metadata from Jupiter token list and Helius
   */
  private async getTokenMetadata(tokenAddress: string): Promise<TokenMetadata | null> {
    try {
      const cacheKey = `token_metadata:${tokenAddress}`
      
      // Try cache first
      const cached = await RedisService.getJSON<TokenMetadata>(cacheKey)
      if (cached) return cached

      // Try Jupiter token list first
      let metadata = await this.getJupiterTokenMetadata(tokenAddress)
      
      // If not found in Jupiter, try Helius
      if (!metadata) {
        metadata = await this.getHeliusTokenMetadata(tokenAddress)
      }

      if (metadata) {
        // Cache for 1 hour
        await RedisService.setJSON(cacheKey, metadata, 3600)
      }

      return metadata
    } catch (error) {
      logger.error(`Failed to get metadata for token ${tokenAddress}:`, error)
      return null
    }
  }

  /**
   * Get token metadata from Jupiter token list
   */
  private async getJupiterTokenMetadata(tokenAddress: string): Promise<TokenMetadata | null> {
    try {
      const response = await axios.get('https://token.jup.ag/all', { timeout: 5000 })
      const token = response.data.find((t: any) => t.address === tokenAddress)
      
      if (token) {
        return {
          address: tokenAddress,
          symbol: token.symbol,
          name: token.name,
          decimals: token.decimals,
          logoURI: token.logoURI,
          verified: true
        }
      }
      return null
    } catch (error) {
      logger.debug(`Jupiter token metadata not found for ${tokenAddress}`)
      return null
    }
  }

  /**
   * Get token metadata from Helius
   */
  private async getHeliusTokenMetadata(tokenAddress: string): Promise<TokenMetadata | null> {
    try {
      const response = await axios.post(
        `https://mainnet.helius-rpc.com/?api-key=${config.helius.apiKey}`,
        {
          jsonrpc: '2.0',
          id: 'helius-token-metadata',
          method: 'getAsset',
          params: {
            id: tokenAddress
          }
        },
        { timeout: 5000 }
      )

      const asset = response.data.result
      if (asset && asset.content) {
        return {
          address: tokenAddress,
          symbol: asset.content.metadata?.symbol || 'UNKNOWN',
          name: asset.content.metadata?.name || 'Unknown Token',
          decimals: asset.token_info?.decimals || 9,
          logoURI: asset.content.files?.[0]?.uri,
          verified: false
        }
      }
      return null
    } catch (error) {
      logger.debug(`Helius token metadata not found for ${tokenAddress}`)
      return null
    }
  }

  /**
   * Get current token prices from Jupiter
   */
  private async getTokenPrices(): Promise<Record<string, number>> {
    try {
      const cacheKey = 'token_prices'
      
      // Try cache first (cache for 1 minute)
      const cached = await RedisService.getJSON<Record<string, number>>(cacheKey)
      if (cached) return cached

      const response = await axios.get('https://price.jup.ag/v4/price?ids=SOL', { timeout: 10000 })
      const prices: Record<string, number> = {}
      
      if (response.data.data) {
        Object.entries(response.data.data).forEach(([address, priceInfo]: [string, any]) => {
          prices[address] = priceInfo.price
        })
      }

      // Cache for 1 minute
      await RedisService.setJSON(cacheKey, prices, 60)
      return prices
    } catch (error) {
      logger.error('Failed to get token prices:', error)
      return {}
    }
  }

  /**
   * Get current SOL price in USD
   */
  private async getSOLPrice(): Promise<number> {
    try {
      const prices = await this.getTokenPrices()
      return prices['So11111111111111111111111111111111111111112'] || 0
    } catch (error) {
      logger.error('Failed to get SOL price:', error)
      return 0
    }
  }

  /**
   * Validate wallet address
   */
  public isValidAddress(address: string): boolean {
    try {
      new PublicKey(address)
      return true
    } catch {
      return false
    }
  }

  /**
   * Get wallet transaction history
   */
  public async getTransactionHistory(limit: number = 50): Promise<any[]> {
    try {
      const signatures = await this.connection.getSignaturesForAddress(
        this.walletAddress,
        { limit }
      )

      const transactions = await this.connection.getParsedTransactions(
        signatures.map(sig => sig.signature),
        { maxSupportedTransactionVersion: 0 }
      )

      return transactions.filter(tx => tx !== null)
    } catch (error) {
      logger.error('Failed to get transaction history:', error)
      return []
    }
  }

  /**
   * Health check for wallet service
   */
  public async healthCheck(): Promise<boolean> {
    try {
      await this.connection.getLatestBlockhash()
      await this.getSOLBalance()
      return true
    } catch (error) {
      logger.error('Wallet service health check failed:', error)
      return false
    }
  }
}

// Export singleton instance
export const WalletService = WalletServiceClass.getInstance()