import { WebSocketServer, WebSocket } from 'ws'
import { IncomingMessage } from 'http'
import jwt from 'jsonwebtoken'
import { config } from '@/config/environment'
import { logger, logWebSocket } from '@/utils/logger'
import { RedisService } from '@/services/redis'

// WebSocket message types
export enum WSMessageType {
  // Authentication
  AUTH = 'auth',
  AUTH_SUCCESS = 'auth_success',
  AUTH_ERROR = 'auth_error',
  
  // Subscriptions
  SUBSCRIBE = 'subscribe',
  UNSUBSCRIBE = 'unsubscribe',
  SUBSCRIPTION_SUCCESS = 'subscription_success',
  SUBSCRIPTION_ERROR = 'subscription_error',
  
  // Price updates
  PRICE_UPDATE = 'price_update',
  PRICE_ALERT = 'price_alert',
  
  // Trading updates
  TRADE_EXECUTED = 'trade_executed',
  POSITION_UPDATE = 'position_update',
  PORTFOLIO_UPDATE = 'portfolio_update',
  
  // System messages
  HEARTBEAT = 'heartbeat',
  ERROR = 'error',
  DISCONNECT = 'disconnect',
}

// WebSocket message interface
export interface WSMessage {
  type: WSMessageType
  data?: any
  timestamp?: number
  id?: string
}

// Client connection interface
interface WSClient {
  id: string
  socket: WebSocket
  userId?: string
  subscriptions: Set<string>
  lastHeartbeat: number
  isAuthenticated: boolean
}

class WebSocketServiceClass {
  private static instance: WebSocketServiceClass
  private wss: WebSocketServer | null = null
  private clients: Map<string, WSClient> = new Map()
  private heartbeatInterval: NodeJS.Timeout | null = null

  private constructor() {}

  public static getInstance(): WebSocketServiceClass {
    if (!WebSocketServiceClass.instance) {
      WebSocketServiceClass.instance = new WebSocketServiceClass()
    }
    return WebSocketServiceClass.instance
  }

  public initialize(wss: WebSocketServer): void {
    this.wss = wss
    this.setupWebSocketServer()
    this.startHeartbeat()
    this.setupRedisSubscriptions()
    
    logWebSocket('WebSocket service initialized')
  }

  private setupWebSocketServer(): void {
    if (!this.wss) return

    this.wss.on('connection', (socket: WebSocket, request: IncomingMessage) => {
      const clientId = this.generateClientId()
      const client: WSClient = {
        id: clientId,
        socket,
        subscriptions: new Set(),
        lastHeartbeat: Date.now(),
        isAuthenticated: false,
      }

      this.clients.set(clientId, client)
      
      logWebSocket('Client connected', { clientId, ip: request.socket.remoteAddress })

      // Set up message handler
      socket.on('message', (data: Buffer) => {
        this.handleMessage(clientId, data)
      })

      // Set up close handler
      socket.on('close', (code: number, reason: Buffer) => {
        this.handleDisconnect(clientId, code, reason.toString())
      })

      // Set up error handler
      socket.on('error', (error: Error) => {
        logWebSocket('Client error', { clientId, error: error.message })
      })

      // Send welcome message
      this.sendMessage(clientId, {
        type: WSMessageType.HEARTBEAT,
        data: { message: 'Connected to MemeTrader Pro WebSocket' },
        timestamp: Date.now(),
      })
    })
  }

  private handleMessage(clientId: string, data: Buffer): void {
    const client = this.clients.get(clientId)
    if (!client) return

    try {
      const message: WSMessage = JSON.parse(data.toString())
      
      logWebSocket('Message received', { clientId, type: message.type })

      switch (message.type) {
        case WSMessageType.AUTH:
          this.handleAuth(clientId, message.data)
          break
          
        case WSMessageType.SUBSCRIBE:
          this.handleSubscribe(clientId, message.data)
          break
          
        case WSMessageType.UNSUBSCRIBE:
          this.handleUnsubscribe(clientId, message.data)
          break
          
        case WSMessageType.HEARTBEAT:
          this.handleHeartbeat(clientId)
          break
          
        default:
          this.sendError(clientId, 'Unknown message type', 'UNKNOWN_MESSAGE_TYPE')
      }
    } catch (error) {
      logWebSocket('Error parsing message', { clientId, error: error instanceof Error ? error.message : 'Unknown error' })
      this.sendError(clientId, 'Invalid message format', 'INVALID_MESSAGE')
    }
  }

  private async handleAuth(clientId: string, data: any): Promise<void> {
    const client = this.clients.get(clientId)
    if (!client) return

    try {
      const { token } = data
      
      if (!token) {
        this.sendMessage(clientId, {
          type: WSMessageType.AUTH_ERROR,
          data: { message: 'Token is required' },
          timestamp: Date.now(),
        })
        return
      }

      // Verify JWT token
      const decoded = jwt.verify(token, config.jwt.secret) as any
      
      client.userId = decoded.id
      client.isAuthenticated = true
      
      logWebSocket('Client authenticated', { clientId, userId: decoded.id })
      
      this.sendMessage(clientId, {
        type: WSMessageType.AUTH_SUCCESS,
        data: { userId: decoded.id },
        timestamp: Date.now(),
      })
      
    } catch (error) {
      logWebSocket('Authentication failed', { clientId, error: error instanceof Error ? error.message : 'Unknown error' })
      
      this.sendMessage(clientId, {
        type: WSMessageType.AUTH_ERROR,
        data: { message: 'Invalid token' },
        timestamp: Date.now(),
      })
    }
  }

  private handleSubscribe(clientId: string, data: any): void {
    const client = this.clients.get(clientId)
    if (!client || !client.isAuthenticated) {
      this.sendError(clientId, 'Authentication required', 'AUTH_REQUIRED')
      return
    }

    const { channel } = data
    
    if (!channel) {
      this.sendError(clientId, 'Channel is required', 'CHANNEL_REQUIRED')
      return
    }

    client.subscriptions.add(channel)
    
    logWebSocket('Client subscribed', { clientId, channel })
    
    this.sendMessage(clientId, {
      type: WSMessageType.SUBSCRIPTION_SUCCESS,
      data: { channel },
      timestamp: Date.now(),
    })
  }

  private handleUnsubscribe(clientId: string, data: any): void {
    const client = this.clients.get(clientId)
    if (!client) return

    const { channel } = data
    
    if (!channel) {
      this.sendError(clientId, 'Channel is required', 'CHANNEL_REQUIRED')
      return
    }

    client.subscriptions.delete(channel)
    
    logWebSocket('Client unsubscribed', { clientId, channel })
    
    this.sendMessage(clientId, {
      type: WSMessageType.SUBSCRIPTION_SUCCESS,
      data: { channel, action: 'unsubscribed' },
      timestamp: Date.now(),
    })
  }

  private handleHeartbeat(clientId: string): void {
    const client = this.clients.get(clientId)
    if (!client) return

    client.lastHeartbeat = Date.now()
    
    this.sendMessage(clientId, {
      type: WSMessageType.HEARTBEAT,
      data: { timestamp: Date.now() },
      timestamp: Date.now(),
    })
  }

  private handleDisconnect(clientId: string, code: number, reason: string): void {
    const client = this.clients.get(clientId)
    if (client) {
      logWebSocket('Client disconnected', { clientId, userId: client.userId, code, reason })
      this.clients.delete(clientId)
    }
  }

  private sendMessage(clientId: string, message: WSMessage): void {
    const client = this.clients.get(clientId)
    if (!client || client.socket.readyState !== WebSocket.OPEN) return

    try {
      client.socket.send(JSON.stringify(message))
    } catch (error) {
      logWebSocket('Error sending message', { clientId, error: error instanceof Error ? error.message : 'Unknown error' })
    }
  }

  private sendError(clientId: string, message: string, code: string): void {
    this.sendMessage(clientId, {
      type: WSMessageType.ERROR,
      data: { message, code },
      timestamp: Date.now(),
    })
  }

  // Public methods for broadcasting
  public broadcast(message: WSMessage, channel?: string): void {
    for (const [clientId, client] of this.clients) {
      if (client.socket.readyState === WebSocket.OPEN) {
        if (!channel || client.subscriptions.has(channel)) {
          this.sendMessage(clientId, message)
        }
      }
    }
  }

  public sendToUser(userId: string, message: WSMessage): void {
    for (const [clientId, client] of this.clients) {
      if (client.userId === userId && client.socket.readyState === WebSocket.OPEN) {
        this.sendMessage(clientId, message)
      }
    }
  }

  public sendToChannel(channel: string, message: WSMessage): void {
    this.broadcast(message, channel)
  }

  // Heartbeat mechanism
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      const now = Date.now()
      const timeout = config.websocket.heartbeatInterval * 2 // 2x heartbeat interval

      for (const [clientId, client] of this.clients) {
        if (now - client.lastHeartbeat > timeout) {
          logWebSocket('Client heartbeat timeout', { clientId, userId: client.userId })
          client.socket.terminate()
          this.clients.delete(clientId)
        }
      }
    }, config.websocket.heartbeatInterval)
  }

  private setupRedisSubscriptions(): void {
    // Subscribe to Redis channels for real-time updates
    RedisService.subscribe('price_updates', (message) => {
      const data = JSON.parse(message)
      this.sendToChannel('price_updates', {
        type: WSMessageType.PRICE_UPDATE,
        data,
        timestamp: Date.now(),
      })
    })

    RedisService.subscribe('trade_executed', (message) => {
      const data = JSON.parse(message)
      this.sendToUser(data.userId, {
        type: WSMessageType.TRADE_EXECUTED,
        data,
        timestamp: Date.now(),
      })
    })

    RedisService.subscribe('position_updates', (message) => {
      const data = JSON.parse(message)
      this.sendToUser(data.userId, {
        type: WSMessageType.POSITION_UPDATE,
        data,
        timestamp: Date.now(),
      })
    })
  }

  private generateClientId(): string {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  public getConnectedClients(): number {
    return this.clients.size
  }

  public getAuthenticatedClients(): number {
    return Array.from(this.clients.values()).filter(client => client.isAuthenticated).length
  }

  public async shutdown(): Promise<void> {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
    }

    // Close all client connections
    for (const [clientId, client] of this.clients) {
      client.socket.close(1000, 'Server shutdown')
    }

    this.clients.clear()
    
    if (this.wss) {
      this.wss.close()
    }

    logWebSocket('WebSocket service shutdown')
  }
}

// Export singleton instance
export const WebSocketService = WebSocketServiceClass.getInstance()
