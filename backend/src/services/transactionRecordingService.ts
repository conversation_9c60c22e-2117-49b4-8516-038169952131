import { EventEmitter } from 'events'
import { logger } from '@/utils/logger'
import { DatabaseService } from '@/services/database'
import { RedisService } from '@/services/redis'
import { AppError } from '@/middleware/errorHandler'
import type { TransactionFees, PresetType } from '@memetrader-pro/shared'

export interface TransactionRecordingConfig {
  enabled: boolean
  batchSize: number
  batchInterval: number // milliseconds
  indexOptimization: boolean
  compressionEnabled: boolean
  retentionDays: number
  archivingEnabled: boolean
}

export interface EnhancedTransactionRecord {
  // Core transaction data
  id?: string
  userId: string
  positionId?: string
  hash: string
  blockNumber?: number
  blockTime?: number
  slot?: number
  
  // Transaction details
  type: 'BUY' | 'SELL' | 'SWAP' | 'TRANSFER' | 'STAKE' | 'UNSTAKE'
  status: 'PENDING' | 'CONFIRMED' | 'FAILED' | 'CANCELLED'
  
  // Token information
  tokenIn: string
  tokenOut: string
  tokenInSymbol: string
  tokenOutSymbol: string
  tokenInName?: string
  tokenOutName?: string
  tokenInDecimals?: number
  tokenOutDecimals?: number
  
  // Amount and pricing
  amountIn: number
  amountOut: number
  amountInRaw: string  // Raw token amount as string to avoid precision loss
  amountOutRaw: string
  price: number
  priceImpact?: number
  
  // Fees and costs
  fees: TransactionFees
  priorityFee?: number
  networkFee?: number
  totalCost: number
  
  // Trading context
  strategyId?: string
  presetUsed: PresetType
  slippageUsed: number
  slippageActual?: number
  mevProtected: boolean
  mevRiskLevel?: string
  
  // Execution metadata
  executionTime: number // milliseconds taken to execute
  confirmationTime?: number // milliseconds to confirm
  retryCount?: number
  errorReason?: string
  
  // Market conditions at time of trade
  marketConditions?: {
    networkCongestion: 'low' | 'medium' | 'high'
    avgPriorityFee: number
    liquidityScore: number
    volatilityIndex: number
    timestamp: number
  }
  
  // Advanced analytics
  analytics?: {
    routeComplexity: number
    liquidityUtilization: number
    timing: {
      quoteTime: number
      validationTime: number
      executionTime: number
      confirmationTime: number
    }
    performance: {
      expectedSlippage: number
      actualSlippage: number
      priceDeviation: number
    }
  }
  
  // Timestamps
  createdAt: Date
  updatedAt: Date
  confirmedAt?: Date
  archivedAt?: Date
}

export interface TransactionQuery {
  userId?: string
  positionId?: string
  type?: string[]
  status?: string[]
  tokenIn?: string
  tokenOut?: string
  strategyId?: string
  presetUsed?: PresetType[]
  mevProtected?: boolean
  
  // Date range filters
  dateFrom?: Date
  dateTo?: Date
  confirmedFrom?: Date
  confirmedTo?: Date
  
  // Amount filters
  minAmount?: number
  maxAmount?: number
  minValue?: number // USD value
  maxValue?: number
  
  // Performance filters
  minSlippage?: number
  maxSlippage?: number
  profitableOnly?: boolean
  
  // Pagination and sorting
  limit?: number
  offset?: number
  sortBy?: 'createdAt' | 'confirmedAt' | 'amountIn' | 'amountOut' | 'price' | 'executionTime'
  sortOrder?: 'asc' | 'desc'
  
  // Search
  searchTerm?: string
  includeArchived?: boolean
}

export interface TransactionBatch {
  id: string
  transactions: EnhancedTransactionRecord[]
  createdAt: number
  processedAt?: number
  status: 'pending' | 'processing' | 'completed' | 'failed'
}

export interface TransactionStats {
  totalTransactions: number
  successfulTransactions: number
  failedTransactions: number
  totalVolume: number
  totalFees: number
  averageExecutionTime: number
  averageSlippage: number
  mevProtectedPercentage: number
  
  // Breakdown by type
  byType: Record<string, {
    count: number
    volume: number
    successRate: number
  }>
  
  // Breakdown by time period
  byPeriod: {
    hourly: Array<{ hour: string, count: number, volume: number }>
    daily: Array<{ date: string, count: number, volume: number }>
    weekly: Array<{ week: string, count: number, volume: number }>
  }
  
  // Performance metrics
  performance: {
    averageSlippage: number
    averagePriceImpact: number
    averageExecutionTime: number
    successRate: number
    mevSavings: number
  }
}

class TransactionRecordingService extends EventEmitter {
  private static instance: TransactionRecordingService
  private config: TransactionRecordingConfig
  private batchQueue: Map<string, EnhancedTransactionRecord[]> = new Map()
  private batchInterval: NodeJS.Timeout | null = null
  private processingBatch = false

  private constructor() {
    super()
    
    this.config = {
      enabled: true,
      batchSize: 50,
      batchInterval: 10000, // 10 seconds
      indexOptimization: true,
      compressionEnabled: false,
      retentionDays: 365,
      archivingEnabled: true
    }

    this.initializeBatchProcessing()
  }

  public static getInstance(): TransactionRecordingService {
    if (!TransactionRecordingService.instance) {
      TransactionRecordingService.instance = new TransactionRecordingService()
    }
    return TransactionRecordingService.instance
  }

  /**
   * Record a comprehensive transaction with enhanced metadata
   */
  public async recordTransaction(transaction: Omit<EnhancedTransactionRecord, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      if (!this.config.enabled) {
        logger.debug('Transaction recording disabled')
        return 'disabled'
      }

      const enhancedTransaction: EnhancedTransactionRecord = {
        ...transaction,
        createdAt: new Date(),
        updatedAt: new Date(),
        totalCost: this.calculateTotalCost(transaction)
      }

      // Validate transaction data
      this.validateTransaction(enhancedTransaction)

      // Add to batch queue for efficient processing
      if (this.config.batchSize > 1) {
        await this.addToBatch(transaction.userId, enhancedTransaction)
      } else {
        // Process immediately for single transactions
        const recordId = await this.insertTransaction(enhancedTransaction)
        await this.updateTransactionCache(enhancedTransaction)
        return recordId
      }

      logger.debug('Transaction queued for batch processing', {
        hash: transaction.hash,
        type: transaction.type,
        userId: transaction.userId
      })

      return 'queued'

    } catch (error) {
      logger.error('Failed to record transaction:', error)
      throw new AppError('Transaction recording failed', 500, 'TRANSACTION_RECORDING_FAILED')
    }
  }

  /**
   * Update transaction status (e.g., when confirmation changes)
   */
  public async updateTransactionStatus(
    hash: string,
    status: EnhancedTransactionRecord['status'],
    metadata?: {
      blockNumber?: number
      blockTime?: number
      slot?: number
      confirmationTime?: number
      actualSlippage?: number
      errorReason?: string
    }
  ): Promise<void> {
    try {
      const updateData: any = {
        status,
        updatedAt: new Date()
      }

      if (metadata) {
        Object.assign(updateData, metadata)
        
        if (status === 'CONFIRMED' && !updateData.confirmedAt) {
          updateData.confirmedAt = new Date()
        }
      }

      await DatabaseService.client.transaction.update({
        where: { hash },
        data: updateData
      })

      // Update cache
      await this.invalidateTransactionCache(hash)

      // Emit status update event
      this.emit('transactionStatusUpdated', { hash, status, metadata })

      logger.debug('Transaction status updated', { hash, status })

    } catch (error) {
      logger.error('Failed to update transaction status:', error)
      throw new AppError('Transaction status update failed', 500, 'TRANSACTION_UPDATE_FAILED')
    }
  }

  /**
   * Query transactions with advanced filtering and pagination
   */
  public async queryTransactions(query: TransactionQuery): Promise<{
    transactions: EnhancedTransactionRecord[]
    total: number
    hasMore: boolean
    page: number
    totalPages: number
  }> {
    try {
      const {
        limit = 50,
        offset = 0,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        ...filters
      } = query

      // Build where clause
      const whereClause = this.buildWhereClause(filters)

      // Execute query with pagination
      const [transactions, total] = await Promise.all([
        DatabaseService.client.transaction.findMany({
          where: whereClause,
          orderBy: { [sortBy]: sortOrder },
          take: limit,
          skip: offset,
          include: {
            position: true,
            user: {
              select: { id: true, username: true }
            }
          }
        }),
        DatabaseService.client.transaction.count({ where: whereClause })
      ])

      const totalPages = Math.ceil(total / limit)
      const currentPage = Math.floor(offset / limit) + 1

      return {
        transactions: transactions as EnhancedTransactionRecord[],
        total,
        hasMore: offset + limit < total,
        page: currentPage,
        totalPages
      }

    } catch (error) {
      logger.error('Transaction query failed:', error)
      throw new AppError('Transaction query failed', 500, 'TRANSACTION_QUERY_FAILED')
    }
  }

  /**
   * Get comprehensive transaction statistics
   */
  public async getTransactionStats(
    userId?: string,
    dateFrom?: Date,
    dateTo?: Date
  ): Promise<TransactionStats> {
    try {
      const whereClause: any = {}
      
      if (userId) whereClause.userId = userId
      if (dateFrom || dateTo) {
        whereClause.createdAt = {}
        if (dateFrom) whereClause.createdAt.gte = dateFrom
        if (dateTo) whereClause.createdAt.lte = dateTo
      }

      // Get basic counts and aggregations
      const [
        totalStats,
        typeBreakdown,
        timeBreakdown
      ] = await Promise.all([
        this.getBasicStats(whereClause),
        this.getTypeBreakdown(whereClause),
        this.getTimeSeriesData(whereClause)
      ])

      return {
        ...totalStats,
        byType: typeBreakdown,
        byPeriod: timeBreakdown,
        performance: await this.getPerformanceMetrics(whereClause)
      }

    } catch (error) {
      logger.error('Failed to get transaction stats:', error)
      throw new AppError('Transaction stats query failed', 500, 'STATS_QUERY_FAILED')
    }
  }

  /**
   * Archive old transactions based on retention policy
   */
  public async archiveOldTransactions(): Promise<{ archived: number, deleted: number }> {
    try {
      if (!this.config.archivingEnabled) {
        return { archived: 0, deleted: 0 }
      }

      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - this.config.retentionDays)

      // Archive instead of delete for important transactions
      const archiveResult = await DatabaseService.client.transaction.updateMany({
        where: {
          createdAt: { lt: cutoffDate },
          archivedAt: null,
          status: { not: 'PENDING' }
        },
        data: {
          archivedAt: new Date()
        }
      })

      logger.info('Archived old transactions', {
        count: archiveResult.count,
        cutoffDate: cutoffDate.toISOString()
      })

      return { archived: archiveResult.count, deleted: 0 }

    } catch (error) {
      logger.error('Archive operation failed:', error)
      return { archived: 0, deleted: 0 }
    }
  }

  /**
   * Get transaction by hash with full details
   */
  public async getTransactionByHash(hash: string): Promise<EnhancedTransactionRecord | null> {
    try {
      // Try cache first
      const cached = await this.getFromCache(`tx:${hash}`)
      if (cached) return cached

      const transaction = await DatabaseService.client.transaction.findUnique({
        where: { hash },
        include: {
          position: true,
          user: {
            select: { id: true, username: true }
          }
        }
      })

      if (transaction) {
        await this.setCache(`tx:${hash}`, transaction, 300) // 5 minutes cache
      }

      return transaction as EnhancedTransactionRecord | null

    } catch (error) {
      logger.error('Failed to get transaction by hash:', error)
      return null
    }
  }

  /**
   * Initialize batch processing system
   */
  private initializeBatchProcessing(): void {
    if (this.config.batchInterval > 0) {
      this.batchInterval = setInterval(() => {
        this.processBatches()
      }, this.config.batchInterval)
    }
  }

  /**
   * Add transaction to batch queue
   */
  private async addToBatch(userId: string, transaction: EnhancedTransactionRecord): Promise<void> {
    const batchKey = `batch:${userId}`
    
    if (!this.batchQueue.has(batchKey)) {
      this.batchQueue.set(batchKey, [])
    }

    const batch = this.batchQueue.get(batchKey)!
    batch.push(transaction)

    // Process batch if it reaches max size
    if (batch.length >= this.config.batchSize) {
      await this.processBatch(batchKey, batch)
      this.batchQueue.delete(batchKey)
    }
  }

  /**
   * Process all pending batches
   */
  private async processBatches(): Promise<void> {
    if (this.processingBatch || this.batchQueue.size === 0) {
      return
    }

    this.processingBatch = true

    try {
      const batches = Array.from(this.batchQueue.entries())
      
      await Promise.all(batches.map(async ([batchKey, transactions]) => {
        try {
          await this.processBatch(batchKey, transactions)
          this.batchQueue.delete(batchKey)
        } catch (error) {
          logger.error(`Failed to process batch ${batchKey}:`, error)
        }
      }))

    } finally {
      this.processingBatch = false
    }
  }

  /**
   * Process a single batch of transactions
   */
  private async processBatch(batchKey: string, transactions: EnhancedTransactionRecord[]): Promise<void> {
    if (transactions.length === 0) return

    try {
      logger.debug(`Processing transaction batch ${batchKey}`, { count: transactions.length })

      // Insert all transactions in the batch
      await DatabaseService.client.transaction.createMany({
        data: transactions.map(tx => ({
          ...tx,
          fees: tx.fees as any, // Prisma JSON handling
          marketConditions: tx.marketConditions as any,
          analytics: tx.analytics as any
        })),
        skipDuplicates: true
      })

      // Update cache for each transaction
      await Promise.all(transactions.map(tx => this.updateTransactionCache(tx)))

      this.emit('batchProcessed', { batchKey, count: transactions.length })

      logger.debug(`Batch ${batchKey} processed successfully`, { count: transactions.length })

    } catch (error) {
      logger.error(`Batch processing failed for ${batchKey}:`, error)
      throw error
    }
  }

  /**
   * Insert single transaction
   */
  private async insertTransaction(transaction: EnhancedTransactionRecord): Promise<string> {
    const result = await DatabaseService.client.transaction.create({
      data: {
        ...transaction,
        fees: transaction.fees as any,
        marketConditions: transaction.marketConditions as any,
        analytics: transaction.analytics as any
      }
    })

    return result.id
  }

  /**
   * Validate transaction data
   */
  private validateTransaction(transaction: EnhancedTransactionRecord): void {
    const required = ['userId', 'hash', 'type', 'tokenIn', 'tokenOut', 'amountIn', 'amountOut']
    
    for (const field of required) {
      if (!transaction[field as keyof EnhancedTransactionRecord]) {
        throw new Error(`Missing required field: ${field}`)
      }
    }

    if (transaction.amountIn <= 0 || transaction.amountOut <= 0) {
      throw new Error('Transaction amounts must be positive')
    }

    if (!transaction.hash.match(/^[A-Za-z0-9]{88}$/)) {
      throw new Error('Invalid transaction hash format')
    }
  }

  /**
   * Calculate total transaction cost
   */
  private calculateTotalCost(transaction: Omit<EnhancedTransactionRecord, 'id' | 'createdAt' | 'updatedAt' | 'totalCost'>): number {
    let totalCost = 0

    // Add network fees
    if (transaction.fees?.total) {
      totalCost += transaction.fees.total
    }

    // Add priority fees
    if (transaction.priorityFee) {
      totalCost += transaction.priorityFee
    }

    // Add network fees
    if (transaction.networkFee) {
      totalCost += transaction.networkFee
    }

    return totalCost
  }

  /**
   * Build SQL where clause from query filters
   */
  private buildWhereClause(filters: Omit<TransactionQuery, 'limit' | 'offset' | 'sortBy' | 'sortOrder'>): any {
    const where: any = {}

    // Direct field matches
    if (filters.userId) where.userId = filters.userId
    if (filters.positionId) where.positionId = filters.positionId
    if (filters.tokenIn) where.tokenIn = filters.tokenIn
    if (filters.tokenOut) where.tokenOut = filters.tokenOut
    if (filters.strategyId) where.strategyId = filters.strategyId
    if (filters.mevProtected !== undefined) where.mevProtected = filters.mevProtected

    // Array filters
    if (filters.type?.length) where.type = { in: filters.type }
    if (filters.status?.length) where.status = { in: filters.status }
    if (filters.presetUsed?.length) where.presetUsed = { in: filters.presetUsed }

    // Date range filters
    if (filters.dateFrom || filters.dateTo) {
      where.createdAt = {}
      if (filters.dateFrom) where.createdAt.gte = filters.dateFrom
      if (filters.dateTo) where.createdAt.lte = filters.dateTo
    }

    if (filters.confirmedFrom || filters.confirmedTo) {
      where.confirmedAt = {}
      if (filters.confirmedFrom) where.confirmedAt.gte = filters.confirmedFrom
      if (filters.confirmedTo) where.confirmedAt.lte = filters.confirmedTo
    }

    // Amount filters
    if (filters.minAmount || filters.maxAmount) {
      where.amountIn = {}
      if (filters.minAmount) where.amountIn.gte = filters.minAmount
      if (filters.maxAmount) where.amountIn.lte = filters.maxAmount
    }

    // Slippage filters
    if (filters.minSlippage || filters.maxSlippage) {
      where.slippageUsed = {}
      if (filters.minSlippage) where.slippageUsed.gte = filters.minSlippage
      if (filters.maxSlippage) where.slippageUsed.lte = filters.maxSlippage
    }

    // Search term (searches across multiple fields)
    if (filters.searchTerm) {
      const searchTerm = filters.searchTerm.toLowerCase()
      where.OR = [
        { hash: { contains: searchTerm } },
        { tokenInSymbol: { contains: searchTerm, mode: 'insensitive' } },
        { tokenOutSymbol: { contains: searchTerm, mode: 'insensitive' } },
        { tokenInName: { contains: searchTerm, mode: 'insensitive' } },
        { tokenOutName: { contains: searchTerm, mode: 'insensitive' } }
      ]
    }

    // Archived filter
    if (!filters.includeArchived) {
      where.archivedAt = null
    }

    return where
  }

  /**
   * Get basic transaction statistics
   */
  private async getBasicStats(whereClause: any): Promise<Omit<TransactionStats, 'byType' | 'byPeriod' | 'performance'>> {
    const stats = await DatabaseService.client.transaction.aggregate({
      where: whereClause,
      _count: { _all: true },
      _sum: {
        amountIn: true,
        totalCost: true,
        executionTime: true,
        slippageUsed: true
      },
      _avg: {
        executionTime: true,
        slippageUsed: true
      }
    })

    const successfulCount = await DatabaseService.client.transaction.count({
      where: { ...whereClause, status: 'CONFIRMED' }
    })

    const mevProtectedCount = await DatabaseService.client.transaction.count({
      where: { ...whereClause, mevProtected: true }
    })

    const total = stats._count._all || 0

    return {
      totalTransactions: total,
      successfulTransactions: successfulCount,
      failedTransactions: total - successfulCount,
      totalVolume: stats._sum.amountIn || 0,
      totalFees: stats._sum.totalCost || 0,
      averageExecutionTime: stats._avg.executionTime || 0,
      averageSlippage: stats._avg.slippageUsed || 0,
      mevProtectedPercentage: total > 0 ? (mevProtectedCount / total) * 100 : 0
    }
  }

  /**
   * Get transaction breakdown by type
   */
  private async getTypeBreakdown(whereClause: any): Promise<TransactionStats['byType']> {
    const breakdown = await DatabaseService.client.transaction.groupBy({
      by: ['type'],
      where: whereClause,
      _count: { _all: true },
      _sum: { amountIn: true }
    })

    const result: TransactionStats['byType'] = {}

    for (const item of breakdown) {
      const successfulCount = await DatabaseService.client.transaction.count({
        where: { ...whereClause, type: item.type, status: 'CONFIRMED' }
      })

      result[item.type] = {
        count: item._count._all,
        volume: item._sum.amountIn || 0,
        successRate: item._count._all > 0 ? (successfulCount / item._count._all) * 100 : 0
      }
    }

    return result
  }

  /**
   * Get time series data for charts
   */
  private async getTimeSeriesData(whereClause: any): Promise<TransactionStats['byPeriod']> {
    // Simplified implementation - would use more sophisticated time grouping in production
    return {
      hourly: [],
      daily: [],
      weekly: []
    }
  }

  /**
   * Get performance metrics
   */
  private async getPerformanceMetrics(whereClause: any): Promise<TransactionStats['performance']> {
    const metrics = await DatabaseService.client.transaction.aggregate({
      where: { ...whereClause, status: 'CONFIRMED' },
      _avg: {
        slippageUsed: true,
        priceImpact: true,
        executionTime: true
      }
    })

    const totalTransactions = await DatabaseService.client.transaction.count({
      where: whereClause
    })

    const successfulTransactions = await DatabaseService.client.transaction.count({
      where: { ...whereClause, status: 'CONFIRMED' }
    })

    return {
      averageSlippage: metrics._avg.slippageUsed || 0,
      averagePriceImpact: metrics._avg.priceImpact || 0,
      averageExecutionTime: metrics._avg.executionTime || 0,
      successRate: totalTransactions > 0 ? (successfulTransactions / totalTransactions) * 100 : 0,
      mevSavings: 0 // Would calculate based on MEV protection data
    }
  }

  /**
   * Cache operations
   */
  private async updateTransactionCache(transaction: EnhancedTransactionRecord): Promise<void> {
    try {
      await this.setCache(`tx:${transaction.hash}`, transaction, 300)
      
      // Update user's recent transactions cache
      const userCacheKey = `user:${transaction.userId}:recent_transactions`
      await RedisService.lPush(userCacheKey, JSON.stringify(transaction))
      await RedisService.lTrim(userCacheKey, 0, 99) // Keep last 100
      await RedisService.expire(userCacheKey, 3600) // 1 hour
    } catch (error) {
      logger.debug('Cache update failed:', error)
    }
  }

  private async invalidateTransactionCache(hash: string): Promise<void> {
    try {
      await RedisService.del(`tx:${hash}`)
    } catch (error) {
      logger.debug('Cache invalidation failed:', error)
    }
  }

  private async getFromCache(key: string): Promise<any> {
    try {
      return await RedisService.getJSON(key)
    } catch (error) {
      return null
    }
  }

  private async setCache(key: string, value: any, ttl: number): Promise<void> {
    try {
      await RedisService.setJSON(key, value, ttl)
    } catch (error) {
      logger.debug('Cache set failed:', error)
    }
  }

  /**
   * Get current configuration
   */
  public getConfig(): TransactionRecordingConfig {
    return { ...this.config }
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<TransactionRecordingConfig>): void {
    this.config = { ...this.config, ...newConfig }
    
    // Restart batch processing if interval changed
    if (newConfig.batchInterval !== undefined) {
      if (this.batchInterval) {
        clearInterval(this.batchInterval)
      }
      this.initializeBatchProcessing()
    }
    
    logger.info('Transaction recording configuration updated')
  }

  /**
   * Health check
   */
  public async healthCheck(): Promise<boolean> {
    try {
      // Test database connectivity
      await DatabaseService.client.transaction.count({ take: 1 })
      
      // Check batch queue size
      const totalQueued = Array.from(this.batchQueue.values()).reduce((sum, batch) => sum + batch.length, 0)
      
      // Consider unhealthy if too many transactions are queued
      return totalQueued < 1000
    } catch (error) {
      logger.error('Transaction recording service health check failed:', error)
      return false
    }
  }

  /**
   * Shutdown cleanup
   */
  public async shutdown(): Promise<void> {
    if (this.batchInterval) {
      clearInterval(this.batchInterval)
      this.batchInterval = null
    }

    // Process any remaining batches
    await this.processBatches()

    this.batchQueue.clear()
    this.removeAllListeners()
    
    logger.info('Transaction recording service shutdown completed')
  }
}

// Export singleton instance
const transactionRecordingServiceInstance = TransactionRecordingService.getInstance()
export { transactionRecordingServiceInstance as TransactionRecordingService }