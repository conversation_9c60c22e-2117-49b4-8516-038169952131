import { Connection, PublicKey, Keypair } from '@solana/web3.js'
import bs58 from 'bs58'
import axios from 'axios'
import { createClient } from 'redis'
import { PrismaClient } from '@prisma/client'
import { config } from '@/config/environment'
import { logger } from '@/utils/logger'
import { HeliusRPC, getConnection, rpcRequest } from '@/services/heliusRPC'

export interface ValidationResult {
  valid: boolean
  service: string
  message: string
  details?: any
}

export interface ValidationSummary {
  success: boolean
  results: ValidationResult[]
  errors: string[]
  warnings: string[]
}

class EnvironmentValidatorService {
  private static instance: EnvironmentValidatorService
  private validationResults: ValidationResult[] = []

  private constructor() {}

  public static getInstance(): EnvironmentValidatorService {
    if (!EnvironmentValidatorService.instance) {
      EnvironmentValidatorService.instance = new EnvironmentValidatorService()
    }
    return EnvironmentValidatorService.instance
  }

  /**
   * Run comprehensive environment validation
   */
  public async validateEnvironment(): Promise<ValidationSummary> {
    logger.info('🔍 Starting comprehensive environment validation...')
    this.validationResults = []

    const validations = [
      () => this.validateNodeEnvironment(),
      () => this.validateDatabaseConnection(),
      () => this.validateRedisConnection(),
      () => this.validateSolanaRPC(),
      () => this.validateSolanaWebSocket(),
      () => this.validateRPCFailoverSystem(),
      () => this.validateWalletConfiguration(),
      () => this.validateJupiterAPI(),
      () => this.validateHeliusAPI(),
      () => this.validateHeliusWebSocket(),
      () => this.validateTradingPresets(),
      () => this.validateSecurityConfiguration(),
    ]

    // Run validations in parallel for faster startup
    const validationPromises = validations.map(async (validation) => {
      try {
        await validation()
      } catch (error) {
        this.addResult(false, 'Unknown', `Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    })

    await Promise.allSettled(validationPromises)

    const summary = this.generateSummary()
    this.logValidationSummary(summary)

    return summary
  }

  /**
   * Validate Node.js environment configuration
   */
  private async validateNodeEnvironment(): Promise<void> {
    try {
      // Check Node.js version
      const nodeVersion = process.version
      const majorVersion = parseInt(nodeVersion.split('.')[0].substring(1))
      
      if (majorVersion < 18) {
        this.addResult(false, 'Node.js', `Node.js version ${nodeVersion} is below minimum required version 18`)
        return
      }

      // Check NODE_ENV
      const nodeEnv = config.nodeEnv
      if (!['development', 'production', 'test'].includes(nodeEnv)) {
        this.addResult(false, 'Environment', `Invalid NODE_ENV: ${nodeEnv}`)
        return
      }

      // Check memory availability
      const memoryUsage = process.memoryUsage()
      const freeMemory = process.memoryUsage().heapTotal - process.memoryUsage().heapUsed
      
      if (freeMemory < 100 * 1024 * 1024) { // Less than 100MB
        this.addResult(false, 'Memory', 'Insufficient free memory available')
        return
      }

      this.addResult(true, 'Node.js', `Environment validated (Node ${nodeVersion}, ENV: ${nodeEnv})`)
    } catch (error) {
      this.addResult(false, 'Node.js', `Environment validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Validate database connection and schema
   */
  private async validateDatabaseConnection(): Promise<void> {
    let prisma: PrismaClient | null = null
    
    try {
      prisma = new PrismaClient()
      
      // Test basic connection
      await prisma.$connect()
      
      // Test a simple query
      await prisma.$queryRaw`SELECT 1 as test`
      
      // Verify critical tables exist
      const tables = ['User', 'Position', 'Transaction', 'TradingPreset']
      for (const table of tables) {
        try {
          await prisma.$queryRaw`SELECT 1 FROM ${table} LIMIT 1`
        } catch (tableError) {
          this.addResult(false, 'Database Schema', `Table ${table} not accessible - run migrations first`)
          return
        }
      }

      this.addResult(true, 'Database', 'Connection and schema validated successfully')
    } catch (error) {
      this.addResult(false, 'Database', `Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      if (prisma) {
        await prisma.$disconnect()
      }
    }
  }

  /**
   * Validate Redis connection and functionality
   */
  private async validateRedisConnection(): Promise<void> {
    let client: any = null
    
    try {
      client = createClient({ url: config.redis.url })
      
      await client.connect()
      
      // Test basic operations
      const testKey = 'env_validation_test'
      const testValue = 'test_value_' + Date.now()
      
      await client.set(testKey, testValue)
      const retrieved = await client.get(testKey)
      await client.del(testKey)
      
      if (retrieved !== testValue) {
        this.addResult(false, 'Redis', 'Data integrity test failed')
        return
      }

      // Test pub/sub functionality
      const pubClient = client.duplicate()
      await pubClient.connect()
      
      let messageReceived = false
      await pubClient.subscribe('test_channel', () => {
        messageReceived = true
      })
      
      await client.publish('test_channel', 'test_message')
      
      // Wait briefly for message
      await new Promise(resolve => setTimeout(resolve, 100))
      
      await pubClient.unsubscribe('test_channel')
      await pubClient.disconnect()

      this.addResult(true, 'Redis', 'Connection and functionality validated successfully')
    } catch (error) {
      this.addResult(false, 'Redis', `Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      if (client && client.isOpen) {
        await client.disconnect()
      }
    }
  }

  /**
   * Validate Solana RPC connection and performance
   */
  private async validateSolanaRPC(): Promise<void> {
    try {
      const connection = new Connection(config.solana.rpcUrl, { commitment: 'confirmed' })
      
      const startTime = Date.now()
      
      // Test basic connectivity
      const version = await connection.getVersion()
      const slot = await connection.getSlot()
      const blockhash = await connection.getLatestBlockhash()
      
      const responseTime = Date.now() - startTime
      
      // Check response time (should be under 2 seconds for good performance)
      if (responseTime > 2000) {
        this.addResult(false, 'Solana RPC', `Slow response time: ${responseTime}ms (should be < 2000ms)`)
        return
      }

      // Validate we're connected to the right network
      const genesisHash = await connection.getGenesisHash()
      const isMainnet = genesisHash === '5eykt4UsFv8P8NJdTREpY1vzqKqZKvdpKuc147dw2N9d'
      const isDevnet = genesisHash === 'EtWTRABZaYq6iMfeYKouRu166VU2xqa1wcaWoxPkrZBG'
      
      if (!isMainnet && !isDevnet) {
        this.addResult(false, 'Solana RPC', `Unknown network - genesis hash: ${genesisHash}`)
        return
      }

      this.addResult(true, 'Solana RPC', `Connected successfully (${isMainnet ? 'mainnet' : 'devnet'}, ${responseTime}ms, slot: ${slot})`)
    } catch (error) {
      this.addResult(false, 'Solana RPC', `Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Validate RPC failover system
   */
  private async validateRPCFailoverSystem(): Promise<void> {
    try {
      // Test RPC service initialization and health
      const healthSummary = HeliusRPC.getHealthSummary()
      
      if (healthSummary.totalEndpoints === 0) {
        this.addResult(false, 'RPC Failover', 'No RPC endpoints configured')
        return
      }

      // Test basic RPC request through failover system
      const startTime = Date.now()
      const slot = await rpcRequest('getSlot', [])
      const responseTime = Date.now() - startTime

      if (typeof slot !== 'number' || slot <= 0) {
        this.addResult(false, 'RPC Failover', 'Invalid slot response from RPC system')
        return
      }

      // Test health endpoint status
      const endpointStatus = HeliusRPC.getEndpointStatus()
      const healthyEndpoints = endpointStatus.filter(e => e.healthy).length
      
      if (healthyEndpoints === 0) {
        this.addResult(false, 'RPC Failover', 'No healthy RPC endpoints available')
        return
      }

      // Test basic failover functionality by getting version
      const version = await rpcRequest('getVersion', [])
      if (!version || !version['solana-core']) {
        this.addResult(false, 'RPC Failover', 'Unable to get version through RPC failover system')
        return
      }

      this.addResult(true, 'RPC Failover', 
        `System healthy (${healthyEndpoints}/${healthSummary.totalEndpoints} endpoints, ${responseTime}ms, slot: ${slot})`)

    } catch (error) {
      this.addResult(false, 'RPC Failover', `Failover system test failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Validate Solana WebSocket connection
   */
  private async validateSolanaWebSocket(): Promise<void> {
    try {
      const connection = new Connection(config.solana.rpcUrl, { 
        commitment: 'confirmed',
        wsEndpoint: config.solana.wsUrl
      })

      // Test WebSocket by subscribing to slot updates
      let slotReceived = false
      const subscriptionId = connection.onSlotChange(() => {
        slotReceived = true
      })

      // Wait for slot update (max 30 seconds)
      await Promise.race([
        new Promise(resolve => {
          const interval = setInterval(() => {
            if (slotReceived) {
              clearInterval(interval)
              resolve(true)
            }
          }, 100)
        }),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('WebSocket timeout')), 30000)
        )
      ])

      await connection.removeSlotChangeListener(subscriptionId)

      this.addResult(true, 'Solana WebSocket', 'Connection and real-time updates validated')
    } catch (error) {
      this.addResult(false, 'Solana WebSocket', `Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Validate wallet configuration and security
   */
  private async validateWalletConfiguration(): Promise<void> {
    try {
      // Validate private key format
      let keypair: Keypair
      try {
        const secretKey = bs58.decode(config.wallet.privateKey)
        keypair = Keypair.fromSecretKey(secretKey)
      } catch (keyError) {
        this.addResult(false, 'Wallet', 'Invalid private key format - must be base58 encoded')
        return
      }

      // Validate address matches private key
      const derivedAddress = keypair.publicKey.toBase58()
      if (derivedAddress !== config.wallet.address) {
        this.addResult(false, 'Wallet', `Address mismatch - private key derives ${derivedAddress} but config has ${config.wallet.address}`)
        return
      }

      // Validate address format
      try {
        new PublicKey(config.wallet.address)
      } catch (addressError) {
        this.addResult(false, 'Wallet', 'Invalid wallet address format')
        return
      }

      // Check wallet balance using failover RPC
      const balance = await rpcRequest('getBalance', [keypair.publicKey.toBase58()])
      const solBalance = balance / 1e9

      if (solBalance < 0.01) {
        this.addResult(false, 'Wallet', `Insufficient balance: ${solBalance} SOL (minimum 0.01 SOL required)`)
        return
      }

      this.addResult(true, 'Wallet', `Configuration validated (Address: ${config.wallet.address}, Balance: ${solBalance.toFixed(4)} SOL)`)
    } catch (error) {
      this.addResult(false, 'Wallet', `Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Validate Jupiter API connectivity and endpoints
   */
  private async validateJupiterAPI(): Promise<void> {
    try {
      const startTime = Date.now()

      // Test quote endpoint with SOL -> USDC
      const quoteResponse = await axios.get(`${config.jupiter.apiUrl}/quote`, {
        params: {
          inputMint: 'So11111111111111111111111111111111111111112', // SOL
          outputMint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
          amount: '1000000000', // 1 SOL
          slippageBps: 50
        },
        timeout: 10000
      })

      const responseTime = Date.now() - startTime

      if (!quoteResponse.data || !quoteResponse.data.outAmount) {
        this.addResult(false, 'Jupiter API', 'Invalid quote response format')
        return
      }

      // Test tokens endpoint
      const tokensResponse = await axios.get(`${config.jupiter.apiUrl}/tokens`, {
        timeout: 5000
      })

      if (!Array.isArray(tokensResponse.data)) {
        this.addResult(false, 'Jupiter API', 'Invalid tokens response format')
        return
      }

      this.addResult(true, 'Jupiter API', `Connected successfully (${responseTime}ms, ${tokensResponse.data.length} tokens available)`)
    } catch (error) {
      if (axios.isAxiosError(error)) {
        this.addResult(false, 'Jupiter API', `HTTP ${error.response?.status}: ${error.message}`)
      } else {
        this.addResult(false, 'Jupiter API', `Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }
  }

  /**
   * Validate Helius API connectivity
   */
  private async validateHeliusAPI(): Promise<void> {
    try {
      const startTime = Date.now()

      // Test RPC endpoint
      const rpcResponse = await axios.post(config.solana.rpcUrl, {
        jsonrpc: '2.0',
        id: 1,
        method: 'getVersion',
        params: []
      }, {
        headers: { 'Content-Type': 'application/json' },
        timeout: 5000
      })

      const responseTime = Date.now() - startTime

      if (!rpcResponse.data || !rpcResponse.data.result) {
        this.addResult(false, 'Helius API', 'Invalid RPC response format')
        return
      }

      // Test API key if provided
      if (config.helius.apiKey) {
        const heliusResponse = await axios.get(`https://api.helius.xyz/v0/addresses/EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v/balances?api-key=${config.helius.apiKey}`, {
          timeout: 5000
        })

        if (heliusResponse.status !== 200) {
          this.addResult(false, 'Helius API', 'API key validation failed')
          return
        }
      }

      this.addResult(true, 'Helius API', `Connected successfully (${responseTime}ms, Solana ${rpcResponse.data.result['solana-core']})`)
    } catch (error) {
      if (axios.isAxiosError(error)) {
        this.addResult(false, 'Helius API', `HTTP ${error.response?.status}: ${error.message}`)
      } else {
        this.addResult(false, 'Helius API', `Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }
  }

  /**
   * Validate Helius WebSocket connectivity
   */
  private async validateHeliusWebSocket(): Promise<void> {
    try {
      // Basic URL format validation
      if (!config.helius.wsUrl.startsWith('wss://')) {
        this.addResult(false, 'Helius WebSocket', 'WebSocket URL must use secure wss:// protocol')
        return
      }

      // Note: Full WebSocket testing requires actual connection which might be complex
      // For now, we validate the URL format and defer connection testing to runtime
      const url = new URL(config.helius.wsUrl)
      if (!url.hostname.includes('helius')) {
        this.addResult(false, 'Helius WebSocket', 'WebSocket URL does not appear to be a Helius endpoint')
        return
      }

      this.addResult(true, 'Helius WebSocket', `Configuration validated (${url.hostname})`)
    } catch (error) {
      this.addResult(false, 'Helius WebSocket', `URL validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Validate trading presets configuration
   */
  private async validateTradingPresets(): Promise<void> {
    try {
      // Validate trading configuration values
      const maxSlippageBps = config.trading.maxSlippageBps
      if (maxSlippageBps < 1 || maxSlippageBps > 10000) {
        this.addResult(false, 'Trading Config', `Invalid max slippage: ${maxSlippageBps} (should be 1-10000 BPS)`)
        return
      }

      const defaultPriorityFee = config.trading.defaultPriorityFee
      if (defaultPriorityFee < 0 || defaultPriorityFee > 100000) {
        this.addResult(false, 'Trading Config', `Invalid priority fee: ${defaultPriorityFee} (should be 0-100000 lamports)`)
        return
      }

      this.addResult(true, 'Trading Config', `Configuration validated (max slippage: ${maxSlippageBps/100}%, priority fee: ${defaultPriorityFee} lamports)`)
    } catch (error) {
      this.addResult(false, 'Trading Config', `Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Validate security configuration
   */
  private async validateSecurityConfiguration(): Promise<void> {
    try {
      // Validate JWT secrets
      if (config.jwt.secret.length < 32) {
        this.addResult(false, 'Security', 'JWT secret must be at least 32 characters')
        return
      }

      if (config.jwt.refreshSecret.length < 32) {
        this.addResult(false, 'Security', 'JWT refresh secret must be at least 32 characters')
        return
      }

      // Check if secrets are identical (security risk)
      if (config.jwt.secret === config.jwt.refreshSecret) {
        this.addResult(false, 'Security', 'JWT secret and refresh secret must be different')
        return
      }

      // Validate bcrypt rounds
      if (config.security.bcryptRounds < 10 || config.security.bcryptRounds > 15) {
        this.addResult(false, 'Security', `Invalid bcrypt rounds: ${config.security.bcryptRounds} (should be 10-15)`)
        return
      }

      // Check for default/weak secrets in production
      if (config.nodeEnv === 'production') {
        const weakSecrets = ['your-super-secret', 'dev-jwt-secret', 'test-secret']
        if (weakSecrets.some(weak => config.jwt.secret.includes(weak))) {
          this.addResult(false, 'Security', 'Production environment detected with default/weak JWT secret')
          return
        }
      }

      this.addResult(true, 'Security', `Configuration validated (bcrypt rounds: ${config.security.bcryptRounds})`)
    } catch (error) {
      this.addResult(false, 'Security', `Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Add validation result
   */
  private addResult(valid: boolean, service: string, message: string, details?: any): void {
    this.validationResults.push({ valid, service, message, details })
  }

  /**
   * Generate validation summary
   */
  private generateSummary(): ValidationSummary {
    const errors = this.validationResults.filter(r => !r.valid).map(r => `${r.service}: ${r.message}`)
    const warnings: string[] = []

    // Add warnings for non-critical issues
    if (config.nodeEnv === 'development') {
      warnings.push('Running in development mode')
    }

    return {
      success: errors.length === 0,
      results: this.validationResults,
      errors,
      warnings
    }
  }

  /**
   * Log validation summary
   */
  private logValidationSummary(summary: ValidationSummary): void {
    if (summary.success) {
      logger.info('✅ All environment validations passed')
      summary.results.forEach(result => {
        if (result.valid) {
          logger.info(`  ✅ ${result.service}: ${result.message}`)
        }
      })
    } else {
      logger.error('❌ Environment validation failed')
      summary.results.forEach(result => {
        if (result.valid) {
          logger.info(`  ✅ ${result.service}: ${result.message}`)
        } else {
          logger.error(`  ❌ ${result.service}: ${result.message}`)
        }
      })
    }

    if (summary.warnings.length > 0) {
      logger.warn('⚠️  Warnings:')
      summary.warnings.forEach(warning => logger.warn(`  ⚠️  ${warning}`))
    }
  }

  /**
   * Quick health check for specific services
   */
  public async quickHealthCheck(): Promise<{ [service: string]: boolean }> {
    const results: { [service: string]: boolean } = {}

    try {
      // Database
      const prisma = new PrismaClient()
      await prisma.$queryRaw`SELECT 1`
      await prisma.$disconnect()
      results.database = true
    } catch {
      results.database = false
    }

    try {
      // Redis
      const client = createClient({ url: config.redis.url })
      await client.connect()
      await client.ping()
      await client.disconnect()
      results.redis = true
    } catch {
      results.redis = false
    }

    try {
      // Jupiter API
      await axios.get(`${config.jupiter.apiUrl}/tokens`, { timeout: 5000 })
      results.jupiter = true
    } catch {
      results.jupiter = false
    }

    try {
      // Solana RPC with failover system
      await rpcRequest('getVersion', [])
      const healthSummary = HeliusRPC.getHealthSummary()
      results.solana = healthSummary.healthyEndpoints > 0
    } catch {
      results.solana = false
    }

    try {
      // RPC Failover System Health
      const healthSummary = HeliusRPC.getHealthSummary()
      const endpointStatus = HeliusRPC.getEndpointStatus()
      const healthyEndpoints = endpointStatus.filter(e => e.healthy).length
      results.rpc_failover = healthyEndpoints > 0 && healthSummary.totalEndpoints > 0
    } catch {
      results.rpc_failover = false
    }

    return results
  }
}

export const EnvironmentValidator = EnvironmentValidatorService.getInstance()