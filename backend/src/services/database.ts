import { PrismaClient } from '@prisma/client'
import { config } from '@/config/environment'
import { logger, logDatabase } from '@/utils/logger'

class DatabaseServiceClass {
  private static instance: DatabaseServiceClass
  private prisma: PrismaClient | null = null

  private constructor() {}

  public static getInstance(): DatabaseServiceClass {
    if (!DatabaseServiceClass.instance) {
      DatabaseServiceClass.instance = new DatabaseServiceClass()
    }
    return DatabaseServiceClass.instance
  }

  public async initialize(): Promise<void> {
    try {
      this.prisma = new PrismaClient({
        datasources: {
          db: {
            url: config.database.url,
          },
        },
        log: [
          {
            emit: 'event',
            level: 'query',
          },
          {
            emit: 'event',
            level: 'error',
          },
          {
            emit: 'event',
            level: 'info',
          },
          {
            emit: 'event',
            level: 'warn',
          },
        ],
      })

      // Set up logging
      this.prisma.$on('query', (e) => {
        logDatabase('Query executed', undefined, {
          query: e.query,
          params: e.params,
          duration: e.duration,
        })
      })

      this.prisma.$on('error', (e) => {
        logDatabase('Database error', undefined, {
          message: e.message,
          target: e.target,
        })
      })

      this.prisma.$on('info', (e) => {
        logDatabase('Database info', undefined, {
          message: e.message,
          target: e.target,
        })
      })

      this.prisma.$on('warn', (e) => {
        logDatabase('Database warning', undefined, {
          message: e.message,
          target: e.target,
        })
      })

      // Test the connection
      await this.prisma.$connect()
      
      logger.info('Database connection established successfully')
    } catch (error) {
      logger.error('Failed to initialize database connection:', error)
      logger.warn('Database will be unavailable - some features will be disabled')
      this.isInitialized = false
      // Don't throw - allow backend to start without database
      return
    }
  }

  public async disconnect(): Promise<void> {
    if (this.prisma) {
      await this.prisma.$disconnect()
      this.prisma = null
      logger.info('Database connection closed')
    }
  }

  public get client(): PrismaClient {
    if (!this.prisma) {
      throw new Error('Database not initialized. Call initialize() first.')
    }
    return this.prisma
  }

  // Convenience getters for common models
  public get user() {
    return this.client.user
  }

  public get position() {
    return this.client.position
  }

  public get transaction() {
    return this.client.transaction
  }

  public get alert() {
    return this.client.alert
  }

  public get exitStrategy() {
    return this.client.exitStrategy
  }

  public get customStrategy() {
    return this.client.customStrategy
  }

  public get watchlist() {
    return this.client.watchlist
  }

  public get portfolio() {
    return this.client.portfolio
  }

  // Health check method
  public async healthCheck(): Promise<boolean> {
    try {
      await this.client.$queryRaw`SELECT 1`
      return true
    } catch (error) {
      logger.error('Database health check failed:', error)
      return false
    }
  }

  // Transaction wrapper
  public async transaction<T>(
    fn: (prisma: PrismaClient) => Promise<T>
  ): Promise<T> {
    return this.client.$transaction(fn)
  }
}

// Export singleton instance
export const DatabaseService = DatabaseServiceClass.getInstance()
