import { EventEmitter } from 'events'
import { logger } from '@/utils/logger'
import { DatabaseService } from '@/services/database'
import { RedisService } from '@/services/redis'
import { PriceService } from '@/services/priceService'
import { TransactionRecordingService } from '@/services/transactionRecordingService'
import { AppError } from '@/middleware/errorHandler'

export interface PortfolioConfig {
  updateInterval: number // milliseconds
  priceUpdateInterval: number
  analyticsUpdateInterval: number
  riskAnalysisEnabled: boolean
  performanceTrackingEnabled: boolean
  realTimeUpdatesEnabled: boolean
  alertsEnabled: boolean
}

export interface PortfolioPosition {
  id: string
  userId: string
  
  // Token information
  tokenAddress: string
  tokenSymbol: string
  tokenName: string
  tokenDecimals: number
  
  // Position details
  quantity: number
  averageEntryPrice: number
  currentPrice: number
  totalValue: number // quantity * currentPrice
  totalCost: number // total amount invested
  
  // Performance metrics
  unrealizedPnL: number
  unrealizedPnLPercent: number
  realizedPnL: number
  totalPnL: number
  totalPnLPercent: number
  
  // Risk metrics
  weight: number // percentage of total portfolio
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  maxDrawdown: number
  sharpeRatio: number
  
  // Time tracking
  holdingPeriod: number // milliseconds
  lastUpdated: Date
  entryTimestamp: Date
  
  // Trading activity
  totalTrades: number
  winRate: number
  avgTradeSize: number
  
  // Strategy information
  strategyId?: string
  strategyName?: string
  
  // Status
  status: 'ACTIVE' | 'CLOSED' | 'PARTIAL'
  alerts: PositionAlert[]
}

export interface PositionAlert {
  id: string
  type: 'PROFIT_TARGET' | 'STOP_LOSS' | 'HIGH_VOLATILITY' | 'LARGE_MOVE' | 'RISK_WARNING'
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  message: string
  triggered: boolean
  createdAt: Date
  triggeredAt?: Date
}

export interface PortfolioSummary {
  userId: string
  
  // Total portfolio values
  totalValue: number
  totalCost: number
  totalPnL: number
  totalPnLPercent: number
  todaysPnL: number
  todaysPnLPercent: number
  
  // Cash position
  availableCash: number
  totalInvested: number
  
  // Portfolio composition
  positions: PortfolioPosition[]
  positionCount: number
  activePositions: number
  
  // Performance metrics
  performance: {
    totalReturn: number
    totalReturnPercent: number
    dayReturn: number
    dayReturnPercent: number
    weekReturn: number
    weekReturnPercent: number
    monthReturn: number
    monthReturnPercent: number
    yearReturn: number
    yearReturnPercent: number
    
    // Risk metrics
    sharpeRatio: number
    sortino: number
    maxDrawdown: number
    volatility: number
    beta: number
    
    // Win/Loss metrics
    winRate: number
    profitFactor: number
    avgWin: number
    avgLoss: number
    
    // Trading activity
    totalTrades: number
    averageTradeSize: number
    tradingFrequency: number
  }
  
  // Diversification metrics
  diversification: {
    concentration: number // Herfindahl index
    largestPosition: number // percentage
    top5Concentration: number
    sectorDistribution: Record<string, number>
    assetAllocation: {
      stocks: number
      crypto: number
      cash: number
      other: number
    }
  }
  
  // Risk analysis
  riskMetrics: {
    portfolioRisk: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
    riskScore: number // 0-100
    correlationRisk: number
    liquidityRisk: number
    concentrationRisk: number
    
    // Value at Risk
    var95: number // 95% confidence
    var99: number // 99% confidence
    expectedShortfall: number
  }
  
  // Real-time data
  lastUpdated: Date
  priceLastUpdated: Date
  
  // Alerts and warnings
  alerts: PortfolioAlert[]
  
  // Historical performance snapshots
  historicalSnapshots: PortfolioSnapshot[]
}

export interface PortfolioAlert {
  id: string
  type: 'LARGE_LOSS' | 'LARGE_GAIN' | 'POSITION_ALERT' | 'RISK_WARNING' | 'CONCENTRATION_RISK'
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  title: string
  message: string
  data: any
  acknowledged: boolean
  createdAt: Date
  acknowledgedAt?: Date
}

export interface PortfolioSnapshot {
  timestamp: Date
  totalValue: number
  totalPnL: number
  totalPnLPercent: number
  positionCount: number
  riskScore: number
}

export interface PortfolioAnalytics {
  userId: string
  period: 'day' | 'week' | 'month' | 'quarter' | 'year' | 'all'
  
  // Performance time series
  performanceChart: Array<{
    timestamp: Date
    portfolioValue: number
    pnl: number
    pnlPercent: number
    benchmarkValue?: number
  }>
  
  // Asset allocation over time
  allocationChart: Array<{
    timestamp: Date
    allocations: Record<string, number>
  }>
  
  // Trading activity
  tradingActivity: Array<{
    date: Date
    tradesCount: number
    volume: number
    pnl: number
  }>
  
  // Risk metrics over time
  riskMetrics: Array<{
    timestamp: Date
    riskScore: number
    sharpeRatio: number
    maxDrawdown: number
    volatility: number
  }>
  
  // Top performers and losers
  topPerformers: Array<{
    tokenSymbol: string
    returnPercent: number
    contribution: number
  }>
  
  topLosers: Array<{
    tokenSymbol: string
    returnPercent: number
    contribution: number
  }>
}

class PortfolioService extends EventEmitter {
  private static instance: PortfolioService
  private config: PortfolioConfig
  private portfolioCache: Map<string, PortfolioSummary> = new Map()
  private priceCache: Map<string, { price: number, timestamp: number }> = new Map()
  private updateInterval: NodeJS.Timeout | null = null
  private priceUpdateInterval: NodeJS.Timeout | null = null

  private constructor() {
    super()
    
    this.config = {
      updateInterval: 5000, // 5 seconds
      priceUpdateInterval: 2000, // 2 seconds
      analyticsUpdateInterval: 60000, // 1 minute
      riskAnalysisEnabled: true,
      performanceTrackingEnabled: true,
      realTimeUpdatesEnabled: true,
      alertsEnabled: true
    }

    this.initializeService()
  }

  public static getInstance(): PortfolioService {
    if (!PortfolioService.instance) {
      PortfolioService.instance = new PortfolioService()
    }
    return PortfolioService.instance
  }

  /**
   * Get real-time portfolio summary for a user
   */
  public async getPortfolioSummary(userId: string, forceRefresh = false): Promise<PortfolioSummary> {
    try {
      // Check cache first
      if (!forceRefresh) {
        const cached = this.portfolioCache.get(userId)
        if (cached && Date.now() - cached.lastUpdated.getTime() < this.config.updateInterval) {
          return cached
        }
      }

      logger.debug('Calculating portfolio summary', { userId })

      // Get all active positions for user
      const positions = await this.getUserPositions(userId)
      
      // Update prices for all tokens
      await this.updatePositionPrices(positions)
      
      // Calculate portfolio metrics
      const portfolioSummary = await this.calculatePortfolioSummary(userId, positions)
      
      // Cache the result
      this.portfolioCache.set(userId, portfolioSummary)
      
      // Emit update event
      this.emit('portfolioUpdated', { userId, portfolio: portfolioSummary })
      
      return portfolioSummary

    } catch (error) {
      logger.error('Portfolio summary calculation failed:', error)
      throw new AppError('Portfolio calculation failed', 500, 'PORTFOLIO_CALCULATION_FAILED')
    }
  }

  /**
   * Get detailed position information
   */
  public async getPositionDetails(positionId: string, userId: string): Promise<PortfolioPosition | null> {
    try {
      const position = await DatabaseService.client.position.findFirst({
        where: { id: positionId, userId },
        include: { 
          exitStrategy: true,
          transactions: true
        }
      })

      if (!position) {
        return null
      }

      return await this.calculatePositionMetrics(position)

    } catch (error) {
      logger.error('Position details fetch failed:', error)
      return null
    }
  }

  /**
   * Get portfolio analytics for dashboard charts
   */
  public async getPortfolioAnalytics(
    userId: string, 
    period: PortfolioAnalytics['period'] = 'month'
  ): Promise<PortfolioAnalytics> {
    try {
      const dateRange = this.getDateRange(period)
      
      // Get historical portfolio snapshots
      const snapshots = await this.getPortfolioSnapshots(userId, dateRange.from, dateRange.to)
      
      // Get trading activity
      const tradingActivity = await this.getTradingActivity(userId, dateRange.from, dateRange.to)
      
      // Calculate performance charts
      const performanceChart = this.buildPerformanceChart(snapshots)
      
      // Get top performers/losers
      const [topPerformers, topLosers] = await this.getTopPerformersAndLosers(userId, dateRange.from, dateRange.to)

      return {
        userId,
        period,
        performanceChart,
        allocationChart: [], // Would build from historical data
        tradingActivity,
        riskMetrics: this.buildRiskMetricsChart(snapshots),
        topPerformers,
        topLosers
      }

    } catch (error) {
      logger.error('Portfolio analytics calculation failed:', error)
      throw new AppError('Portfolio analytics failed', 500, 'PORTFOLIO_ANALYTICS_FAILED')
    }
  }

  /**
   * Update portfolio prices in real-time
   */
  public async updatePortfolioPrices(userId: string): Promise<void> {
    try {
      const positions = await this.getUserPositions(userId)
      await this.updatePositionPrices(positions)
      
      // Recalculate portfolio if prices changed significantly
      const hasSignificantChange = positions.some(pos => 
        this.hasSignificantPriceChange(pos.tokenAddress)
      )

      if (hasSignificantChange) {
        const portfolioSummary = await this.getPortfolioSummary(userId, true)
        
        // Broadcast update via WebSocket
        await RedisService.publishJSON('portfolio_update', {
          userId,
          portfolio: portfolioSummary,
          timestamp: Date.now()
        })
      }

    } catch (error) {
      logger.debug('Portfolio price update failed:', error)
    }
  }

  /**
   * Create portfolio snapshot for historical tracking
   */
  public async createPortfolioSnapshot(userId: string): Promise<void> {
    try {
      const portfolio = await this.getPortfolioSummary(userId)
      
      const snapshot: PortfolioSnapshot = {
        timestamp: new Date(),
        totalValue: portfolio.totalValue,
        totalPnL: portfolio.totalPnL,
        totalPnLPercent: portfolio.totalPnLPercent,
        positionCount: portfolio.positionCount,
        riskScore: portfolio.riskMetrics.riskScore
      }

      // Store in database
      await DatabaseService.client.portfolioSnapshot.create({
        data: {
          userId,
          totalValue: snapshot.totalValue,
          totalPnL: snapshot.totalPnL,
          totalPnLPercent: snapshot.totalPnLPercent,
          positionCount: snapshot.positionCount,
          riskScore: snapshot.riskScore,
          timestamp: snapshot.timestamp
        }
      })

      // Cache recent snapshots
      const cacheKey = `portfolio_snapshots:${userId}`
      await RedisService.lPush(cacheKey, JSON.stringify(snapshot))
      await RedisService.lTrim(cacheKey, 0, 999) // Keep last 1000 snapshots
      await RedisService.expire(cacheKey, 86400 * 30) // 30 days

    } catch (error) {
      logger.debug('Portfolio snapshot creation failed:', error)
    }
  }

  /**
   * Get all active positions for a user
   */
  private async getUserPositions(userId: string): Promise<any[]> {
    return await DatabaseService.client.position.findMany({
      where: { 
        userId,
        status: 'ACTIVE'
      },
      include: {
        exitStrategy: true,
        transactions: {
          where: { status: 'CONFIRMED' },
          orderBy: { createdAt: 'desc' }
        }
      }
    })
  }

  /**
   * Update prices for all position tokens
   */
  private async updatePositionPrices(positions: any[]): Promise<void> {
    const tokenAddresses = [...new Set(positions.map(pos => pos.tokenAddress))]
    
    await Promise.all(tokenAddresses.map(async (tokenAddress) => {
      try {
        const price = await PriceService.getTokenPrice(tokenAddress)
        this.priceCache.set(tokenAddress, {
          price,
          timestamp: Date.now()
        })
      } catch (error) {
        logger.debug(`Price update failed for ${tokenAddress}:`, error)
      }
    }))
  }

  /**
   * Calculate comprehensive portfolio summary
   */
  private async calculatePortfolioSummary(userId: string, positions: any[]): Promise<PortfolioSummary> {
    const portfolioPositions: PortfolioPosition[] = []
    let totalValue = 0
    let totalCost = 0
    let totalPnL = 0

    // Calculate each position
    for (const position of positions) {
      const portfolioPosition = await this.calculatePositionMetrics(position)
      portfolioPositions.push(portfolioPosition)
      
      totalValue += portfolioPosition.totalValue
      totalCost += portfolioPosition.totalCost
      totalPnL += portfolioPosition.totalPnL
    }

    // Calculate performance metrics
    const performance = await this.calculatePerformanceMetrics(userId, portfolioPositions)
    
    // Calculate diversification metrics
    const diversification = this.calculateDiversificationMetrics(portfolioPositions, totalValue)
    
    // Calculate risk metrics
    const riskMetrics = await this.calculateRiskMetrics(portfolioPositions, totalValue)
    
    // Get recent snapshots for historical context
    const historicalSnapshots = await this.getRecentSnapshots(userId, 30)

    // Calculate today's PnL from snapshots
    const todaysPnL = this.calculateTodaysPnL(historicalSnapshots, totalPnL)

    return {
      userId,
      totalValue,
      totalCost,
      totalPnL,
      totalPnLPercent: totalCost > 0 ? (totalPnL / totalCost) * 100 : 0,
      todaysPnL: todaysPnL.pnl,
      todaysPnLPercent: todaysPnL.pnlPercent,
      availableCash: 0, // Would get from wallet balance
      totalInvested: totalCost,
      positions: portfolioPositions,
      positionCount: portfolioPositions.length,
      activePositions: portfolioPositions.filter(p => p.status === 'ACTIVE').length,
      performance,
      diversification,
      riskMetrics,
      lastUpdated: new Date(),
      priceLastUpdated: new Date(),
      alerts: [], // Would generate based on risk analysis
      historicalSnapshots
    }
  }

  /**
   * Calculate detailed position metrics
   */
  private async calculatePositionMetrics(position: any): Promise<PortfolioPosition> {
    const tokenPrice = this.priceCache.get(position.tokenAddress)?.price || position.currentPrice || 0
    const totalValue = position.quantity * tokenPrice
    const totalCost = position.entryPrice * position.quantity
    const unrealizedPnL = totalValue - totalCost
    const unrealizedPnLPercent = totalCost > 0 ? (unrealizedPnL / totalCost) * 100 : 0

    // Calculate realized PnL from transactions
    const realizedPnL = await this.calculateRealizedPnL(position.id)
    const totalPnL = unrealizedPnL + realizedPnL
    const totalPnLPercent = totalCost > 0 ? (totalPnL / totalCost) * 100 : 0

    // Calculate trading metrics
    const tradingMetrics = await this.calculateTradingMetrics(position.id)

    return {
      id: position.id,
      userId: position.userId,
      tokenAddress: position.tokenAddress,
      tokenSymbol: position.tokenSymbol,
      tokenName: position.tokenName,
      tokenDecimals: 9, // Default for Solana tokens
      quantity: position.quantity,
      averageEntryPrice: position.entryPrice,
      currentPrice: tokenPrice,
      totalValue,
      totalCost,
      unrealizedPnL,
      unrealizedPnLPercent,
      realizedPnL,
      totalPnL,
      totalPnLPercent,
      weight: 0, // Will be calculated at portfolio level
      riskLevel: this.calculatePositionRisk(unrealizedPnLPercent),
      maxDrawdown: 0, // Would calculate from historical data
      sharpeRatio: 0, // Would calculate from price history
      holdingPeriod: Date.now() - new Date(position.entryTimestamp).getTime(),
      lastUpdated: new Date(),
      entryTimestamp: new Date(position.entryTimestamp),
      totalTrades: tradingMetrics.totalTrades,
      winRate: tradingMetrics.winRate,
      avgTradeSize: tradingMetrics.avgTradeSize,
      strategyId: position.strategyId,
      strategyName: position.exitStrategy?.customName,
      status: position.status,
      alerts: [] // Would generate based on position analysis
    }
  }

  /**
   * Calculate performance metrics
   */
  private async calculatePerformanceMetrics(userId: string, positions: PortfolioPosition[]): Promise<PortfolioSummary['performance']> {
    // Get historical data for performance calculations
    const snapshots = await this.getRecentSnapshots(userId, 365)
    
    return {
      totalReturn: positions.reduce((sum, pos) => sum + pos.totalPnL, 0),
      totalReturnPercent: this.calculateTotalReturnPercent(positions),
      dayReturn: 0, // Would calculate from daily snapshots
      dayReturnPercent: 0,
      weekReturn: 0,
      weekReturnPercent: 0,
      monthReturn: 0,
      monthReturnPercent: 0,
      yearReturn: 0,
      yearReturnPercent: 0,
      sharpeRatio: 0, // Would calculate from returns and volatility
      sortino: 0,
      maxDrawdown: 0,
      volatility: 0,
      beta: 0,
      winRate: this.calculateOverallWinRate(positions),
      profitFactor: this.calculateProfitFactor(positions),
      avgWin: this.calculateAverageWin(positions),
      avgLoss: this.calculateAverageLoss(positions),
      totalTrades: positions.reduce((sum, pos) => sum + pos.totalTrades, 0),
      averageTradeSize: this.calculateAverageTradeSize(positions),
      tradingFrequency: 0 // Would calculate from transaction history
    }
  }

  /**
   * Calculate diversification metrics
   */
  private calculateDiversificationMetrics(positions: PortfolioPosition[], totalValue: number): PortfolioSummary['diversification'] {
    if (positions.length === 0 || totalValue === 0) {
      return {
        concentration: 0,
        largestPosition: 0,
        top5Concentration: 0,
        sectorDistribution: {},
        assetAllocation: { stocks: 0, crypto: 100, cash: 0, other: 0 }
      }
    }

    // Calculate position weights
    const weights = positions.map(pos => pos.totalValue / totalValue)
    
    // Herfindahl-Hirschman Index for concentration
    const concentration = weights.reduce((sum, weight) => sum + weight * weight, 0)
    
    // Largest position percentage
    const largestPosition = Math.max(...weights) * 100
    
    // Top 5 concentration
    const sortedWeights = weights.sort((a, b) => b - a)
    const top5Concentration = sortedWeights.slice(0, 5).reduce((sum, weight) => sum + weight, 0) * 100

    return {
      concentration,
      largestPosition,
      top5Concentration,
      sectorDistribution: { crypto: 100 }, // Simplified
      assetAllocation: { stocks: 0, crypto: 100, cash: 0, other: 0 }
    }
  }

  /**
   * Calculate risk metrics
   */
  private async calculateRiskMetrics(positions: PortfolioPosition[], totalValue: number): Promise<PortfolioSummary['riskMetrics']> {
    const riskScore = this.calculatePortfolioRiskScore(positions)
    
    return {
      portfolioRisk: this.getRiskLevel(riskScore),
      riskScore,
      correlationRisk: 0, // Would calculate from price correlations
      liquidityRisk: 0, // Would assess based on token liquidity
      concentrationRisk: this.calculateConcentrationRisk(positions, totalValue),
      var95: 0, // Would calculate Value at Risk
      var99: 0,
      expectedShortfall: 0
    }
  }

  /**
   * Helper methods for calculations
   */
  private calculateRealizedPnL(positionId: string): Promise<number> {
    // Would calculate from closed trades/partial exits
    return Promise.resolve(0)
  }

  private calculateTradingMetrics(positionId: string): Promise<{ totalTrades: number, winRate: number, avgTradeSize: number }> {
    // Would calculate from transaction history
    return Promise.resolve({ totalTrades: 1, winRate: 0, avgTradeSize: 0 })
  }

  private calculatePositionRisk(pnlPercent: number): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    if (pnlPercent < -50) return 'CRITICAL'
    if (pnlPercent < -20) return 'HIGH'
    if (pnlPercent < -10) return 'MEDIUM'
    return 'LOW'
  }

  private calculateTotalReturnPercent(positions: PortfolioPosition[]): number {
    const totalCost = positions.reduce((sum, pos) => sum + pos.totalCost, 0)
    const totalPnL = positions.reduce((sum, pos) => sum + pos.totalPnL, 0)
    return totalCost > 0 ? (totalPnL / totalCost) * 100 : 0
  }

  private calculateOverallWinRate(positions: PortfolioPosition[]): number {
    const winningPositions = positions.filter(pos => pos.totalPnL > 0).length
    return positions.length > 0 ? (winningPositions / positions.length) * 100 : 0
  }

  private calculateProfitFactor(positions: PortfolioPosition[]): number {
    const totalWins = positions.filter(pos => pos.totalPnL > 0).reduce((sum, pos) => sum + pos.totalPnL, 0)
    const totalLosses = Math.abs(positions.filter(pos => pos.totalPnL < 0).reduce((sum, pos) => sum + pos.totalPnL, 0))
    return totalLosses > 0 ? totalWins / totalLosses : 0
  }

  private calculateAverageWin(positions: PortfolioPosition[]): number {
    const wins = positions.filter(pos => pos.totalPnL > 0)
    return wins.length > 0 ? wins.reduce((sum, pos) => sum + pos.totalPnL, 0) / wins.length : 0
  }

  private calculateAverageLoss(positions: PortfolioPosition[]): number {
    const losses = positions.filter(pos => pos.totalPnL < 0)
    return losses.length > 0 ? losses.reduce((sum, pos) => sum + pos.totalPnL, 0) / losses.length : 0
  }

  private calculateAverageTradeSize(positions: PortfolioPosition[]): number {
    const totalTrades = positions.reduce((sum, pos) => sum + pos.totalTrades, 0)
    const totalValue = positions.reduce((sum, pos) => sum + pos.totalValue, 0)
    return totalTrades > 0 ? totalValue / totalTrades : 0
  }

  private calculatePortfolioRiskScore(positions: PortfolioPosition[]): number {
    if (positions.length === 0) return 0
    
    const avgRisk = positions.map(pos => {
      switch (pos.riskLevel) {
        case 'CRITICAL': return 100
        case 'HIGH': return 75
        case 'MEDIUM': return 50
        case 'LOW': return 25
        default: return 0
      }
    }).reduce((sum, risk) => sum + risk, 0) / positions.length

    return avgRisk
  }

  private getRiskLevel(riskScore: number): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    if (riskScore >= 80) return 'CRITICAL'
    if (riskScore >= 60) return 'HIGH'
    if (riskScore >= 40) return 'MEDIUM'
    return 'LOW'
  }

  private calculateConcentrationRisk(positions: PortfolioPosition[], totalValue: number): number {
    if (positions.length === 0 || totalValue === 0) return 0
    
    const largestPosition = Math.max(...positions.map(pos => pos.totalValue))
    return (largestPosition / totalValue) * 100
  }

  private hasSignificantPriceChange(tokenAddress: string): boolean {
    // Would compare current price with previous price
    // Consider significant if change > 5%
    return Math.random() > 0.7 // Simplified for now
  }

  private getDateRange(period: string) {
    const now = new Date()
    const from = new Date()
    
    switch (period) {
      case 'day':
        from.setDate(now.getDate() - 1)
        break
      case 'week':
        from.setDate(now.getDate() - 7)
        break
      case 'month':
        from.setMonth(now.getMonth() - 1)
        break
      case 'quarter':
        from.setMonth(now.getMonth() - 3)
        break
      case 'year':
        from.setFullYear(now.getFullYear() - 1)
        break
      case 'all':
        from.setFullYear(2020) // Start from 2020
        break
    }
    
    return { from, to: now }
  }

  private async getPortfolioSnapshots(userId: string, from: Date, to: Date): Promise<PortfolioSnapshot[]> {
    try {
      const snapshots = await DatabaseService.client.portfolioSnapshot.findMany({
        where: {
          userId,
          timestamp: { gte: from, lte: to }
        },
        orderBy: { timestamp: 'asc' }
      })

      return snapshots.map(s => ({
        timestamp: s.timestamp,
        totalValue: s.totalValue,
        totalPnL: s.totalPnL,
        totalPnLPercent: s.totalPnLPercent,
        positionCount: s.positionCount,
        riskScore: s.riskScore
      }))
    } catch (error) {
      return []
    }
  }

  private async getTradingActivity(userId: string, from: Date, to: Date): Promise<PortfolioAnalytics['tradingActivity']> {
    // Would aggregate transaction data by date
    return []
  }

  private buildPerformanceChart(snapshots: PortfolioSnapshot[]): PortfolioAnalytics['performanceChart'] {
    return snapshots.map(snapshot => ({
      timestamp: snapshot.timestamp,
      portfolioValue: snapshot.totalValue,
      pnl: snapshot.totalPnL,
      pnlPercent: snapshot.totalPnLPercent
    }))
  }

  private buildRiskMetricsChart(snapshots: PortfolioSnapshot[]): PortfolioAnalytics['riskMetrics'] {
    return snapshots.map(snapshot => ({
      timestamp: snapshot.timestamp,
      riskScore: snapshot.riskScore,
      sharpeRatio: 0, // Would calculate
      maxDrawdown: 0, // Would calculate
      volatility: 0 // Would calculate
    }))
  }

  private async getTopPerformersAndLosers(userId: string, from: Date, to: Date): Promise<[PortfolioAnalytics['topPerformers'], PortfolioAnalytics['topLosers']]> {
    // Would calculate from position performance
    return [[], []]
  }

  private async getRecentSnapshots(userId: string, days: number): Promise<PortfolioSnapshot[]> {
    const from = new Date()
    from.setDate(from.getDate() - days)
    return this.getPortfolioSnapshots(userId, from, new Date())
  }

  private calculateTodaysPnL(snapshots: PortfolioSnapshot[], currentPnL: number): { pnl: number, pnlPercent: number } {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    
    const todayStart = snapshots.find(s => s.timestamp >= today)
    if (!todayStart) {
      return { pnl: 0, pnlPercent: 0 }
    }
    
    const pnlChange = currentPnL - todayStart.totalPnL
    const pnlPercentChange = todayStart.totalPnL !== 0 ? (pnlChange / Math.abs(todayStart.totalPnL)) * 100 : 0
    
    return { pnl: pnlChange, pnlPercent: pnlPercentChange }
  }

  /**
   * Initialize service intervals
   */
  private initializeService(): void {
    // Start real-time updates
    if (this.config.realTimeUpdatesEnabled) {
      this.updateInterval = setInterval(() => {
        this.updateAllPortfolios()
      }, this.config.updateInterval)

      this.priceUpdateInterval = setInterval(() => {
        this.updateAllPrices()
      }, this.config.priceUpdateInterval)
    }
  }

  /**
   * Update all active portfolios
   */
  private async updateAllPortfolios(): Promise<void> {
    try {
      const activeUsers = Array.from(this.portfolioCache.keys())
      await Promise.all(activeUsers.map(userId => this.updatePortfolioPrices(userId)))
    } catch (error) {
      logger.debug('Portfolio update cycle failed:', error)
    }
  }

  /**
   * Update all token prices
   */
  private async updateAllPrices(): Promise<void> {
    try {
      // Get unique token addresses from all cached portfolios
      const tokenAddresses = new Set<string>()
      
      for (const portfolio of this.portfolioCache.values()) {
        portfolio.positions.forEach(pos => tokenAddresses.add(pos.tokenAddress))
      }

      // Update prices for all tokens
      await Promise.all(Array.from(tokenAddresses).map(async (tokenAddress) => {
        try {
          const price = await PriceService.getTokenPrice(tokenAddress)
          this.priceCache.set(tokenAddress, { price, timestamp: Date.now() })
        } catch (error) {
          logger.debug(`Price update failed for ${tokenAddress}:`, error)
        }
      }))
    } catch (error) {
      logger.debug('Price update cycle failed:', error)
    }
  }

  /**
   * Get current configuration
   */
  public getConfig(): PortfolioConfig {
    return { ...this.config }
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<PortfolioConfig>): void {
    this.config = { ...this.config, ...newConfig }
    
    // Restart intervals if timing changed
    if (newConfig.updateInterval || newConfig.priceUpdateInterval) {
      if (this.updateInterval) clearInterval(this.updateInterval)
      if (this.priceUpdateInterval) clearInterval(this.priceUpdateInterval)
      this.initializeService()
    }
    
    logger.info('Portfolio service configuration updated')
  }

  /**
   * Health check
   */
  public async healthCheck(): Promise<boolean> {
    try {
      // Test database connectivity
      await DatabaseService.client.position.count({ take: 1 })
      
      // Check cache sizes
      const portfolioCacheSize = this.portfolioCache.size
      const priceCacheSize = this.priceCache.size
      
      // Consider healthy if caches aren't too large
      return portfolioCacheSize < 10000 && priceCacheSize < 50000
    } catch (error) {
      logger.error('Portfolio service health check failed:', error)
      return false
    }
  }

  /**
   * Shutdown cleanup
   */
  public shutdown(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval)
      this.updateInterval = null
    }

    if (this.priceUpdateInterval) {
      clearInterval(this.priceUpdateInterval)
      this.priceUpdateInterval = null
    }

    this.portfolioCache.clear()
    this.priceCache.clear()
    this.removeAllListeners()
    
    logger.info('Portfolio service shutdown completed')
  }
}

// Export singleton instance
const portfolioServiceInstance = PortfolioService.getInstance()
export { portfolioServiceInstance as PortfolioService }