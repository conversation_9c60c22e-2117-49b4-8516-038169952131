import { EventEmitter } from 'events'
import { logger } from '@/utils/logger'
import { RedisService } from '@/services/redis'
import { config } from '@/config/environment'

export interface LaserStreamConfig {
  apiKey: string
  endpoint: string
  reconnectAttempts?: number
  reconnectDelay?: number
}

export interface SubscribeRequest {
  entry?: any
  accounts?: any
  accountsDataSlice?: any[]
  slots?: any
  blocks?: any
  blocksMeta?: any
  transactions?: any
  transactionsStatus?: any
  commitment?: number // 0 = processed, 1 = confirmed, 2 = finalized
}

export interface TransactionUpdate {
  signature: string
  slot: number
  blockTime?: number
  confirmationStatus: 'processed' | 'confirmed' | 'finalized'
  err?: any
  logs?: string[]
  meta?: any
  transaction?: any
}

export interface AccountUpdate {
  account: string
  slot: number
  data: any
  executable: boolean
  lamports: number
  owner: string
  rentEpoch: number
}

class HeliusLaserStreamService extends EventEmitter {
  private config: LaserStreamConfig
  private ws: WebSocket | null = null
  private isConnected: boolean = false
  private reconnectAttempts: number = 0
  private maxReconnectAttempts: number = 10
  private reconnectDelay: number = 1000
  private subscriptions: Map<string, SubscribeRequest> = new Map()
  private heartbeatInterval: NodeJS.Timeout | null = null
  private connectionId: string | null = null

  constructor(configOverrides?: Partial<LaserStreamConfig>) {
    super()
    
    this.config = {
      apiKey: configOverrides?.apiKey || config.helius.apiKey || '',
      endpoint: configOverrides?.endpoint || 'wss://laserstream-mainnet-ewr.helius-rpc.com',
      reconnectAttempts: configOverrides?.reconnectAttempts || 10,
      reconnectDelay: configOverrides?.reconnectDelay || 1000
    }

    this.maxReconnectAttempts = configOverrides?.reconnectAttempts || 10
    this.reconnectDelay = configOverrides?.reconnectDelay || 1000

    if (!this.config.apiKey) {
      logger.error('Helius API key is required for LaserStream')
      throw new Error('Helius API key is required for LaserStream')
    }
  }

  /**
   * Connect to Helius LaserStream
   */
  public async connect(): Promise<void> {
    try {
      if (this.isConnected) {
        logger.debug('LaserStream already connected')
        return
      }

      const wsUrl = `${this.config.endpoint}?api-key=${this.config.apiKey}`
      logger.info('Connecting to Helius LaserStream:', { endpoint: this.config.endpoint })

      // Use a WebSocket library that works in Node.js
      const WebSocket = await import('ws')
      this.ws = new WebSocket.default(wsUrl) as any

      return new Promise((resolve, reject) => {
        if (!this.ws) {
          reject(new Error('WebSocket initialization failed'))
          return
        }

        this.ws.onopen = () => {
          this.isConnected = true
          this.reconnectAttempts = 0
          this.connectionId = `laserstream-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
          
          logger.info('LaserStream connected successfully', { connectionId: this.connectionId })
          this.emit('connected')
          this.startHeartbeat()
          resolve()
        }

        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)
            this.handleMessage(data)
          } catch (error) {
            logger.error('Failed to parse LaserStream message:', error)
          }
        }

        this.ws.onerror = (error) => {
          logger.warn('LaserStream connection error (API key issue):', error.message || error)
          // Don't emit error to prevent crashes - just log it
          this.isConnected = false
        }

        this.ws.onclose = (event) => {
          this.isConnected = false
          this.stopHeartbeat()
          
          logger.warn('LaserStream connection closed:', { 
            code: event.code, 
            reason: event.reason,
            connectionId: this.connectionId 
          })
          
          this.emit('disconnected', event)
          
          // Attempt to reconnect if not intentionally closed
          if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect()
          }
        }

        // Set connection timeout
        setTimeout(() => {
          if (!this.isConnected) {
            reject(new Error('LaserStream connection timeout'))
          }
        }, 10000) // 10 second timeout
      })

    } catch (error) {
      logger.error('Failed to connect to LaserStream:', error)
      throw error
    }
  }

  /**
   * Disconnect from LaserStream
   */
  public disconnect(): void {
    if (this.ws) {
      this.ws.close(1000, 'Intentional disconnect')
      this.ws = null
    }
    
    this.isConnected = false
    this.stopHeartbeat()
    this.subscriptions.clear()
    
    logger.info('LaserStream disconnected', { connectionId: this.connectionId })
  }

  /**
   * Subscribe to transaction updates for specific signatures
   */
  public async subscribeToTransactions(signatures: string[], userId?: string): Promise<string> {
    const subscriptionId = `tx-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    
    const subscribeRequest: SubscribeRequest = {
      transactions: {
        vote: false,
        failed: false,
        signature: signatures,
        accountInclude: [],
        accountExclude: [],
        accountRequired: []
      },
      commitment: 1 // confirmed
    }

    await this.subscribe(subscriptionId, subscribeRequest)
    
    logger.info('Subscribed to transaction updates', { 
      subscriptionId, 
      signatures: signatures.length,
      userId 
    })

    return subscriptionId
  }

  /**
   * Subscribe to account updates for specific addresses
   */
  public async subscribeToAccounts(accounts: string[], userId?: string): Promise<string> {
    const subscriptionId = `acc-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    
    const subscribeRequest: SubscribeRequest = {
      accounts: {
        accountSubscribe: {
          account: accounts,
          owner: [],
          filters: []
        }
      },
      commitment: 1 // confirmed
    }

    await this.subscribe(subscriptionId, subscribeRequest)
    
    logger.info('Subscribed to account updates', { 
      subscriptionId, 
      accounts: accounts.length,
      userId 
    })

    return subscriptionId
  }

  /**
   * Subscribe to slot updates
   */
  public async subscribeToSlots(): Promise<string> {
    const subscriptionId = `slots-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    
    const subscribeRequest: SubscribeRequest = {
      slots: {
        slot: { filterByCommitment: true }
      },
      commitment: 1 // confirmed
    }

    await this.subscribe(subscriptionId, subscribeRequest)
    
    logger.info('Subscribed to slot updates', { subscriptionId })

    return subscriptionId
  }

  /**
   * Generic subscribe method
   */
  private async subscribe(subscriptionId: string, request: SubscribeRequest): Promise<void> {
    if (!this.isConnected || !this.ws) {
      throw new Error('Not connected to LaserStream')
    }

    this.subscriptions.set(subscriptionId, request)

    const message = {
      jsonrpc: '2.0',
      id: subscriptionId,
      method: 'subscribe',
      params: request
    }

    this.ws.send(JSON.stringify(message))
    
    logger.debug('LaserStream subscription sent', { subscriptionId })
  }

  /**
   * Unsubscribe from a subscription
   */
  public async unsubscribe(subscriptionId: string): Promise<void> {
    if (!this.isConnected || !this.ws) {
      logger.warn('Cannot unsubscribe - not connected to LaserStream')
      return
    }

    const message = {
      jsonrpc: '2.0',
      id: subscriptionId,
      method: 'unsubscribe',
      params: [subscriptionId]
    }

    this.ws.send(JSON.stringify(message))
    this.subscriptions.delete(subscriptionId)
    
    logger.info('Unsubscribed from LaserStream', { subscriptionId })
  }

  /**
   * Handle incoming messages
   */
  private handleMessage(data: any): void {
    try {
      // Handle subscription confirmations
      if (data.id && data.result) {
        logger.debug('LaserStream subscription confirmed', { 
          subscriptionId: data.id,
          result: data.result 
        })
        this.emit('subscriptionConfirmed', data.id, data.result)
        return
      }

      // Handle subscription data
      if (data.params && data.params.subscription) {
        const subscriptionData = data.params.result
        const subscriptionId = data.params.subscription

        // Determine data type and emit appropriate events
        if (subscriptionData.transaction) {
          this.handleTransactionUpdate(subscriptionData, subscriptionId)
        } else if (subscriptionData.account) {
          this.handleAccountUpdate(subscriptionData, subscriptionId)
        } else if (subscriptionData.slot) {
          this.handleSlotUpdate(subscriptionData, subscriptionId)
        } else {
          logger.debug('Unknown LaserStream data type:', subscriptionData)
        }
      }

      // Handle errors
      if (data.error) {
        logger.error('LaserStream error:', data.error)
        this.emit('error', data.error)
      }

    } catch (error) {
      logger.error('Error handling LaserStream message:', error)
    }
  }

  /**
   * Handle transaction updates
   */
  private async handleTransactionUpdate(data: any, subscriptionId: string): Promise<void> {
    try {
      const update: TransactionUpdate = {
        signature: data.transaction.signatures[0],
        slot: data.context?.slot || 0,
        blockTime: data.blockTime,
        confirmationStatus: this.getConfirmationStatus(data.context?.slot),
        err: data.meta?.err,
        logs: data.meta?.logMessages,
        meta: data.meta,
        transaction: data.transaction
      }

      logger.info('Transaction update received', { 
        signature: update.signature,
        status: update.confirmationStatus,
        slot: update.slot,
        subscriptionId 
      })

      // Emit transaction update event
      this.emit('transactionUpdate', update)

      // Publish to Redis for real-time updates
      await RedisService.publishJSON('laserstream_transaction_update', {
        ...update,
        subscriptionId,
        timestamp: Date.now()
      })

    } catch (error) {
      logger.error('Error handling transaction update:', error)
    }
  }

  /**
   * Handle account updates
   */
  private async handleAccountUpdate(data: any, subscriptionId: string): Promise<void> {
    try {
      const update: AccountUpdate = {
        account: data.pubkey,
        slot: data.context?.slot || 0,
        data: data.account?.data,
        executable: data.account?.executable,
        lamports: data.account?.lamports,
        owner: data.account?.owner,
        rentEpoch: data.account?.rentEpoch
      }

      logger.debug('Account update received', { 
        account: update.account,
        slot: update.slot,
        subscriptionId 
      })

      // Emit account update event
      this.emit('accountUpdate', update)

      // Publish to Redis for real-time updates
      await RedisService.publishJSON('laserstream_account_update', {
        ...update,
        subscriptionId,
        timestamp: Date.now()
      })

    } catch (error) {
      logger.error('Error handling account update:', error)
    }
  }

  /**
   * Handle slot updates
   */
  private async handleSlotUpdate(data: any, subscriptionId: string): Promise<void> {
    try {
      logger.debug('Slot update received', { 
        slot: data.slot,
        parent: data.parent,
        root: data.root,
        subscriptionId 
      })

      // Emit slot update event
      this.emit('slotUpdate', data)

      // Update latest slot in Redis
      await RedisService.set('latest_slot', data.slot.toString(), 5)

    } catch (error) {
      logger.error('Error handling slot update:', error)
    }
  }

  /**
   * Get confirmation status based on slot
   */
  private getConfirmationStatus(slot?: number): 'processed' | 'confirmed' | 'finalized' {
    // This is a simplified implementation
    // In reality, you'd compare against current slot and finalized slot
    return 'confirmed'
  }

  /**
   * Start heartbeat to keep connection alive
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected && this.ws) {
        this.ws.ping?.()
      }
    }, 30000) // 30 seconds
  }

  /**
   * Stop heartbeat
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }

  /**
   * Schedule reconnection attempt
   */
  private scheduleReconnect(): void {
    this.reconnectAttempts++
    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), 30000)
    
    logger.info(`Scheduling LaserStream reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`)
    
    setTimeout(() => {
      this.connect().catch(error => {
        logger.error(`LaserStream reconnect attempt ${this.reconnectAttempts} failed:`, error)
      })
    }, delay)
  }

  /**
   * Get connection status
   */
  public getStatus(): {
    isConnected: boolean
    reconnectAttempts: number
    subscriptions: number
    connectionId: string | null
  } {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      subscriptions: this.subscriptions.size,
      connectionId: this.connectionId
    }
  }

  /**
   * Health check
   */
  public async healthCheck(): Promise<boolean> {
    return this.isConnected && this.ws?.readyState === 1 // WebSocket.OPEN
  }
}

// Export singleton instance factory (lazy initialization)
let heliusLaserStreamServiceInstance: HeliusLaserStreamService | null = null

export const heliusLaserStreamService = {
  getInstance(): HeliusLaserStreamService {
    if (!heliusLaserStreamServiceInstance) {
      heliusLaserStreamServiceInstance = new HeliusLaserStreamService()
    }
    return heliusLaserStreamServiceInstance
  },
  
  async connect(): Promise<void> {
    return this.getInstance().connect()
  },
  
  async disconnect(): Promise<void> {
    return this.getInstance().disconnect()
  },
  
  async healthCheck(): Promise<boolean> {
    return this.getInstance().healthCheck()
  }
}

export { HeliusLaserStreamService }