import { config } from '@/config/environment'
import { logger, logStrategy } from '@/utils/logger'
import { DatabaseService } from '@/services/database'
import { RedisService } from '@/services/redis'
import { TradingService } from '@/services/tradingService'
import { PriceService } from '@/services/priceService'
import { NotificationService } from '@/services/notificationService'
import { AppError } from '@/middleware/errorHandler'
import type {
  ExitStrategy,
  CustomStrategy,
  Position,
  StopLossConfig,
  ProfitTarget,
  MoonBagConfig,
  ExecutionResult,
  TokenMarketData
} from '@memetrader-pro/shared'
import { 
  StrategyType, 
  StrategyExecutionState, 
  PositionStatus, 
  AlertType, 
  AlertPriority,
  PresetType 
} from '@memetrader-pro/shared'

interface StrategyTrigger {
  id: string
  strategyId: string
  positionId: string
  type: 'STOP_LOSS' | 'PROFIT_TARGET' | 'TRAILING_STOP' | 'MOON_BAG_EXIT'
  triggerPrice: number
  currentPrice: number
  percentage: number
  sellPercentage: number
  triggered: boolean
  metadata?: Record<string, any>
}

interface StrategyMonitoringState {
  strategy: ExitStrategy
  position: Position
  lastPrice: number
  highestPrice: number
  lowestPrice: number
  trailingStopPrice: number
  activeTriggers: StrategyTrigger[]
  lastUpdate: Date
}

interface PRDCompliantTemplate {
  stopLoss: StopLossConfig
  profitTargets: ProfitTarget[]
  moonBag: MoonBagConfig
  locked: boolean
}

class StrategyServiceClass {
  private static instance: StrategyServiceClass
  private monitoringStates: Map<string, StrategyMonitoringState> = new Map()
  private executionQueue: Map<string, StrategyTrigger> = new Map()
  private monitoringInterval: NodeJS.Timeout | null = null

  // PRD-compliant default template
  private prdCompliantTemplate: PRDCompliantTemplate = {
    stopLoss: {
      percentage: 15,
      trailing: true,
      emergencySlippage: 10
    },
    profitTargets: [
      { percentage: 50, sellPercentage: 15, priority: 1 },
      { percentage: 100, sellPercentage: 15, priority: 2 },
      { percentage: 150, sellPercentage: 15, priority: 3 },
      { percentage: 200, sellPercentage: 15, priority: 4 }
    ],
    moonBag: {
      percentage: 25,
      exitTarget: 500,
      enabled: true
    },
    locked: true
  }

  private constructor() {
    this.startStrategyMonitoring()
  }

  public static getInstance(): StrategyServiceClass {
    if (!StrategyServiceClass.instance) {
      StrategyServiceClass.instance = new StrategyServiceClass()
    }
    return StrategyServiceClass.instance
  }

  /**
   * Create a new exit strategy
   */
  public async createExitStrategy(
    userId: string,
    type: StrategyType,
    config: {
      stopLoss: StopLossConfig
      profitTargets: ProfitTarget[]
      moonBag?: MoonBagConfig
      customName?: string
    },
    positionId?: string
  ): Promise<string> {
    try {
      logStrategy('Creating exit strategy', userId, { type, positionId })

      // Validate PRD compliance if required
      if (type === StrategyType.PRD_COMPLIANT) {
        this.validatePRDCompliance(config)
      }

      // Create strategy in database
      const strategy = await DatabaseService.client.exitStrategy.create({
        data: {
          userId,
          positionId,
          type,
          customName: config.customName,
          stopLoss: config.stopLoss,
          profitTargets: config.profitTargets,
          moonBag: config.moonBag || null,
          executionState: StrategyExecutionState.PENDING,
          locked: type === StrategyType.PRD_COMPLIANT
        }
      })

      // If attached to a position, activate immediately
      if (positionId) {
        await this.activateStrategy(strategy.id, userId)
      }

      logStrategy('Exit strategy created', userId, { strategyId: strategy.id })

      return strategy.id

    } catch (error) {
      logStrategy('Failed to create exit strategy', userId, {
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      throw error
    }
  }

  /**
   * Create custom strategy template
   */
  public async createCustomStrategy(
    userId: string,
    name: string,
    description: string,
    config: {
      stopLoss: StopLossConfig
      profitTargets: ProfitTarget[]
      moonBag?: MoonBagConfig
      riskParameters: any
    }
  ): Promise<string> {
    try {
      logStrategy('Creating custom strategy template', userId, { name })

      // Check PRD compliance
      const prdCompliant = this.checkPRDCompliance(config)

      const customStrategy = await DatabaseService.client.customStrategy.create({
        data: {
          userId,
          name,
          description,
          config,
          prdCompliant
        }
      })

      logStrategy('Custom strategy template created', userId, { 
        strategyId: customStrategy.id,
        prdCompliant
      })

      return customStrategy.id

    } catch (error) {
      logStrategy('Failed to create custom strategy', userId, {
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      throw new AppError('Failed to create custom strategy', 500, 'CUSTOM_STRATEGY_CREATION_FAILED')
    }
  }

  /**
   * Activate strategy for monitoring
   */
  public async activateStrategy(strategyId: string, userId: string): Promise<void> {
    try {
      logStrategy('Activating strategy', userId, { strategyId })

      // Get strategy and position
      const strategy = await DatabaseService.client.exitStrategy.findUnique({
        where: { id: strategyId },
        include: { positions: true }
      })

      if (!strategy) {
        throw new AppError('Strategy not found', 404, 'STRATEGY_NOT_FOUND')
      }

      if (strategy.userId !== userId) {
        throw new AppError('Unauthorized strategy access', 403, 'UNAUTHORIZED')
      }

      // Get position
      let position: any = null
      if (strategy.positionId) {
        position = await DatabaseService.client.position.findUnique({
          where: { id: strategy.positionId }
        })
      } else if (strategy.positions.length > 0) {
        position = strategy.positions[0]
      }

      if (!position) {
        throw new AppError('No position associated with strategy', 400, 'NO_POSITION')
      }

      // Get current market data
      const marketData = await PriceService.getTokenMarketData(position.tokenAddress)
      if (!marketData) {
        throw new AppError('Unable to get market data for strategy activation', 400, 'MARKET_DATA_UNAVAILABLE')
      }

      // Create monitoring state
      const monitoringState: StrategyMonitoringState = {
        strategy: strategy as ExitStrategy,
        position,
        lastPrice: marketData.price,
        highestPrice: marketData.price,
        lowestPrice: marketData.price,
        trailingStopPrice: this.calculateTrailingStopPrice(
          marketData.price,
          parseFloat(position.entryPrice.toString()),
          strategy.stopLoss as StopLossConfig
        ),
        activeTriggers: this.generateTriggers(strategy as ExitStrategy, position, marketData.price),
        lastUpdate: new Date()
      }

      this.monitoringStates.set(strategyId, monitoringState)

      // Update strategy state in database
      await DatabaseService.client.exitStrategy.update({
        where: { id: strategyId },
        data: {
          executionState: StrategyExecutionState.ACTIVE,
          lastTriggered: new Date()
        }
      })

      // Subscribe to price updates for this token
      await PriceService.subscribeToToken(
        position.tokenAddress,
        userId,
        (data) => this.handlePriceUpdate(strategyId, data)
      )

      // Send activation notification
      await NotificationService.sendNotification(
        userId,
        AlertType.STRATEGY_TRIGGER,
        AlertPriority.MEDIUM,
        'Exit Strategy Activated',
        `Strategy "${strategy.customName || strategy.type}" is now monitoring your position`,
        { strategyId, positionId: position.id }
      )

      logStrategy('Strategy activated', userId, { strategyId })

    } catch (error) {
      logStrategy('Failed to activate strategy', userId, {
        strategyId,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      throw error
    }
  }

  /**
   * Handle price update for monitored strategies
   */
  private async handlePriceUpdate(strategyId: string, marketData: TokenMarketData): Promise<void> {
    try {
      const state = this.monitoringStates.get(strategyId)
      if (!state) return

      const currentPrice = marketData.price
      state.lastPrice = currentPrice
      state.lastUpdate = new Date()

      // Update highest/lowest prices
      if (currentPrice > state.highestPrice) {
        state.highestPrice = currentPrice
      }
      if (currentPrice < state.lowestPrice) {
        state.lowestPrice = currentPrice
      }

      // Update trailing stop price if applicable
      const stopLoss = state.strategy.stopLoss as StopLossConfig
      if (stopLoss.trailing) {
        const newTrailingStop = this.calculateTrailingStopPrice(
          currentPrice,
          parseFloat(state.position.entryPrice.toString()),
          stopLoss
        )
        
        // Trailing stop can only move up (for long positions)
        if (newTrailingStop > state.trailingStopPrice) {
          state.trailingStopPrice = newTrailingStop
        }
      }

      // Check for triggers
      await this.checkStrategyTriggers(strategyId, currentPrice)

    } catch (error) {
      logStrategy('Error handling price update', undefined, {
        strategyId,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Check if any strategy triggers should fire
   */
  private async checkStrategyTriggers(strategyId: string, currentPrice: number): Promise<void> {
    try {
      const state = this.monitoringStates.get(strategyId)
      if (!state) return

      const entryPrice = parseFloat(state.position.entryPrice.toString())
      const currentGainPercent = ((currentPrice - entryPrice) / entryPrice) * 100

      // Check stop loss triggers
      await this.checkStopLossTriggers(state, currentPrice, currentGainPercent)

      // Check profit target triggers
      await this.checkProfitTargetTriggers(state, currentPrice, currentGainPercent)

      // Check moon bag exit triggers
      await this.checkMoonBagTriggers(state, currentPrice, currentGainPercent)

    } catch (error) {
      logStrategy('Error checking strategy triggers', undefined, {
        strategyId,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Check stop loss triggers
   */
  private async checkStopLossTriggers(
    state: StrategyMonitoringState,
    currentPrice: number,
    currentGainPercent: number
  ): Promise<void> {
    const stopLoss = state.strategy.stopLoss as StopLossConfig
    const shouldTriggerStopLoss = stopLoss.trailing 
      ? currentPrice <= state.trailingStopPrice
      : currentGainPercent <= -stopLoss.percentage

    if (shouldTriggerStopLoss) {
      const trigger: StrategyTrigger = {
        id: `stop_loss_${Date.now()}`,
        strategyId: state.strategy.id,
        positionId: state.position.id,
        type: 'STOP_LOSS',
        triggerPrice: currentPrice,
        currentPrice,
        percentage: stopLoss.percentage,
        sellPercentage: 100, // Sell entire position on stop loss
        triggered: false,
        metadata: {
          trailing: stopLoss.trailing,
          trailingStopPrice: state.trailingStopPrice,
          emergencySlippage: stopLoss.emergencySlippage
        }
      }

      await this.executeTrigger(trigger, state)
    }
  }

  /**
   * Check profit target triggers
   */
  private async checkProfitTargetTriggers(
    state: StrategyMonitoringState,
    currentPrice: number,
    currentGainPercent: number
  ): Promise<void> {
    const profitTargets = state.strategy.profitTargets as ProfitTarget[]
    
    for (const target of profitTargets) {
      // Check if this target hasn't been triggered yet
      const alreadyTriggered = state.activeTriggers.some(
        t => t.type === 'PROFIT_TARGET' && t.percentage === target.percentage && t.triggered
      )

      if (!alreadyTriggered && currentGainPercent >= target.percentage) {
        const trigger: StrategyTrigger = {
          id: `profit_target_${target.percentage}_${Date.now()}`,
          strategyId: state.strategy.id,
          positionId: state.position.id,
          type: 'PROFIT_TARGET',
          triggerPrice: currentPrice,
          currentPrice,
          percentage: target.percentage,
          sellPercentage: target.sellPercentage,
          triggered: false,
          metadata: {
            priority: target.priority,
            targetGain: target.percentage
          }
        }

        await this.executeTrigger(trigger, state)
      }
    }
  }

  /**
   * Check moon bag exit triggers
   */
  private async checkMoonBagTriggers(
    state: StrategyMonitoringState,
    currentPrice: number,
    currentGainPercent: number
  ): Promise<void> {
    const moonBag = state.strategy.moonBag as MoonBagConfig
    if (!moonBag?.enabled) return

    // Check if moon bag exit target is reached
    if (currentGainPercent >= moonBag.exitTarget) {
      const trigger: StrategyTrigger = {
        id: `moon_bag_exit_${Date.now()}`,
        strategyId: state.strategy.id,
        positionId: state.position.id,
        type: 'MOON_BAG_EXIT',
        triggerPrice: currentPrice,
        currentPrice,
        percentage: moonBag.exitTarget,
        sellPercentage: moonBag.percentage,
        triggered: false,
        metadata: {
          moonBagTarget: moonBag.exitTarget,
          moonBagPercentage: moonBag.percentage
        }
      }

      await this.executeTrigger(trigger, state)
    }
  }

  /**
   * Execute a strategy trigger
   */
  private async executeTrigger(trigger: StrategyTrigger, state: StrategyMonitoringState): Promise<void> {
    try {
      logStrategy('Executing strategy trigger', state.strategy.userId, {
        triggerId: trigger.id,
        type: trigger.type,
        price: trigger.triggerPrice
      })

      // Mark trigger as triggered
      trigger.triggered = true
      state.activeTriggers.push(trigger)

      // Calculate sell amount
      const positionQuantity = parseFloat(state.position.quantity.toString())
      const sellQuantity = (positionQuantity * trigger.sellPercentage) / 100

      // Execute the trade
      const executionResult = await this.executeSell(
        state.strategy.userId,
        state.position.tokenAddress,
        sellQuantity,
        trigger
      )

      if (executionResult.success) {
        // Update position quantity
        const newQuantity = positionQuantity - sellQuantity
        await DatabaseService.client.position.update({
          where: { id: state.position.id },
          data: {
            quantity: newQuantity,
            status: newQuantity <= 0 ? PositionStatus.CLOSED : PositionStatus.ACTIVE
          }
        })

        // Update strategy execution count
        await DatabaseService.client.exitStrategy.update({
          where: { id: state.strategy.id },
          data: {
            totalTriggers: { increment: 1 },
            lastTriggered: new Date(),
            executionState: newQuantity <= 0 ? StrategyExecutionState.COMPLETED : StrategyExecutionState.ACTIVE
          }
        })

        // Send notification
        await NotificationService.sendNotification(
          state.strategy.userId,
          AlertType.STRATEGY_TRIGGER,
          AlertPriority.HIGH,
          `${trigger.type.replace('_', ' ')} Triggered`,
          `Sold ${trigger.sellPercentage}% of position at ${trigger.currentPrice.toFixed(6)} (+${((trigger.currentPrice / parseFloat(state.position.entryPrice.toString()) - 1) * 100).toFixed(2)}%)`,
          {
            strategyId: state.strategy.id,
            positionId: state.position.id,
            trigger,
            executionResult
          }
        )

        // If position is fully closed, deactivate strategy
        if (newQuantity <= 0) {
          await this.deactivateStrategy(state.strategy.id)
        }

        logStrategy('Strategy trigger executed successfully', state.strategy.userId, {
          triggerId: trigger.id,
          sellQuantity,
          transactionHash: executionResult.transactionHash
        })

      } else {
        logStrategy('Strategy trigger execution failed', state.strategy.userId, {
          triggerId: trigger.id,
          error: executionResult.error
        })

        // Send error notification
        await NotificationService.sendNotification(
          state.strategy.userId,
          AlertType.ERROR_ALERT,
          AlertPriority.CRITICAL,
          'Strategy Execution Failed',
          `Failed to execute ${trigger.type}: ${executionResult.error}`,
          { strategyId: state.strategy.id, trigger, error: executionResult.error }
        )
      }

    } catch (error) {
      logStrategy('Error executing trigger', state.strategy.userId, {
        triggerId: trigger.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Execute sell order for strategy trigger
   */
  private async executeSell(
    userId: string,
    tokenAddress: string,
    sellQuantity: number,
    trigger: StrategyTrigger
  ): Promise<ExecutionResult> {
    try {
      // Determine slippage based on trigger type
      let slippage = 1.0 // Default 1%
      if (trigger.type === 'STOP_LOSS' && trigger.metadata?.emergencySlippage) {
        slippage = trigger.metadata.emergencySlippage
      }

      // Execute trade via TradingService
      const tradeResult = await TradingService.executeTrade(
        {
          tokenIn: tokenAddress,
          tokenOut: 'So11111111111111111111111111111111111111112', // SOL
          amount: sellQuantity,
          slippage,
          preset: PresetType.DEFAULT,
          strategyId: trigger.strategyId
        },
        userId, // This would normally be the wallet public key
        userId
      )

      return {
        success: tradeResult.success,
        transactionHash: tradeResult.transactionHash,
        amountExecuted: sellQuantity,
        priceExecuted: trigger.triggerPrice,
        fees: tradeResult.quote?.fees || { jupiterFee: 0, priorityFee: 0, networkFee: 0, total: 0 },
        error: tradeResult.error
      }

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        amountExecuted: 0,
        priceExecuted: 0,
        fees: { jupiterFee: 0, priorityFee: 0, networkFee: 0, total: 0 }
      }
    }
  }

  /**
   * Deactivate strategy
   */
  public async deactivateStrategy(strategyId: string): Promise<void> {
    try {
      // Remove from monitoring
      const state = this.monitoringStates.get(strategyId)
      if (state) {
        // Unsubscribe from price updates
        await PriceService.unsubscribeFromToken(
          state.position.tokenAddress,
          state.strategy.userId
        )

        this.monitoringStates.delete(strategyId)
      }

      // Update database
      await DatabaseService.client.exitStrategy.update({
        where: { id: strategyId },
        data: {
          executionState: StrategyExecutionState.COMPLETED
        }
      })

      logStrategy('Strategy deactivated', undefined, { strategyId })

    } catch (error) {
      logStrategy('Error deactivating strategy', undefined, {
        strategyId,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Generate strategy triggers
   */
  private generateTriggers(
    strategy: ExitStrategy,
    position: any,
    currentPrice: number
  ): StrategyTrigger[] {
    const triggers: StrategyTrigger[] = []
    const entryPrice = parseFloat(position.entryPrice.toString())

    // Generate profit target triggers
    const profitTargets = strategy.profitTargets as ProfitTarget[]
    for (const target of profitTargets) {
      triggers.push({
        id: `profit_target_${target.percentage}`,
        strategyId: strategy.id,
        positionId: position.id,
        type: 'PROFIT_TARGET',
        triggerPrice: entryPrice * (1 + target.percentage / 100),
        currentPrice,
        percentage: target.percentage,
        sellPercentage: target.sellPercentage,
        triggered: false
      })
    }

    return triggers
  }

  /**
   * Calculate trailing stop price
   */
  private calculateTrailingStopPrice(
    currentPrice: number,
    entryPrice: number,
    stopLoss: StopLossConfig
  ): number {
    if (!stopLoss.trailing) {
      return entryPrice * (1 - stopLoss.percentage / 100)
    }

    // For trailing stops, the stop price follows the market price
    return currentPrice * (1 - stopLoss.percentage / 100)
  }

  /**
   * Validate PRD compliance
   */
  private validatePRDCompliance(config: any): void {
    const template = this.prdCompliantTemplate

    // Check stop loss
    if (config.stopLoss.percentage !== template.stopLoss.percentage) {
      throw new AppError('PRD-compliant strategy requires 15% stop loss', 400, 'PRD_VIOLATION')
    }

    if (!config.stopLoss.trailing) {
      throw new AppError('PRD-compliant strategy requires trailing stop loss', 400, 'PRD_VIOLATION')
    }

    // Check profit targets
    if (config.profitTargets.length !== template.profitTargets.length) {
      throw new AppError('PRD-compliant strategy requires exactly 4 profit targets', 400, 'PRD_VIOLATION')
    }

    for (let i = 0; i < template.profitTargets.length; i++) {
      const expected = template.profitTargets[i]
      const actual = config.profitTargets[i]

      if (actual.percentage !== expected.percentage || actual.sellPercentage !== expected.sellPercentage) {
        throw new AppError(`PRD-compliant strategy profit target ${i + 1} must be ${expected.percentage}% gain with ${expected.sellPercentage}% sell`, 400, 'PRD_VIOLATION')
      }
    }

    // Check moon bag
    if (!config.moonBag?.enabled || config.moonBag.percentage !== template.moonBag.percentage) {
      throw new AppError('PRD-compliant strategy requires 25% moon bag', 400, 'PRD_VIOLATION')
    }
  }

  /**
   * Check PRD compliance (returns boolean)
   */
  private checkPRDCompliance(config: any): boolean {
    try {
      this.validatePRDCompliance(config)
      return true
    } catch {
      return false
    }
  }

  /**
   * Get PRD-compliant template
   */
  public getPRDCompliantTemplate(): PRDCompliantTemplate {
    return { ...this.prdCompliantTemplate }
  }

  /**
   * Get user strategies
   */
  public async getUserStrategies(userId: string, options: { activeOnly?: boolean } = {}) {
    const { getUserStrategies } = await import('@/services/tradingHelpers')
    return getUserStrategies(userId, options)
  }

  /**
   * Get custom strategies
   */
  public async getCustomStrategies(userId: string) {
    const { getCustomStrategies } = await import('@/services/tradingHelpers')
    return getCustomStrategies(userId)
  }

  /**
   * Start strategy monitoring service
   */
  private startStrategyMonitoring(): void {
    // Monitor strategies every 500ms for high-frequency updates
    this.monitoringInterval = setInterval(async () => {
      try {
        // Check execution queue
        for (const [triggerId, trigger] of this.executionQueue) {
          const state = this.monitoringStates.get(trigger.strategyId)
          if (state) {
            await this.executeTrigger(trigger, state)
            this.executionQueue.delete(triggerId)
          }
        }

        // Update position P&L for monitored strategies
        for (const [strategyId, state] of this.monitoringStates) {
          if (Date.now() - state.lastUpdate.getTime() > 60000) { // 1 minute timeout
            // Remove stale monitoring states
            this.monitoringStates.delete(strategyId)
            continue
          }

          // Update position current price and P&L
          await DatabaseService.client.position.update({
            where: { id: state.position.id },
            data: {
              currentPrice: state.lastPrice,
              pnl: (state.lastPrice - parseFloat(state.position.entryPrice.toString())) * parseFloat(state.position.quantity.toString()),
              pnlPercent: ((state.lastPrice / parseFloat(state.position.entryPrice.toString())) - 1) * 100
            }
          })
        }

      } catch (error) {
        logger.error('Strategy monitoring error:', error)
      }
    }, 500) // 500ms interval for real-time monitoring
  }

  /**
   * Get strategy performance analytics
   */
  public async getStrategyAnalytics(strategyId: string, userId: string): Promise<any> {
    try {
      const strategy = await DatabaseService.client.exitStrategy.findUnique({
        where: { id: strategyId },
        include: {
          positions: {
            include: {
              transactions: true
            }
          }
        }
      })

      if (!strategy || strategy.userId !== userId) {
        throw new AppError('Strategy not found', 404, 'STRATEGY_NOT_FOUND')
      }

      // Calculate analytics
      const totalTriggers = strategy.totalTriggers
      const totalPnl = parseFloat(strategy.totalPnl.toString())
      const successRate = strategy.successRate

      return {
        strategyId,
        type: strategy.type,
        totalTriggers,
        totalPnl,
        successRate,
        executionState: strategy.executionState,
        createdAt: strategy.createdAt,
        lastTriggered: strategy.lastTriggered
      }

    } catch (error) {
      logStrategy('Failed to get strategy analytics', userId, {
        strategyId,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      throw error
    }
  }

  /**
   * Health check
   */
  public async healthCheck(): Promise<boolean> {
    try {
      // Test database connectivity
      await DatabaseService.client.exitStrategy.findFirst()
      
      // Check monitoring service
      const isMonitoring = this.monitoringInterval !== null
      const activeStrategies = this.monitoringStates.size

      logger.info('Strategy service health check:', {
        isMonitoring,
        activeStrategies,
        executionQueueSize: this.executionQueue.size
      })

      return true
    } catch (error) {
      logger.error('Strategy service health check failed:', error)
      return false
    }
  }

  /**
   * Shutdown gracefully
   */
  public async shutdown(): Promise<void> {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = null
    }

    // Unsubscribe from all price feeds
    for (const [strategyId, state] of this.monitoringStates) {
      try {
        await PriceService.unsubscribeFromToken(
          state.position.tokenAddress,
          state.strategy.userId
        )
      } catch (error) {
        logger.error('Error unsubscribing from price feed during shutdown:', error)
      }
    }

    this.monitoringStates.clear()
    this.executionQueue.clear()

    logStrategy('Strategy service shutdown completed')
  }
}

// Export singleton instance
export const StrategyService = StrategyServiceClass.getInstance()