import nodemailer from 'nodemailer'
import axios from 'axios'
import { config } from '@/config/environment'
import { logger, logNotification } from '@/utils/logger'
import { DatabaseService } from '@/services/database'
import { RedisService } from '@/services/redis'
import { WebSocketService } from '@/services/websocket'
import { AppError } from '@/middleware/errorHandler'
import type { Alert } from '@memetrader-pro/shared'
import { AlertType, AlertPriority } from '@memetrader-pro/shared'

interface NotificationChannel {
  type: 'email' | 'desktop' | 'sound' | 'webhook' | 'websocket'
  enabled: boolean
  config: Record<string, any>
}

interface NotificationConfig {
  channels: NotificationChannel[]
  quietHours: {
    enabled: boolean
    start: string // "22:00"
    end: string   // "08:00"
    highPriorityOverride: boolean
  }
  filters: {
    minPriority: AlertPriority
    categories: AlertType[]
    tokens: string[]
  }
}

interface NotificationTemplate {
  subject: string
  title: string
  message: string
  html?: string
  metadata?: Record<string, any>
}

interface DeliveryResult {
  success: boolean
  channel: string
  messageId?: string
  error?: string
  deliveredAt: Date
}

class NotificationServiceClass {
  private static instance: NotificationServiceClass
  private emailTransporter: nodemailer.Transporter | null = null
  private defaultConfig: NotificationConfig = {
    channels: [
      { type: 'websocket', enabled: true, config: {} },
      { type: 'desktop', enabled: true, config: {} },
      { type: 'sound', enabled: false, config: {} },
      { type: 'email', enabled: false, config: {} },
      { type: 'webhook', enabled: false, config: {} }
    ],
    quietHours: {
      enabled: false,
      start: '22:00',
      end: '08:00',
      highPriorityOverride: true
    },
    filters: {
      minPriority: AlertPriority.LOW,
      categories: Object.values(AlertType),
      tokens: []
    }
  }

  private constructor() {
    this.initializeEmailTransporter()
  }

  public static getInstance(): NotificationServiceClass {
    if (!NotificationServiceClass.instance) {
      NotificationServiceClass.instance = new NotificationServiceClass()
    }
    return NotificationServiceClass.instance
  }

  /**
   * Initialize email transporter
   */
  private initializeEmailTransporter(): void {
    try {
      if (process.env.SMTP_HOST && process.env.SMTP_USER && process.env.SMTP_PASS) {
        this.emailTransporter = nodemailer.createTransporter({
          host: process.env.SMTP_HOST,
          port: parseInt(process.env.SMTP_PORT || '587'),
          secure: process.env.SMTP_SECURE === 'true',
          auth: {
            user: process.env.SMTP_USER,
            pass: process.env.SMTP_PASS
          }
        })

        logNotification('Email transporter initialized')
      } else {
        logNotification('Email configuration not found - email notifications disabled')
      }
    } catch (error) {
      logNotification('Failed to initialize email transporter', undefined, {
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Send notification to user
   */
  public async sendNotification(
    userId: string,
    alertType: AlertType,
    priority: AlertPriority,
    title: string,
    message: string,
    metadata?: Record<string, any>
  ): Promise<string> {
    try {
      logNotification('Sending notification', userId, {
        alertType,
        priority,
        title
      })

      // 1. Create alert record in database
      const alert = await DatabaseService.client.alert.create({
        data: {
          userId,
          type: alertType,
          priority,
          title,
          message,
          metadata: metadata || {},
          actionable: this.isActionableAlert(alertType),
          timestamp: new Date()
        }
      })

      // 2. Get user notification preferences
      const notificationConfig = await this.getUserNotificationConfig(userId)

      // 3. Check if notification should be sent (quiet hours, filters, etc.)
      const shouldSend = await this.shouldSendNotification(userId, alertType, priority, notificationConfig)
      
      if (!shouldSend) {
        logNotification('Notification filtered out', userId, { alertId: alert.id, reason: 'quiet hours or filters' })
        return alert.id
      }

      // 4. Generate notification template
      const template = this.generateNotificationTemplate(alertType, title, message, metadata)

      // 5. Send via enabled channels
      const deliveryPromises = notificationConfig.channels
        .filter(channel => channel.enabled)
        .map(channel => this.deliverNotification(userId, channel, template, alert.id))

      const results = await Promise.allSettled(deliveryPromises)

      // 6. Update alert with delivery status
      const deliveryResults: DeliveryResult[] = []
      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          deliveryResults.push(result.value)
        } else {
          deliveryResults.push({
            success: false,
            channel: notificationConfig.channels[index].type,
            error: result.reason?.message || 'Unknown error',
            deliveredAt: new Date()
          })
        }
      })

      const successfulDeliveries = deliveryResults.filter(r => r.success)
      const failedDeliveries = deliveryResults.filter(r => !r.success)

      await DatabaseService.client.alert.update({
        where: { id: alert.id },
        data: {
          deliveredAt: successfulDeliveries.length > 0 ? new Date() : null,
          channels: deliveryResults,
          retryCount: failedDeliveries.length
        }
      })

      logNotification('Notification sent', userId, {
        alertId: alert.id,
        successful: successfulDeliveries.length,
        failed: failedDeliveries.length
      })

      // 7. If high priority and some deliveries failed, schedule retry
      if (priority === AlertPriority.HIGH || priority === AlertPriority.CRITICAL) {
        if (failedDeliveries.length > 0) {
          await this.scheduleRetry(alert.id, failedDeliveries, userId)
        }
      }

      return alert.id

    } catch (error) {
      logNotification('Failed to send notification', userId, {
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      throw new AppError('Failed to send notification', 500, 'NOTIFICATION_FAILED')
    }
  }

  /**
   * Deliver notification via specific channel
   */
  private async deliverNotification(
    userId: string,
    channel: NotificationChannel,
    template: NotificationTemplate,
    alertId: string
  ): Promise<DeliveryResult> {
    try {
      switch (channel.type) {
        case 'websocket':
          return await this.deliverWebSocketNotification(userId, template, alertId)
        
        case 'email':
          return await this.deliverEmailNotification(userId, template, alertId, channel.config)
        
        case 'desktop':
          return await this.deliverDesktopNotification(userId, template, alertId)
        
        case 'sound':
          return await this.deliverSoundNotification(userId, template, alertId)
        
        case 'webhook':
          return await this.deliverWebhookNotification(userId, template, alertId, channel.config)
        
        default:
          throw new Error(`Unsupported channel type: ${channel.type}`)
      }
    } catch (error) {
      return {
        success: false,
        channel: channel.type,
        error: error instanceof Error ? error.message : 'Unknown error',
        deliveredAt: new Date()
      }
    }
  }

  /**
   * Deliver WebSocket notification
   */
  private async deliverWebSocketNotification(
    userId: string,
    template: NotificationTemplate,
    alertId: string
  ): Promise<DeliveryResult> {
    try {
      WebSocketService.sendToUser(userId, {
        type: 'ALERT' as any,
        data: {
          id: alertId,
          title: template.title,
          message: template.message,
          metadata: template.metadata,
          timestamp: Date.now()
        },
        timestamp: Date.now()
      })

      return {
        success: true,
        channel: 'websocket',
        messageId: alertId,
        deliveredAt: new Date()
      }
    } catch (error) {
      throw new Error(`WebSocket delivery failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Deliver email notification
   */
  private async deliverEmailNotification(
    userId: string,
    template: NotificationTemplate,
    alertId: string,
    channelConfig: Record<string, any>
  ): Promise<DeliveryResult> {
    try {
      if (!this.emailTransporter) {
        throw new Error('Email transporter not configured')
      }

      // Get user email
      const user = await DatabaseService.client.user.findUnique({
        where: { id: userId },
        select: { email: true }
      })

      if (!user?.email) {
        throw new Error('User email not found')
      }

      const mailOptions = {
        from: process.env.SMTP_FROM || 'MemeTrader Pro <<EMAIL>>',
        to: user.email,
        subject: template.subject,
        text: template.message,
        html: template.html || `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">${template.title}</h2>
            <p style="color: #666; line-height: 1.6;">${template.message}</p>
            <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
            <p style="color: #999; font-size: 12px;">
              This is an automated notification from MemeTrader Pro.
              <br>Alert ID: ${alertId}
            </p>
          </div>
        `
      }

      const result = await this.emailTransporter.sendMail(mailOptions)

      return {
        success: true,
        channel: 'email',
        messageId: result.messageId,
        deliveredAt: new Date()
      }
    } catch (error) {
      throw new Error(`Email delivery failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Deliver desktop notification
   */
  private async deliverDesktopNotification(
    userId: string,
    template: NotificationTemplate,
    alertId: string
  ): Promise<DeliveryResult> {
    try {
      // Send desktop notification via WebSocket with special type
      WebSocketService.sendToUser(userId, {
        type: 'DESKTOP_NOTIFICATION' as any,
        data: {
          id: alertId,
          title: template.title,
          message: template.message,
          icon: this.getNotificationIcon(template.metadata?.type),
          timestamp: Date.now()
        },
        timestamp: Date.now()
      })

      return {
        success: true,
        channel: 'desktop',
        messageId: alertId,
        deliveredAt: new Date()
      }
    } catch (error) {
      throw new Error(`Desktop notification failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Deliver sound notification
   */
  private async deliverSoundNotification(
    userId: string,
    template: NotificationTemplate,
    alertId: string
  ): Promise<DeliveryResult> {
    try {
      // Send sound notification trigger via WebSocket
      WebSocketService.sendToUser(userId, {
        type: 'SOUND_NOTIFICATION' as any,
        data: {
          id: alertId,
          soundType: this.getSoundType(template.metadata?.priority),
          volume: 0.7,
          timestamp: Date.now()
        },
        timestamp: Date.now()
      })

      return {
        success: true,
        channel: 'sound',
        messageId: alertId,
        deliveredAt: new Date()
      }
    } catch (error) {
      throw new Error(`Sound notification failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Deliver webhook notification
   */
  private async deliverWebhookNotification(
    userId: string,
    template: NotificationTemplate,
    alertId: string,
    channelConfig: Record<string, any>
  ): Promise<DeliveryResult> {
    try {
      const webhookUrl = channelConfig.url
      if (!webhookUrl) {
        throw new Error('Webhook URL not configured')
      }

      const payload = {
        id: alertId,
        userId,
        title: template.title,
        message: template.message,
        metadata: template.metadata,
        timestamp: new Date().toISOString()
      }

      const response = await axios.post(webhookUrl, payload, {
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'MemeTrader-Pro-Notifications/1.0',
          ...channelConfig.headers
        },
        timeout: 10000
      })

      return {
        success: response.status >= 200 && response.status < 300,
        channel: 'webhook',
        messageId: response.data?.messageId || alertId,
        deliveredAt: new Date()
      }
    } catch (error) {
      throw new Error(`Webhook delivery failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Generate notification template based on alert type
   */
  private generateNotificationTemplate(
    alertType: AlertType,
    title: string,
    message: string,
    metadata?: Record<string, any>
  ): NotificationTemplate {
    const templates: Record<AlertType, Partial<NotificationTemplate>> = {
      [AlertType.TRADE_EXECUTED]: {
        subject: '🚀 Trade Executed - MemeTrader Pro',
        title: `🚀 ${title}`,
      },
      [AlertType.POSITION_UPDATE]: {
        subject: '📊 Position Update - MemeTrader Pro',
        title: `📊 ${title}`,
      },
      [AlertType.STRATEGY_TRIGGER]: {
        subject: '⚡ Strategy Triggered - MemeTrader Pro',
        title: `⚡ ${title}`,
      },
      [AlertType.PRICE_ALERT]: {
        subject: '💰 Price Alert - MemeTrader Pro',
        title: `💰 ${title}`,
      },
      [AlertType.SYSTEM_ALERT]: {
        subject: '🔧 System Alert - MemeTrader Pro',
        title: `🔧 ${title}`,
      },
      [AlertType.ERROR_ALERT]: {
        subject: '❌ Error Alert - MemeTrader Pro',
        title: `❌ ${title}`,
      }
    }

    const template = templates[alertType] || {}

    return {
      subject: template.subject || `🔔 ${title} - MemeTrader Pro`,
      title: template.title || title,
      message,
      metadata
    }
  }

  /**
   * Get user notification configuration
   */
  private async getUserNotificationConfig(userId: string): Promise<NotificationConfig> {
    try {
      const userPrefs = await DatabaseService.client.userPreferences.findUnique({
        where: { userId }
      })

      if (!userPrefs) {
        return this.defaultConfig
      }

      // Build configuration from user preferences
      const config: NotificationConfig = {
        channels: [
          { type: 'websocket', enabled: true, config: {} },
          { type: 'desktop', enabled: userPrefs.desktopAlerts, config: {} },
          { type: 'sound', enabled: userPrefs.soundAlerts, config: {} },
          { type: 'email', enabled: userPrefs.emailAlerts, config: {} },
          { type: 'webhook', enabled: false, config: {} }
        ],
        quietHours: {
          enabled: userPrefs.quietHoursEnabled,
          start: userPrefs.quietHoursStart || '22:00',
          end: userPrefs.quietHoursEnd || '08:00',
          highPriorityOverride: true
        },
        filters: {
          minPriority: AlertPriority.LOW,
          categories: Object.values(AlertType),
          tokens: []
        }
      }

      return config
    } catch (error) {
      logNotification('Failed to get user notification config', userId, {
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      return this.defaultConfig
    }
  }

  /**
   * Check if notification should be sent based on filters and quiet hours
   */
  private async shouldSendNotification(
    userId: string,
    alertType: AlertType,
    priority: AlertPriority,
    config: NotificationConfig
  ): Promise<boolean> {
    try {
      // Check priority filter
      const priorityOrder = [AlertPriority.LOW, AlertPriority.MEDIUM, AlertPriority.HIGH, AlertPriority.CRITICAL]
      const minPriorityIndex = priorityOrder.indexOf(config.filters.minPriority)
      const alertPriorityIndex = priorityOrder.indexOf(priority)
      
      if (alertPriorityIndex < minPriorityIndex) {
        return false
      }

      // Check category filter
      if (!config.filters.categories.includes(alertType)) {
        return false
      }

      // Check quiet hours
      if (config.quietHours.enabled && !config.quietHours.highPriorityOverride) {
        const now = new Date()
        const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`
        
        if (this.isInQuietHours(currentTime, config.quietHours.start, config.quietHours.end)) {
          return false
        }
      }

      // High/Critical priority overrides quiet hours
      if (config.quietHours.enabled && config.quietHours.highPriorityOverride) {
        if (priority === AlertPriority.HIGH || priority === AlertPriority.CRITICAL) {
          return true
        }

        const now = new Date()
        const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`
        
        if (this.isInQuietHours(currentTime, config.quietHours.start, config.quietHours.end)) {
          return false
        }
      }

      return true
    } catch (error) {
      logNotification('Error checking notification filters', userId, {
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      return true // Default to sending on error
    }
  }

  /**
   * Check if current time is in quiet hours
   */
  private isInQuietHours(currentTime: string, startTime: string, endTime: string): boolean {
    const [currentHour, currentMinute] = currentTime.split(':').map(Number)
    const [startHour, startMinute] = startTime.split(':').map(Number)
    const [endHour, endMinute] = endTime.split(':').map(Number)

    const currentMinutes = currentHour * 60 + currentMinute
    const startMinutes = startHour * 60 + startMinute
    const endMinutes = endHour * 60 + endMinute

    if (startMinutes <= endMinutes) {
      // Same day quiet hours (e.g., 22:00 - 23:59)
      return currentMinutes >= startMinutes && currentMinutes <= endMinutes
    } else {
      // Overnight quiet hours (e.g., 22:00 - 08:00)
      return currentMinutes >= startMinutes || currentMinutes <= endMinutes
    }
  }

  /**
   * Determine if alert type is actionable
   */
  private isActionableAlert(alertType: AlertType): boolean {
    const actionableTypes = [
      AlertType.TRADE_EXECUTED,
      AlertType.STRATEGY_TRIGGER,
      AlertType.PRICE_ALERT,
      AlertType.ERROR_ALERT
    ]

    return actionableTypes.includes(alertType)
  }

  /**
   * Get notification icon based on alert type
   */
  private getNotificationIcon(alertType?: AlertType): string {
    const icons: Record<AlertType, string> = {
      [AlertType.TRADE_EXECUTED]: '🚀',
      [AlertType.POSITION_UPDATE]: '📊',
      [AlertType.STRATEGY_TRIGGER]: '⚡',
      [AlertType.PRICE_ALERT]: '💰',
      [AlertType.SYSTEM_ALERT]: '🔧',
      [AlertType.ERROR_ALERT]: '❌'
    }

    return alertType ? icons[alertType] || '🔔' : '🔔'
  }

  /**
   * Get sound type based on priority
   */
  private getSoundType(priority?: AlertPriority): string {
    const sounds: Record<AlertPriority, string> = {
      [AlertPriority.LOW]: 'notification',
      [AlertPriority.MEDIUM]: 'alert',
      [AlertPriority.HIGH]: 'urgent',
      [AlertPriority.CRITICAL]: 'emergency'
    }

    return priority ? sounds[priority] || 'notification' : 'notification'
  }

  /**
   * Schedule retry for failed deliveries
   */
  private async scheduleRetry(
    alertId: string,
    failedDeliveries: DeliveryResult[],
    userId: string
  ): Promise<void> {
    try {
      // Simple retry mechanism - in production, you'd use a proper job queue
      setTimeout(async () => {
        logNotification('Retrying failed notification deliveries', userId, {
          alertId,
          failedChannels: failedDeliveries.map(d => d.channel)
        })

        // Increment retry count
        await DatabaseService.client.alert.update({
          where: { id: alertId },
          data: {
            retryCount: {
              increment: 1
            }
          }
        })

        // TODO: Implement actual retry logic
        // This would typically involve re-queuing the notification job
      }, 60000) // Retry after 1 minute

    } catch (error) {
      logNotification('Failed to schedule retry', userId, {
        alertId,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Mark alert as read
   */
  public async markAlertAsRead(alertId: string, userId: string): Promise<void> {
    try {
      await DatabaseService.client.alert.updateMany({
        where: {
          id: alertId,
          userId
        },
        data: {
          read: true
        }
      })

      logNotification('Alert marked as read', userId, { alertId })
    } catch (error) {
      logNotification('Failed to mark alert as read', userId, {
        alertId,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      throw new AppError('Failed to mark alert as read', 500, 'ALERT_UPDATE_FAILED')
    }
  }

  /**
   * Get user alerts with pagination
   */
  public async getUserAlerts(
    userId: string,
    options: {
      limit?: number
      offset?: number
      unreadOnly?: boolean
      priority?: AlertPriority
      type?: AlertType
    } = {}
  ): Promise<{ alerts: Alert[]; total: number }> {
    try {
      const {
        limit = 20,
        offset = 0,
        unreadOnly = false,
        priority,
        type
      } = options

      const where: any = { userId }
      
      if (unreadOnly) {
        where.read = false
      }
      
      if (priority) {
        where.priority = priority
      }
      
      if (type) {
        where.type = type
      }

      const [alerts, total] = await Promise.all([
        DatabaseService.client.alert.findMany({
          where,
          orderBy: { timestamp: 'desc' },
          skip: offset,
          take: limit
        }),
        DatabaseService.client.alert.count({ where })
      ])

      return { alerts: alerts as Alert[], total }
    } catch (error) {
      logNotification('Failed to get user alerts', userId, {
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      throw new AppError('Failed to get alerts', 500, 'ALERTS_FETCH_FAILED')
    }
  }

  /**
   * Health check for notification service
   */
  public async healthCheck(): Promise<boolean> {
    try {
      // Test database connectivity
      await DatabaseService.client.alert.findFirst()
      
      // Test Redis connectivity
      await RedisService.mainClient.ping()

      // Test email transporter if configured
      if (this.emailTransporter) {
        await this.emailTransporter.verify()
      }

      return true
    } catch (error) {
      logger.error('Notification service health check failed:', error)
      return false
    }
  }
}

// Export singleton instance
export const NotificationService = NotificationServiceClass.getInstance()