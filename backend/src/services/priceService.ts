import WebSocket from 'ws'
import axios from 'axios'
import { config } from '@/config/environment'
import { logger, logPrice } from '@/utils/logger'
import { DatabaseService } from '@/services/database'
import { RedisService } from '@/services/redis'
import { AppError } from '@/middleware/errorHandler'
import type { TokenMarketData } from '@memetrader-pro/shared'

interface HeliusPriceData {
  tokenAddress: string
  price: number
  priceUsd: number
  change24h: number
  volume24h: number
  marketCap: number
  liquidity?: number
  holders?: number
  lastUpdated: number
}

interface HeliusWebSocketMessage {
  jsonrpc: string
  method: string
  params: {
    result: {
      context: { slot: number }
      value: any
    }
    subscription: number
  }
}

interface PriceSubscription {
  tokenAddress: string
  userId?: string
  callback?: (data: TokenMarketData) => void
  lastUpdate: number
}

interface PriceAlert {
  id: string
  userId: string
  tokenAddress: string
  type: 'ABOVE' | 'BELOW' | 'CHANGE_PERCENT'
  targetValue: number
  currentValue: number
  triggered: boolean
  createdAt: number
}

class PriceServiceClass {
  private static instance: PriceServiceClass
  private heliusWs: WebSocket | null = null
  private reconnectInterval: NodeJS.Timeout | null = null
  private subscriptions: Map<string, PriceSubscription> = new Map()
  private priceAlerts: Map<string, PriceAlert> = new Map()
  private priceCache: Map<string, TokenMarketData> = new Map()
  private isConnected: boolean = false

  private constructor() {
    this.initializeWebSocket()
    this.startPriceCleanupJob()
  }

  public static getInstance(): PriceServiceClass {
    if (!PriceServiceClass.instance) {
      PriceServiceClass.instance = new PriceServiceClass()
    }
    return PriceServiceClass.instance
  }

  /**
   * Initialize Helius WebSocket connection
   */
  private initializeWebSocket(): void {
    try {
      const wsUrl = `${config.helius.wsUrl}?api-key=${config.helius.apiKey}`
      this.heliusWs = new WebSocket(wsUrl)

      this.heliusWs.on('open', () => {
        this.isConnected = true
        logPrice('Helius WebSocket connected')
        this.resubscribeAll()
      })

      this.heliusWs.on('message', (data: WebSocket.Data) => {
        this.handleWebSocketMessage(data.toString())
      })

      this.heliusWs.on('close', (code: number, reason: Buffer) => {
        this.isConnected = false
        logPrice('Helius WebSocket disconnected', undefined, { code, reason: reason.toString() })
        this.scheduleReconnect()
      })

      this.heliusWs.on('error', (error: Error) => {
        this.isConnected = false
        logPrice('Helius WebSocket error', undefined, { error: error.message })
        this.scheduleReconnect()
      })

    } catch (error) {
      logPrice('Failed to initialize WebSocket', undefined, { error: error instanceof Error ? error.message : 'Unknown error' })
      this.scheduleReconnect()
    }
  }

  /**
   * Handle incoming WebSocket messages
   */
  private handleWebSocketMessage(message: string): void {
    try {
      const data: HeliusWebSocketMessage = JSON.parse(message)
      
      if (data.method === 'accountNotification' || data.method === 'programNotification') {
        // Process price update from Helius
        this.processPriceUpdate(data.params.result.value)
      }

    } catch (error) {
      logPrice('Error processing WebSocket message', undefined, { error: error instanceof Error ? error.message : 'Unknown error' })
    }
  }

  /**
   * Process price update from WebSocket
   */
  private async processPriceUpdate(data: any): Promise<void> {
    try {
      // This is a simplified implementation
      // In reality, you'd need to parse the Solana account data to extract price information
      const tokenAddress = data.pubkey
      
      if (!this.subscriptions.has(tokenAddress)) {
        return
      }

      // Get additional market data from REST API
      const marketData = await this.getTokenMarketData(tokenAddress)
      
      if (marketData) {
        // Update cache
        this.priceCache.set(tokenAddress, marketData)
        
        // Store in database for historical tracking
        await this.storePriceHistory(tokenAddress, marketData)
        
        // Publish to Redis for real-time updates
        await RedisService.publishJSON('price_updates', {
          tokenAddress,
          ...marketData,
          timestamp: Date.now()
        })

        // Check price alerts
        await this.checkPriceAlerts(tokenAddress, marketData)

        // Execute subscription callbacks
        const subscription = this.subscriptions.get(tokenAddress)
        if (subscription?.callback) {
          subscription.callback(marketData)
        }

        subscription!.lastUpdate = Date.now()

        logPrice('Price update processed', undefined, {
          tokenAddress,
          price: marketData.price,
          change24h: marketData.change24h
        })
      }

    } catch (error) {
      logPrice('Error processing price update', undefined, { error: error instanceof Error ? error.message : 'Unknown error' })
    }
  }

  /**
   * Subscribe to price updates for a token
   */
  public async subscribeToToken(
    tokenAddress: string, 
    userId?: string,
    callback?: (data: TokenMarketData) => void
  ): Promise<void> {
    try {
      logPrice('Subscribing to token price', userId, { tokenAddress })

      // Add to local subscriptions
      this.subscriptions.set(tokenAddress, {
        tokenAddress,
        userId,
        callback,
        lastUpdate: Date.now()
      })

      // Subscribe via WebSocket if connected
      if (this.isConnected && this.heliusWs) {
        const subscribeMessage = {
          jsonrpc: '2.0',
          id: 1,
          method: 'accountSubscribe',
          params: [
            tokenAddress,
            {
              encoding: 'base64',
              commitment: 'finalized'
            }
          ]
        }

        this.heliusWs.send(JSON.stringify(subscribeMessage))
      }

      // Get initial price data
      const initialData = await this.getTokenMarketData(tokenAddress)
      if (initialData && callback) {
        callback(initialData)
      }

    } catch (error) {
      logPrice('Failed to subscribe to token', userId, { 
        tokenAddress, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      })
      throw new AppError('Failed to subscribe to price updates', 500, 'SUBSCRIPTION_FAILED')
    }
  }

  /**
   * Unsubscribe from price updates for a token
   */
  public async unsubscribeFromToken(tokenAddress: string, userId?: string): Promise<void> {
    try {
      logPrice('Unsubscribing from token price', userId, { tokenAddress })

      this.subscriptions.delete(tokenAddress)

      // Unsubscribe via WebSocket if connected
      if (this.isConnected && this.heliusWs) {
        const unsubscribeMessage = {
          jsonrpc: '2.0',
          id: 1,
          method: 'accountUnsubscribe',
          params: [tokenAddress]
        }

        this.heliusWs.send(JSON.stringify(unsubscribeMessage))
      }

    } catch (error) {
      logPrice('Failed to unsubscribe from token', userId, { 
        tokenAddress, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      })
    }
  }

  /**
   * Get current token market data
   */
  public async getTokenMarketData(tokenAddress: string): Promise<TokenMarketData | null> {
    try {
      // Check cache first (5 second cache)
      const cached = this.priceCache.get(tokenAddress)
      if (cached && Date.now() - (cached as any).lastUpdated < 5000) {
        return cached
      }

      // Check Redis cache
      const cacheKey = `price:${tokenAddress}`
      const redisCached = await RedisService.getJSON<TokenMarketData>(cacheKey)
      if (redisCached) {
        this.priceCache.set(tokenAddress, redisCached)
        return redisCached
      }

      // Fetch from Helius API
      const response = await axios.get(`https://api.helius.xyz/v0/token-metadata`, {
        params: {
          'api-key': config.helius.apiKey
        },
        headers: {
          'Content-Type': 'application/json'
        },
        data: {
          mintAccounts: [tokenAddress]
        },
        timeout: 5000
      })

      if (!response.data || response.data.length === 0) {
        return null
      }

      const tokenData = response.data[0]
      
      // Get price data from Jupiter or other price sources
      const priceData = await this.fetchPriceFromJupiter(tokenAddress)
      
      const marketData: TokenMarketData = {
        price: priceData?.price || 0,
        change24h: priceData?.change24h || 0,
        volume24h: priceData?.volume24h || 0,
        marketCap: priceData?.marketCap || 0,
        liquidity: priceData?.liquidity,
        holders: priceData?.holders
      }

      // Cache in Redis for 5 seconds
      await RedisService.setJSON(cacheKey, marketData, 5)
      
      // Cache locally
      this.priceCache.set(tokenAddress, marketData)

      return marketData

    } catch (error) {
      logPrice('Failed to get token market data', undefined, { 
        tokenAddress, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      })
      return null
    }
  }

  /**
   * Fetch price data from Jupiter API
   */
  private async fetchPriceFromJupiter(tokenAddress: string): Promise<any> {
    try {
      // Get price by comparing to SOL
      const response = await axios.get(`${config.jupiter.apiUrl}/price`, {
        params: {
          ids: tokenAddress,
          vsToken: 'So11111111111111111111111111111111111111112' // SOL
        },
        timeout: 3000
      })

      return response.data.data?.[tokenAddress]
    } catch (error) {
      logPrice('Failed to fetch price from Jupiter', undefined, { 
        tokenAddress, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      })
      return null
    }
  }

  /**
   * Create price alert
   */
  public async createPriceAlert(
    userId: string,
    tokenAddress: string,
    type: 'ABOVE' | 'BELOW' | 'CHANGE_PERCENT',
    targetValue: number
  ): Promise<string> {
    try {
      const alertId = `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      
      const currentData = await this.getTokenMarketData(tokenAddress)
      const currentValue = type === 'CHANGE_PERCENT' ? (currentData?.change24h || 0) : (currentData?.price || 0)

      const alert: PriceAlert = {
        id: alertId,
        userId,
        tokenAddress,
        type,
        targetValue,
        currentValue,
        triggered: false,
        createdAt: Date.now()
      }

      this.priceAlerts.set(alertId, alert)

      // Store in database
      await DatabaseService.client.alert.create({
        data: {
          id: alertId,
          userId,
          type: 'PRICE_ALERT',
          priority: 'MEDIUM',
          title: `Price Alert for ${tokenAddress}`,
          message: `Alert will trigger when price ${type.toLowerCase()} ${targetValue}`,
          metadata: {
            tokenAddress,
            alertType: type,
            targetValue,
            currentValue
          },
          actionable: true
        }
      })

      // Subscribe to token if not already subscribed
      if (!this.subscriptions.has(tokenAddress)) {
        await this.subscribeToToken(tokenAddress, userId)
      }

      logPrice('Price alert created', userId, { alertId, tokenAddress, type, targetValue })

      return alertId

    } catch (error) {
      logPrice('Failed to create price alert', userId, { 
        tokenAddress, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      })
      throw new AppError('Failed to create price alert', 500, 'ALERT_CREATION_FAILED')
    }
  }

  /**
   * Check price alerts and trigger if conditions are met
   */
  private async checkPriceAlerts(tokenAddress: string, marketData: TokenMarketData): Promise<void> {
    try {
      for (const [alertId, alert] of this.priceAlerts) {
        if (alert.tokenAddress !== tokenAddress || alert.triggered) {
          continue
        }

        let shouldTrigger = false
        let currentValue = 0

        switch (alert.type) {
          case 'ABOVE':
            currentValue = marketData.price
            shouldTrigger = currentValue >= alert.targetValue
            break
          case 'BELOW':
            currentValue = marketData.price
            shouldTrigger = currentValue <= alert.targetValue
            break
          case 'CHANGE_PERCENT':
            currentValue = marketData.change24h
            shouldTrigger = Math.abs(currentValue) >= alert.targetValue
            break
        }

        if (shouldTrigger) {
          await this.triggerPriceAlert(alert, currentValue, marketData)
        }
      }
    } catch (error) {
      logPrice('Error checking price alerts', undefined, { 
        tokenAddress, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      })
    }
  }

  /**
   * Trigger a price alert
   */
  private async triggerPriceAlert(
    alert: PriceAlert, 
    currentValue: number, 
    marketData: TokenMarketData
  ): Promise<void> {
    try {
      alert.triggered = true
      alert.currentValue = currentValue

      // Update database
      await DatabaseService.client.alert.update({
        where: { id: alert.id },
        data: {
          read: false,
          metadata: {
            ...alert,
            triggeredAt: Date.now(),
            triggerValue: currentValue,
            marketData
          }
        }
      })

      // Publish to Redis for real-time notifications
      await RedisService.publishJSON('price_alert_triggered', {
        alertId: alert.id,
        userId: alert.userId,
        tokenAddress: alert.tokenAddress,
        type: alert.type,
        targetValue: alert.targetValue,
        currentValue,
        marketData
      })

      logPrice('Price alert triggered', alert.userId, {
        alertId: alert.id,
        tokenAddress: alert.tokenAddress,
        targetValue: alert.targetValue,
        currentValue
      })

    } catch (error) {
      logPrice('Failed to trigger price alert', alert.userId, {
        alertId: alert.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * Store price history in database
   */
  private async storePriceHistory(tokenAddress: string, marketData: TokenMarketData): Promise<void> {
    try {
      await DatabaseService.client.priceHistory.upsert({
        where: {
          tokenAddress_timestamp: {
            tokenAddress,
            timestamp: new Date()
          }
        },
        update: {
          price: marketData.price,
          priceUsd: marketData.price, // Assuming price is already in USD
          volume24h: marketData.volume24h,
          marketCap: marketData.marketCap,
          change24h: marketData.change24h
        },
        create: {
          tokenAddress,
          price: marketData.price,
          priceUsd: marketData.price,
          volume24h: marketData.volume24h,
          marketCap: marketData.marketCap,
          change24h: marketData.change24h,
          timestamp: new Date()
        }
      })
    } catch (error) {
      // Don't log every failure as this runs frequently
      if (Math.random() < 0.01) { // Log 1% of failures
        logPrice('Failed to store price history', undefined, {
          tokenAddress,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }
  }

  /**
   * Schedule WebSocket reconnection
   */
  private scheduleReconnect(): void {
    if (this.reconnectInterval) {
      clearTimeout(this.reconnectInterval)
    }

    this.reconnectInterval = setTimeout(() => {
      logPrice('Attempting to reconnect WebSocket')
      this.initializeWebSocket()
    }, 5000) // Reconnect after 5 seconds
  }

  /**
   * Resubscribe to all tokens after reconnection
   */
  private resubscribeAll(): void {
    for (const [tokenAddress] of this.subscriptions) {
      this.subscribeToToken(tokenAddress).catch(error => {
        logPrice('Failed to resubscribe to token', undefined, {
          tokenAddress,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      })
    }
  }

  /**
   * Start price cache cleanup job
   */
  private startPriceCleanupJob(): void {
    setInterval(() => {
      const now = Date.now()
      const expireTime = 60000 // 1 minute

      // Clean up old subscriptions
      for (const [tokenAddress, subscription] of this.subscriptions) {
        if (now - subscription.lastUpdate > expireTime) {
          this.subscriptions.delete(tokenAddress)
        }
      }

      // Clean up triggered alerts
      for (const [alertId, alert] of this.priceAlerts) {
        if (alert.triggered && now - alert.createdAt > 3600000) { // 1 hour
          this.priceAlerts.delete(alertId)
        }
      }

      // Clean up price cache
      for (const [tokenAddress, data] of this.priceCache) {
        if (now - ((data as any).lastUpdated || 0) > expireTime) {
          this.priceCache.delete(tokenAddress)
        }
      }

    }, 30000) // Run every 30 seconds
  }

  /**
   * Get multiple token prices efficiently
   */
  public async getMultipleTokenPrices(tokenAddresses: string[]): Promise<Map<string, TokenMarketData>> {
    const results = new Map<string, TokenMarketData>()
    
    // Process in batches to avoid overwhelming the API
    const batchSize = 10
    for (let i = 0; i < tokenAddresses.length; i += batchSize) {
      const batch = tokenAddresses.slice(i, i + batchSize)
      const promises = batch.map(address => this.getTokenMarketData(address))
      
      const batchResults = await Promise.allSettled(promises)
      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value) {
          results.set(batch[index], result.value)
        }
      })
    }

    return results
  }

  /**
   * Health check for price service
   */
  public async healthCheck(): Promise<boolean> {
    try {
      // Check WebSocket connection
      if (!this.isConnected) {
        return false
      }

      // Test API connectivity
      await axios.get(`https://api.helius.xyz/v0/health?api-key=${config.helius.apiKey}`, {
        timeout: 5000
      })

      return true
    } catch (error) {
      logger.error('Price service health check failed:', error)
      return false
    }
  }

  /**
   * Shutdown gracefully
   */
  public async shutdown(): Promise<void> {
    if (this.heliusWs) {
      this.heliusWs.close()
    }

    if (this.reconnectInterval) {
      clearTimeout(this.reconnectInterval)
    }

    this.subscriptions.clear()
    this.priceAlerts.clear()
    this.priceCache.clear()

    logPrice('Price service shutdown completed')
  }
}

// Export singleton instance
export const PriceService = PriceServiceClass.getInstance()