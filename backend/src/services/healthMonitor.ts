import { EventEmitter } from 'events'
import { logger } from '@/utils/logger'
import { RedisService } from '@/services/redis'
import { DatabaseService } from '@/services/database'
import { HeliusRPC } from '@/services/heliusRPC'
import { heliusWebSocketService } from '@/services/heliusWebSocket'
import { TradingService } from '@/services/tradingService'
import { PriceService } from '@/services/priceService'
import { NotificationService } from '@/services/notificationService'
import { QueueManager } from '@/jobs/queueManager'
import { EnhancedJupiterService } from '@/services/enhancedJupiterService'
import { QuoteValidationService } from '@/services/quoteValidationService'
import { SlippageProtectionService } from '@/services/slippageProtectionService'
import { TransactionConfirmationService } from '@/services/transactionConfirmationService'
import { MEVProtectionService } from '@/services/mevProtectionService'
import { WalletValidationService } from '@/services/walletValidationService'
import { TransactionRecordingService } from '@/services/transactionRecordingService'
import { enhancedErrorHandler } from '@/middleware/enhancedErrorHandler'
import axios from 'axios'
import { config } from '@/config/environment'

export interface ServiceHealthStatus {
  name: string
  status: 'healthy' | 'degraded' | 'unhealthy' | 'unknown'
  lastCheck: number
  responseTime: number
  error?: string
  details?: any
  uptime?: number
  version?: string
}

export interface SystemHealthSummary {
  status: 'healthy' | 'degraded' | 'unhealthy'
  timestamp: number
  uptime: number
  services: ServiceHealthStatus[]
  summary: {
    total: number
    healthy: number
    degraded: number
    unhealthy: number
    unknown: number
  }
  environment: string
  version: string
}

export interface HealthCheckConfig {
  interval: number // milliseconds
  timeout: number // milliseconds
  retries: number
  alertThreshold: number // consecutive failures before alerting
}

class HealthMonitorService extends EventEmitter {
  private static instance: HealthMonitorService
  private healthCheckInterval: NodeJS.Timeout | null = null
  private serviceStatuses: Map<string, ServiceHealthStatus> = new Map()
  private alertCounters: Map<string, number> = new Map()
  private startTime: number = Date.now()

  private readonly config: HealthCheckConfig = {
    interval: 30000, // 30 seconds
    timeout: 10000, // 10 seconds
    retries: 2,
    alertThreshold: 3
  }

  private readonly healthChecks: Array<{
    name: string
    check: () => Promise<Partial<ServiceHealthStatus>>
    critical: boolean
  }> = []

  private constructor() {
    super()
    this.setMaxListeners(0)
    this.initializeHealthChecks()
  }

  public static getInstance(): HealthMonitorService {
    if (!HealthMonitorService.instance) {
      HealthMonitorService.instance = new HealthMonitorService()
    }
    return HealthMonitorService.instance
  }

  /**
   * Initialize all health check functions
   */
  private initializeHealthChecks(): void {
    // Critical services
    this.addHealthCheck('database', this.checkDatabase.bind(this), true)
    this.addHealthCheck('redis', this.checkRedis.bind(this), true)
    this.addHealthCheck('rpc_failover', this.checkRPCFailover.bind(this), true)
    
    // Important services
    this.addHealthCheck('trading_service', this.checkTradingService.bind(this), false)
    this.addHealthCheck('websocket', this.checkWebSocket.bind(this), false)
    this.addHealthCheck('jupiter_api', this.checkJupiterAPI.bind(this), false)
    this.addHealthCheck('helius_api', this.checkHeliusAPI.bind(this), false)
    
    // Supporting services
    this.addHealthCheck('price_service', this.checkPriceService.bind(this), false)
    this.addHealthCheck('notification_service', this.checkNotificationService.bind(this), false)
    this.addHealthCheck('queue_manager', this.checkQueueManager.bind(this), false)
    this.addHealthCheck('quote_validation', this.checkQuoteValidation.bind(this), false)
    this.addHealthCheck('slippage_protection', this.checkSlippageProtection.bind(this), false)
    this.addHealthCheck('transaction_confirmation', this.checkTransactionConfirmation.bind(this), false)
    this.addHealthCheck('mev_protection', this.checkMEVProtection.bind(this), false)
    this.addHealthCheck('wallet_validation', this.checkWalletValidation.bind(this), false)
    this.addHealthCheck('transaction_recording', this.checkTransactionRecording.bind(this), false)
    this.addHealthCheck('system_resources', this.checkSystemResources.bind(this), false)
  }

  /**
   * Add a health check function
   */
  public addHealthCheck(
    name: string, 
    checkFunction: () => Promise<Partial<ServiceHealthStatus>>, 
    critical: boolean = false
  ): void {
    this.healthChecks.push({ name, check: checkFunction, critical })
    
    // Initialize status
    this.serviceStatuses.set(name, {
      name,
      status: 'unknown',
      lastCheck: 0,
      responseTime: 0
    })
    
    this.alertCounters.set(name, 0)
  }

  /**
   * Start health monitoring
   */
  public start(): void {
    if (this.healthCheckInterval) {
      return // Already running
    }

    logger.info('Starting health monitoring system', {
      interval: this.config.interval,
      timeout: this.config.timeout,
      services: this.healthChecks.length
    })

    // Perform initial health check
    this.performHealthChecks()

    // Schedule periodic health checks
    this.healthCheckInterval = setInterval(() => {
      this.performHealthChecks()
    }, this.config.interval)

    this.emit('started')
  }

  /**
   * Stop health monitoring
   */
  public stop(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval)
      this.healthCheckInterval = null
    }

    logger.info('Health monitoring system stopped')
    this.emit('stopped')
  }

  /**
   * Perform health checks on all services
   */
  private async performHealthChecks(): Promise<void> {
    const checkPromises = this.healthChecks.map(async ({ name, check, critical }) => {
      try {
        const startTime = Date.now()
        
        // Execute health check with timeout
        const result = await Promise.race([
          check(),
          new Promise<Partial<ServiceHealthStatus>>((_, reject) => 
            setTimeout(() => reject(new Error('Health check timeout')), this.config.timeout)
          )
        ])

        const responseTime = Date.now() - startTime
        
        const status: ServiceHealthStatus = {
          name,
          status: 'healthy',
          lastCheck: Date.now(),
          responseTime,
          ...result
        }

        this.updateServiceStatus(name, status, critical)
        
      } catch (error) {
        const status: ServiceHealthStatus = {
          name,
          status: 'unhealthy',
          lastCheck: Date.now(),
          responseTime: Date.now() - Date.now(),
          error: error instanceof Error ? error.message : 'Unknown error'
        }

        this.updateServiceStatus(name, status, critical)
      }
    })

    await Promise.allSettled(checkPromises)
    
    // Emit health summary
    const summary = this.getHealthSummary()
    this.emit('healthCheck', summary)
    
    // Cache health status
    await this.cacheHealthStatus(summary)
  }

  /**
   * Update service status and handle alerting
   */
  private updateServiceStatus(name: string, status: ServiceHealthStatus, critical: boolean): void {
    const previousStatus = this.serviceStatuses.get(name)
    this.serviceStatuses.set(name, status)

    // Handle status changes
    if (previousStatus && previousStatus.status !== status.status) {
      logger.info(`Service ${name} status changed`, {
        from: previousStatus.status,
        to: status.status,
        critical
      })

      this.emit('statusChange', { name, previous: previousStatus.status, current: status.status, critical })
    }

    // Handle alerting for failures
    if (status.status === 'unhealthy') {
      const alertCount = this.alertCounters.get(name) || 0
      this.alertCounters.set(name, alertCount + 1)

      if (alertCount >= this.config.alertThreshold) {
        this.triggerAlert(name, status, critical)
        this.alertCounters.set(name, 0) // Reset counter after alerting
      }
    } else {
      // Reset alert counter on successful check
      this.alertCounters.set(name, 0)
    }
  }

  /**
   * Trigger alert for service failure
   */
  private async triggerAlert(name: string, status: ServiceHealthStatus, critical: boolean): Promise<void> {
    const alertData = {
      service: name,
      status: status.status,
      error: status.error,
      critical,
      timestamp: Date.now(),
      responseTime: status.responseTime
    }

    logger.error(`Health alert triggered for service ${name}`, alertData)
    
    try {
      // Publish alert to Redis for real-time notifications
      await RedisService.publishJSON('health_alert', alertData)
      
      // Emit alert event
      this.emit('alert', alertData)
      
    } catch (error) {
      logger.error('Failed to send health alert:', error)
    }
  }

  /**
   * Cache health status in Redis
   */
  private async cacheHealthStatus(summary: SystemHealthSummary): Promise<void> {
    try {
      await RedisService.setJSON('system:health_status', summary, 60) // Cache for 1 minute
    } catch (error) {
      logger.error('Failed to cache health status:', error)
    }
  }

  /**
   * Get current health summary
   */
  public getHealthSummary(): SystemHealthSummary {
    const services = Array.from(this.serviceStatuses.values())
    
    const summary = {
      total: services.length,
      healthy: services.filter(s => s.status === 'healthy').length,
      degraded: services.filter(s => s.status === 'degraded').length,
      unhealthy: services.filter(s => s.status === 'unhealthy').length,
      unknown: services.filter(s => s.status === 'unknown').length
    }

    // Determine overall system status
    let systemStatus: 'healthy' | 'degraded' | 'unhealthy' = 'healthy'
    
    if (summary.unhealthy > 0) {
      systemStatus = 'unhealthy'
    } else if (summary.degraded > 0 || summary.unknown > 0) {
      systemStatus = 'degraded'
    }

    return {
      status: systemStatus,
      timestamp: Date.now(),
      uptime: Date.now() - this.startTime,
      services,
      summary,
      environment: config.nodeEnv,
      version: process.env.npm_package_version || '1.0.0'
    }
  }

  /**
   * Get status of a specific service
   */
  public getServiceStatus(serviceName: string): ServiceHealthStatus | null {
    return this.serviceStatuses.get(serviceName) || null
  }

  /**
   * Force health check for specific service
   */
  public async checkService(serviceName: string): Promise<ServiceHealthStatus | null> {
    const healthCheck = this.healthChecks.find(check => check.name === serviceName)
    if (!healthCheck) {
      return null
    }

    try {
      const startTime = Date.now()
      const result = await healthCheck.check()
      const responseTime = Date.now() - startTime

      const status: ServiceHealthStatus = {
        name: serviceName,
        status: 'healthy',
        lastCheck: Date.now(),
        responseTime,
        ...result
      }

      this.updateServiceStatus(serviceName, status, healthCheck.critical)
      return status

    } catch (error) {
      const status: ServiceHealthStatus = {
        name: serviceName,
        status: 'unhealthy',
        lastCheck: Date.now(),
        responseTime: 0,
        error: error instanceof Error ? error.message : 'Unknown error'
      }

      this.updateServiceStatus(serviceName, status, healthCheck.critical)
      return status
    }
  }

  // Individual health check implementations
  private async checkDatabase(): Promise<Partial<ServiceHealthStatus>> {
    try {
      const startTime = Date.now()
      await DatabaseService.client.$queryRaw`SELECT 1 as test`
      const responseTime = Date.now() - startTime
      
      return {
        status: 'healthy',
        responseTime,
        details: { type: 'postgresql', responseTime }
      }
    } catch (error) {
      throw new Error(`Database health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async checkRedis(): Promise<Partial<ServiceHealthStatus>> {
    try {
      const startTime = Date.now()
      await RedisService.ping()
      const responseTime = Date.now() - startTime
      
      return {
        status: 'healthy',
        responseTime,
        details: { type: 'redis', responseTime }
      }
    } catch (error) {
      throw new Error(`Redis health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async checkRPCFailover(): Promise<Partial<ServiceHealthStatus>> {
    try {
      const healthSummary = HeliusRPC.getHealthSummary()
      
      if (healthSummary.healthyEndpoints === 0) {
        return {
          status: 'unhealthy',
          error: 'No healthy RPC endpoints available',
          details: healthSummary
        }
      }

      const status = healthSummary.healthyEndpoints === healthSummary.totalEndpoints ? 'healthy' : 'degraded'
      
      return {
        status,
        responseTime: healthSummary.avgResponseTime,
        details: healthSummary
      }
    } catch (error) {
      throw new Error(`RPC failover health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async checkTradingService(): Promise<Partial<ServiceHealthStatus>> {
    try {
      const isHealthy = await TradingService.healthCheck()
      
      return {
        status: isHealthy ? 'healthy' : 'unhealthy',
        details: { healthy: isHealthy }
      }
    } catch (error) {
      throw new Error(`Trading service health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async checkWebSocket(): Promise<Partial<ServiceHealthStatus>> {
    try {
      const wsHealth = heliusWebSocketService.healthCheck()
      
      return {
        status: wsHealth.healthy ? 'healthy' : 'unhealthy',
        details: wsHealth
      }
    } catch (error) {
      throw new Error(`WebSocket health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async checkJupiterAPI(): Promise<Partial<ServiceHealthStatus>> {
    try {
      const startTime = Date.now()
      const isHealthy = await EnhancedJupiterService.healthCheck()
      const responseTime = Date.now() - startTime
      
      if (!isHealthy) {
        return {
          status: 'unhealthy',
          error: 'Jupiter service reported unhealthy status',
          responseTime
        }
      }
      
      // Get enhanced Jupiter service status details
      const healthStatus = EnhancedJupiterService.getHealthStatus()
      
      return {
        status: healthStatus.status,
        responseTime,
        details: {
          endpoints: healthStatus.endpoints.length,
          healthyEndpoints: healthStatus.endpoints.filter(e => e.status === 'healthy').length,
          metrics: healthStatus.metrics,
          circuitBreakers: healthStatus.endpoints.filter(e => e.circuitBreakerOpen).length
        }
      }
    } catch (error) {
      throw new Error(`Enhanced Jupiter API health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async checkHeliusAPI(): Promise<Partial<ServiceHealthStatus>> {
    try {
      const startTime = Date.now()
      const response = await axios.post(config.solana.rpcUrl, {
        jsonrpc: '2.0',
        id: 1,
        method: 'getHealth',
        params: []
      }, { timeout: 5000 })
      const responseTime = Date.now() - startTime

      return {
        status: 'healthy',
        responseTime,
        details: { result: response.data.result }
      }
    } catch (error) {
      throw new Error(`Helius API health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async checkPriceService(): Promise<Partial<ServiceHealthStatus>> {
    try {
      // Check if PriceService has a health check method
      if (typeof PriceService.healthCheck === 'function') {
        const isHealthy = await PriceService.healthCheck()
        return {
          status: isHealthy ? 'healthy' : 'unhealthy',
          details: { healthy: isHealthy }
        }
      }

      // Fallback: assume healthy if service exists
      return {
        status: 'healthy',
        details: { method: 'fallback' }
      }
    } catch (error) {
      throw new Error(`Price service health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async checkNotificationService(): Promise<Partial<ServiceHealthStatus>> {
    try {
      // Check if NotificationService has a health check method
      if (typeof NotificationService.healthCheck === 'function') {
        const isHealthy = await NotificationService.healthCheck()
        return {
          status: isHealthy ? 'healthy' : 'unhealthy',
          details: { healthy: isHealthy }
        }
      }

      return {
        status: 'healthy',
        details: { method: 'fallback' }
      }
    } catch (error) {
      throw new Error(`Notification service health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async checkQueueManager(): Promise<Partial<ServiceHealthStatus>> {
    try {
      // Check if QueueManager has a health check method
      if (typeof QueueManager.healthCheck === 'function') {
        const health = await QueueManager.healthCheck()
        return {
          status: health.healthy ? 'healthy' : 'degraded',
          details: health
        }
      }

      return {
        status: 'healthy',
        details: { method: 'fallback' }
      }
    } catch (error) {
      throw new Error(`Queue manager health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async checkSlippageProtection(): Promise<Partial<ServiceHealthStatus>> {
    try {
      const startTime = Date.now()
      const isHealthy = await SlippageProtectionService.healthCheck()
      const responseTime = Date.now() - startTime
      
      if (!isHealthy) {
        return {
          status: 'unhealthy',
          error: 'Slippage protection service health check failed',
          responseTime
        }
      }

      const config = SlippageProtectionService.getDefaultConfig()
      
      return {
        status: 'healthy',
        responseTime,
        details: {
          defaultSlippage: config.userDefined,
          emergencyMax: config.emergencyMax,
          timeDecayEnabled: config.timeDecay.enabled,
          mevAdjustmentEnabled: config.mevAdjustment.enabled,
          volatilityAdjustmentEnabled: config.volatilityAdjustment.enabled
        }
      }
    } catch (error) {
      throw new Error(`Slippage protection service health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async checkTransactionConfirmation(): Promise<Partial<ServiceHealthStatus>> {
    try {
      const startTime = Date.now()
      const isHealthy = await TransactionConfirmationService.healthCheck()
      const responseTime = Date.now() - startTime
      
      if (!isHealthy) {
        return {
          status: 'unhealthy',
          error: 'Transaction confirmation service health check failed',
          responseTime
        }
      }

      const networkMetrics = TransactionConfirmationService.getNetworkMetrics()
      const config = TransactionConfirmationService.getConfig()
      const pendingConfirmations = TransactionConfirmationService.getPendingConfirmations()
      
      return {
        status: 'healthy',
        responseTime,
        details: {
          networkCongestion: networkMetrics.networkCongestion,
          averageConfirmationTime: networkMetrics.averageConfirmationTime,
          pendingConfirmations: pendingConfirmations.length,
          configuredTimeouts: {
            processed: config.commitmentLevels.processed.timeout,
            confirmed: config.commitmentLevels.confirmed.timeout,
            finalized: config.commitmentLevels.finalized.timeout
          },
          adaptivePollingEnabled: config.adaptivePolling.enabled,
          parallelEndpointsEnabled: config.parallelEndpoints.enabled
        }
      }
    } catch (error) {
      throw new Error(`Transaction confirmation service health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async checkMEVProtection(): Promise<Partial<ServiceHealthStatus>> {
    try {
      const startTime = Date.now()
      const isHealthy = await MEVProtectionService.healthCheck()
      const responseTime = Date.now() - startTime
      
      if (!isHealthy) {
        return {
          status: 'unhealthy',
          error: 'MEV protection service health check failed',
          responseTime
        }
      }

      const networkMetrics = MEVProtectionService.getNetworkMetrics()
      const config = MEVProtectionService.getConfig()
      
      return {
        status: 'healthy',
        responseTime,
        details: {
          protectionEnabled: config.enabled,
          maxProtectionLevel: config.maxProtectionLevel,
          networkCongestion: networkMetrics.congestion,
          avgPriorityFee: networkMetrics.avgPriorityFee,
          mempoolSize: networkMetrics.mempoolSize,
          blockUtilization: networkMetrics.blockUtilization,
          sandwichDetectionEnabled: config.sandwichDetection.enabled,
          frontrunningProtectionEnabled: config.frontrunningProtection.enabled,
          jitLiquidityProtectionEnabled: config.jitLiquidityProtection.enabled,
          bundleSubmissionEnabled: config.bundleSubmission.enabled
        }
      }
    } catch (error) {
      throw new Error(`MEV protection service health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async checkSystemResources(): Promise<Partial<ServiceHealthStatus>> {
    try {
      const memoryUsage = process.memoryUsage()
      const cpuUsage = process.cpuUsage()
      
      // Calculate memory usage percentage (assuming 1GB max for basic check)
      const maxMemory = 1024 * 1024 * 1024 // 1GB
      const memoryPercent = (memoryUsage.heapUsed / maxMemory) * 100
      
      let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy'
      if (memoryPercent > 90) {
        status = 'unhealthy'
      } else if (memoryPercent > 75) {
        status = 'degraded'
      }

      return {
        status,
        details: {
          memory: {
            used: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
            total: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
            percent: Math.round(memoryPercent)
          },
          uptime: process.uptime(),
          pid: process.pid
        }
      }
    } catch (error) {
      throw new Error(`System resources health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async checkQuoteValidation(): Promise<Partial<ServiceHealthStatus>> {
    try {
      const startTime = Date.now()
      const isHealthy = await QuoteValidationService.healthCheck()
      const responseTime = Date.now() - startTime
      
      if (!isHealthy) {
        return {
          status: 'unhealthy',
          error: 'Quote validation service health check failed',
          responseTime
        }
      }

      const config = QuoteValidationService.getConfig()
      
      return {
        status: 'healthy',
        responseTime,
        details: {
          priceImpactThresholds: config.priceImpact,
          routeComplexity: config.routeComplexity,
          liquidityRequirements: config.liquidityDepth
        }
      }
    } catch (error) {
      throw new Error(`Quote validation service health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async checkWalletValidation(): Promise<Partial<ServiceHealthStatus>> {
    try {
      const startTime = Date.now()
      const isHealthy = await WalletValidationService.healthCheck()
      const responseTime = Date.now() - startTime
      
      if (!isHealthy) {
        return {
          status: 'unhealthy',
          error: 'Wallet validation service health check failed',
          responseTime
        }
      }

      const config = WalletValidationService.getConfig()
      
      return {
        status: 'healthy',
        responseTime,
        details: {
          securityLevel: config.securityLevel,
          balanceMonitoringEnabled: config.balanceMonitoring.enabled,
          connectionResilienceEnabled: config.connectionResilience.maxRetries > 0,
          multiSigSupportEnabled: config.multiSigSupport.enabled,
          securityValidationEnabled: config.securityValidation.validateKeyFormat
        }
      }
    } catch (error) {
      throw new Error(`Wallet validation service health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async checkTransactionRecording(): Promise<Partial<ServiceHealthStatus>> {
    try {
      const startTime = Date.now()
      const isHealthy = await TransactionRecordingService.healthCheck()
      const responseTime = Date.now() - startTime
      
      if (!isHealthy) {
        return {
          status: 'unhealthy',
          error: 'Transaction recording service health check failed',
          responseTime
        }
      }

      const config = TransactionRecordingService.getConfig()
      
      return {
        status: 'healthy',
        responseTime,
        details: {
          enabled: config.enabled,
          batchSize: config.batchSize,
          batchInterval: config.batchInterval,
          archivingEnabled: config.archivingEnabled,
          retentionDays: config.retentionDays,
          compressionEnabled: config.compressionEnabled
        }
      }
    } catch (error) {
      throw new Error(`Transaction recording service health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
}

// Export singleton instance
export const HealthMonitor = HealthMonitorService.getInstance()