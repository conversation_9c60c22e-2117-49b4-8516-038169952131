import { Connection, TransactionSignature, Commitment, ConfirmedSignatureInfo } from '@solana/web3.js'
import { EventEmitter } from 'events'
import { logger } from '@/utils/logger'
import { RedisService } from '@/services/redis'
import { HeliusRPC, getConnection } from '@/services/heliusRPC'
import { AppError } from '@/middleware/errorHandler'

export interface ConfirmationConfig {
  commitmentLevels: {
    processed: {
      timeout: number      // 15s - Initial network acceptance
      maxRetries: number   // 3 retries
      pollInterval: number // 1s polling interval
    }
    confirmed: {
      timeout: number      // 30s - Validator majority consensus
      maxRetries: number   // 5 retries  
      pollInterval: number // 2s polling interval
    }
    finalized: {
      timeout: number      // 60s - Irreversible commitment
      maxRetries: number   // 10 retries
      pollInterval: number // 3s polling interval
    }
  }
  adaptivePolling: {
    enabled: boolean
    minInterval: number    // 0.5s minimum
    maxInterval: number    // 10s maximum
    backoffFactor: number  // 1.5x multiplier
  }
  parallelEndpoints: {
    enabled: boolean
    maxEndpoints: number   // Max 3 parallel endpoints
    consensusRequired: number // 2 out of 3 consensus
  }
  networkConditions: {
    adjustTimeouts: boolean
    congestionMultiplier: number // 1.5x for high congestion
    lowLatencyBonus: number     // -20% for low latency
  }
}

export interface ConfirmationResult {
  signature: string
  status: 'processing' | 'confirmed' | 'finalized' | 'failed' | 'timeout'
  confirmationLevel: 'processed' | 'confirmed' | 'finalized' | null
  slot: number | null
  blockTime: number | null
  confirmations: number
  error: string | null
  timeline: {
    submitted: number
    processed?: number
    confirmed?: number
    finalized?: number
    failed?: number
  }
  attempts: {
    processed: number
    confirmed: number
    finalized: number
  }
  networkMetrics: {
    averageConfirmationTime: number
    networkCongestion: 'low' | 'medium' | 'high'
    estimatedFinalizationTime: number
  }
  retryHistory: Array<{
    level: string
    attempt: number
    timestamp: number
    success: boolean
    error?: string
    responseTime: number
  }>
}

export interface PendingConfirmation {
  signature: string
  startTime: number
  lastPoll: number
  currentLevel: 'processed' | 'confirmed' | 'finalized'
  result: ConfirmationResult
  config: ConfirmationConfig
  abortController: AbortController
  userId?: string
  metadata?: any
}

class TransactionConfirmationService extends EventEmitter {
  private static instance: TransactionConfirmationService
  private connection: Connection
  private pendingConfirmations: Map<string, PendingConfirmation> = new Map()
  private defaultConfig: ConfirmationConfig
  private networkMetricsCache: {
    averageConfirmationTime: number
    networkCongestion: 'low' | 'medium' | 'high'
    lastUpdate: number
  } = {
    averageConfirmationTime: 15000, // 15s default
    networkCongestion: 'medium',
    lastUpdate: 0
  }

  private constructor() {
    super()
    this.connection = getConnection()
    
    this.defaultConfig = {
      commitmentLevels: {
        processed: {
          timeout: 15000,    // 15 seconds
          maxRetries: 3,
          pollInterval: 1000 // 1 second
        },
        confirmed: {
          timeout: 30000,    // 30 seconds  
          maxRetries: 5,
          pollInterval: 2000 // 2 seconds
        },
        finalized: {
          timeout: 60000,    // 60 seconds
          maxRetries: 10,
          pollInterval: 3000 // 3 seconds
        }
      },
      adaptivePolling: {
        enabled: true,
        minInterval: 500,   // 0.5 seconds
        maxInterval: 10000, // 10 seconds
        backoffFactor: 1.5
      },
      parallelEndpoints: {
        enabled: true,
        maxEndpoints: 3,
        consensusRequired: 2
      },
      networkConditions: {
        adjustTimeouts: true,
        congestionMultiplier: 1.5,
        lowLatencyBonus: 0.8 // 20% reduction
      }
    }

    // Update network metrics periodically
    setInterval(() => this.updateNetworkMetrics(), 30000) // Every 30s
  }

  public static getInstance(): TransactionConfirmationService {
    if (!TransactionConfirmationService.instance) {
      TransactionConfirmationService.instance = new TransactionConfirmationService()
    }
    return TransactionConfirmationService.instance
  }

  /**
   * Start comprehensive transaction confirmation tracking
   */
  public async confirmTransaction(
    signature: string,
    options: {
      userId?: string
      metadata?: any
      config?: Partial<ConfirmationConfig>
      requireFinalized?: boolean
      onProgress?: (result: ConfirmationResult) => void
    } = {}
  ): Promise<ConfirmationResult> {
    try {
      const { userId, metadata, config, requireFinalized = true, onProgress } = options

      logger.debug('Starting transaction confirmation', {
        signature,
        userId,
        requireFinalized
      })

      // Merge custom config with defaults
      const confirmationConfig = this.mergeConfig(config)
      
      // Create confirmation tracking object
      const pending: PendingConfirmation = {
        signature,
        startTime: Date.now(),
        lastPoll: 0,
        currentLevel: 'processed',
        config: confirmationConfig,
        abortController: new AbortController(),
        userId,
        metadata,
        result: {
          signature,
          status: 'processing',
          confirmationLevel: null,
          slot: null,
          blockTime: null,
          confirmations: 0,
          error: null,
          timeline: {
            submitted: Date.now()
          },
          attempts: {
            processed: 0,
            confirmed: 0,
            finalized: 0
          },
          networkMetrics: {
            averageConfirmationTime: this.networkMetricsCache.averageConfirmationTime,
            networkCongestion: this.networkMetricsCache.networkCongestion,
            estimatedFinalizationTime: this.estimateFinalizationTime()
          },
          retryHistory: []
        }
      }

      this.pendingConfirmations.set(signature, pending)

      // Start polling process
      const result = await this.pollTransactionStatus(pending, requireFinalized, onProgress)
      
      // Clean up
      this.pendingConfirmations.delete(signature)
      
      return result

    } catch (error) {
      logger.error('Transaction confirmation failed:', error)
      throw new AppError('Transaction confirmation failed', 500, 'CONFIRMATION_FAILED')
    }
  }

  /**
   * Main polling logic with multi-level confirmation
   */
  private async pollTransactionStatus(
    pending: PendingConfirmation,
    requireFinalized: boolean,
    onProgress?: (result: ConfirmationResult) => void
  ): Promise<ConfirmationResult> {
    const { signature, config, result } = pending

    try {
      // Level 1: Check for processed status
      await this.waitForCommitmentLevel(pending, 'processed')
      
      if (onProgress) onProgress({ ...result })
      this.emit('processed', signature, result)

      // Level 2: Check for confirmed status
      await this.waitForCommitmentLevel(pending, 'confirmed')
      
      if (onProgress) onProgress({ ...result })
      this.emit('confirmed', signature, result)

      // Level 3: Check for finalized status (if required)
      if (requireFinalized) {
        await this.waitForCommitmentLevel(pending, 'finalized')
        
        if (onProgress) onProgress({ ...result })
        this.emit('finalized', signature, result)
      }

      // Final success state
      result.status = requireFinalized ? 'finalized' : 'confirmed'
      logger.info('Transaction confirmation completed', {
        signature,
        level: result.confirmationLevel,
        totalTime: Date.now() - result.timeline.submitted
      })

      return result

    } catch (error) {
      result.status = 'failed'
      result.error = error instanceof Error ? error.message : 'Unknown error'
      result.timeline.failed = Date.now()

      logger.error('Transaction confirmation failed', {
        signature,
        error: result.error,
        attempts: result.attempts
      })

      this.emit('failed', signature, result)
      throw error
    }
  }

  /**
   * Wait for specific commitment level with adaptive polling
   */
  private async waitForCommitmentLevel(
    pending: PendingConfirmation,
    targetLevel: 'processed' | 'confirmed' | 'finalized'
  ): Promise<void> {
    const { signature, config, result } = pending
    const levelConfig = config.commitmentLevels[targetLevel]
    
    let attempts = 0
    let pollInterval = levelConfig.pollInterval
    const startTime = Date.now()
    
    // Adjust timeout based on network conditions
    const adjustedTimeout = this.adjustTimeoutForNetworkConditions(levelConfig.timeout, config)

    while (attempts < levelConfig.maxRetries) {
      try {
        attempts++
        result.attempts[targetLevel] = attempts
        pending.lastPoll = Date.now()

        logger.debug(`Polling for ${targetLevel} confirmation`, {
          signature,
          attempt: attempts,
          maxRetries: levelConfig.maxRetries
        })

        // Check if we've exceeded timeout
        if (Date.now() - startTime > adjustedTimeout) {
          throw new AppError(
            `Timeout waiting for ${targetLevel} confirmation after ${adjustedTimeout}ms`,
            408,
            'CONFIRMATION_TIMEOUT'
          )
        }

        // Check transaction status
        const status = await this.checkTransactionStatus(signature, targetLevel)
        
        const retryEntry = {
          level: targetLevel,
          attempt: attempts,
          timestamp: Date.now(),
          success: status.confirmed,
          responseTime: Date.now() - pending.lastPoll
        }

        if (status.error) {
          retryEntry.success = false
          retryEntry.error = status.error
        }

        result.retryHistory.push(retryEntry)

        if (status.confirmed) {
          // Update result with confirmation data
          result.confirmationLevel = targetLevel
          result.slot = status.slot
          result.blockTime = status.blockTime
          result.confirmations = status.confirmations
          result.timeline[targetLevel] = Date.now()
          
          pending.currentLevel = targetLevel
          
          logger.debug(`Transaction ${targetLevel} confirmed`, {
            signature,
            slot: status.slot,
            confirmations: status.confirmations,
            timeTaken: Date.now() - startTime
          })

          return // Success!
        }

        if (status.failed) {
          throw new AppError(
            `Transaction failed at ${targetLevel} level: ${status.error}`,
            400,
            'TRANSACTION_FAILED'
          )
        }

        // Adaptive polling interval adjustment
        if (config.adaptivePolling.enabled) {
          pollInterval = Math.min(
            pollInterval * config.adaptivePolling.backoffFactor,
            config.adaptivePolling.maxInterval
          )
        }

        // Wait before next poll
        await this.sleep(pollInterval)

      } catch (error) {
        if (error instanceof AppError) {
          throw error
        }

        logger.warn(`Polling attempt ${attempts} failed for ${targetLevel}`, {
          signature,
          error: error instanceof Error ? error.message : 'Unknown error'
        })

        result.retryHistory.push({
          level: targetLevel,
          attempt: attempts,
          timestamp: Date.now(),
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          responseTime: 0
        })

        // If this was the last attempt, throw the error
        if (attempts >= levelConfig.maxRetries) {
          throw new AppError(
            `Failed to confirm transaction at ${targetLevel} level after ${attempts} attempts`,
            500,
            'CONFIRMATION_FAILED'
          )
        }

        // Wait before retry
        await this.sleep(pollInterval)
      }
    }

    throw new AppError(
      `Maximum retries (${levelConfig.maxRetries}) exceeded for ${targetLevel} confirmation`,
      408,
      'MAX_RETRIES_EXCEEDED'
    )
  }

  /**
   * Check transaction status at specific commitment level
   */
  private async checkTransactionStatus(
    signature: string,
    commitment: Commitment
  ): Promise<{
    confirmed: boolean
    failed: boolean
    slot: number | null
    blockTime: number | null
    confirmations: number
    error: string | null
  }> {
    try {
      // Use parallel endpoint checking if enabled
      if (this.defaultConfig.parallelEndpoints.enabled) {
        return await this.checkStatusWithConsensus(signature, commitment)
      }

      const response = await this.connection.getSignatureStatus(signature, {
        searchTransactionHistory: true
      })

      if (!response.value) {
        return {
          confirmed: false,
          failed: false,
          slot: null,
          blockTime: null,
          confirmations: 0,
          error: null
        }
      }

      const status = response.value
      
      if (status.err) {
        return {
          confirmed: false,
          failed: true,
          slot: status.slot,
          blockTime: null,
          confirmations: 0,
          error: JSON.stringify(status.err)
        }
      }

      // Check if confirmation level is met
      let confirmed = false
      if (commitment === 'processed') {
        confirmed = status.slot !== null
      } else if (commitment === 'confirmed') {
        confirmed = status.confirmationStatus === 'confirmed' || status.confirmationStatus === 'finalized'
      } else if (commitment === 'finalized') {
        confirmed = status.confirmationStatus === 'finalized'
      }

      return {
        confirmed,
        failed: false,
        slot: status.slot,
        blockTime: null, // Will be fetched separately if needed
        confirmations: status.confirmations || 0,
        error: null
      }

    } catch (error) {
      logger.debug('Error checking transaction status:', error)
      return {
        confirmed: false,
        failed: false,
        slot: null,
        blockTime: null,
        confirmations: 0,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Check status across multiple endpoints for consensus
   */
  private async checkStatusWithConsensus(
    signature: string,
    commitment: Commitment
  ): Promise<{
    confirmed: boolean
    failed: boolean
    slot: number | null
    blockTime: number | null
    confirmations: number
    error: string | null
  }> {
    // In a full implementation, this would check multiple RPC endpoints
    // and require consensus (e.g., 2 out of 3 endpoints agreeing)
    
    // For now, use the single connection with enhanced error handling
    return this.checkTransactionStatus(signature, commitment)
  }

  /**
   * Adjust timeout based on network conditions
   */
  private adjustTimeoutForNetworkConditions(baseTimeout: number, config: ConfirmationConfig): number {
    if (!config.networkConditions.adjustTimeouts) {
      return baseTimeout
    }

    let adjusted = baseTimeout

    switch (this.networkMetricsCache.networkCongestion) {
      case 'high':
        adjusted *= config.networkConditions.congestionMultiplier
        break
      case 'low':
        adjusted *= config.networkConditions.lowLatencyBonus
        break
      case 'medium':
      default:
        // No adjustment
        break
    }

    return Math.round(adjusted)
  }

  /**
   * Estimate finalization time based on current network conditions
   */
  private estimateFinalizationTime(): number {
    const baseTime = 30000 // 30 seconds base
    
    switch (this.networkMetricsCache.networkCongestion) {
      case 'high': return baseTime * 2
      case 'low': return baseTime * 0.6
      case 'medium':
      default: return baseTime
    }
  }

  /**
   * Update network metrics cache
   */
  private async updateNetworkMetrics(): Promise<void> {
    try {
      // In a full implementation, this would:
      // 1. Query recent finalization times
      // 2. Check network congestion metrics
      // 3. Analyze recent transaction patterns
      
      // For now, simulate network metrics
      const now = Date.now()
      
      // Simulate network conditions based on time of day
      const hour = new Date().getHours()
      let congestion: 'low' | 'medium' | 'high'
      let avgTime: number

      if ((hour >= 8 && hour <= 11) || (hour >= 14 && hour <= 17)) {
        // Peak hours
        congestion = 'high'
        avgTime = 25000 // 25s
      } else if (hour >= 22 || hour <= 6) {
        // Quiet hours
        congestion = 'low'
        avgTime = 8000 // 8s
      } else {
        // Normal hours
        congestion = 'medium'
        avgTime = 15000 // 15s
      }

      this.networkMetricsCache = {
        averageConfirmationTime: avgTime,
        networkCongestion: congestion,
        lastUpdate: now
      }

      logger.debug('Network metrics updated', this.networkMetricsCache)

    } catch (error) {
      logger.debug('Failed to update network metrics:', error)
    }
  }

  /**
   * Cancel pending confirmation
   */
  public cancelConfirmation(signature: string): boolean {
    const pending = this.pendingConfirmations.get(signature)
    
    if (pending) {
      pending.abortController.abort()
      pending.result.status = 'failed'
      pending.result.error = 'Cancelled by user'
      
      this.pendingConfirmations.delete(signature)
      this.emit('cancelled', signature, pending.result)
      
      logger.info('Transaction confirmation cancelled', { signature })
      return true
    }
    
    return false
  }

  /**
   * Get status of pending confirmation
   */
  public getConfirmationStatus(signature: string): ConfirmationResult | null {
    const pending = this.pendingConfirmations.get(signature)
    return pending ? { ...pending.result } : null
  }

  /**
   * Get all pending confirmations
   */
  public getPendingConfirmations(): ConfirmationResult[] {
    return Array.from(this.pendingConfirmations.values()).map(p => ({ ...p.result }))
  }

  /**
   * Merge custom config with defaults
   */
  private mergeConfig(customConfig?: Partial<ConfirmationConfig>): ConfirmationConfig {
    if (!customConfig) return { ...this.defaultConfig }

    return {
      commitmentLevels: {
        processed: { ...this.defaultConfig.commitmentLevels.processed, ...customConfig.commitmentLevels?.processed },
        confirmed: { ...this.defaultConfig.commitmentLevels.confirmed, ...customConfig.commitmentLevels?.confirmed },
        finalized: { ...this.defaultConfig.commitmentLevels.finalized, ...customConfig.commitmentLevels?.finalized }
      },
      adaptivePolling: { ...this.defaultConfig.adaptivePolling, ...customConfig.adaptivePolling },
      parallelEndpoints: { ...this.defaultConfig.parallelEndpoints, ...customConfig.parallelEndpoints },
      networkConditions: { ...this.defaultConfig.networkConditions, ...customConfig.networkConditions }
    }
  }

  /**
   * Get network metrics
   */
  public getNetworkMetrics(): typeof this.networkMetricsCache {
    return { ...this.networkMetricsCache }
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<ConfirmationConfig>): void {
    this.defaultConfig = this.mergeConfig(newConfig)
    logger.info('Transaction confirmation configuration updated')
  }

  /**
   * Get current configuration
   */
  public getConfig(): ConfirmationConfig {
    return { ...this.defaultConfig }
  }

  /**
   * Health check
   */
  public async healthCheck(): Promise<boolean> {
    try {
      // Test basic RPC connectivity
      await this.connection.getLatestBlockhash('confirmed')
      
      // Check if we have reasonable network metrics
      const metrics = this.getNetworkMetrics()
      const isStale = Date.now() - metrics.lastUpdate > 120000 // 2 minutes
      
      return !isStale
    } catch (error) {
      logger.error('Transaction confirmation service health check failed:', error)
      return false
    }
  }

  /**
   * Utility sleep function
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Cleanup method
   */
  public shutdown(): void {
    // Cancel all pending confirmations
    for (const [signature, pending] of this.pendingConfirmations) {
      pending.abortController.abort()
      this.emit('cancelled', signature, pending.result)
    }
    
    this.pendingConfirmations.clear()
    this.removeAllListeners()
    
    logger.info('Transaction confirmation service shutdown completed')
  }
}

// Export singleton instance
const transactionConfirmationServiceInstance = TransactionConfirmationService.getInstance()
export { transactionConfirmationServiceInstance as TransactionConfirmationService }