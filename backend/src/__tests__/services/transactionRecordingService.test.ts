/**
 * Unit tests for TransactionRecordingService
 */

import { TransactionRecordingService } from '@/services/transactionRecordingService'
import { DatabaseTestHelper } from '../utils/databaseHelpers'
import { mockRedisService } from '../mocks/redisMock'
import { fixtures } from '../fixtures/testFixtures'
import { resetTestDatabase, waitFor, createMockTransactionData } from '../utils/testUtils'

// Mock dependencies
jest.mock('@/services/database')

describe('TransactionRecordingService', () => {
  let dbHelper: DatabaseTestHelper
  let redisMock: any
  let transactionService: any
  let testUser: any

  beforeAll(async () => {
    dbHelper = new DatabaseTestHelper()
    const { mock } = mockRedisService()
    redisMock = mock
  })

  beforeEach(async () => {
    await resetTestDatabase()
    await redisMock.flushAll()
    
    // Create test user
    testUser = await dbHelper.createTestUser(fixtures.users.basicUser)
    
    // Get fresh instance
    transactionService = (await import('@/services/transactionRecordingService')).TransactionRecordingService
  })

  afterAll(async () => {
    await dbHelper.cleanupTestPrismaClient()
  })

  describe('recordTransaction', () => {
    it('should record a simple transaction successfully', async () => {
      // Arrange
      const transactionData = createMockTransactionData({
        userId: testUser.id,
        ...fixtures.transactions.buyTransaction
      })

      // Act
      const recordId = await transactionService.recordTransaction(transactionData)

      // Assert
      expect(recordId).toBeDefined()
      expect(recordId).not.toBe('disabled')
      expect(recordId).not.toBe('queued')

      // Verify in database
      const savedTransaction = await dbHelper.getUserWithAllData(testUser.id)
      expect(savedTransaction?.transactions).toHaveLength(1)
      expect(savedTransaction?.transactions[0]).toBeValidTransaction()
    })

    it('should calculate total cost correctly', async () => {
      // Arrange
      const transactionData = createMockTransactionData({
        userId: testUser.id,
        fees: {
          total: 0.001,
          network: 0.0005,
          jupiter: 0.0005
        },
        priorityFee: 0.002,
        networkFee: 0.001
      })

      // Act
      await transactionService.recordTransaction(transactionData)

      // Assert
      const transactions = await dbHelper.getUserWithAllData(testUser.id)
      const transaction = transactions?.transactions[0]
      expect(transaction?.totalCost).toBe(0.004) // 0.001 + 0.002 + 0.001
    })

    it('should validate required transaction fields', async () => {
      // Arrange
      const invalidTransaction = {
        userId: testUser.id,
        // Missing required fields
        type: 'SWAP'
      }

      // Act & Assert
      await expect(transactionService.recordTransaction(invalidTransaction))
        .rejects.toThrow('Missing required field')
    })

    it('should validate transaction hash format', async () => {
      // Arrange
      const invalidTransaction = createMockTransactionData({
        userId: testUser.id,
        hash: 'invalid-hash-format' // Too short
      })

      // Act & Assert
      await expect(transactionService.recordTransaction(invalidTransaction))
        .rejects.toThrow('Invalid transaction hash format')
    })

    it('should validate positive amounts', async () => {
      // Arrange
      const invalidTransaction = createMockTransactionData({
        userId: testUser.id,
        amountIn: -100 // Negative amount
      })

      // Act & Assert
      await expect(transactionService.recordTransaction(invalidTransaction))
        .rejects.toThrow('Transaction amounts must be positive')
    })

    it('should handle batch recording when batch size > 1', async () => {
      // Arrange
      const originalConfig = transactionService.getConfig()
      transactionService.updateConfig({ batchSize: 3 })

      const transactions = Array(3).fill(null).map((_, index) => 
        createMockTransactionData({
          userId: testUser.id,
          tokenInSymbol: `TOKEN${index}`,
          amountIn: 100 + index
        })
      )

      // Act
      const promises = transactions.map(tx => transactionService.recordTransaction(tx))
      const results = await Promise.all(promises)

      // Assert
      expect(results.every(r => r === 'queued')).toBe(true)

      // Wait for batch processing
      await waitFor(async () => {
        const userData = await dbHelper.getUserWithAllData(testUser.id)
        return userData?.transactions.length === 3
      }, 5000)

      // Verify all transactions were saved
      const userData = await dbHelper.getUserWithAllData(testUser.id)
      expect(userData?.transactions).toHaveLength(3)

      // Restore config
      transactionService.updateConfig(originalConfig)
    })

    it('should cache recent transactions in Redis', async () => {
      // Arrange
      const transactionData = createMockTransactionData({
        userId: testUser.id
      })

      // Act
      await transactionService.recordTransaction(transactionData)

      // Assert
      const cachedTransaction = await redisMock.getJSON(`tx:${transactionData.hash}`)
      expect(cachedTransaction).toBeDefined()
      expect(cachedTransaction.hash).toBe(transactionData.hash)

      // Check user's recent transactions list
      const recentTransactions = await redisMock.lRange(`user:${testUser.id}:recent_transactions`, 0, -1)
      expect(recentTransactions).toHaveLength(1)
    })
  })

  describe('updateTransactionStatus', () => {
    let testTransaction: any

    beforeEach(async () => {
      const position = await dbHelper.createTestPosition(testUser.id)
      testTransaction = await dbHelper.createTestTransaction(testUser.id, position.id, {
        status: 'PENDING'
      })
    })

    it('should update transaction status successfully', async () => {
      // Act
      await transactionService.updateTransactionStatus(
        testTransaction.hash,
        'CONFIRMED',
        {
          blockNumber: 200000000,
          blockTime: Math.floor(Date.now() / 1000),
          confirmationTime: 2500
        }
      )

      // Assert
      const updatedTransaction = await transactionService.getTransactionByHash(testTransaction.hash)
      expect(updatedTransaction?.status).toBe('CONFIRMED')
      expect(updatedTransaction?.blockNumber).toBeDefined()
      expect(updatedTransaction?.confirmationTime).toBe(2500)
      expect(updatedTransaction?.confirmedAt).toBeDefined()
    })

    it('should set confirmedAt timestamp for confirmed transactions', async () => {
      // Act
      await transactionService.updateTransactionStatus(testTransaction.hash, 'CONFIRMED')

      // Assert
      const updatedTransaction = await transactionService.getTransactionByHash(testTransaction.hash)
      expect(updatedTransaction?.confirmedAt).toBeDefined()
      expect(updatedTransaction?.confirmedAt).toBeInstanceOf(Date)
    })

    it('should invalidate cache on status update', async () => {
      // Arrange - First cache the transaction
      await transactionService.getTransactionByHash(testTransaction.hash)
      const cached = await redisMock.getJSON(`tx:${testTransaction.hash}`)
      expect(cached).toBeDefined()

      // Act
      await transactionService.updateTransactionStatus(testTransaction.hash, 'CONFIRMED')

      // Assert - Cache should be invalidated
      const cachedAfterUpdate = await redisMock.getJSON(`tx:${testTransaction.hash}`)
      expect(cachedAfterUpdate).toBeNull()
    })

    it('should handle non-existent transaction gracefully', async () => {
      // Act & Assert
      await expect(transactionService.updateTransactionStatus('nonexistent-hash', 'CONFIRMED'))
        .rejects.toThrow('Transaction status update failed')
    })
  })

  describe('queryTransactions', () => {
    beforeEach(async () => {
      // Create test data
      const position = await dbHelper.createTestPosition(testUser.id)
      
      // Create multiple transactions with different characteristics
      await Promise.all([
        dbHelper.createTestTransaction(testUser.id, position.id, {
          type: 'BUY',
          status: 'CONFIRMED',
          tokenInSymbol: 'SOL',
          tokenOutSymbol: 'BONK',
          amountIn: '1.0',
          timestamp: new Date('2024-01-01T10:00:00Z')
        }),
        dbHelper.createTestTransaction(testUser.id, position.id, {
          type: 'SELL',
          status: 'CONFIRMED',
          tokenInSymbol: 'BONK',
          tokenOutSymbol: 'SOL',
          amountIn: '50000',
          timestamp: new Date('2024-01-02T14:00:00Z')
        }),
        dbHelper.createTestTransaction(testUser.id, position.id, {
          type: 'SWAP',
          status: 'FAILED',
          tokenInSymbol: 'SOL',
          tokenOutSymbol: 'USDC',
          amountIn: '2.0',
          timestamp: new Date('2024-01-03T09:00:00Z')
        })
      ])
    })

    it('should return all transactions with default pagination', async () => {
      // Act
      const result = await transactionService.queryTransactions({ userId: testUser.id })

      // Assert
      expect(result.transactions).toHaveLength(3)
      expect(result.total).toBe(3)
      expect(result.hasMore).toBe(false)
      expect(result.page).toBe(1)
      expect(result.totalPages).toBe(1)
    })

    it('should filter by transaction type', async () => {
      // Act
      const result = await transactionService.queryTransactions({
        userId: testUser.id,
        type: ['BUY', 'SELL']
      })

      // Assert
      expect(result.transactions).toHaveLength(2)
      expect(result.transactions.every(tx => ['BUY', 'SELL'].includes(tx.type))).toBe(true)
    })

    it('should filter by transaction status', async () => {
      // Act
      const result = await transactionService.queryTransactions({
        userId: testUser.id,
        status: ['CONFIRMED']
      })

      // Assert
      expect(result.transactions).toHaveLength(2)
      expect(result.transactions.every(tx => tx.status === 'CONFIRMED')).toBe(true)
    })

    it('should filter by date range', async () => {
      // Act
      const result = await transactionService.queryTransactions({
        userId: testUser.id,
        dateFrom: new Date('2024-01-02T00:00:00Z'),
        dateTo: new Date('2024-01-03T23:59:59Z')
      })

      // Assert
      expect(result.transactions).toHaveLength(2)
    })

    it('should support pagination', async () => {
      // Act
      const page1 = await transactionService.queryTransactions({
        userId: testUser.id,
        limit: 2,
        offset: 0
      })

      const page2 = await transactionService.queryTransactions({
        userId: testUser.id,
        limit: 2,
        offset: 2
      })

      // Assert
      expect(page1.transactions).toHaveLength(2)
      expect(page1.hasMore).toBe(true)
      expect(page2.transactions).toHaveLength(1)
      expect(page2.hasMore).toBe(false)
    })

    it('should support search across multiple fields', async () => {
      // Act
      const result = await transactionService.queryTransactions({
        userId: testUser.id,
        searchTerm: 'BONK'
      })

      // Assert
      expect(result.transactions).toHaveLength(2) // Transactions with BONK as input or output
    })

    it('should sort by different fields', async () => {
      // Act
      const byAmount = await transactionService.queryTransactions({
        userId: testUser.id,
        sortBy: 'amountIn',
        sortOrder: 'desc'
      })

      // Assert
      expect(parseFloat(byAmount.transactions[0].amountIn as any)).toBeGreaterThan(
        parseFloat(byAmount.transactions[1].amountIn as any)
      )
    })
  })

  describe('getTransactionStats', () => {
    beforeEach(async () => {
      // Create comprehensive test data
      const position = await dbHelper.createTestPosition(testUser.id)
      
      await Promise.all([
        dbHelper.createTestTransaction(testUser.id, position.id, {
          type: 'BUY',
          status: 'CONFIRMED',
          amountIn: '100',
          executionTime: 2000,
          slippageUsed: 1.0,
          mevProtected: true,
          totalCost: 0.1
        }),
        dbHelper.createTestTransaction(testUser.id, position.id, {
          type: 'SELL',
          status: 'CONFIRMED',
          amountIn: '200',
          executionTime: 1500,
          slippageUsed: 1.5,
          mevProtected: false,
          totalCost: 0.15
        }),
        dbHelper.createTestTransaction(testUser.id, position.id, {
          type: 'SWAP',
          status: 'FAILED',
          amountIn: '50',
          executionTime: 5000,
          slippageUsed: 2.0,
          mevProtected: true,
          totalCost: 0.05
        })
      ])
    })

    it('should calculate comprehensive transaction statistics', async () => {
      // Act
      const stats = await transactionService.getTransactionStats(testUser.id)

      // Assert
      expect(stats.totalTransactions).toBe(3)
      expect(stats.successfulTransactions).toBe(2)
      expect(stats.failedTransactions).toBe(1)
      expect(stats.totalVolume).toBe(350) // 100 + 200 + 50
      expect(stats.totalFees).toBe(0.3) // 0.1 + 0.15 + 0.05
      expect(stats.averageExecutionTime).toBeCloseTo(2833.33, 2) // (2000 + 1500 + 5000) / 3
      expect(stats.averageSlippage).toBeCloseTo(1.5, 2) // (1.0 + 1.5 + 2.0) / 3
      expect(stats.mevProtectedPercentage).toBeCloseTo(66.67, 2) // 2/3 * 100
    })

    it('should break down statistics by transaction type', async () => {
      // Act
      const stats = await transactionService.getTransactionStats(testUser.id)

      // Assert
      expect(stats.byType.BUY.count).toBe(1)
      expect(stats.byType.BUY.volume).toBe(100)
      expect(stats.byType.BUY.successRate).toBe(100)

      expect(stats.byType.SELL.count).toBe(1)
      expect(stats.byType.SELL.volume).toBe(200)
      expect(stats.byType.SELL.successRate).toBe(100)

      expect(stats.byType.SWAP.count).toBe(1)
      expect(stats.byType.SWAP.volume).toBe(50)
      expect(stats.byType.SWAP.successRate).toBe(0)
    })

    it('should calculate performance metrics correctly', async () => {
      // Act
      const stats = await transactionService.getTransactionStats(testUser.id)

      // Assert
      expect(stats.performance.averageSlippage).toBeCloseTo(1.25, 2) // Average of confirmed only
      expect(stats.performance.averageExecutionTime).toBeCloseTo(1750, 2) // Average of confirmed only
      expect(stats.performance.successRate).toBeCloseTo(66.67, 2) // 2/3 * 100
    })

    it('should filter stats by date range', async () => {
      // Act
      const stats = await transactionService.getTransactionStats(
        testUser.id,
        new Date('2024-01-01T00:00:00Z'),
        new Date('2024-01-01T23:59:59Z')
      )

      // Assert - Should only include transactions from that day
      expect(stats.totalTransactions).toBeLessThanOrEqual(3)
    })
  })

  describe('archiveOldTransactions', () => {
    beforeEach(async () => {
      const position = await dbHelper.createTestPosition(testUser.id)
      
      // Create old transaction (beyond retention period)
      const oldDate = new Date()
      oldDate.setDate(oldDate.getDate() - 400) // 400 days ago

      await dbHelper.createTestTransaction(testUser.id, position.id, {
        status: 'CONFIRMED',
        timestamp: oldDate
      })

      // Create recent transaction
      await dbHelper.createTestTransaction(testUser.id, position.id, {
        status: 'CONFIRMED',
        timestamp: new Date()
      })
    })

    it('should archive transactions older than retention period', async () => {
      // Act
      const result = await transactionService.archiveOldTransactions()

      // Assert
      expect(result.archived).toBe(1)
      expect(result.deleted).toBe(0)

      // Verify archived transaction has archivedAt timestamp
      const transactions = await dbHelper.getUserWithAllData(testUser.id)
      const archivedTransaction = transactions?.transactions.find(tx => tx.archivedAt !== null)
      expect(archivedTransaction).toBeDefined()
    })

    it('should not archive pending transactions', async () => {
      // Arrange - Create old pending transaction
      const position = await dbHelper.createTestPosition(testUser.id)
      const oldDate = new Date()
      oldDate.setDate(oldDate.getDate() - 400)

      await dbHelper.createTestTransaction(testUser.id, position.id, {
        status: 'PENDING',
        timestamp: oldDate
      })

      // Act
      const result = await transactionService.archiveOldTransactions()

      // Assert - Should not archive pending transactions
      const transactions = await dbHelper.getUserWithAllData(testUser.id)
      const pendingTransaction = transactions?.transactions.find(tx => tx.status === 'PENDING')
      expect(pendingTransaction?.archivedAt).toBeNull()
    })
  })

  describe('batch processing', () => {
    it('should process batches automatically at specified intervals', async () => {
      // Arrange
      transactionService.updateConfig({ 
        batchSize: 2, 
        batchInterval: 1000 // 1 second
      })

      const transactions = Array(2).fill(null).map((_, index) => 
        createMockTransactionData({
          userId: testUser.id,
          tokenInSymbol: `TOKEN${index}`
        })
      )

      // Act
      await Promise.all(transactions.map(tx => transactionService.recordTransaction(tx)))

      // Wait for batch processing
      await waitFor(async () => {
        const userData = await dbHelper.getUserWithAllData(testUser.id)
        return userData?.transactions.length === 2
      }, 3000)

      // Assert
      const userData = await dbHelper.getUserWithAllData(testUser.id)
      expect(userData?.transactions).toHaveLength(2)
    })

    it('should handle batch processing errors gracefully', async () => {
      // This would test error scenarios in batch processing
      // Implementation depends on error handling strategy
    })
  })

  describe('configuration management', () => {
    it('should return current configuration', () => {
      // Act
      const config = transactionService.getConfig()

      // Assert
      expect(config).toHaveProperty('enabled')
      expect(config).toHaveProperty('batchSize')
      expect(config).toHaveProperty('batchInterval')
      expect(config).toHaveProperty('retentionDays')
    })

    it('should update configuration and restart batch processing', () => {
      // Arrange
      const newConfig = {
        batchSize: 10,
        batchInterval: 5000,
        retentionDays: 180
      }

      // Act
      transactionService.updateConfig(newConfig)
      const updatedConfig = transactionService.getConfig()

      // Assert
      expect(updatedConfig.batchSize).toBe(10)
      expect(updatedConfig.batchInterval).toBe(5000)
      expect(updatedConfig.retentionDays).toBe(180)
    })
  })

  describe('health check', () => {
    it('should return true when service is healthy', async () => {
      // Act
      const isHealthy = await transactionService.healthCheck()

      // Assert
      expect(isHealthy).toBe(true)
    })

    it('should return false when batch queue is overloaded', async () => {
      // This would test overload scenarios
      // Implementation depends on queue size limits
    })
  })
})