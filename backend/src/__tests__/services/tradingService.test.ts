/**
 * Unit tests for TradingService
 */

import { TradingService } from '@/services/tradingService'
import { DatabaseTestHelper } from '../utils/databaseHelpers'
import { mockRedisService } from '../mocks/redisMock'
import { externalMocks } from '../mocks/externalApiMocks'
import { fixtures } from '../fixtures/testFixtures'
import { resetTestDatabase } from '../utils/testUtils'

// Mock external dependencies
jest.mock('@/services/database')
jest.mock('@/services/heliusRPC')
jest.mock('@/services/enhancedJupiterService')
jest.mock('@/services/positionMonitor')
jest.mock('@/services/heliusWebSocket')
jest.mock('@/services/quoteValidationService')
jest.mock('@/services/slippageProtectionService')
jest.mock('@/services/transactionConfirmationService')
jest.mock('@/services/mevProtectionService')
jest.mock('@/services/walletValidationService')
jest.mock('@/services/transactionRecordingService')

describe('TradingService', () => {
  let dbHelper: DatabaseTestHelper
  let redisMock: any
  let tradingService: any

  beforeAll(async () => {
    // Set up test environment
    dbHelper = new DatabaseTestHelper()
    const { mock } = mockRedisService()
    redisMock = mock
    
    // Configure external mocks
    externalMocks.setHealthyState()
  })

  beforeEach(async () => {
    // Reset database and cache
    await resetTestDatabase()
    await redisMock.flushAll()
    
    // Reset external mocks
    externalMocks.reset()
    
    // Get fresh instance of trading service
    tradingService = (await import('@/services/tradingService')).TradingService
  })

  afterAll(async () => {
    await dbHelper.cleanupTestPrismaClient()
  })

  describe('getQuote', () => {
    it('should successfully get a quote from Jupiter', async () => {
      // Arrange
      const quoteParams = {
        inputMint: fixtures.tokens.SOL.address,
        outputMint: fixtures.tokens.BONK.address,
        amount: **********, // 1 SOL
        slippageBps: 100,
        userPublicKey: fixtures.users.basicUser.walletAddress
      }

      externalMocks.jupiter.setCustomQuoteResponse(fixtures.jupiter.validQuoteResponse)

      // Act
      const result = await tradingService.getQuote(quoteParams)

      // Assert
      expect(result).toBeValidQuote()
      expect(result.inAmount).toBe(fixtures.jupiter.validQuoteResponse.inAmount)
      expect(result.outAmount).toBe(fixtures.jupiter.validQuoteResponse.outAmount)
      expect(result.priceImpactPct).toBe(fixtures.jupiter.validQuoteResponse.priceImpactPct)
      expect(result.routePlan).toHaveLength(1)
    })

    it('should retry on validation failure with adjusted parameters', async () => {
      // Arrange
      const quoteParams = {
        inputMint: fixtures.tokens.SOL.address,
        outputMint: fixtures.tokens.BONK.address,
        amount: **********,
        slippageBps: 50, // Low slippage that will be increased on retry
        userPublicKey: fixtures.users.basicUser.walletAddress
      }

      // Mock validation service to fail first attempt
      const mockValidationService = {
        validateQuote: jest.fn()
          .mockResolvedValueOnce({
            recommendation: 'retry_later',
            warnings: ['High price impact'],
            errors: [],
            score: 40
          })
          .mockResolvedValueOnce({
            recommendation: 'proceed',
            warnings: [],
            errors: [],
            score: 80
          })
      }

      jest.doMock('@/services/quoteValidationService', () => ({
        QuoteValidationService: mockValidationService
      }))

      // Act
      const result = await tradingService.getValidatedQuoteWithRetry(quoteParams)

      // Assert
      expect(result.retryAttempts).toBe(2)
      expect(mockValidationService.validateQuote).toHaveBeenCalledTimes(2)
    })

    it('should handle Jupiter API failures gracefully', async () => {
      // Arrange
      const quoteParams = {
        inputMint: fixtures.tokens.SOL.address,
        outputMint: fixtures.tokens.BONK.address,
        amount: **********,
        slippageBps: 100,
        userPublicKey: fixtures.users.basicUser.walletAddress
      }

      externalMocks.jupiter.setQuoteFailure(true)

      // Act & Assert
      await expect(tradingService.getQuote(quoteParams)).rejects.toThrow('Failed to get quote from Jupiter')
    })

    it('should validate quote parameters', async () => {
      // Arrange
      const invalidParams = {
        inputMint: '', // Missing input mint
        outputMint: fixtures.tokens.BONK.address,
        amount: 0, // Invalid amount
        slippageBps: -1 // Invalid slippage
      }

      // Act & Assert
      await expect(tradingService.getQuote(invalidParams)).rejects.toThrow()
    })
  })

  describe('executeTrade', () => {
    let testUser: any
    let testPosition: any

    beforeEach(async () => {
      // Create test user and position
      testUser = await dbHelper.createTestUser(fixtures.users.basicUser)
      testPosition = await dbHelper.createTestPosition(testUser.id, fixtures.positions.profitablePosition)
    })

    it('should successfully execute a trade', async () => {
      // Arrange
      const tradeParams = {
        tokenIn: fixtures.tokens.SOL.address,
        tokenOut: fixtures.tokens.BONK.address,
        amount: **********,
        slippage: 1.0,
        preset: 'DEFAULT' as const,
        strategyId: undefined
      }

      // Mock all validation services to pass
      const mockWalletValidation = {
        validateWallet: jest.fn().mockResolvedValue({
          isValid: true,
          securityLevel: 'good',
          balance: { status: 'sufficient' },
          issues: []
        })
      }

      const mockSlippageProtection = {
        calculateOptimalSlippage: jest.fn().mockResolvedValue({
          finalSlippage: 1.0,
          riskLevel: 'low',
          recommendation: 'proceed',
          confidence: 95,
          reasoning: []
        })
      }

      const mockMEVProtection = {
        analyzeMEVRisk: jest.fn().mockResolvedValue({
          riskLevel: 'low',
          riskScore: 25,
          detectedThreats: [],
          recommendedFee: 0.001,
          protectionStrategy: 'none',
          confidence: 90
        })
      }

      jest.doMock('@/services/walletValidationService', () => ({
        WalletValidationService: mockWalletValidation
      }))

      jest.doMock('@/services/slippageProtectionService', () => ({
        SlippageProtectionService: mockSlippageProtection
      }))

      jest.doMock('@/services/mevProtectionService', () => ({
        MEVProtectionService: mockMEVProtection
      }))

      // Configure successful external API responses
      externalMocks.jupiter.setCustomQuoteResponse(fixtures.jupiter.validQuoteResponse)
      externalMocks.jupiter.setCustomSwapResponse(fixtures.jupiter.validSwapResponse)

      // Act
      const result = await tradingService.executeTrade(tradeParams, testUser.walletAddress, testUser.id)

      // Assert
      expect(result).toMatchTradeResult()
      expect(result.success).toBe(true)
      expect(result.transactionHash).toBeDefined()
      expect(result.executionTime).toBeGreaterThan(0)
    })

    it('should fail trade execution on wallet validation failure', async () => {
      // Arrange
      const tradeParams = {
        tokenIn: fixtures.tokens.SOL.address,
        tokenOut: fixtures.tokens.BONK.address,
        amount: **********,
        slippage: 1.0,
        preset: 'DEFAULT' as const
      }

      const mockWalletValidation = {
        validateWallet: jest.fn().mockResolvedValue({
          isValid: false,
          securityLevel: 'critical',
          balance: { status: 'insufficient' },
          issues: [{ message: 'Insufficient balance' }]
        })
      }

      jest.doMock('@/services/walletValidationService', () => ({
        WalletValidationService: mockWalletValidation
      }))

      // Act
      const result = await tradingService.executeTrade(tradeParams, testUser.walletAddress, testUser.id)

      // Assert
      expect(result.success).toBe(false)
      expect(result.error).toContain('Wallet validation failed')
    })

    it('should handle high MEV risk appropriately', async () => {
      // Arrange
      const tradeParams = {
        tokenIn: fixtures.tokens.SOL.address,
        tokenOut: fixtures.tokens.BONK.address,
        amount: **********0, // Large trade
        slippage: 1.0,
        preset: 'VOL' as const // Volatile preset with maximum MEV protection
      }

      const mockMEVProtection = {
        analyzeMEVRisk: jest.fn().mockResolvedValue({
          riskLevel: 'high',
          riskScore: 85,
          detectedThreats: ['sandwich_attack', 'frontrunning'],
          recommendedFee: 0.01,
          protectionStrategy: 'delay',
          confidence: 95,
          recommendations: ['Increase slippage tolerance', 'Use private mempool']
        })
      }

      jest.doMock('@/services/mevProtectionService', () => ({
        MEVProtectionService: mockMEVProtection
      }))

      // Act
      const result = await tradingService.executeTrade(tradeParams, testUser.walletAddress, testUser.id)

      // Assert
      expect(result.success).toBe(false)
      expect(result.error).toContain('High MEV risk detected')
    })

    it('should record transaction details correctly', async () => {
      // Arrange
      const tradeParams = {
        tokenIn: fixtures.tokens.SOL.address,
        tokenOut: fixtures.tokens.BONK.address,
        amount: **********,
        slippage: 1.0,
        preset: 'DEFAULT' as const
      }

      const mockTransactionRecording = {
        recordTransaction: jest.fn().mockResolvedValue('tx-record-id')
      }

      jest.doMock('@/services/transactionRecordingService', () => ({
        TransactionRecordingService: mockTransactionRecording
      }))

      // Setup successful mocks
      externalMocks.jupiter.setCustomQuoteResponse(fixtures.jupiter.validQuoteResponse)
      externalMocks.jupiter.setCustomSwapResponse(fixtures.jupiter.validSwapResponse)

      // Act
      await tradingService.executeTrade(tradeParams, testUser.walletAddress, testUser.id)

      // Assert
      expect(mockTransactionRecording.recordTransaction).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: testUser.id,
          type: 'SWAP',
          status: 'CONFIRMED',
          tokenIn: tradeParams.tokenIn,
          tokenOut: tradeParams.tokenOut,
          presetUsed: tradeParams.preset,
          slippageUsed: tradeParams.slippage,
          mevProtected: false
        })
      )
    })
  })

  describe('simulateTrade', () => {
    it('should simulate trade execution without actual execution', async () => {
      // Arrange
      const simulationParams = {
        tokenIn: fixtures.tokens.SOL.address,
        tokenOut: fixtures.tokens.BONK.address,
        amount: **********,
        preset: 'DEFAULT' as const
      }

      externalMocks.jupiter.setCustomQuoteResponse(fixtures.jupiter.validQuoteResponse)

      // Act
      const result = await tradingService.simulateTrade(simulationParams)

      // Assert
      expect(result.success).toBe(true)
      expect(result.estimatedOutput).toBe(parseFloat(fixtures.jupiter.validQuoteResponse.outAmount))
      expect(result.priceImpact).toBe(fixtures.jupiter.validQuoteResponse.priceImpactPct)
      expect(result.fees).toBeDefined()
      expect(result.route).toBeDefined()
      expect(result.warnings).toBeInstanceOf(Array)
    })

    it('should include warnings for high price impact', async () => {
      // Arrange
      const simulationParams = {
        tokenIn: fixtures.tokens.SOL.address,
        tokenOut: fixtures.tokens.PEPE.address,
        amount: **********0, // Large amount
        preset: 'DEFAULT' as const
      }

      // Use high impact quote
      externalMocks.jupiter.setCustomQuoteResponse(fixtures.jupiter.highImpactQuote)

      // Act
      const result = await tradingService.simulateTrade(simulationParams)

      // Assert
      expect(result.success).toBe(true)
      expect(result.warnings).toContain('High price impact detected')
      expect(result.priceImpact).toBeGreaterThan(2)
    })
  })

  describe('healthCheck', () => {
    it('should return true when all services are healthy', async () => {
      // Arrange
      externalMocks.setHealthyState()

      // Act
      const isHealthy = await tradingService.healthCheck()

      // Assert
      expect(isHealthy).toBe(true)
    })

    it('should return false when Jupiter service is unhealthy', async () => {
      // Arrange
      externalMocks.jupiter.setQuoteFailure(true)

      // Act
      const isHealthy = await tradingService.healthCheck()

      // Assert
      expect(isHealthy).toBe(false)
    })

    it('should handle RPC connection failures', async () => {
      // Arrange
      externalMocks.helius.setRpcFailure(true)

      // Act
      const isHealthy = await tradingService.healthCheck()

      // Assert
      expect(isHealthy).toBe(false)
    })
  })

  describe('getTradingPresets', () => {
    beforeEach(async () => {
      // Create test trading presets
      await dbHelper.createTestTradingPreset('DEFAULT', fixtures.tradingPresets.default)
      await dbHelper.createTestTradingPreset('VOL', fixtures.tradingPresets.volatile)
    })

    it('should return all available trading presets', async () => {
      // Act
      const presets = await tradingService.getTradingPresets()

      // Assert
      expect(presets).toHaveLength(2)
      expect(presets[0]).toHaveProperty('name')
      expect(presets[0]).toHaveProperty('priorityFee')
      expect(presets[0]).toHaveProperty('slippageLimit')
      expect(presets[0]).toHaveProperty('mevProtectionLevel')
    })

    it('should cache trading presets for performance', async () => {
      // Act
      const presets1 = await tradingService.getTradingPresets()
      const presets2 = await tradingService.getTradingPresets()

      // Assert
      expect(presets1).toEqual(presets2)
      
      // Check Redis cache was used
      const cachedPreset = await redisMock.getJSON('preset:DEFAULT')
      expect(cachedPreset).toBeDefined()
    })
  })

  describe('error handling', () => {
    it('should handle network timeouts gracefully', async () => {
      // Arrange
      externalMocks.setDelays(10000) // 10 second delay

      const quoteParams = {
        inputMint: fixtures.tokens.SOL.address,
        outputMint: fixtures.tokens.BONK.address,
        amount: **********,
        slippageBps: 100
      }

      // Act & Assert
      await expect(tradingService.getQuote(quoteParams)).rejects.toThrow()
    })

    it('should handle invalid token addresses', async () => {
      // Arrange
      const invalidParams = {
        inputMint: 'invalid-address',
        outputMint: fixtures.tokens.BONK.address,
        amount: **********,
        slippageBps: 100
      }

      // Act & Assert
      await expect(tradingService.getQuote(invalidParams)).rejects.toThrow()
    })

    it('should handle database connection failures', async () => {
      // This would test database failure scenarios
      // Implementation depends on how database errors are handled
    })
  })

  describe('performance tests', () => {
    it('should complete quote requests within acceptable time limits', async () => {
      // Arrange
      const quoteParams = {
        inputMint: fixtures.tokens.SOL.address,
        outputMint: fixtures.tokens.BONK.address,
        amount: **********,
        slippageBps: 100
      }

      const startTime = Date.now()

      // Act
      await tradingService.getQuote(quoteParams)
      
      // Assert
      const executionTime = Date.now() - startTime
      expect(executionTime).toBeLessThan(5000) // Should complete within 5 seconds
    })

    it('should handle concurrent quote requests efficiently', async () => {
      // Arrange
      const quoteParams = {
        inputMint: fixtures.tokens.SOL.address,
        outputMint: fixtures.tokens.BONK.address,
        amount: **********,
        slippageBps: 100
      }

      // Act
      const promises = Array(10).fill(null).map(() => tradingService.getQuote(quoteParams))
      const results = await Promise.all(promises)

      // Assert
      expect(results).toHaveLength(10)
      results.forEach(result => {
        expect(result).toBeValidQuote()
      })
    })
  })
})