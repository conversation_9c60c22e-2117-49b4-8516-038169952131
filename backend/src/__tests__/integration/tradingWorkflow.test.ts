/**
 * Integration tests for complete trading workflows
 */

import { DatabaseTestHelper } from '../utils/databaseHelpers'
import { mockRedisService } from '../mocks/redisMock'
import { externalMocks } from '../mocks/externalApiMocks'
import { fixtures } from '../fixtures/testFixtures'
import { resetTestDatabase, waitFor } from '../utils/testUtils'

// Import services for integration testing
import { TradingService } from '@/services/tradingService'
import { PortfolioService } from '@/services/portfolioService'
import { TransactionRecordingService } from '@/services/transactionRecordingService'
import { ExitStrategyService } from '@/services/exitStrategyService'

describe('Integration: Complete Trading Workflows', () => {
  let dbHelper: DatabaseTestHelper
  let redisMock: any
  let testUser: any

  beforeAll(async () => {
    dbHelper = new DatabaseTestHelper()
    const { mock } = mockRedisService()
    redisMock = mock
    externalMocks.setHealthyState()
  })

  beforeEach(async () => {
    await resetTestDatabase()
    await redisMock.flushAll()
    externalMocks.reset()
    
    // Create test user for all workflows
    testUser = await dbHelper.createTestUser(fixtures.users.basicUser)
  })

  afterAll(async () => {
    await dbHelper.cleanupTestPrismaClient()
  })

  describe('Buy → Hold → Sell Workflow', () => {
    it('should complete full trading cycle successfully', async () => {
      // Arrange
      const tradeParams = {
        tokenIn: fixtures.tokens.SOL.address,
        tokenOut: fixtures.tokens.BONK.address,
        amount: **********, // 1 SOL
        slippage: 1.0,
        preset: 'DEFAULT' as const
      }

      // Configure successful external responses
      externalMocks.jupiter.setCustomQuoteResponse(fixtures.jupiter.validQuoteResponse)
      externalMocks.jupiter.setCustomSwapResponse(fixtures.jupiter.validSwapResponse)
      
      // Set up price data
      externalMocks.priceService.setPriceData(fixtures.tokens.SOL.address, 120.50)
      externalMocks.priceService.setPriceData(fixtures.tokens.BONK.address, 0.000012)

      // Step 1: Execute Buy Trade
      const buyResult = await TradingService.executeTrade(
        tradeParams,
        testUser.walletAddress,
        testUser.id
      )

      expect(buyResult.success).toBe(true)
      expect(buyResult.transactionHash).toBeDefined()

      // Wait for transaction processing
      await waitFor(async () => {
        const transactions = await TransactionRecordingService.queryTransactions({
          userId: testUser.id
        })
        return transactions.total > 0
      }, 5000)

      // Step 2: Verify Position Creation
      const portfolio = await PortfolioService.getPortfolioSummary(testUser.id)
      expect(portfolio.positions).toHaveLength(1)
      expect(portfolio.totalValue).toBeGreaterThan(0)
      expect(portfolio.activePositions).toBe(1)

      const position = portfolio.positions[0]
      expect(position.tokenSymbol).toBe('BONK')
      expect(position.status).toBe('ACTIVE')

      // Step 3: Simulate Price Movement (Profit)
      externalMocks.priceService.setPriceData(fixtures.tokens.BONK.address, 0.000018) // +50%

      // Update portfolio with new prices
      const updatedPortfolio = await PortfolioService.getPortfolioSummary(testUser.id, true)
      expect(updatedPortfolio.totalPnL).toBeGreaterThan(0)
      expect(updatedPortfolio.totalPnLPercent).toBeGreaterThan(0)

      // Step 4: Execute Sell Trade
      const sellParams = {
        tokenIn: fixtures.tokens.BONK.address,
        tokenOut: fixtures.tokens.SOL.address,
        amount: parseInt(fixtures.jupiter.validQuoteResponse.outAmount),
        slippage: 1.0,
        preset: 'DEFAULT' as const
      }

      // Configure sell response
      externalMocks.jupiter.setCustomQuoteResponse({
        ...fixtures.jupiter.validQuoteResponse,
        inputMint: fixtures.tokens.BONK.address,
        outputMint: fixtures.tokens.SOL.address,
        inAmount: fixtures.jupiter.validQuoteResponse.outAmount,
        outAmount: '1500000000' // 1.5 SOL (profit)
      })

      const sellResult = await TradingService.executeTrade(
        sellParams,
        testUser.walletAddress,
        testUser.id
      )

      expect(sellResult.success).toBe(true)

      // Step 5: Verify Final State
      await waitFor(async () => {
        const transactions = await TransactionRecordingService.queryTransactions({
          userId: testUser.id
        })
        return transactions.total >= 2
      }, 5000)

      const finalPortfolio = await PortfolioService.getPortfolioSummary(testUser.id, true)
      expect(finalPortfolio.performance.totalReturn).toBeGreaterThan(0)
      expect(finalPortfolio.performance.winRate).toBeGreaterThan(0)

      // Verify transaction history
      const transactionHistory = await TransactionRecordingService.queryTransactions({
        userId: testUser.id,
        sortBy: 'timestamp',
        sortOrder: 'asc'
      })

      expect(transactionHistory.transactions).toHaveLength(2)
      expect(transactionHistory.transactions[0].type).toBe('SWAP')
      expect(transactionHistory.transactions[1].type).toBe('SWAP')
    })

    it('should handle failed trade execution gracefully', async () => {
      // Arrange
      const tradeParams = {
        tokenIn: fixtures.tokens.SOL.address,
        tokenOut: fixtures.tokens.BONK.address,
        amount: **********,
        slippage: 1.0,
        preset: 'DEFAULT' as const
      }

      // Configure external services to fail
      externalMocks.jupiter.setQuoteFailure(true)

      // Act
      const result = await TradingService.executeTrade(
        tradeParams,
        testUser.walletAddress,
        testUser.id
      )

      // Assert
      expect(result.success).toBe(false)
      expect(result.error).toBeDefined()

      // Verify no position was created
      const portfolio = await PortfolioService.getPortfolioSummary(testUser.id)
      expect(portfolio.positions).toHaveLength(0)

      // Verify no transaction was recorded
      const transactions = await TransactionRecordingService.queryTransactions({
        userId: testUser.id
      })
      expect(transactions.total).toBe(0)
    })
  })

  describe('Exit Strategy Integration', () => {
    it('should create position with exit strategy and monitor correctly', async () => {
      // Step 1: Create successful trade
      const tradeParams = {
        tokenIn: fixtures.tokens.SOL.address,
        tokenOut: fixtures.tokens.BONK.address,
        amount: **********,
        slippage: 1.0,
        preset: 'DEFAULT' as const
      }

      externalMocks.jupiter.setCustomQuoteResponse(fixtures.jupiter.validQuoteResponse)
      externalMocks.jupiter.setCustomSwapResponse(fixtures.jupiter.validSwapResponse)

      const tradeResult = await TradingService.executeTrade(
        tradeParams,
        testUser.walletAddress,
        testUser.id
      )

      expect(tradeResult.success).toBe(true)

      // Wait for position creation
      await waitFor(async () => {
        const portfolio = await PortfolioService.getPortfolioSummary(testUser.id)
        return portfolio.positions.length > 0
      }, 5000)

      const portfolio = await PortfolioService.getPortfolioSummary(testUser.id)
      const position = portfolio.positions[0]

      // Step 2: Create exit strategy
      const strategyData = {
        ...fixtures.exitStrategies.basicStopLoss,
        userId: testUser.id,
        positionId: position.id
      }

      const strategy = await ExitStrategyService.createExitStrategy(strategyData)
      expect(strategy.id).toBeDefined()
      expect(strategy.status).toBe('ACTIVE')

      // Step 3: Simulate price drop to trigger stop loss
      externalMocks.priceService.setPriceData(fixtures.tokens.BONK.address, 0.000008) // -33%

      // Trigger monitoring cycle
      await ExitStrategyService.executeMonitoringCycle()

      // Step 4: Verify strategy execution (in real scenario, this would trigger a sell)
      const updatedStrategy = await ExitStrategyService.getStrategy(strategy.id, testUser.id)
      expect(updatedStrategy).toBeDefined()

      // In a full implementation, we would verify that the stop loss was triggered
      // and a sell transaction was created
    })

    it('should handle multiple strategies on same position', async () => {
      // Create position
      const position = await dbHelper.createTestPosition(testUser.id, fixtures.positions.profitablePosition)

      // Create multiple strategies
      const stopLossStrategy = await ExitStrategyService.createExitStrategy({
        ...fixtures.exitStrategies.basicStopLoss,
        userId: testUser.id,
        positionId: position.id,
        name: 'Stop Loss Strategy'
      })

      const takeProfitStrategy = await ExitStrategyService.createExitStrategy({
        ...fixtures.exitStrategies.advancedStrategy,
        userId: testUser.id,
        positionId: position.id,
        name: 'Take Profit Strategy'
      })

      // Verify both strategies are active
      const userStrategies = await ExitStrategyService.getUserStrategies(testUser.id)
      expect(userStrategies).toHaveLength(2)
      expect(userStrategies.every(s => s.status === 'ACTIVE')).toBe(true)

      // Execute monitoring cycle
      await ExitStrategyService.executeMonitoringCycle()

      // Verify strategies are still properly managed
      const updatedStrategies = await ExitStrategyService.getUserStrategies(testUser.id)
      expect(updatedStrategies).toHaveLength(2)
    })
  })

  describe('Portfolio Management Integration', () => {
    it('should maintain accurate portfolio state across multiple trades', async () => {
      // Create multiple positions through trades
      const trades = [
        {
          tokenOut: fixtures.tokens.BONK.address,
          outSymbol: 'BONK',
          amount: **********
        },
        {
          tokenOut: fixtures.tokens.WIF.address,
          outSymbol: 'WIF',
          amount: 500000000
        },
        {
          tokenOut: fixtures.tokens.PEPE.address,
          outSymbol: 'PEPE',
          amount: 2000000000
        }
      ]

      // Execute trades sequentially
      for (const trade of trades) {
        const tradeParams = {
          tokenIn: fixtures.tokens.SOL.address,
          tokenOut: trade.tokenOut,
          amount: trade.amount,
          slippage: 1.0,
          preset: 'DEFAULT' as const
        }

        externalMocks.jupiter.setCustomQuoteResponse({
          ...fixtures.jupiter.validQuoteResponse,
          outputMint: trade.tokenOut,
          outAmount: (trade.amount * 100).toString() // Mock conversion rate
        })

        const result = await TradingService.executeTrade(
          tradeParams,
          testUser.walletAddress,
          testUser.id
        )

        expect(result.success).toBe(true)

        // Wait for transaction processing
        await waitFor(async () => {
          const transactions = await TransactionRecordingService.queryTransactions({
            userId: testUser.id
          })
          return transactions.total >= trades.indexOf(trade) + 1
        }, 5000)
      }

      // Verify portfolio state
      const portfolio = await PortfolioService.getPortfolioSummary(testUser.id, true)
      expect(portfolio.positions).toHaveLength(3)
      expect(portfolio.activePositions).toBe(3)
      expect(portfolio.totalValue).toBeGreaterThan(0)

      // Check diversification metrics
      expect(portfolio.diversification.concentration).toBeLessThan(1) // Not fully concentrated
      expect(portfolio.diversification.largestPosition).toBeLessThan(100) // No single position is 100%

      // Verify individual positions
      const bondPosition = portfolio.positions.find(p => p.tokenSymbol === 'BONK')
      const wifPosition = portfolio.positions.find(p => p.tokenSymbol === 'WIF')
      const pepePosition = portfolio.positions.find(p => p.tokenSymbol === 'PEPE')

      expect(bondPosition).toBeDefined()
      expect(wifPosition).toBeDefined()
      expect(pepePosition).toBeDefined()

      // Check transaction recording
      const allTransactions = await TransactionRecordingService.queryTransactions({
        userId: testUser.id
      })
      expect(allTransactions.total).toBe(3)
    })

    it('should calculate portfolio analytics correctly', async () => {
      // Create test positions manually for predictable analytics
      const positions = await Promise.all([
        dbHelper.createTestPosition(testUser.id, {
          ...fixtures.positions.profitablePosition,
          tokenSymbol: 'BONK',
          entryPrice: '0.000010',
          currentPrice: '0.000015',
          quantity: '1000000'
        }),
        dbHelper.createTestPosition(testUser.id, {
          ...fixtures.positions.losingPosition,
          tokenSymbol: 'WIF',
          entryPrice: '3.00',
          currentPrice: '2.00',
          quantity: '100'
        })
      ])

      // Create corresponding transactions
      await Promise.all(positions.map(position => 
        dbHelper.createTestTransaction(testUser.id, position.id, {
          type: 'BUY',
          tokenOutSymbol: position.tokenSymbol,
          amountOut: position.quantity
        })
      ))

      // Get portfolio analytics
      const analytics = await PortfolioService.getPortfolioAnalytics(testUser.id, 'month')

      expect(analytics.userId).toBe(testUser.id)
      expect(analytics.period).toBe('month')
      expect(analytics.performanceChart).toBeDefined()
      expect(analytics.topPerformers).toBeDefined()
      expect(analytics.topLosers).toBeDefined()

      // Verify performance calculation
      const portfolio = await PortfolioService.getPortfolioSummary(testUser.id)
      expect(portfolio.performance.winRate).toBeGreaterThan(0)
      expect(portfolio.performance.winRate).toBeLessThan(100)
    })
  })

  describe('Transaction Recording Integration', () => {
    it('should maintain transaction history consistency', async () => {
      // Execute multiple trades to build history
      const tradeCount = 5
      const trades = Array(tradeCount).fill(null).map((_, index) => ({
        tokenOut: fixtures.tokens.BONK.address,
        amount: ********** + (index * 100000000),
        type: index % 2 === 0 ? 'BUY' : 'SELL'
      }))

      for (const [index, trade] of trades.entries()) {
        externalMocks.jupiter.setCustomQuoteResponse({
          ...fixtures.jupiter.validQuoteResponse,
          inAmount: trade.amount.toString(),
          outAmount: (trade.amount * (trade.type === 'BUY' ? 100 : 0.01)).toString()
        })

        const tradeParams = {
          tokenIn: trade.type === 'BUY' ? fixtures.tokens.SOL.address : fixtures.tokens.BONK.address,
          tokenOut: trade.type === 'BUY' ? fixtures.tokens.BONK.address : fixtures.tokens.SOL.address,
          amount: trade.amount,
          slippage: 1.0,
          preset: 'DEFAULT' as const
        }

        const result = await TradingService.executeTrade(
          tradeParams,
          testUser.walletAddress,
          testUser.id
        )

        expect(result.success).toBe(true)

        // Wait for transaction recording
        await waitFor(async () => {
          const transactions = await TransactionRecordingService.queryTransactions({
            userId: testUser.id
          })
          return transactions.total >= index + 1
        }, 5000)
      }

      // Verify transaction statistics
      const stats = await TransactionRecordingService.getTransactionStats(testUser.id)
      expect(stats.totalTransactions).toBe(tradeCount)
      expect(stats.successfulTransactions).toBe(tradeCount)
      expect(stats.totalVolume).toBeGreaterThan(0)
      expect(stats.averageExecutionTime).toBeGreaterThan(0)

      // Verify transaction querying capabilities
      const buyTransactions = await TransactionRecordingService.queryTransactions({
        userId: testUser.id,
        type: ['BUY']
      })
      
      expect(buyTransactions.total).toBe(Math.ceil(tradeCount / 2))

      const recentTransactions = await TransactionRecordingService.queryTransactions({
        userId: testUser.id,
        limit: 3,
        sortBy: 'timestamp',
        sortOrder: 'desc'
      })

      expect(recentTransactions.transactions).toHaveLength(3)
      expect(recentTransactions.hasMore).toBe(true)
    })
  })

  describe('Error Recovery and Resilience', () => {
    it('should recover from temporary external service failures', async () => {
      const tradeParams = {
        tokenIn: fixtures.tokens.SOL.address,
        tokenOut: fixtures.tokens.BONK.address,
        amount: **********,
        slippage: 1.0,
        preset: 'DEFAULT' as const
      }

      // First attempt fails
      externalMocks.jupiter.setQuoteFailure(true)
      
      const failedResult = await TradingService.executeTrade(
        tradeParams,
        testUser.walletAddress,
        testUser.id
      )
      
      expect(failedResult.success).toBe(false)

      // Service recovers
      externalMocks.jupiter.setQuoteFailure(false)
      externalMocks.jupiter.setCustomQuoteResponse(fixtures.jupiter.validQuoteResponse)
      externalMocks.jupiter.setCustomSwapResponse(fixtures.jupiter.validSwapResponse)

      // Second attempt succeeds
      const successResult = await TradingService.executeTrade(
        tradeParams,
        testUser.walletAddress,
        testUser.id
      )

      expect(successResult.success).toBe(true)

      // Verify only successful trade is recorded
      const transactions = await TransactionRecordingService.queryTransactions({
        userId: testUser.id,
        status: ['CONFIRMED']
      })

      expect(transactions.total).toBe(1)
    })

    it('should handle database connection interruptions', async () => {
      // This test would require more sophisticated database mocking
      // to simulate connection failures and recovery
      expect(true).toBe(true) // Placeholder
    })
  })

  describe('Performance and Scalability', () => {
    it('should handle concurrent trading operations', async () => {
      const concurrentTrades = 5
      const tradePromises = Array(concurrentTrades).fill(null).map(async (_, index) => {
        const tradeParams = {
          tokenIn: fixtures.tokens.SOL.address,
          tokenOut: fixtures.tokens.BONK.address,
          amount: ********** + (index * 100000000),
          slippage: 1.0,
          preset: 'DEFAULT' as const
        }

        externalMocks.jupiter.setCustomQuoteResponse({
          ...fixtures.jupiter.validQuoteResponse,
          inAmount: tradeParams.amount.toString()
        })

        return TradingService.executeTrade(
          tradeParams,
          testUser.walletAddress,
          testUser.id
        )
      })

      const results = await Promise.all(tradePromises)

      // All trades should succeed
      expect(results.every(r => r.success)).toBe(true)

      // Wait for all transactions to be processed
      await waitFor(async () => {
        const transactions = await TransactionRecordingService.queryTransactions({
          userId: testUser.id
        })
        return transactions.total >= concurrentTrades
      }, 10000)

      // Verify all transactions were recorded
      const finalTransactions = await TransactionRecordingService.queryTransactions({
        userId: testUser.id
      })
      expect(finalTransactions.total).toBe(concurrentTrades)

      // Verify portfolio state consistency
      const portfolio = await PortfolioService.getPortfolioSummary(testUser.id, true)
      expect(portfolio.positions).toHaveLength(1) // All trades for same token
      expect(portfolio.totalValue).toBeGreaterThan(0)
    })
  })
})