import { TradingService } from '@/services/tradingService'
import { ExitStrategyService } from '@/services/exitStrategyService'
import { PortfolioService } from '@/services/portfolioService'
import { TransactionRecordingService } from '@/services/transactionRecordingService'
import { PriceService } from '@/services/priceService'
import { HealthMonitor } from '@/services/healthMonitor'
import { QueueManager } from '@/jobs/queueManager'
import { testFixtures } from '../fixtures/testFixtures'
import { createTestUser, cleanupTestData } from '../utils/databaseHelpers'

describe('Performance & Stress Testing', () => {
  let testUserId: string
  let testUsers: string[] = []

  beforeAll(async () => {
    // Create multiple test users for concurrent testing
    for (let i = 0; i < 10; i++) {
      const user = await createTestUser()
      testUsers.push(user.id)
    }
    testUserId = testUsers[0]
  })

  afterAll(async () => {
    await cleanupTestData()
  })

  describe('High-Load Concurrent Operations', () => {
    it('should handle 100 concurrent trade requests', async () => {
      const startTime = Date.now()
      
      // Generate 100 concurrent trade requests with different parameters
      const tradePromises = Array(100).fill(null).map((_, index) => 
        TradingService.executeTrade(
          {
            ...testFixtures.tradeParams.validBuy,
            amount: 0.001 + (index * 0.001), // Vary amounts
            slippage: 1 + (index % 5) // Vary slippage 1-5%
          },
          testFixtures.users.basicUser.walletAddress,
          testUsers[index % testUsers.length] // Distribute across users
        )
      )

      const results = await Promise.allSettled(tradePromises)
      const endTime = Date.now()

      const successful = results.filter(r => 
        r.status === 'fulfilled' && r.value.success
      ).length
      
      const failed = results.filter(r => 
        r.status === 'rejected' || 
        (r.status === 'fulfilled' && !r.value.success)
      ).length

      // Performance assertions
      expect(endTime - startTime).toBeLessThan(30000) // Should complete within 30 seconds
      expect(successful).toBeGreaterThan(70) // At least 70% success rate
      expect(failed).toBeLessThan(30) // Less than 30% failure rate

      console.log(`Concurrent trades: ${successful} successful, ${failed} failed in ${endTime - startTime}ms`)
    })

    it('should handle concurrent exit strategy monitoring', async () => {
      // Create multiple exit strategies
      const strategies = []
      for (let i = 0; i < 50; i++) {
        const strategy = await ExitStrategyService.createExitStrategy({
          userId: testUsers[i % testUsers.length],
          positionId: `concurrent_position_${i}`,
          name: `Concurrent Strategy ${i}`,
          type: 'STOP_LOSS',
          stopLoss: {
            enabled: true,
            type: 'PERCENTAGE',
            triggerPercent: 10 + (i % 20) // Vary trigger percentages
          },
          status: 'ACTIVE'
        })
        strategies.push(strategy)
      }

      const startTime = Date.now()
      
      // Execute multiple monitoring cycles concurrently
      const monitoringPromises = Array(10).fill(null).map(() => 
        ExitStrategyService.executeMonitoringCycle()
      )

      await Promise.all(monitoringPromises)
      const endTime = Date.now()

      // Should complete monitoring efficiently
      expect(endTime - startTime).toBeLessThan(15000) // Within 15 seconds
      
      // Clean up strategies
      for (const strategy of strategies) {
        await ExitStrategyService.deleteExitStrategy(strategy.id, strategy.userId)
      }
    })

    it('should handle concurrent portfolio calculations', async () => {
      // Create positions for all test users
      for (const userId of testUsers) {
        for (let i = 0; i < 20; i++) {
          await PortfolioService.createPosition({
            userId,
            tokenAddress: `token_${i}_address`,
            tokenSymbol: `TOK${i}`,
            quantity: Math.random() * 1000,
            entryPrice: Math.random() * 100,
            currentPrice: Math.random() * 100
          })
        }
      }

      const startTime = Date.now()
      
      // Calculate portfolios for all users concurrently
      const portfolioPromises = testUsers.map(userId => 
        PortfolioService.getPortfolioSummary(userId)
      )

      const portfolios = await Promise.all(portfolioPromises)
      const endTime = Date.now()

      // All portfolios should be calculated successfully
      expect(portfolios).toHaveLength(testUsers.length)
      expect(portfolios.every(p => p.totalValue !== undefined)).toBe(true)
      expect(endTime - startTime).toBeLessThan(10000) // Within 10 seconds

      console.log(`Portfolio calculations: ${portfolios.length} users in ${endTime - startTime}ms`)
    })
  })

  describe('Memory & Resource Management', () => {
    it('should handle memory pressure during large data processing', async () => {
      const initialMemory = process.memoryUsage()
      
      // Create large dataset
      const largeTransactionBatch = Array(10000).fill(null).map((_, index) => ({
        userId: testUsers[index % testUsers.length],
        type: index % 2 === 0 ? 'BUY' : 'SELL',
        tokenIn: testFixtures.tokens.sol.address,
        tokenOut: testFixtures.tokens.usdc.address,
        amountIn: Math.random() * 10,
        amountOut: Math.random() * 1000,
        price: Math.random() * 200,
        signature: `large_batch_${index}`,
        status: 'CONFIRMED'
      }))

      // Process in chunks to test memory management
      const chunkSize = 100
      const chunks = []
      for (let i = 0; i < largeTransactionBatch.length; i += chunkSize) {
        chunks.push(largeTransactionBatch.slice(i, i + chunkSize))
      }

      for (const chunk of chunks) {
        const promises = chunk.map(tx => 
          TransactionRecordingService.recordTransaction(tx)
        )
        await Promise.all(promises)
        
        // Force garbage collection if available
        if (global.gc) {
          global.gc()
        }
      }

      const finalMemory = process.memoryUsage()
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed

      // Memory increase should be reasonable (less than 500MB)
      expect(memoryIncrease).toBeLessThan(500 * 1024 * 1024)
      
      console.log(`Memory increase: ${Math.round(memoryIncrease / 1024 / 1024)}MB`)
    })

    it('should handle CPU-intensive operations efficiently', async () => {
      const startTime = Date.now()
      let operationsCompleted = 0

      // CPU-intensive portfolio risk calculations
      const riskCalculationPromises = testUsers.map(async (userId) => {
        for (let i = 0; i < 10; i++) {
          await RiskService.calculatePortfolioRisk(userId)
          operationsCompleted++
        }
      })

      await Promise.all(riskCalculationPromises)
      const endTime = Date.now()

      const operationsPerSecond = (operationsCompleted * 1000) / (endTime - startTime)

      // Should maintain reasonable throughput
      expect(operationsPerSecond).toBeGreaterThan(5) // At least 5 operations per second
      expect(endTime - startTime).toBeLessThan(60000) // Complete within 1 minute

      console.log(`Risk calculations: ${operationsCompleted} ops in ${endTime - startTime}ms (${operationsPerSecond.toFixed(2)} ops/sec)`)
    })

    it('should handle database connection pool exhaustion', async () => {
      // Simulate heavy database load
      const heavyDbOperations = Array(200).fill(null).map((_, index) => 
        TransactionRecordingService.queryTransactions({
          userId: testUsers[index % testUsers.length],
          limit: 100,
          offset: index * 10
        })
      )

      const startTime = Date.now()
      const results = await Promise.allSettled(heavyDbOperations)
      const endTime = Date.now()

      const successful = results.filter(r => r.status === 'fulfilled').length
      const failed = results.filter(r => r.status === 'rejected').length

      // Should handle connection pool pressure gracefully
      expect(successful).toBeGreaterThan(150) // At least 75% success
      expect(failed).toBeLessThan(50) // Less than 25% failure
      expect(endTime - startTime).toBeLessThan(45000) // Within 45 seconds

      console.log(`DB operations: ${successful} successful, ${failed} failed in ${endTime - startTime}ms`)
    })
  })

  describe('Queue System Performance', () => {
    it('should handle queue overflow scenarios', async () => {
      // Flood the queue with jobs
      const queueJobs = Array(1000).fill(null).map((_, index) => ({
        type: 'PRICE_UPDATE',
        data: {
          tokenAddress: `token_${index % 100}`,
          price: Math.random() * 1000,
          timestamp: Date.now()
        }
      }))

      const startTime = Date.now()
      
      const queuePromises = queueJobs.map(job => 
        QueueManager.addJob(job.type, job.data).catch(err => ({ error: err.message }))
      )

      const results = await Promise.allSettled(queuePromises)
      const endTime = Date.now()

      const successful = results.filter(r => 
        r.status === 'fulfilled' && !r.value.error
      ).length

      // Should handle queue pressure without complete failure
      expect(successful).toBeGreaterThan(500) // At least 50% success
      expect(endTime - startTime).toBeLessThan(30000) // Within 30 seconds

      console.log(`Queue operations: ${successful} successful in ${endTime - startTime}ms`)
    })

    it('should handle queue processing delays', async () => {
      // Add jobs that simulate processing delays
      const delayedJobs = Array(50).fill(null).map((_, index) => 
        QueueManager.addJob('DELAYED_PROCESSING', {
          processingTime: 1000 + (index * 100), // Variable processing times
          data: `delayed_job_${index}`
        })
      )

      const startTime = Date.now()
      await Promise.all(delayedJobs)
      
      // Wait for processing to complete
      await new Promise(resolve => setTimeout(resolve, 5000))
      
      const endTime = Date.now()
      
      // Should handle delayed processing efficiently
      expect(endTime - startTime).toBeLessThan(60000) // Within 1 minute
    })
  })

  describe('API Response Time Performance', () => {
    it('should maintain response times under load', async () => {
      const responseTimes: number[] = []
      
      // Test API endpoints under concurrent load
      const apiTests = Array(100).fill(null).map(async (_, index) => {
        const startTime = Date.now()
        
        try {
          await PortfolioService.getPortfolioSummary(testUsers[index % testUsers.length])
          const endTime = Date.now()
          responseTimes.push(endTime - startTime)
        } catch (error) {
          responseTimes.push(10000) // 10 second penalty for errors
        }
      })

      await Promise.all(apiTests)

      const avgResponseTime = responseTimes.reduce((a, b) => a + b) / responseTimes.length
      const maxResponseTime = Math.max(...responseTimes)
      const p95ResponseTime = responseTimes.sort((a, b) => a - b)[Math.floor(responseTimes.length * 0.95)]

      // Performance thresholds
      expect(avgResponseTime).toBeLessThan(2000) // Average under 2 seconds
      expect(p95ResponseTime).toBeLessThan(5000) // 95th percentile under 5 seconds
      expect(maxResponseTime).toBeLessThan(10000) // Max under 10 seconds

      console.log(`Response times - Avg: ${avgResponseTime.toFixed(0)}ms, P95: ${p95ResponseTime}ms, Max: ${maxResponseTime}ms`)
    })

    it('should handle price service load efficiently', async () => {
      const priceRequests = Array(200).fill(null).map(() => 
        PriceService.getTokenPrice(testFixtures.tokens.sol.address)
      )

      const startTime = Date.now()
      const prices = await Promise.all(priceRequests)
      const endTime = Date.now()

      const validPrices = prices.filter(p => p !== null && p > 0).length
      const requestsPerSecond = (priceRequests.length * 1000) / (endTime - startTime)

      // Should handle price requests efficiently
      expect(validPrices).toBeGreaterThan(150) // At least 75% valid responses
      expect(requestsPerSecond).toBeGreaterThan(10) // At least 10 requests per second
      expect(endTime - startTime).toBeLessThan(20000) // Within 20 seconds

      console.log(`Price requests: ${validPrices} valid prices, ${requestsPerSecond.toFixed(1)} req/sec`)
    })
  })

  describe('System Health Under Stress', () => {
    it('should maintain health monitoring accuracy under load', async () => {
      // Generate background load
      const backgroundLoad = Array(50).fill(null).map(() => 
        TradingService.executeTrade(
          testFixtures.tradeParams.validBuy,
          testFixtures.users.basicUser.walletAddress,
          testUserId
        )
      )

      // Monitor health during load
      const healthChecks = Array(20).fill(null).map(async () => {
        await new Promise(resolve => setTimeout(resolve, Math.random() * 1000))
        return HealthMonitor.getHealthSummary()
      })

      const [loadResults, healthResults] = await Promise.all([
        Promise.allSettled(backgroundLoad),
        Promise.all(healthChecks)
      ])

      // Health monitoring should remain accurate
      healthResults.forEach(health => {
        expect(health.status).toMatch(/^(healthy|degraded|unhealthy)$/)
        expect(health.services).toBeInstanceOf(Array)
        expect(health.services.length).toBeGreaterThan(0)
      })

      const degradedChecks = healthResults.filter(h => h.status === 'degraded').length
      
      // Some degradation is expected under load
      expect(degradedChecks).toBeLessThan(healthResults.length) // Not all should be degraded
    })

    it('should handle gradual performance degradation', async () => {
      const performanceMetrics: Array<{ timestamp: number, responseTime: number }> = []
      
      // Gradually increase load and measure performance
      for (let load = 10; load <= 100; load += 10) {
        const startTime = Date.now()
        
        const requests = Array(load).fill(null).map(() => 
          PortfolioService.getPortfolioSummary(testUserId)
        )
        
        await Promise.all(requests)
        const endTime = Date.now()
        
        performanceMetrics.push({
          timestamp: Date.now(),
          responseTime: (endTime - startTime) / load // Average response time per request
        })
        
        // Brief pause between load increases
        await new Promise(resolve => setTimeout(resolve, 1000))
      }

      // Performance should degrade gradually, not cliff-drop
      const responseTimeIncrease = performanceMetrics[performanceMetrics.length - 1].responseTime - 
                                  performanceMetrics[0].responseTime

      expect(responseTimeIncrease).toBeLessThan(5000) // Less than 5 second increase
      
      // No single jump should be more than 2x
      for (let i = 1; i < performanceMetrics.length; i++) {
        const ratio = performanceMetrics[i].responseTime / performanceMetrics[i - 1].responseTime
        expect(ratio).toBeLessThan(3) // No more than 3x increase between measurements
      }

      console.log('Performance degradation:', performanceMetrics.map(m => 
        `${m.responseTime.toFixed(0)}ms`
      ).join(' -> '))
    })
  })

  describe('Recovery Performance', () => {
    it('should recover quickly from temporary overload', async () => {
      // Create temporary overload
      const overloadPromises = Array(500).fill(null).map(() => 
        PortfolioService.getPortfolioSummary(testUserId)
      )

      // Start overload
      const overloadStart = Date.now()
      await Promise.allSettled(overloadPromises)
      
      // Wait for system to stabilize
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Test recovery performance
      const recoveryStart = Date.now()
      const recoveryTest = await PortfolioService.getPortfolioSummary(testUserId)
      const recoveryEnd = Date.now()
      
      const recoveryTime = recoveryEnd - recoveryStart

      // Should recover to normal performance quickly
      expect(recoveryTest).toBeDefined()
      expect(recoveryTime).toBeLessThan(3000) // Under 3 seconds after stabilization

      console.log(`Recovery time: ${recoveryTime}ms after overload`)
    })

    it('should maintain service availability during peak load', async () => {
      // Simulate peak trading hours with mixed operations
      const peakLoadOperations = [
        ...Array(50).fill(null).map(() => () => 
          TradingService.executeTrade(testFixtures.tradeParams.validBuy, testFixtures.users.basicUser.walletAddress, testUserId)
        ),
        ...Array(50).fill(null).map(() => () => 
          PortfolioService.getPortfolioSummary(testUserId)
        ),
        ...Array(30).fill(null).map(() => () => 
          TransactionRecordingService.queryTransactions({ userId: testUserId, limit: 10 })
        ),
        ...Array(20).fill(null).map(() => () => 
          PriceService.getTokenPrice(testFixtures.tokens.sol.address)
        )
      ]

      const startTime = Date.now()
      const results = await Promise.allSettled(
        peakLoadOperations.map(op => op())
      )
      const endTime = Date.now()

      const successful = results.filter(r => 
        r.status === 'fulfilled' && 
        (typeof r.value === 'object' && r.value !== null)
      ).length

      const availabilityPercent = (successful / results.length) * 100

      // Should maintain high availability during peak load
      expect(availabilityPercent).toBeGreaterThan(85) // At least 85% availability
      expect(endTime - startTime).toBeLessThan(60000) // Complete within 1 minute

      console.log(`Peak load: ${availabilityPercent.toFixed(1)}% availability in ${endTime - startTime}ms`)
    })
  })
})