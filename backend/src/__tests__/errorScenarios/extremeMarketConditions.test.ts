import { TradingService } from '@/services/tradingService'
import { ExitStrategyService } from '@/services/exitStrategyService'
import { PortfolioService } from '@/services/portfolioService'
import { PriceService } from '@/services/priceService'
import { RiskService } from '@/services/riskService'
import { MEVProtectionService } from '@/services/mevProtectionService'
import { SlippageProtectionService } from '@/services/slippageProtectionService'
import { testFixtures } from '../fixtures/testFixtures'
import { createTestUser, cleanupTestData } from '../utils/databaseHelpers'
import nock from 'nock'

describe('Extreme Market Conditions', () => {
  let testUserId: string

  beforeAll(async () => {
    const testUser = await createTestUser()
    testUserId = testUser.id
  })

  afterAll(async () => {
    await cleanupTestData()
    nock.cleanAll()
  })

  beforeEach(() => {
    nock.cleanAll()
  })

  describe('Flash Crash Scenarios', () => {
    it('should handle 90% price drop within seconds', async () => {
      // Mock extreme price volatility
      let priceCallCount = 0
      const crashPrices = [100, 95, 80, 50, 20, 10] // 90% crash

      nock('https://api.coingecko.com')
        .get(/.*/)
        .times(6)
        .reply(() => [200, { 
          price: crashPrices[priceCallCount++], 
          timestamp: Date.now() 
        }])

      // Create position before crash
      const position = await PortfolioService.createPosition({
        userId: testUserId,
        tokenAddress: testFixtures.tokens.sol.address,
        tokenSymbol: 'SOL',
        quantity: 10,
        entryPrice: 100,
        currentPrice: 100
      })

      // Create stop-loss strategy
      const strategy = await ExitStrategyService.createExitStrategy({
        userId: testUserId,
        positionId: position.id,
        name: 'Flash Crash Protection',
        type: 'STOP_LOSS',
        stopLoss: {
          enabled: true,
          type: 'PERCENTAGE',
          triggerPercent: 20 // 20% stop loss
        },
        status: 'ACTIVE'
      })

      // Simulate price monitoring during crash
      for (let i = 0; i < 6; i++) {
        await ExitStrategyService.executeMonitoringCycle()
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      // Strategy should have triggered during crash
      const updatedStrategy = await ExitStrategyService.getStrategy(strategy.id, testUserId)
      expect(updatedStrategy.executionState.executionHistory.length).toBeGreaterThan(0)
      
      const execution = updatedStrategy.executionState.executionHistory[0]
      expect(execution.type).toBe('STOP_LOSS')
      expect(execution.trigger).toContain('Loss')
    })

    it('should handle liquidity disappearance', async () => {
      // Mock Jupiter quote with zero liquidity
      nock('https://quote-api.jup.ag')
        .get('/v6/quote')
        .query(true)
        .reply(200, {
          ...testFixtures.jupiterResponses.validQuote,
          priceImpactPct: '50.0', // Extreme price impact
          outAmount: '100', // Extremely low output
          routePlan: []
        })

      const result = await TradingService.executeTrade(
        {
          ...testFixtures.tradeParams.validBuy,
          amount: 10 // Large amount during liquidity crisis
        },
        testFixtures.users.basicUser.walletAddress,
        testUserId
      )

      expect(result.success).toBe(false)
      expect(result.error).toContain('liquidity') || expect(result.error).toContain('impact')
    })

    it('should handle circuit breaker activation in traditional markets', async () => {
      // Mock market data showing extreme volatility
      const extremeVolatilityData = {
        price: 50,
        volatility: 150, // 150% volatility
        volume24h: 1000000000, // Extremely high volume
        priceChange24h: -85, // 85% drop
        marketCap: 500000000
      }

      nock('https://api.coingecko.com')
        .get(/.*/)
        .reply(200, extremeVolatilityData)

      // Risk service should detect extreme conditions
      const riskMetrics = await RiskService.calculatePortfolioRisk(testUserId)
      
      expect(riskMetrics.riskScore).toBeGreaterThan(90) // Extreme risk
      expect(riskMetrics.volatilityScore).toBeGreaterThan(80)
      
      // Trading should be restricted during extreme conditions
      const result = await TradingService.executeTrade(
        testFixtures.tradeParams.validBuy,
        testFixtures.users.basicUser.walletAddress,
        testUserId
      )

      expect(result.success).toBe(false)
      expect(result.error).toContain('extreme market conditions')
    })
  })

  describe('Network Congestion Scenarios', () => {
    it('should handle Solana network congestion', async () => {
      // Mock high congestion scenario
      nock('https://mainnet.helius-rpc.com')
        .post('/')
        .reply(200, {
          jsonrpc: '2.0',
          id: 1,
          result: {
            value: {
              blockhash: 'test_blockhash',
              lastValidBlockHeight: 1000,
              feeCalculator: {
                lamportsPerSignature: 50000 // Very high fees
              }
            }
          }
        })

      // MEV protection should adjust for congestion
      const mevProtection = await MEVProtectionService.analyzeTransaction({
        signature: 'test_signature',
        instructions: []
      })

      expect(mevProtection.recommendedPriorityFee).toBeGreaterThan(10000)
      expect(mevProtection.networkCongestion).toBe('HIGH')
    })

    it('should handle transaction timeout during congestion', async () => {
      // Mock slow transaction confirmation
      nock('https://mainnet.helius-rpc.com')
        .post('/')
        .reply(200, {
          jsonrpc: '2.0',
          id: 1,
          result: null // Transaction not confirmed
        })
        .persist()

      const result = await TradingService.executeTrade(
        testFixtures.tradeParams.validBuy,
        testFixtures.users.basicUser.walletAddress,
        testUserId
      )

      expect(result.success).toBe(false)
      expect(result.error).toContain('timeout') || expect(result.error).toContain('confirmation')
    })

    it('should handle priority fee escalation', async () => {
      let feeCallCount = 0
      const escalatingFees = [5000, 10000, 20000, 50000] // Escalating priority fees

      nock('https://mainnet.helius-rpc.com')
        .post('/')
        .times(4)
        .reply(() => [200, {
          jsonrpc: '2.0',
          id: 1,
          result: {
            value: escalatingFees[feeCallCount++]
          }
        }])

      // Execute trade during fee escalation
      const result = await TradingService.executeTrade(
        testFixtures.tradeParams.validBuy,
        testFixtures.users.basicUser.walletAddress,
        testUserId
      )

      // Should adapt to higher fees or fail gracefully
      if (!result.success) {
        expect(result.error).toContain('fee') || expect(result.error).toContain('cost')
      }
    })
  })

  describe('Extreme Slippage Scenarios', () => {
    it('should handle 50%+ slippage scenarios', async () => {
      // Mock quote with extreme slippage
      nock('https://quote-api.jup.ag')
        .get('/v6/quote')
        .query(true)
        .reply(200, {
          ...testFixtures.jupiterResponses.validQuote,
          priceImpactPct: '55.5', // 55.5% price impact
          outAmount: String(Math.floor(parseInt(testFixtures.jupiterResponses.validQuote.outAmount) * 0.445))
        })

      const slippageConfig = SlippageProtectionService.calculateOptimalSlippage({
        tokenPair: {
          input: testFixtures.tokens.sol.address,
          output: testFixtures.tokens.usdc.address
        },
        tradeSize: 1000000000,
        marketConditions: {
          volatility: 80,
          liquidity: 10000,
          volume24h: 500000
        }
      })

      // Should recommend very high slippage or reject trade
      expect(slippageConfig.recommended).toBeGreaterThan(20) || expect(slippageConfig.shouldReject).toBe(true)
    })

    it('should handle sandwich attack during high slippage', async () => {
      // Mock sandwich attack scenario
      const sandwichQuote = {
        ...testFixtures.jupiterResponses.validQuote,
        priceImpactPct: '25.0', // High but not extreme
        outAmount: String(Math.floor(parseInt(testFixtures.jupiterResponses.validQuote.outAmount) * 0.75))
      }

      nock('https://quote-api.jup.ag')
        .get('/v6/quote')
        .query(true)
        .reply(200, sandwichQuote)

      // MEV protection should detect potential sandwich
      const mevAnalysis = await MEVProtectionService.analyzeTransaction({
        signature: 'test_signature',
        instructions: [
          {
            programId: 'JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4',
            data: 'sandwich_attack_pattern'
          }
        ]
      })

      expect(mevAnalysis.riskLevel).toBe('HIGH')
      expect(mevAnalysis.threats.some(t => t.type === 'SANDWICH_ATTACK')).toBe(true)
    })

    it('should handle dynamic slippage during volatile periods', async () => {
      // Mock rapidly changing quotes
      const volatileQuotes = [
        { ...testFixtures.jupiterResponses.validQuote, priceImpactPct: '2.0' },
        { ...testFixtures.jupiterResponses.validQuote, priceImpactPct: '8.5' },
        { ...testFixtures.jupiterResponses.validQuote, priceImpactPct: '15.2' },
        { ...testFixtures.jupiterResponses.validQuote, priceImpactPct: '22.8' }
      ]

      let quoteIndex = 0
      nock('https://quote-api.jup.ag')
        .get('/v6/quote')
        .query(true)
        .times(4)
        .reply(() => [200, volatileQuotes[quoteIndex++]])

      // Multiple rapid quote requests should show increasing slippage
      const quotes = []
      for (let i = 0; i < 4; i++) {
        const quote = await TradingService.getQuote({
          inputMint: testFixtures.tokens.sol.address,
          outputMint: testFixtures.tokens.usdc.address,
          amount: 1000000000
        })
        quotes.push(quote)
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      // Should show increasing price impact
      const impacts = quotes.map(q => parseFloat(q.priceImpactPct))
      expect(impacts[3]).toBeGreaterThan(impacts[0])
    })
  })

  describe('Extreme Portfolio Scenarios', () => {
    it('should handle portfolio with 100+ positions', async () => {
      // Create many small positions
      const positions = []
      for (let i = 0; i < 100; i++) {
        const position = await PortfolioService.createPosition({
          userId: testUserId,
          tokenAddress: `token_${i}_address`,
          tokenSymbol: `TOK${i}`,
          quantity: Math.random() * 1000,
          entryPrice: Math.random() * 100,
          currentPrice: Math.random() * 100
        })
        positions.push(position)
      }

      // Portfolio calculation should handle large number of positions
      const startTime = Date.now()
      const portfolio = await PortfolioService.getPortfolioSummary(testUserId)
      const endTime = Date.now()

      expect(portfolio.totalPositions).toBe(100)
      expect(endTime - startTime).toBeLessThan(5000) // Should complete within 5 seconds
      expect(portfolio.diversificationScore).toBeDefined()
    })

    it('should handle portfolio with extreme concentration', async () => {
      // Create one dominant position (95% of portfolio)
      await PortfolioService.createPosition({
        userId: testUserId,
        tokenAddress: testFixtures.tokens.sol.address,
        tokenSymbol: 'SOL',
        quantity: 1000,
        entryPrice: 100,
        currentPrice: 100
      })

      // Create small positions for remaining 5%
      for (let i = 0; i < 10; i++) {
        await PortfolioService.createPosition({
          userId: testUserId,
          tokenAddress: `small_token_${i}`,
          tokenSymbol: `SMALL${i}`,
          quantity: 1,
          entryPrice: 5,
          currentPrice: 5
        })
      }

      const riskMetrics = await RiskService.calculatePortfolioRisk(testUserId)
      
      expect(riskMetrics.concentration).toBeGreaterThan(90) // Extreme concentration
      expect(riskMetrics.riskScore).toBeGreaterThan(70) // High risk due to concentration
    })

    it('should handle portfolio during extreme drawdown', async () => {
      // Create profitable positions
      const positions = []
      for (let i = 0; i < 10; i++) {
        const position = await PortfolioService.createPosition({
          userId: testUserId,
          tokenAddress: `token_${i}`,
          tokenSymbol: `TOK${i}`,
          quantity: 100,
          entryPrice: 10,
          currentPrice: 20 // 100% profit initially
        })
        positions.push(position)
      }

      // Simulate extreme drawdown (90% loss from peak)
      for (const position of positions) {
        await PortfolioService.updatePositionPrice(position.id, 2) // 80% loss
      }

      const portfolio = await PortfolioService.getPortfolioSummary(testUserId)
      const riskMetrics = await RiskService.calculatePortfolioRisk(testUserId)

      expect(portfolio.totalPnLPercent).toBeLessThan(-70) // Extreme drawdown
      expect(riskMetrics.maxDrawdown).toBeGreaterThan(70)
      expect(riskMetrics.riskScore).toBeGreaterThan(80) // Very high risk
    })
  })

  describe('Black Swan Events', () => {
    it('should handle complete market collapse', async () => {
      // Mock all tokens losing 95% of value
      nock('https://api.coingecko.com')
        .get(/.*/)
        .reply(200, {
          price: 0.05, // 95% drop
          priceChange24h: -95,
          volume24h: 0, // No trading volume
          marketCap: 1000
        })
        .persist()

      // Create diverse portfolio before collapse
      const tokens = ['SOL', 'USDC', 'BONK', 'WIF', 'PEPE']
      for (let i = 0; i < tokens.length; i++) {
        await PortfolioService.createPosition({
          userId: testUserId,
          tokenAddress: `${tokens[i]}_address`,
          tokenSymbol: tokens[i],
          quantity: 1000,
          entryPrice: 1,
          currentPrice: 1
        })
      }

      const portfolio = await PortfolioService.getPortfolioSummary(testUserId)
      const riskMetrics = await RiskService.calculatePortfolioRisk(testUserId)

      // System should handle extreme scenario without crashing
      expect(portfolio).toBeDefined()
      expect(riskMetrics.riskScore).toBe(100) // Maximum risk
      expect(portfolio.totalPnLPercent).toBeLessThan(-90) // Extreme loss
    })

    it('should handle exchange/DEX shutdown scenario', async () => {
      // Mock all DEX endpoints returning shutdown messages
      nock('https://quote-api.jup.ag')
        .get(/.*/)
        .reply(503, { error: 'Service permanently discontinued' })
        .persist()

      nock('https://api.raydium.io')
        .get(/.*/)
        .reply(503, { error: 'Service unavailable' })
        .persist()

      // Trading should be completely unavailable
      const result = await TradingService.executeTrade(
        testFixtures.tradeParams.validBuy,
        testFixtures.users.basicUser.walletAddress,
        testUserId
      )

      expect(result.success).toBe(false)
      expect(result.error).toContain('unavailable') || expect(result.error).toContain('shutdown')

      // Portfolio should still be viewable (read-only mode)
      const portfolio = await PortfolioService.getPortfolioSummary(testUserId)
      expect(portfolio).toBeDefined()
      expect(portfolio.tradingEnabled).toBe(false)
    })

    it('should handle regulatory shutdown scenario', async () => {
      // Mock regulatory compliance error
      const regulatoryError = {
        error: 'REGULATORY_COMPLIANCE_VIOLATION',
        message: 'Trading suspended due to regulatory requirements',
        code: 451 // Unavailable for legal reasons
      }

      nock('https://quote-api.jup.ag')
        .get(/.*/)
        .reply(451, regulatoryError)
        .persist()

      const result = await TradingService.executeTrade(
        testFixtures.tradeParams.validBuy,
        testFixtures.users.basicUser.walletAddress,
        testUserId
      )

      expect(result.success).toBe(false)
      expect(result.error).toContain('regulatory') || expect(result.error).toContain('compliance')
      expect(result.complianceViolation).toBe(true)
    })
  })
})