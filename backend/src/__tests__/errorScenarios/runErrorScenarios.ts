#!/usr/bin/env ts-node

import { execSync } from 'child_process'
import { logger } from '@/utils/logger'

interface TestScenario {
  name: string
  file: string
  description: string
  criticality: 'HIGH' | 'MEDIUM' | 'LOW'
  estimatedTime: string
}

const errorScenarios: TestScenario[] = [
  {
    name: 'Database Failures',
    file: 'databaseFailures.test.ts',
    description: 'Tests database connection failures, timeouts, and recovery scenarios',
    criticality: 'HIGH',
    estimatedTime: '5-10 minutes'
  },
  {
    name: 'External API Failures',
    file: 'externalApiFailures.test.ts',
    description: 'Tests Jupiter API, Helius RPC, and price service failures',
    criticality: 'HIGH',
    estimatedTime: '8-12 minutes'
  },
  {
    name: 'Chaos Engineering',
    file: 'chaosEngineering.test.ts',
    description: 'Tests system resilience under partial failures and cascading errors',
    criticality: 'HIGH',
    estimatedTime: '10-15 minutes'
  },
  {
    name: 'Extreme Market Conditions',
    file: 'extremeMarketConditions.test.ts',
    description: 'Tests flash crashes, liquidity crises, and black swan events',
    criticality: 'MEDIUM',
    estimatedTime: '6-10 minutes'
  },
  {
    name: 'Security Breach Scenarios',
    file: 'securityBreachScenarios.test.ts',
    description: 'Tests injection attacks, authentication bypasses, and data exfiltration',
    criticality: 'HIGH',
    estimatedTime: '7-12 minutes'
  },
  {
    name: 'Performance Stress Testing',
    file: 'performanceStress.test.ts',
    description: 'Tests system performance under high load and resource exhaustion',
    criticality: 'MEDIUM',
    estimatedTime: '15-25 minutes'
  }
]

interface TestResult {
  scenario: string
  passed: number
  failed: number
  skipped: number
  duration: number
  success: boolean
  errors: string[]
}

class ErrorScenarioRunner {
  private results: TestResult[] = []
  private startTime: number = 0

  public async runAllScenarios(): Promise<void> {
    console.log('🧪 Starting Comprehensive Error Scenario Testing')
    console.log('=' .repeat(60))
    
    this.startTime = Date.now()
    
    // Display test plan
    this.displayTestPlan()
    
    // Run scenarios based on criticality
    await this.runScenariosByPriority()
    
    // Generate comprehensive report
    this.generateReport()
  }

  private displayTestPlan(): void {
    console.log('\n📋 Test Execution Plan:')
    console.log('-'.repeat(40))
    
    errorScenarios.forEach((scenario, index) => {
      const priority = scenario.criticality === 'HIGH' ? '🔴' : 
                      scenario.criticality === 'MEDIUM' ? '🟡' : '🟢'
      
      console.log(`${index + 1}. ${priority} ${scenario.name}`)
      console.log(`   📁 ${scenario.file}`)
      console.log(`   📖 ${scenario.description}`)
      console.log(`   ⏱️  ${scenario.estimatedTime}`)
      console.log('')
    })
  }

  private async runScenariosByPriority(): Promise<void> {
    // Run HIGH priority tests first
    const highPriorityTests = errorScenarios.filter(s => s.criticality === 'HIGH')
    const mediumPriorityTests = errorScenarios.filter(s => s.criticality === 'MEDIUM')
    const lowPriorityTests = errorScenarios.filter(s => s.criticality === 'LOW')

    console.log('\n🔴 Running HIGH Priority Error Scenarios...')
    for (const scenario of highPriorityTests) {
      await this.runScenario(scenario)
    }

    console.log('\n🟡 Running MEDIUM Priority Error Scenarios...')
    for (const scenario of mediumPriorityTests) {
      await this.runScenario(scenario)
    }

    if (lowPriorityTests.length > 0) {
      console.log('\n🟢 Running LOW Priority Error Scenarios...')
      for (const scenario of lowPriorityTests) {
        await this.runScenario(scenario)
      }
    }
  }

  private async runScenario(scenario: TestScenario): Promise<void> {
    console.log(`\n▶️  Running: ${scenario.name}`)
    console.log(`   📄 ${scenario.description}`)
    
    const startTime = Date.now()
    
    try {
      // Run the Jest test for this specific file
      const command = `npx jest src/__tests__/errorScenarios/${scenario.file} --verbose --no-cache`
      const output = execSync(command, { 
        encoding: 'utf-8',
        timeout: 30 * 60 * 1000 // 30 minute timeout
      })
      
      const endTime = Date.now()
      const duration = endTime - startTime
      
      // Parse Jest output for results
      const result = this.parseJestOutput(output, scenario.name, duration)
      this.results.push(result)
      
      if (result.success) {
        console.log(`   ✅ PASSED - ${result.passed} tests passed in ${this.formatDuration(duration)}`)
      } else {
        console.log(`   ❌ FAILED - ${result.failed} tests failed, ${result.passed} passed in ${this.formatDuration(duration)}`)
        result.errors.forEach(error => console.log(`      🔸 ${error}`))
      }
      
    } catch (error) {
      const endTime = Date.now()
      const duration = endTime - startTime
      
      console.log(`   💥 ERROR - Test execution failed in ${this.formatDuration(duration)}`)
      console.log(`      ${error.message}`)
      
      this.results.push({
        scenario: scenario.name,
        passed: 0,
        failed: 1,
        skipped: 0,
        duration,
        success: false,
        errors: [error.message]
      })
    }
  }

  private parseJestOutput(output: string, scenarioName: string, duration: number): TestResult {
    const lines = output.split('\n')
    
    let passed = 0
    let failed = 0
    let skipped = 0
    const errors: string[] = []
    
    // Parse Jest output for test results
    for (const line of lines) {
      if (line.includes('✓') || line.includes('PASS')) {
        passed++
      } else if (line.includes('✗') || line.includes('FAIL')) {
        failed++
        errors.push(line.trim())
      } else if (line.includes('○') || line.includes('SKIP')) {
        skipped++
      }
    }
    
    // Look for summary line
    const summaryMatch = output.match(/Tests:\s*(\d+)\s*failed,\s*(\d+)\s*passed/)
    if (summaryMatch) {
      failed = parseInt(summaryMatch[1])
      passed = parseInt(summaryMatch[2])
    }
    
    return {
      scenario: scenarioName,
      passed,
      failed,
      skipped,
      duration,
      success: failed === 0,
      errors
    }
  }

  private generateReport(): void {
    const totalTime = Date.now() - this.startTime
    
    console.log('\n' + '='.repeat(60))
    console.log('📊 ERROR SCENARIO TESTING REPORT')
    console.log('='.repeat(60))
    
    // Overall statistics
    const totalPassed = this.results.reduce((sum, r) => sum + r.passed, 0)
    const totalFailed = this.results.reduce((sum, r) => sum + r.failed, 0)
    const totalSkipped = this.results.reduce((sum, r) => sum + r.skipped, 0)
    const totalTests = totalPassed + totalFailed + totalSkipped
    const successfulScenarios = this.results.filter(r => r.success).length
    
    console.log(`\n📈 Overall Statistics:`)
    console.log(`   Total Scenarios: ${this.results.length}`)
    console.log(`   Successful Scenarios: ${successfulScenarios}`)
    console.log(`   Failed Scenarios: ${this.results.length - successfulScenarios}`)
    console.log(`   Total Tests: ${totalTests}`)
    console.log(`   Tests Passed: ${totalPassed} (${((totalPassed / totalTests) * 100).toFixed(1)}%)`)
    console.log(`   Tests Failed: ${totalFailed} (${((totalFailed / totalTests) * 100).toFixed(1)}%)`)
    console.log(`   Tests Skipped: ${totalSkipped}`)
    console.log(`   Total Runtime: ${this.formatDuration(totalTime)}`)
    
    // Detailed results
    console.log(`\n📋 Detailed Results:`)
    console.log('-'.repeat(40))
    
    this.results.forEach((result, index) => {
      const status = result.success ? '✅' : '❌'
      const passRate = result.passed + result.failed > 0 ? 
        ((result.passed / (result.passed + result.failed)) * 100).toFixed(1) : '0.0'
      
      console.log(`${index + 1}. ${status} ${result.scenario}`)
      console.log(`   📊 ${result.passed} passed, ${result.failed} failed, ${result.skipped} skipped`)
      console.log(`   📈 ${passRate}% pass rate`)
      console.log(`   ⏱️  ${this.formatDuration(result.duration)}`)
      
      if (result.errors.length > 0) {
        console.log(`   🔸 Errors:`)
        result.errors.slice(0, 3).forEach(error => {
          console.log(`      • ${error.substring(0, 80)}${error.length > 80 ? '...' : ''}`)
        })
        if (result.errors.length > 3) {
          console.log(`      • ... and ${result.errors.length - 3} more errors`)
        }
      }
      console.log('')
    })
    
    // Recommendations
    this.generateRecommendations()
    
    // System resilience score
    this.calculateResilienceScore()
  }

  private generateRecommendations(): void {
    console.log(`\n💡 Recommendations:`)
    console.log('-'.repeat(30))
    
    const failedScenarios = this.results.filter(r => !r.success)
    const highFailureRate = this.results.filter(r => {
      const total = r.passed + r.failed
      return total > 0 && (r.failed / total) > 0.2 // More than 20% failure rate
    })
    
    if (failedScenarios.length === 0) {
      console.log('🎉 All error scenarios passed! Your system shows excellent resilience.')
    } else {
      console.log(`🔧 ${failedScenarios.length} scenarios need attention:`)
      failedScenarios.forEach(scenario => {
        console.log(`   • ${scenario.scenario}: Review error handling and recovery mechanisms`)
      })
    }
    
    if (highFailureRate.length > 0) {
      console.log(`\n⚠️  High failure rate scenarios:`)
      highFailureRate.forEach(scenario => {
        const total = scenario.passed + scenario.failed
        const rate = ((scenario.failed / total) * 100).toFixed(1)
        console.log(`   • ${scenario.scenario}: ${rate}% failure rate - needs improvement`)
      })
    }
    
    // Performance recommendations
    const slowScenarios = this.results.filter(r => r.duration > 10 * 60 * 1000) // > 10 minutes
    if (slowScenarios.length > 0) {
      console.log(`\n🐌 Performance optimization needed:`)
      slowScenarios.forEach(scenario => {
        console.log(`   • ${scenario.scenario}: ${this.formatDuration(scenario.duration)} - consider optimization`)
      })
    }
  }

  private calculateResilienceScore(): void {
    const totalTests = this.results.reduce((sum, r) => sum + r.passed + r.failed, 0)
    const passedTests = this.results.reduce((sum, r) => sum + r.passed, 0)
    
    const baseScore = (passedTests / totalTests) * 100
    
    // Adjust score based on scenario criticality
    let weightedScore = 0
    let totalWeight = 0
    
    this.results.forEach(result => {
      const scenario = errorScenarios.find(s => s.name === result.scenario)
      const weight = scenario?.criticality === 'HIGH' ? 3 : 
                    scenario?.criticality === 'MEDIUM' ? 2 : 1
      
      const scenarioScore = result.passed + result.failed > 0 ? 
        (result.passed / (result.passed + result.failed)) * 100 : 0
      
      weightedScore += scenarioScore * weight
      totalWeight += weight
    })
    
    const finalScore = totalWeight > 0 ? weightedScore / totalWeight : 0
    
    console.log(`\n🏆 System Resilience Score: ${finalScore.toFixed(1)}/100`)
    
    if (finalScore >= 90) {
      console.log('   🌟 EXCELLENT - Your system demonstrates exceptional resilience')
    } else if (finalScore >= 80) {
      console.log('   ✅ GOOD - Your system handles most error conditions well')
    } else if (finalScore >= 70) {
      console.log('   ⚠️  FAIR - Your system needs improvement in error handling')
    } else {
      console.log('   🚨 POOR - Critical improvements needed for production readiness')
    }
  }

  private formatDuration(ms: number): string {
    const minutes = Math.floor(ms / 60000)
    const seconds = Math.floor((ms % 60000) / 1000)
    return `${minutes}m ${seconds}s`
  }
}

// CLI execution
if (require.main === module) {
  const runner = new ErrorScenarioRunner()
  
  runner.runAllScenarios()
    .then(() => {
      console.log('\n🎯 Error scenario testing completed!')
      process.exit(0)
    })
    .catch(error => {
      console.error('💥 Error scenario runner failed:', error)
      process.exit(1)
    })
}

export { ErrorScenarioRunner }