/**
 * Test data fixtures for consistent testing
 */

import { generateMockSolanaAddress, generateMockTransactionHash, generateMockDecimal, deepClone } from '../utils/testUtils'

/**
 * User fixtures
 */
export const userFixtures = {
  basicUser: {
    email: '<EMAIL>',
    walletAddress: 'So11111111111111111111111111111111111111112',
    role: 'user',
    isActive: true
  },

  premiumUser: {
    email: '<EMAIL>',
    walletAddress: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
    role: 'premium',
    isActive: true
  },

  adminUser: {
    email: '<EMAIL>',
    walletAddress: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
    role: 'admin',
    isActive: true
  },

  inactiveUser: {
    email: '<EMAIL>',
    walletAddress: generateMockSolanaAddress(),
    role: 'user',
    isActive: false
  }
}

/**
 * Token fixtures
 */
export const tokenFixtures = {
  SOL: {
    address: 'So11111111111111111111111111111111111111112',
    symbol: 'SOL',
    name: 'Solana',
    decimals: 9,
    price: 120.50
  },

  USDC: {
    address: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
    symbol: 'USDC',
    name: 'USD Coin',
    decimals: 6,
    price: 1.00
  },

  BONK: {
    address: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
    symbol: 'BONK',
    name: 'Bonk',
    decimals: 5,
    price: 0.000012
  },

  WIF: {
    address: 'EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm',
    symbol: 'WIF',
    name: 'dogwifhat',
    decimals: 6,
    price: 2.45
  },

  PEPE: {
    address: 'BxqxgRS7E9vj6xjLk7VZqjpzJDjSMNwb7XYm2EaUGNKE',
    symbol: 'PEPE',
    name: 'Pepe',
    decimals: 6,
    price: 0.0000008
  }
}

/**
 * Position fixtures
 */
export const positionFixtures = {
  profitablePosition: {
    tokenAddress: tokenFixtures.BONK.address,
    tokenSymbol: tokenFixtures.BONK.symbol,
    tokenName: tokenFixtures.BONK.name,
    entryPrice: '0.000010',
    currentPrice: '0.000015',
    quantity: '1000000',
    entryTimestamp: new Date('2024-01-01T10:00:00Z'),
    presetUsed: 'DEFAULT',
    riskLevel: 'MEDIUM',
    status: 'ACTIVE',
    pnl: '5.0',
    pnlPercent: 50.0
  },

  losingPosition: {
    tokenAddress: tokenFixtures.WIF.address,
    tokenSymbol: tokenFixtures.WIF.symbol,
    tokenName: tokenFixtures.WIF.name,
    entryPrice: '3.00',
    currentPrice: '2.00',
    quantity: '100',
    entryTimestamp: new Date('2024-01-02T14:30:00Z'),
    presetUsed: 'VOL',
    riskLevel: 'HIGH',
    status: 'ACTIVE',
    pnl: '-100.0',
    pnlPercent: -33.33
  },

  closedPosition: {
    tokenAddress: tokenFixtures.PEPE.address,
    tokenSymbol: tokenFixtures.PEPE.symbol,
    tokenName: tokenFixtures.PEPE.name,
    entryPrice: '0.0000005',
    currentPrice: '0.0000008',
    quantity: '5000000',
    entryTimestamp: new Date('2024-01-03T09:15:00Z'),
    exitTimestamp: new Date('2024-01-04T16:45:00Z'),
    presetUsed: 'DEFAULT',
    riskLevel: 'MEDIUM',
    status: 'CLOSED',
    pnl: '1.5',
    pnlPercent: 60.0
  }
}

/**
 * Transaction fixtures
 */
export const transactionFixtures = {
  buyTransaction: {
    hash: generateMockTransactionHash(),
    type: 'BUY',
    status: 'CONFIRMED',
    tokenIn: tokenFixtures.SOL.address,
    tokenOut: tokenFixtures.BONK.address,
    tokenInSymbol: tokenFixtures.SOL.symbol,
    tokenOutSymbol: tokenFixtures.BONK.symbol,
    tokenInName: tokenFixtures.SOL.name,
    tokenOutName: tokenFixtures.BONK.name,
    amountIn: '1.0',
    amountOut: '100000',
    amountInRaw: '1000000000',
    amountOutRaw: '10000000000',
    price: '0.00001',
    priceImpact: 0.5,
    fees: {
      total: 0.001,
      network: 0.0005,
      jupiter: 0.0005
    },
    presetUsed: 'DEFAULT',
    slippageUsed: 1.0,
    slippageActual: 0.8,
    mevProtected: false,
    executionTime: 2500,
    totalCost: 0.001,
    timestamp: new Date('2024-01-01T10:00:00Z')
  },

  sellTransaction: {
    hash: generateMockTransactionHash(),
    type: 'SELL',
    status: 'CONFIRMED',
    tokenIn: tokenFixtures.BONK.address,
    tokenOut: tokenFixtures.SOL.address,
    tokenInSymbol: tokenFixtures.BONK.symbol,
    tokenOutSymbol: tokenFixtures.SOL.symbol,
    tokenInName: tokenFixtures.BONK.name,
    tokenOutName: tokenFixtures.SOL.name,
    amountIn: '50000',
    amountOut: '0.75',
    amountInRaw: '5000000000',
    amountOutRaw: '750000000',
    price: '0.000015',
    priceImpact: 0.3,
    fees: {
      total: 0.001,
      network: 0.0005,
      jupiter: 0.0005
    },
    presetUsed: 'DEFAULT',
    slippageUsed: 1.5,
    slippageActual: 1.2,
    mevProtected: true,
    mevRiskLevel: 'low',
    executionTime: 1800,
    totalCost: 0.001,
    timestamp: new Date('2024-01-02T15:30:00Z')
  },

  failedTransaction: {
    hash: generateMockTransactionHash(),
    type: 'SWAP',
    status: 'FAILED',
    tokenIn: tokenFixtures.SOL.address,
    tokenOut: tokenFixtures.USDC.address,
    tokenInSymbol: tokenFixtures.SOL.symbol,
    tokenOutSymbol: tokenFixtures.USDC.symbol,
    tokenInName: tokenFixtures.SOL.name,
    tokenOutName: tokenFixtures.USDC.name,
    amountIn: '2.0',
    amountOut: '241.0',
    amountInRaw: '2000000000',
    amountOutRaw: '241000000',
    price: '120.5',
    fees: {
      total: 0.001,
      network: 0.0005,
      jupiter: 0.0005
    },
    presetUsed: 'VOL',
    slippageUsed: 2.0,
    mevProtected: false,
    executionTime: 5000,
    retryCount: 3,
    errorReason: 'Insufficient balance',
    totalCost: 0.001,
    timestamp: new Date('2024-01-03T12:15:00Z')
  }
}

/**
 * Exit strategy fixtures
 */
export const exitStrategyFixtures = {
  basicStopLoss: {
    type: 'CUSTOM',
    customName: 'Basic Stop Loss',
    stopLoss: {
      enabled: true,
      type: 'PERCENTAGE',
      triggerPercent: 10,
      timeDelay: 0,
      volumeConfirmation: false,
      partialExecution: false
    },
    profitTargets: [],
    moonBag: {
      enabled: false,
      reservePercent: 0,
      triggerConditions: {
        minProfitPercent: 0
      },
      neverSell: false
    },
    executionState: 'PENDING',
    locked: false,
    totalTriggers: 0,
    totalPnl: '0',
    successRate: 0
  },

  advancedStrategy: {
    type: 'CUSTOM',
    customName: 'Advanced Multi-Target',
    stopLoss: {
      enabled: true,
      type: 'PERCENTAGE',
      triggerPercent: 15,
      timeDelay: 5000,
      volumeConfirmation: true,
      partialExecution: false
    },
    profitTargets: [
      {
        id: 'tp1',
        enabled: true,
        type: 'PERCENTAGE',
        triggerPercent: 25,
        sellPercent: 30,
        executed: false,
        timeWindow: { start: 9, end: 17 },
        volumeThreshold: 1000000,
        priceMovementConfirmation: true
      },
      {
        id: 'tp2',
        enabled: true,
        type: 'PERCENTAGE',
        triggerPercent: 50,
        sellPercent: 50,
        executed: false
      },
      {
        id: 'tp3',
        enabled: true,
        type: 'PERCENTAGE',
        triggerPercent: 100,
        sellPercent: 20,
        executed: false
      }
    ],
    moonBag: {
      enabled: true,
      reservePercent: 10,
      triggerConditions: {
        minProfitPercent: 200,
        priceMultiple: 3,
        timeHolding: 86400000, // 24 hours
        volumeSpike: true
      },
      neverSell: false,
      sellRules: {
        priceTargets: [500, 1000, 2000],
        sellPercentages: [30, 50, 20]
      }
    },
    executionState: 'ACTIVE',
    locked: false,
    totalTriggers: 0,
    totalPnl: '0',
    successRate: 0
  },

  trailingStopStrategy: {
    type: 'CUSTOM',
    customName: 'Trailing Stop',
    stopLoss: {
      enabled: false
    },
    profitTargets: [],
    moonBag: {
      enabled: false,
      reservePercent: 0,
      triggerConditions: {
        minProfitPercent: 0
      },
      neverSell: false
    },
    trailingStop: {
      enabled: true,
      type: 'PERCENTAGE',
      trailPercent: 5,
      highWaterMark: 100,
      currentStopPrice: 95,
      activationPrice: 110,
      minProfit: 10,
      accelerated: true
    },
    executionState: 'ACTIVE',
    locked: false,
    totalTriggers: 0,
    totalPnl: '0',
    successRate: 0
  }
}

/**
 * Trading preset fixtures
 */
export const tradingPresetFixtures = {
  default: {
    name: 'DEFAULT',
    priorityFee: '0.001',
    slippageLimit: 1.0,
    mevProtectionLevel: 'BASIC',
    brideAmount: null,
    buySettings: {
      maxSlippage: 2.0,
      priorityFee: 0.001,
      confirmationTimeout: 30000
    },
    sellSettings: {
      maxSlippage: 3.0,
      priorityFee: 0.002,
      confirmationTimeout: 45000
    },
    locked: true,
    systemDefault: true,
    totalUsage: 1000,
    averagePerformance: 85.5
  },

  volatile: {
    name: 'VOL',
    priorityFee: '0.005',
    slippageLimit: 5.0,
    mevProtectionLevel: 'MAXIMUM',
    brideAmount: '0.01',
    buySettings: {
      maxSlippage: 8.0,
      priorityFee: 0.005,
      confirmationTimeout: 20000
    },
    sellSettings: {
      maxSlippage: 10.0,
      priorityFee: 0.01,
      confirmationTimeout: 15000
    },
    locked: true,
    systemDefault: false,
    totalUsage: 250,
    averagePerformance: 78.2
  },

  conservative: {
    name: 'DEAD',
    priorityFee: '0.0005',
    slippageLimit: 0.5,
    mevProtectionLevel: 'ADVANCED',
    brideAmount: null,
    buySettings: {
      maxSlippage: 1.0,
      priorityFee: 0.0005,
      confirmationTimeout: 60000
    },
    sellSettings: {
      maxSlippage: 1.5,
      priorityFee: 0.001,
      confirmationTimeout: 90000
    },
    locked: true,
    systemDefault: false,
    totalUsage: 150,
    averagePerformance: 92.1
  }
}

/**
 * Portfolio fixtures
 */
export const portfolioFixtures = {
  smallPortfolio: {
    totalValue: '1500.50',
    totalValueUsd: '1500.50',
    totalPnl: '150.25',
    totalPnlUsd: '150.25',
    totalPnlPercent: 11.13,
    riskScore: 35.5,
    exposurePercent: 75.0,
    maxDrawdown: 8.5,
    winRate: 66.7,
    totalTrades: 12,
    profitFactor: 1.85,
    sharpeRatio: 1.42
  },

  largePortfolio: {
    totalValue: '25000.00',
    totalValueUsd: '25000.00',
    totalPnl: '-2500.00',
    totalPnlUsd: '-2500.00',
    totalPnlPercent: -9.09,
    riskScore: 72.3,
    exposurePercent: 92.5,
    maxDrawdown: 15.8,
    winRate: 45.2,
    totalTrades: 84,
    profitFactor: 0.78,
    sharpeRatio: -0.23
  }
}

/**
 * Jupiter API response fixtures
 */
export const jupiterFixtures = {
  validQuoteResponse: {
    inputMint: tokenFixtures.SOL.address,
    outputMint: tokenFixtures.BONK.address,
    inAmount: '1000000000', // 1 SOL
    outAmount: '83333333333', // ~83,333 BONK
    priceImpactPct: 0.12,
    fees: {
      total: 1000000, // 0.001 SOL
      network: 500000,
      jupiter: 500000
    },
    routePlan: [
      {
        swapInfo: {
          ammKey: generateMockSolanaAddress(),
          label: 'Raydium',
          inputMint: tokenFixtures.SOL.address,
          outputMint: tokenFixtures.BONK.address,
          inAmount: '1000000000',
          outAmount: '83333333333',
          feeAmount: '500000',
          feeMint: tokenFixtures.SOL.address
        },
        percent: 100
      }
    ],
    slippageBps: 100, // 1%
    marketInfos: [
      {
        id: generateMockSolanaAddress(),
        label: 'Raydium',
        inputMint: tokenFixtures.SOL.address,
        outputMint: tokenFixtures.BONK.address,
        notEnoughLiquidity: false,
        inAmount: '1000000000',
        outAmount: '83333333333',
        priceImpactPct: 0.12,
        lpFee: {
          amount: '250000',
          mint: tokenFixtures.SOL.address,
          pct: 0.025
        },
        platformFee: {
          amount: '250000',
          mint: tokenFixtures.SOL.address,
          pct: 0.025
        }
      }
    ]
  },

  validSwapResponse: {
    swapTransaction: 'base64-encoded-transaction-data',
    lastValidBlockHeight: 200000000,
    prioritizationFeeLamports: 5000
  },

  highImpactQuote: {
    inputMint: tokenFixtures.SOL.address,
    outputMint: tokenFixtures.PEPE.address,
    inAmount: '10000000000', // 10 SOL
    outAmount: '1000000000000', // Large amount
    priceImpactPct: 5.67,
    fees: {
      total: 10000000, // 0.01 SOL
      network: 5000000,
      jupiter: 5000000
    },
    routePlan: [
      {
        swapInfo: {
          ammKey: generateMockSolanaAddress(),
          label: 'Orca',
          inputMint: tokenFixtures.SOL.address,
          outputMint: tokenFixtures.USDC.address,
          inAmount: '10000000000',
          outAmount: '1205000000', // ~1205 USDC
          feeAmount: '5000000',
          feeMint: tokenFixtures.SOL.address
        },
        percent: 100
      }
    ],
    slippageBps: 200 // 2%
  }
}

/**
 * Market conditions fixtures
 */
export const marketConditionsFixtures = {
  normal: {
    networkCongestion: 'medium',
    avgPriorityFee: 0.001,
    liquidityScore: 85,
    volatilityIndex: 25,
    timestamp: Date.now()
  },

  highCongestion: {
    networkCongestion: 'high',
    avgPriorityFee: 0.01,
    liquidityScore: 65,
    volatilityIndex: 45,
    timestamp: Date.now()
  },

  lowLiquidity: {
    networkCongestion: 'low',
    avgPriorityFee: 0.0005,
    liquidityScore: 25,
    volatilityIndex: 15,
    timestamp: Date.now()
  }
}

/**
 * Fixture factory functions
 */
export class FixtureFactory {
  /**
   * Create a complete test scenario with all related data
   */
  static createCompleteScenario(overrides: any = {}) {
    const user = { ...userFixtures.basicUser, ...overrides.user }
    const positions = [
      { ...positionFixtures.profitablePosition, ...overrides.positions?.[0] },
      { ...positionFixtures.losingPosition, ...overrides.positions?.[1] }
    ]
    const transactions = [
      { ...transactionFixtures.buyTransaction, ...overrides.transactions?.[0] },
      { ...transactionFixtures.sellTransaction, ...overrides.transactions?.[1] }
    ]
    const strategies = [
      { ...exitStrategyFixtures.basicStopLoss, ...overrides.strategies?.[0] },
      { ...exitStrategyFixtures.advancedStrategy, ...overrides.strategies?.[1] }
    ]
    const portfolio = { ...portfolioFixtures.smallPortfolio, ...overrides.portfolio }

    return {
      user,
      positions,
      transactions,
      strategies,
      portfolio
    }
  }

  /**
   * Create trading scenario for specific test cases
   */
  static createTradingScenario(type: 'profitable' | 'losing' | 'mixed') {
    switch (type) {
      case 'profitable':
        return {
          position: positionFixtures.profitablePosition,
          transaction: transactionFixtures.buyTransaction,
          strategy: exitStrategyFixtures.basicStopLoss
        }
      case 'losing':
        return {
          position: positionFixtures.losingPosition,
          transaction: transactionFixtures.failedTransaction,
          strategy: exitStrategyFixtures.advancedStrategy
        }
      case 'mixed':
      default:
        return {
          positions: [positionFixtures.profitablePosition, positionFixtures.losingPosition],
          transactions: [transactionFixtures.buyTransaction, transactionFixtures.sellTransaction],
          strategies: [exitStrategyFixtures.basicStopLoss, exitStrategyFixtures.trailingStopStrategy]
        }
    }
  }

  /**
   * Generate random fixture data based on template
   */
  static randomize<T>(template: T): T {
    const randomized = deepClone(template)
    
    // Add some randomization logic here if needed
    return randomized
  }
}

/**
 * Export all fixtures
 */
export const fixtures = {
  users: userFixtures,
  tokens: tokenFixtures,
  positions: positionFixtures,
  transactions: transactionFixtures,
  exitStrategies: exitStrategyFixtures,
  tradingPresets: tradingPresetFixtures,
  portfolios: portfolioFixtures,
  jupiter: jupiterFixtures,
  marketConditions: marketConditionsFixtures,
  factory: FixtureFactory
}