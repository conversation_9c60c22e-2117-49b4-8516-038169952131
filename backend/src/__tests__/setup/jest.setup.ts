/**
 * Jest setup file - runs after test environment is set up, before each test file
 */

import { config } from 'dotenv'

// Load test environment variables
config({ path: '.env.test' })

// Global test configuration
jest.setTimeout(30000) // 30 seconds timeout for all tests

// Mock console methods to reduce noise during tests (can be overridden in individual tests)
global.console = {
  ...console,
  // Uncomment to suppress logs during tests
  // log: jest.fn(),
  // warn: jest.fn(),
  // error: jest.fn(),
  // info: jest.fn(),
  // debug: jest.fn(),
}

// Global error handler for unhandled rejections in tests
process.on('unhandledRejection', (error) => {
  console.error('Unhandled promise rejection in test:', error)
})

// Global type definitions for Jest matchers
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidSolanaAddress(): R
      toBeValidTransaction(): R
      toBeValidQuote(): R
      toMatchTradeResult(): R
    }
  }
}

// Custom Jest matchers for trading system
expect.extend({
  toBeValidSolanaAddress(received: string) {
    const isValid = typeof received === 'string' &&
                   received.length >= 32 &&
                   received.length <= 44 &&
                   /^[1-9A-HJ-NP-Za-km-z]+$/.test(received)

    return {
      message: () => `expected ${received} to be a valid Solana address`,
      pass: isValid,
    }
  },

  toBeValidTransaction(received: any) {
    const requiredFields = ['hash', 'type', 'status', 'tokenIn', 'tokenOut', 'amountIn', 'amountOut']
    const hasRequiredFields = requiredFields.every(field =>
      received && typeof received[field] !== 'undefined'
    )

    return {
      message: () => `expected transaction to have required fields: ${requiredFields.join(', ')}`,
      pass: hasRequiredFields,
    }
  },

  toBeValidQuote(received: any) {
    const requiredFields = ['inAmount', 'outAmount', 'priceImpactPct', 'routePlan']
    const hasRequiredFields = requiredFields.every(field =>
      received && typeof received[field] !== 'undefined'
    )

    return {
      message: () => `expected quote to have required fields: ${requiredFields.join(', ')}`,
      pass: hasRequiredFields,
    }
  },

  toMatchTradeResult(received: any) {
    const isValid = received &&
                   typeof received.success === 'boolean' &&
                   (received.success ?
                     typeof received.transactionHash === 'string' :
                     typeof received.error === 'string')

    return {
      message: () => `expected trade result to have success boolean and either transactionHash or error`,
      pass: isValid,
    }
  }
})

// Mock environment variables for tests
process.env.NODE_ENV = 'test'
process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || 'postgresql://test:test@localhost:5432/memetrader_test'
process.env.REDIS_URL = process.env.TEST_REDIS_URL || 'redis://localhost:6379/1'
process.env['JUPITER_API_URL'] = 'https://lite-api.jup.ag/swap/v1'
process.env.HELIUS_API_KEY = 'test-helius-key'
process.env.WALLET_PRIVATE_KEY = 'test-private-key'
process.env.WALLET_ADDRESS = 'test-wallet-address'

export {}
