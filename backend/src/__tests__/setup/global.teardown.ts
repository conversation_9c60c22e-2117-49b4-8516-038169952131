/**
 * Global Jest teardown - runs once after all tests
 */

export default async function globalTeardown() {
  console.log('🧹 Starting global test teardown...')

  try {
    // Clean up test database connections
    console.log('📊 Cleaning up database connections...')
    await cleanupDatabase()
    
    // Clean up Redis connections
    console.log('🔴 Cleaning up Redis connections...')
    await cleanupRedis()
    
    // Clean up any other resources
    console.log('🧹 Cleaning up other test resources...')
    await cleanupResources()
    
    console.log('✅ Global test teardown completed successfully')
    
  } catch (error) {
    console.error('❌ Global test teardown failed:', error)
    // Don't exit with error code in teardown as tests have already run
  }
}

async function cleanupDatabase() {
  try {
    // Import and disconnect Prisma client if it exists
    const { PrismaClient } = await import('@prisma/client')
    const prisma = new PrismaClient()
    
    await prisma.$disconnect()
    console.log('  ✅ Database connections closed')
  } catch (error) {
    console.log('  ⚠️  Database cleanup skipped (client may not be initialized)')
  }
}

async function cleanupRedis() {
  try {
    // Clean up any Redis connections
    // This is mostly handled by individual test cleanup
    console.log('  ✅ Redis cleanup completed')
  } catch (error) {
    console.log('  ⚠️  Redis cleanup skipped')
  }
}

async function cleanupResources() {
  try {
    // Clean up temporary files, processes, etc.
    // Force garbage collection if available
    if (global.gc) {
      global.gc()
    }
    
    console.log('  ✅ Resource cleanup completed')
  } catch (error) {
    console.log('  ⚠️  Resource cleanup encountered minor issues')
  }
}