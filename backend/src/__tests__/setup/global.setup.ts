/**
 * Global Jest setup - runs once before all tests
 */

import { execSync } from 'child_process'
import { config } from 'dotenv'
import path from 'path'

export default async function globalSetup() {
  console.log('🚀 Starting global test setup...')

  // Load test environment variables
  config({ path: path.resolve(__dirname, '../../../.env.test') })

  try {
    // Set test environment
    process.env.NODE_ENV = 'test'
    
    // Database setup
    console.log('📊 Setting up test database...')
    await setupTestDatabase()
    
    // Redis setup
    console.log('🔴 Setting up Redis for tests...')
    await setupTestRedis()
    
    // External service mocks
    console.log('🌐 Setting up external service mocks...')
    await setupExternalMocks()
    
    console.log('✅ Global test setup completed successfully')
    
  } catch (error) {
    console.error('❌ Global test setup failed:', error)
    process.exit(1)
  }
}

async function setupTestDatabase() {
  try {
    const testDbUrl = process.env.TEST_DATABASE_URL || process.env.DATABASE_URL
    
    if (!testDbUrl) {
      throw new Error('TEST_DATABASE_URL or DATABASE_URL must be set for tests')
    }

    // Reset and migrate test database
    console.log('  - Resetting test database schema...')
    execSync('npx prisma migrate reset --force --skip-seed', {
      stdio: 'pipe',
      env: { ...process.env, DATABASE_URL: testDbUrl }
    })

    console.log('  - Running database migrations...')
    execSync('npx prisma migrate deploy', {
      stdio: 'pipe', 
      env: { ...process.env, DATABASE_URL: testDbUrl }
    })

    console.log('  - Generating Prisma client...')
    execSync('npx prisma generate', {
      stdio: 'pipe',
      env: { ...process.env, DATABASE_URL: testDbUrl }
    })

    console.log('  ✅ Test database setup completed')
  } catch (error) {
    console.error('  ❌ Test database setup failed:', error)
    throw error
  }
}

async function setupTestRedis() {
  try {
    // Redis connection will be mocked in most tests
    // This just ensures the test environment variables are set
    const testRedisUrl = process.env.TEST_REDIS_URL || 'redis://localhost:6379/1'
    process.env.REDIS_URL = testRedisUrl
    
    console.log('  ✅ Redis test configuration set')
  } catch (error) {
    console.error('  ❌ Redis setup failed:', error)
    throw error
  }
}

async function setupExternalMocks() {
  try {
    // Set mock API endpoints for external services
    process.env.JUPITER_API_URL = process.env.JUPITER_API_URL || 'https://quote-api.jup.ag/v6'
    process.env.HELIUS_API_KEY = process.env.HELIUS_API_KEY || 'test-helius-key'
    process.env.HELIUS_RPC_URL = process.env.HELIUS_RPC_URL || 'https://rpc.helius.xyz'
    
    // Mock wallet configuration
    process.env.WALLET_PRIVATE_KEY = process.env.WALLET_PRIVATE_KEY || 'test-private-key'
    process.env.WALLET_ADDRESS = process.env.WALLET_ADDRESS || 'test-wallet-address'
    
    console.log('  ✅ External service mocks configured')
  } catch (error) {
    console.error('  ❌ External service mock setup failed:', error)
    throw error
  }
}