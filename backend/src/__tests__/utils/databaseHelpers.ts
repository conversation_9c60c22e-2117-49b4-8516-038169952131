/**
 * Database testing helpers and utilities
 */

import { PrismaClient, User, Position, Transaction, ExitStrategy, Portfolio } from '@prisma/client'
import { getTestPrismaClient, generateMockSolanaAddress, generateMockTransactionHash, generateMockDecimal } from './testUtils'

/**
 * Database test helper class
 */
export class DatabaseTestHelper {
  private prisma: PrismaClient

  constructor() {
    this.prisma = getTestPrismaClient()
  }

  /**
   * Create test user with preferences
   */
  async createTestUser(overrides: Partial<User> = {}): Promise<User> {
    const userData = {
      email: `test${Date.now()}@example.com`,
      walletAddress: generateMockSolanaAddress(),
      role: 'user',
      isActive: true,
      ...overrides
    }

    const user = await this.prisma.user.create({
      data: userData
    })

    // Create default preferences
    await this.prisma.userPreferences.create({
      data: {
        userId: user.id,
        defaultPreset: 'DEFAULT',
        defaultSlippage: 1.0,
        defaultPriorityFee: 0.001,
        riskTolerance: 'MEDIUM',
        maxPositionSize: 5.0,
        alertsEnabled: true,
        theme: 'dark',
        currency: 'USD',
        timezone: 'UTC'
      }
    })

    return user
  }

  /**
   * Create test position
   */
  async createTestPosition(userId: string, overrides: Partial<Position> = {}): Promise<Position> {
    const positionData = {
      userId,
      tokenAddress: generateMockSolanaAddress(),
      tokenSymbol: 'TEST',
      tokenName: 'Test Token',
      entryPrice: generateMockDecimal(0.01, 10),
      currentPrice: generateMockDecimal(0.01, 10),
      quantity: generateMockDecimal(100, 10000),
      entryTimestamp: new Date(),
      presetUsed: 'DEFAULT',
      riskLevel: 'MEDIUM',
      status: 'ACTIVE',
      pnl: generateMockDecimal(-100, 100),
      pnlPercent: Math.random() * 200 - 100, // -100% to +100%
      ...overrides
    }

    return this.prisma.position.create({
      data: positionData as any
    })
  }

  /**
   * Create test transaction
   */
  async createTestTransaction(userId: string, positionId?: string, overrides: Partial<Transaction> = {}): Promise<Transaction> {
    const transactionData = {
      userId,
      positionId,
      hash: generateMockTransactionHash(),
      type: 'SWAP',
      status: 'CONFIRMED',
      tokenIn: generateMockSolanaAddress(),
      tokenOut: generateMockSolanaAddress(),
      tokenInSymbol: 'SOL',
      tokenOutSymbol: 'USDC',
      tokenInName: 'Solana',
      tokenOutName: 'USD Coin',
      amountIn: generateMockDecimal(1, 100),
      amountOut: generateMockDecimal(100, 10000),
      amountInRaw: '1000000000',
      amountOutRaw: '1000000000',
      price: generateMockDecimal(0.01, 100),
      fees: {
        total: 0.001,
        network: 0.0005,
        jupiter: 0.0005
      },
      presetUsed: 'DEFAULT',
      slippageUsed: 1.0,
      mevProtected: false,
      executionTime: Math.floor(Math.random() * 5000) + 1000,
      totalCost: 0.001,
      timestamp: new Date(),
      ...overrides
    }

    return this.prisma.transaction.create({
      data: transactionData as any
    })
  }

  /**
   * Create test exit strategy
   */
  async createTestExitStrategy(userId: string, positionId?: string, overrides: Partial<ExitStrategy> = {}): Promise<ExitStrategy> {
    const strategyData = {
      userId,
      positionId,
      type: 'CUSTOM',
      customName: 'Test Strategy',
      stopLoss: {
        enabled: true,
        type: 'PERCENTAGE',
        triggerPercent: 10
      },
      profitTargets: [
        {
          id: 'tp1',
          enabled: true,
          type: 'PERCENTAGE',
          triggerPercent: 20,
          sellPercent: 50,
          executed: false
        }
      ],
      moonBag: {
        enabled: false,
        reservePercent: 10,
        triggerConditions: {
          minProfitPercent: 100
        },
        neverSell: false
      },
      executionState: 'PENDING',
      locked: false,
      totalTriggers: 0,
      totalPnl: 0,
      successRate: 0,
      ...overrides
    }

    return this.prisma.exitStrategy.create({
      data: strategyData as any
    })
  }

  /**
   * Create test portfolio
   */
  async createTestPortfolio(userId: string, overrides: Partial<Portfolio> = {}): Promise<Portfolio> {
    const portfolioData = {
      userId,
      totalValue: generateMockDecimal(1000, 100000),
      totalValueUsd: generateMockDecimal(1000, 100000),
      totalPnl: generateMockDecimal(-1000, 10000),
      totalPnlUsd: generateMockDecimal(-1000, 10000),
      totalPnlPercent: Math.random() * 200 - 100,
      riskScore: Math.random() * 100,
      exposurePercent: Math.random() * 100,
      maxDrawdown: Math.random() * 50,
      winRate: Math.random() * 100,
      totalTrades: Math.floor(Math.random() * 100),
      profitFactor: Math.random() * 5,
      sharpeRatio: Math.random() * 3 - 1,
      ...overrides
    }

    return this.prisma.portfolio.create({
      data: portfolioData as any
    })
  }

  /**
   * Create complete test scenario with user, positions, transactions, and strategies
   */
  async createTestScenario(options: {
    positionCount?: number
    transactionCount?: number
    strategyCount?: number
  } = {}): Promise<{
    user: User
    positions: Position[]
    transactions: Transaction[]
    strategies: ExitStrategy[]
    portfolio: Portfolio
  }> {
    const { positionCount = 3, transactionCount = 5, strategyCount = 2 } = options

    // Create user
    const user = await this.createTestUser()

    // Create positions
    const positions: Position[] = []
    for (let i = 0; i < positionCount; i++) {
      const position = await this.createTestPosition(user.id, {
        tokenSymbol: `TOKEN${i + 1}`,
        tokenName: `Test Token ${i + 1}`
      })
      positions.push(position)
    }

    // Create transactions
    const transactions: Transaction[] = []
    for (let i = 0; i < transactionCount; i++) {
      const position = positions[i % positions.length]
      const transaction = await this.createTestTransaction(user.id, position.id, {
        tokenInSymbol: i % 2 === 0 ? 'SOL' : position.tokenSymbol,
        tokenOutSymbol: i % 2 === 0 ? position.tokenSymbol : 'SOL'
      })
      transactions.push(transaction)
    }

    // Create exit strategies
    const strategies: ExitStrategy[] = []
    for (let i = 0; i < strategyCount; i++) {
      const position = positions[i % positions.length]
      const strategy = await this.createTestExitStrategy(user.id, position.id, {
        customName: `Strategy ${i + 1}`
      })
      strategies.push(strategy)
    }

    // Create portfolio
    const portfolio = await this.createTestPortfolio(user.id)

    return {
      user,
      positions,
      transactions,
      strategies,
      portfolio
    }
  }

  /**
   * Clean up all test data for a user
   */
  async cleanupUserData(userId: string): Promise<void> {
    await this.prisma.alert.deleteMany({ where: { userId } })
    await this.prisma.transaction.deleteMany({ where: { userId } })
    await this.prisma.position.deleteMany({ where: { userId } })
    await this.prisma.exitStrategy.deleteMany({ where: { userId } })
    await this.prisma.customStrategy.deleteMany({ where: { userId } })
    await this.prisma.portfolio.deleteMany({ where: { userId } })
    await this.prisma.watchlist.deleteMany({ where: { userId } })
    await this.prisma.userPreferences.deleteMany({ where: { userId } })
    await this.prisma.user.delete({ where: { id: userId } })
  }

  /**
   * Get user with all related data
   */
  async getUserWithAllData(userId: string) {
    return this.prisma.user.findUnique({
      where: { id: userId },
      include: {
        preferences: true,
        positions: {
          include: {
            transactions: true,
            strategy: true
          }
        },
        transactions: true,
        strategies: true,
        portfolios: true,
        alerts: true,
        watchlist: true
      }
    })
  }

  /**
   * Assert database state matches expectations
   */
  async assertDatabaseState(expectations: {
    userCount?: number
    positionCount?: number
    transactionCount?: number
    strategyCount?: number
  }): Promise<void> {
    const { userCount, positionCount, transactionCount, strategyCount } = expectations

    if (userCount !== undefined) {
      const count = await this.prisma.user.count()
      expect(count).toBe(userCount)
    }

    if (positionCount !== undefined) {
      const count = await this.prisma.position.count()
      expect(count).toBe(positionCount)
    }

    if (transactionCount !== undefined) {
      const count = await this.prisma.transaction.count()
      expect(count).toBe(transactionCount)
    }

    if (strategyCount !== undefined) {
      const count = await this.prisma.exitStrategy.count()
      expect(count).toBe(strategyCount)
    }
  }

  /**
   * Create trading preset for tests
   */
  async createTestTradingPreset(name: string = 'TEST', overrides: any = {}) {
    return this.prisma.tradingPreset.create({
      data: {
        name: name,
        priorityFee: '0.001',
        slippageLimit: 1.0,
        mevProtectionLevel: 'BASIC',
        buySettings: {
          maxSlippage: 2.0,
          priorityFee: 0.001
        },
        sellSettings: {
          maxSlippage: 3.0,
          priorityFee: 0.002
        },
        locked: false,
        systemDefault: false,
        totalUsage: 0,
        averagePerformance: 0,
        ...overrides
      }
    })
  }

  /**
   * Seed database with realistic test data
   */
  async seedRealisticTestData(): Promise<{
    users: User[]
    positions: Position[]
    transactions: Transaction[]
  }> {
    const users: User[] = []
    const positions: Position[] = []
    const transactions: Transaction[] = []

    // Create multiple users with different profiles
    for (let i = 0; i < 5; i++) {
      const user = await this.createTestUser({
        email: `trader${i + 1}@example.com`
      })
      users.push(user)

      // Create positions for each user
      const userPositions = await Promise.all([
        this.createTestPosition(user.id, {
          tokenSymbol: 'BONK',
          tokenName: 'Bonk',
          entryPrice: '0.000012',
          currentPrice: '0.000015',
          quantity: '1000000',
          status: 'ACTIVE'
        }),
        this.createTestPosition(user.id, {
          tokenSymbol: 'PEPE',
          tokenName: 'Pepe',
          entryPrice: '0.0000008',
          currentPrice: '0.0000006',
          quantity: '5000000',
          status: 'ACTIVE'
        }),
        this.createTestPosition(user.id, {
          tokenSymbol: 'WIF',
          tokenName: 'dogwifhat',
          entryPrice: '2.45',
          currentPrice: '3.12',
          quantity: '100',
          status: 'CLOSED'
        })
      ])
      positions.push(...userPositions)

      // Create transactions for positions
      for (const position of userPositions) {
        const buyTransaction = await this.createTestTransaction(user.id, position.id, {
          type: 'BUY',
          tokenIn: 'So11111111111111111111111111111111111111112', // SOL
          tokenOut: position.tokenAddress,
          tokenInSymbol: 'SOL',
          tokenOutSymbol: position.tokenSymbol,
          amountIn: '1.0',
          amountOut: position.quantity,
          price: position.entryPrice
        })
        transactions.push(buyTransaction)

        // Add sell transaction for closed positions
        if (position.status === 'CLOSED') {
          const sellTransaction = await this.createTestTransaction(user.id, position.id, {
            type: 'SELL',
            tokenIn: position.tokenAddress,
            tokenOut: 'So11111111111111111111111111111111111111112',
            tokenInSymbol: position.tokenSymbol,
            tokenOutSymbol: 'SOL',
            amountIn: position.quantity,
            amountOut: '1.5',
            price: position.currentPrice
          })
          transactions.push(sellTransaction)
        }
      }
    }

    return { users, positions, transactions }
  }
}