/**
 * Test utility functions and helpers
 */

import { PrismaClient } from '@prisma/client'
import { execSync } from 'child_process'
import { randomBytes } from 'crypto'

// Test database client
let testPrisma: PrismaClient | null = null

/**
 * Get or create test database client
 */
export function getTestPrismaClient(): PrismaClient {
  if (!testPrisma) {
    testPrisma = new PrismaClient({
      datasources: {
        db: {
          url: process.env.TEST_DATABASE_URL || process.env.DATABASE_URL
        }
      }
    })
  }
  return testPrisma
}

/**
 * Clean up test database client
 */
export async function cleanupTestPrismaClient(): Promise<void> {
  if (testPrisma) {
    await testPrisma.$disconnect()
    testPrisma = null
  }
}

/**
 * Reset test database to clean state
 */
export async function resetTestDatabase(): Promise<void> {
  const prisma = getTestPrismaClient()
  
  // Delete all data in correct order (respecting foreign key constraints)
  await prisma.alert.deleteMany()
  await prisma.transaction.deleteMany()
  await prisma.position.deleteMany()
  await prisma.exitStrategy.deleteMany()
  await prisma.customStrategy.deleteMany()
  await prisma.portfolio.deleteMany()
  await prisma.watchlist.deleteMany()
  await prisma.userPreferences.deleteMany()
  await prisma.user.deleteMany()
  await prisma.priceHistory.deleteMany()
  await prisma.systemConfig.deleteMany()
}

/**
 * Create isolated test database
 */
export async function createIsolatedTestDatabase(): Promise<string> {
  const testDbName = `test_${randomBytes(8).toString('hex')}`
  const baseUrl = process.env.DATABASE_URL || 'postgresql://test:test@localhost:5432'
  const testDbUrl = `${baseUrl.split('/').slice(0, -1).join('/')}/${testDbName}`
  
  try {
    // Create database
    execSync(`createdb ${testDbName}`, { stdio: 'pipe' })
    
    // Run migrations
    execSync('npx prisma migrate deploy', {
      stdio: 'pipe',
      env: { ...process.env, DATABASE_URL: testDbUrl }
    })
    
    return testDbUrl
  } catch (error) {
    console.error(`Failed to create isolated test database: ${error}`)
    throw error
  }
}

/**
 * Drop isolated test database
 */
export async function dropIsolatedTestDatabase(dbName: string): Promise<void> {
  try {
    execSync(`dropdb ${dbName}`, { stdio: 'pipe' })
  } catch (error) {
    console.warn(`Failed to drop test database ${dbName}: ${error}`)
  }
}

/**
 * Wait for condition with timeout
 */
export async function waitFor(
  condition: () => Promise<boolean> | boolean,
  timeoutMs: number = 5000,
  intervalMs: number = 100
): Promise<void> {
  const startTime = Date.now()
  
  while (Date.now() - startTime < timeoutMs) {
    if (await condition()) {
      return
    }
    await sleep(intervalMs)
  }
  
  throw new Error(`Condition not met within ${timeoutMs}ms`)
}

/**
 * Sleep for specified milliseconds
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * Generate random Solana address for testing
 */
export function generateMockSolanaAddress(): string {
  const chars = '123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz'
  let result = ''
  for (let i = 0; i < 44; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * Generate random transaction hash
 */
export function generateMockTransactionHash(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < 88; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * Generate random decimal number for prices/amounts
 */
export function generateMockDecimal(min: number = 0.001, max: number = 1000): string {
  const value = Math.random() * (max - min) + min
  return value.toFixed(9)
}

/**
 * Mock Date.now for time-based tests
 */
export function mockTimeAt(timestamp: number): () => void {
  const originalNow = Date.now
  Date.now = jest.fn(() => timestamp)
  
  return () => {
    Date.now = originalNow
  }
}

/**
 * Mock console methods and capture output
 */
export function mockConsole(): {
  log: jest.SpyInstance
  error: jest.SpyInstance
  warn: jest.SpyInstance
  info: jest.SpyInstance
  restore: () => void
} {
  const originalConsole = { ...console }
  
  const log = jest.spyOn(console, 'log').mockImplementation(() => {})
  const error = jest.spyOn(console, 'error').mockImplementation(() => {})
  const warn = jest.spyOn(console, 'warn').mockImplementation(() => {})
  const info = jest.spyOn(console, 'info').mockImplementation(() => {})
  
  return {
    log,
    error,
    warn,
    info,
    restore: () => {
      Object.assign(console, originalConsole)
    }
  }
}

/**
 * Create timeout promise for race conditions
 */
export function timeout(ms: number, message?: string): Promise<never> {
  return new Promise((_, reject) => {
    setTimeout(() => {
      reject(new Error(message || `Timeout after ${ms}ms`))
    }, ms)
  })
}

/**
 * Deep clone object for test data isolation
 */
export function deepClone<T>(obj: T): T {
  return JSON.parse(JSON.stringify(obj))
}

/**
 * Assert that value is defined (useful for TypeScript)
 */
export function assertDefined<T>(value: T | undefined | null, message?: string): asserts value is T {
  if (value === undefined || value === null) {
    throw new Error(message || 'Expected value to be defined')
  }
}

/**
 * Create test transaction data
 */
export function createMockTransactionData(overrides: any = {}) {
  return {
    hash: generateMockTransactionHash(),
    type: 'SWAP',
    status: 'CONFIRMED',
    tokenIn: generateMockSolanaAddress(),
    tokenOut: generateMockSolanaAddress(),
    tokenInSymbol: 'SOL',
    tokenOutSymbol: 'USDC',
    amountIn: generateMockDecimal(1, 100),
    amountOut: generateMockDecimal(100, 10000),
    amountInRaw: '1000000000',
    amountOutRaw: '1000000000',
    price: parseFloat(generateMockDecimal(0.01, 100)),
    fees: {
      total: 0.001,
      network: 0.0005,
      jupiter: 0.0005
    },
    presetUsed: 'DEFAULT',
    slippageUsed: 1.0,
    mevProtected: false,
    executionTime: Math.floor(Math.random() * 5000) + 1000,
    totalCost: 0.001,
    ...overrides
  }
}

/**
 * Environment variable helpers
 */
export class TestEnvironment {
  private originalEnv: NodeJS.ProcessEnv = {}
  
  constructor() {
    this.originalEnv = { ...process.env }
  }
  
  set(key: string, value: string): void {
    process.env[key] = value
  }
  
  unset(key: string): void {
    delete process.env[key]
  }
  
  restore(): void {
    process.env = { ...this.originalEnv }
  }
}

/**
 * Performance measurement utilities
 */
export class PerformanceTimer {
  private startTime: number = 0
  
  start(): void {
    this.startTime = performance.now()
  }
  
  end(): number {
    return performance.now() - this.startTime
  }
  
  static async measure<T>(fn: () => Promise<T>): Promise<{ result: T, duration: number }> {
    const timer = new PerformanceTimer()
    timer.start()
    const result = await fn()
    const duration = timer.end()
    return { result, duration }
  }
}