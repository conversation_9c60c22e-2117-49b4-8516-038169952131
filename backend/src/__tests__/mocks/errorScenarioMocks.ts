/**
 * Enhanced error scenario mocks for comprehensive failure testing
 */

import { generateMockSolanaAddress, generateMockTransactionHash, sleep } from '../utils/testUtils'

export interface ErrorScenarioConfig {
  type: 'timeout' | 'connection' | 'validation' | 'corruption' | 'resource' | 'circuit_breaker'
  severity: 'low' | 'medium' | 'high' | 'critical'
  duration?: number // How long the error persists
  recovery?: boolean // Whether the service should recover
  cascade?: boolean // Whether the error should cascade to other services
}

/**
 * Database Error Scenario Mock
 */
export class DatabaseErrorMock {
  private connectionFailure: boolean = false
  private queryTimeout: number = 0
  private corruptData: boolean = false
  private resourceExhaustion: boolean = false
  private transactionFailure: boolean = false
  private deadlockScenario: boolean = false
  private readonly failedQueries: Set<string> = new Set()
  private readonly slowQueries: Map<string, number> = new Map()

  /**
   * Simulate connection pool exhaustion
   */
  simulateConnectionPoolExhaustion(): void {
    this.connectionFailure = true
    this.resourceExhaustion = true
  }

  /**
   * Simulate database deadlock scenario
   */
  simulateDeadlock(queries: string[]): void {
    this.deadlockScenario = true
    queries.forEach(q => this.failedQueries.add(q))
  }

  /**
   * Simulate query timeout
   */
  simulateQueryTimeout(timeoutMs: number, queryPattern?: string): void {
    this.queryTimeout = timeoutMs
    if (queryPattern) {
      this.slowQueries.set(queryPattern, timeoutMs)
    }
  }

  /**
   * Simulate data corruption
   */
  simulateDataCorruption(): void {
    this.corruptData = true
  }

  /**
   * Simulate transaction rollback scenarios
   */
  simulateTransactionFailure(): void {
    this.transactionFailure = true
  }

  /**
   * Execute mock database operation with potential failures
   */
  async executeQuery(query: string, data?: any): Promise<any> {
    // Check for connection failure
    if (this.connectionFailure) {
      throw new Error('Database connection failed: Connection pool exhausted')
    }

    // Check for deadlock
    if (this.deadlockScenario && this.failedQueries.has(query)) {
      throw new Error('Database deadlock detected: Transaction aborted')
    }

    // Check for query-specific timeout
    const queryTimeout = this.slowQueries.get(query) || this.queryTimeout
    if (queryTimeout > 0) {
      await sleep(queryTimeout)
      throw new Error(`Query timeout after ${queryTimeout}ms`)
    }

    // Check for transaction failure
    if (this.transactionFailure && query.includes('BEGIN') || query.includes('COMMIT')) {
      throw new Error('Transaction failed: Unable to acquire lock')
    }

    // Check for data corruption
    if (this.corruptData && query.includes('SELECT')) {
      throw new Error('Data corruption detected: Invalid data format')
    }

    // Return mock successful result
    return { success: true, data: data || {} }
  }

  /**
   * Reset all error scenarios
   */
  reset(): void {
    this.connectionFailure = false
    this.queryTimeout = 0
    this.corruptData = false
    this.resourceExhaustion = false
    this.transactionFailure = false
    this.deadlockScenario = false
    this.failedQueries.clear()
    this.slowQueries.clear()
  }

  /**
   * Get current error state
   */
  getErrorState(): any {
    return {
      connectionFailure: this.connectionFailure,
      queryTimeout: this.queryTimeout,
      corruptData: this.corruptData,
      resourceExhaustion: this.resourceExhaustion,
      transactionFailure: this.transactionFailure,
      deadlockScenario: this.deadlockScenario,
      failedQueries: Array.from(this.failedQueries),
      slowQueries: Object.fromEntries(this.slowQueries)
    }
  }
}

/**
 * Redis Error Scenario Mock
 */
export class RedisErrorMock {
  private connectionLost: boolean = false
  private memoryExhaustion: boolean = false
  private keyExpiration: boolean = false
  private replicationFailure: boolean = false
  private clusterFailure: boolean = false
  private readonly failedOperations: Set<string> = new Set()

  /**
   * Simulate Redis connection loss
   */
  simulateConnectionLoss(): void {
    this.connectionLost = true
  }

  /**
   * Simulate Redis memory exhaustion
   */
  simulateMemoryExhaustion(): void {
    this.memoryExhaustion = true
  }

  /**
   * Simulate unexpected key expiration
   */
  simulateKeyExpiration(): void {
    this.keyExpiration = true
  }

  /**
   * Simulate replication failure in Redis cluster
   */
  simulateReplicationFailure(): void {
    this.replicationFailure = true
  }

  /**
   * Simulate cluster node failure
   */
  simulateClusterFailure(): void {
    this.clusterFailure = true
  }

  /**
   * Execute Redis operation with potential failures
   */
  async executeOperation(operation: string, key: string, value?: any): Promise<any> {
    // Check for connection loss
    if (this.connectionLost) {
      throw new Error('Redis connection lost: Unable to connect to Redis server')
    }

    // Check for memory exhaustion
    if (this.memoryExhaustion && (operation === 'SET' || operation === 'LPUSH')) {
      throw new Error('Redis out of memory: Cannot allocate space for operation')
    }

    // Check for cluster failure
    if (this.clusterFailure) {
      throw new Error('Redis cluster failure: Majority of nodes unavailable')
    }

    // Check for replication failure
    if (this.replicationFailure && operation === 'SET') {
      throw new Error('Redis replication failure: Unable to replicate to slaves')
    }

    // Check for key expiration issues
    if (this.keyExpiration && operation === 'GET') {
      return null // Simulate unexpected expiration
    }

    // Return mock successful result
    return operation === 'GET' ? value || 'mock-value' : 'OK'
  }

  /**
   * Reset all error scenarios
   */
  reset(): void {
    this.connectionLost = false
    this.memoryExhaustion = false
    this.keyExpiration = false
    this.replicationFailure = false
    this.clusterFailure = false
    this.failedOperations.clear()
  }
}

/**
 * Network Error Scenario Mock
 */
export class NetworkErrorMock {
  private timeoutMs: number = 0
  private packetLoss: number = 0
  private dnsFailed: boolean = false
  private sslError: boolean = false
  private rateLimited: boolean = false
  private circuitBreakerOpen: boolean = false

  /**
   * Simulate network timeout
   */
  simulateTimeout(ms: number): void {
    this.timeoutMs = ms
  }

  /**
   * Simulate packet loss
   */
  simulatePacketLoss(percentage: number): void {
    this.packetLoss = percentage
  }

  /**
   * Simulate DNS resolution failure
   */
  simulateDnsFailure(): void {
    this.dnsFailed = true
  }

  /**
   * Simulate SSL/TLS handshake failure
   */
  simulateSslError(): void {
    this.sslError = true
  }

  /**
   * Simulate rate limiting
   */
  simulateRateLimit(): void {
    this.rateLimited = true
  }

  /**
   * Simulate circuit breaker activation
   */
  simulateCircuitBreakerOpen(): void {
    this.circuitBreakerOpen = true
  }

  /**
   * Execute network request with potential failures
   */
  async executeRequest(url: string, options?: any): Promise<any> {
    // Check for circuit breaker
    if (this.circuitBreakerOpen) {
      throw new Error('Circuit breaker is open: Service temporarily unavailable')
    }

    // Check for DNS failure
    if (this.dnsFailed) {
      throw new Error('DNS resolution failed: Unable to resolve hostname')
    }

    // Check for SSL error
    if (this.sslError) {
      throw new Error('SSL handshake failed: Certificate verification error')
    }

    // Check for rate limiting
    if (this.rateLimited) {
      throw new Error('Rate limit exceeded: Too many requests')
    }

    // Simulate packet loss
    if (this.packetLoss > 0 && Math.random() * 100 < this.packetLoss) {
      throw new Error('Network error: Packet loss detected')
    }

    // Simulate timeout
    if (this.timeoutMs > 0) {
      await sleep(this.timeoutMs)
      throw new Error(`Network timeout after ${this.timeoutMs}ms`)
    }

    // Return mock successful response
    return { status: 200, data: { success: true } }
  }

  /**
   * Reset all error scenarios
   */
  reset(): void {
    this.timeoutMs = 0
    this.packetLoss = 0
    this.dnsFailed = false
    this.sslError = false
    this.rateLimited = false
    this.circuitBreakerOpen = false
  }
}

/**
 * Jupiter API Enhanced Error Mock
 */
export class JupiterApiErrorMock {
  private quoteFailed: boolean = false
  private swapFailed: boolean = false
  private highLatency: number = 0
  private priceImpactThreshold: number = 0
  private liquidityDepleted: boolean = false
  private routeNotFound: boolean = false
  private slippageExceeded: boolean = false

  /**
   * Simulate high price impact scenario
   */
  simulateHighPriceImpact(threshold: number): void {
    this.priceImpactThreshold = threshold
  }

  /**
   * Simulate liquidity depletion
   */
  simulateLiquidityDepletion(): void {
    this.liquidityDepleted = true
  }

  /**
   * Simulate no route found
   */
  simulateNoRoute(): void {
    this.routeNotFound = true
  }

  /**
   * Simulate slippage exceeded
   */
  simulateSlippageExceeded(): void {
    this.slippageExceeded = true
  }

  /**
   * Execute Jupiter operation with enhanced error scenarios
   */
  async executeOperation(operation: 'quote' | 'swap', params: any): Promise<any> {
    // Add artificial latency
    if (this.highLatency > 0) {
      await sleep(this.highLatency)
    }

    if (operation === 'quote') {
      if (this.quoteFailed) {
        throw new Error('Jupiter quote failed: Service temporarily unavailable')
      }

      if (this.routeNotFound) {
        throw new Error('No route found: Insufficient liquidity for requested swap')
      }

      if (this.liquidityDepleted) {
        throw new Error('Liquidity depleted: Pool has insufficient reserves')
      }

      // Return quote with potential high price impact
      const priceImpact = this.priceImpactThreshold > 0 ? this.priceImpactThreshold + 1 : 0.5
      return {
        inAmount: params.amount.toString(),
        outAmount: Math.floor(params.amount * 0.95).toString(),
        priceImpactPct: priceImpact,
        routePlan: [{ swapInfo: { label: 'Raydium' }, percent: 100 }]
      }
    }

    if (operation === 'swap') {
      if (this.swapFailed) {
        throw new Error('Jupiter swap failed: Transaction could not be constructed')
      }

      if (this.slippageExceeded) {
        throw new Error('Slippage exceeded: Price moved unfavorably during execution')
      }

      return {
        swapTransaction: Buffer.from('mock-tx').toString('base64'),
        lastValidBlockHeight: *********
      }
    }
  }

  /**
   * Set high latency simulation
   */
  setHighLatency(ms: number): void {
    this.highLatency = ms
  }

  /**
   * Reset all error scenarios
   */
  reset(): void {
    this.quoteFailed = false
    this.swapFailed = false
    this.highLatency = 0
    this.priceImpactThreshold = 0
    this.liquidityDepleted = false
    this.routeNotFound = false
    this.slippageExceeded = false
  }
}

/**
 * Helius RPC Enhanced Error Mock
 */
export class HeliusRpcErrorMock {
  private rpcFailed: boolean = false
  private slowResponse: number = 0
  private invalidBlockhash: boolean = false
  private transactionExpired: boolean = false
  private accountNotFound: boolean = false
  private networkCongestion: boolean = false

  /**
   * Simulate invalid blockhash scenario
   */
  simulateInvalidBlockhash(): void {
    this.invalidBlockhash = true
  }

  /**
   * Simulate transaction expiration
   */
  simulateTransactionExpired(): void {
    this.transactionExpired = true
  }

  /**
   * Simulate account not found
   */
  simulateAccountNotFound(): void {
    this.accountNotFound = true
  }

  /**
   * Simulate network congestion
   */
  simulateNetworkCongestion(): void {
    this.networkCongestion = true
  }

  /**
   * Execute RPC operation with enhanced error scenarios
   */
  async executeRpcCall(method: string, params: any[]): Promise<any> {
    // Add slow response simulation
    if (this.slowResponse > 0) {
      await sleep(this.slowResponse)
    }

    if (this.rpcFailed) {
      throw new Error('RPC call failed: Node is not responding')
    }

    switch (method) {
      case 'getLatestBlockhash':
        if (this.invalidBlockhash) {
          return { value: { blockhash: 'invalid-hash', lastValidBlockHeight: 0 } }
        }
        if (this.networkCongestion) {
          throw new Error('Network congested: Unable to get latest blockhash')
        }
        return { value: { blockhash: generateMockSolanaAddress(), lastValidBlockHeight: ********* } }

      case 'sendRawTransaction':
        if (this.transactionExpired) {
          throw new Error('Transaction expired: Blockhash not found')
        }
        if (this.networkCongestion) {
          throw new Error('Network congested: Transaction dropped')
        }
        return generateMockTransactionHash()

      case 'getAccountInfo':
        if (this.accountNotFound) {
          return { value: null }
        }
        return { value: { lamports: **********, owner: generateMockSolanaAddress() } }

      default:
        return { success: true }
    }
  }

  /**
   * Set slow response simulation
   */
  setSlowResponse(ms: number): void {
    this.slowResponse = ms
  }

  /**
   * Reset all error scenarios
   */
  reset(): void {
    this.rpcFailed = false
    this.slowResponse = 0
    this.invalidBlockhash = false
    this.transactionExpired = false
    this.accountNotFound = false
    this.networkCongestion = false
  }
}

/**
 * Comprehensive Error Scenario Manager
 */
export class ErrorScenarioManager {
  public database: DatabaseErrorMock
  public redis: RedisErrorMock
  public network: NetworkErrorMock
  public jupiter: JupiterApiErrorMock
  public helius: HeliusRpcErrorMock

  constructor() {
    this.database = new DatabaseErrorMock()
    this.redis = new RedisErrorMock()
    this.network = new NetworkErrorMock()
    this.jupiter = new JupiterApiErrorMock()
    this.helius = new HeliusRpcErrorMock()
  }

  /**
   * Apply predefined error scenario combinations
   */
  applyScenario(scenarioName: string): void {
    this.resetAll()

    switch (scenarioName) {
      case 'total_system_failure':
        this.database.simulateConnectionPoolExhaustion()
        this.redis.simulateConnectionLoss()
        this.network.simulateTimeout(10000)
        this.jupiter.quoteFailed = true
        this.helius.rpcFailed = true
        break

      case 'network_partition':
        this.network.simulateTimeout(5000)
        this.network.simulatePacketLoss(50)
        this.jupiter.setHighLatency(8000)
        this.helius.setSlowResponse(6000)
        break

      case 'database_corruption':
        this.database.simulateDataCorruption()
        this.database.simulateDeadlock(['SELECT', 'UPDATE'])
        break

      case 'high_load_stress':
        this.database.simulateQueryTimeout(3000)
        this.redis.simulateMemoryExhaustion()
        this.network.simulateRateLimit()
        this.jupiter.setHighLatency(2000)
        break

      case 'cascade_failure':
        this.database.simulateConnectionPoolExhaustion()
        this.redis.simulateClusterFailure()
        this.network.simulateCircuitBreakerOpen()
        break

      case 'trading_failure':
        this.jupiter.simulateLiquidityDepletion()
        this.jupiter.simulateSlippageExceeded()
        this.helius.simulateTransactionExpired()
        break

      default:
        throw new Error(`Unknown error scenario: ${scenarioName}`)
    }
  }

  /**
   * Reset all error mocks
   */
  resetAll(): void {
    this.database.reset()
    this.redis.reset()
    this.network.reset()
    this.jupiter.reset()
    this.helius.reset()
  }

  /**
   * Get current error state across all services
   */
  getErrorState(): any {
    return {
      database: this.database.getErrorState(),
      redis: this.redis,
      network: this.network,
      jupiter: this.jupiter,
      helius: this.helius
    }
  }
}

/**
 * Global error scenario manager instance
 */
export const errorScenarioManager = new ErrorScenarioManager()