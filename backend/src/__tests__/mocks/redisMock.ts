/**
 * Redis mocking utilities for testing
 */

import { EventEmitter } from 'events'

/**
 * In-memory Redis mock implementation
 */
export class RedisMock extends EventEmitter {
  private store: Map<string, any> = new Map()
  private expireTimers: Map<string, NodeJS.Timeout> = new Map()
  private subscribers: Map<string, Set<(message: string) => void>> = new Map()
  private listData: Map<string, any[]> = new Map()

  /**
   * String operations
   */
  async get(key: string): Promise<string | null> {
    return this.store.get(key) || null
  }

  async set(key: string, value: string, ttl?: number): Promise<'OK'> {
    this.store.set(key, value)
    
    if (ttl) {
      this._setExpiration(key, ttl * 1000)
    }
    
    return 'OK'
  }

  async del(key: string): Promise<number> {
    const existed = this.store.has(key)
    this.store.delete(key)
    this._clearExpiration(key)
    return existed ? 1 : 0
  }

  async exists(key: string): Promise<number> {
    return this.store.has(key) ? 1 : 0
  }

  async expire(key: string, seconds: number): Promise<number> {
    if (!this.store.has(key)) return 0
    
    this._setExpiration(key, seconds * 1000)
    return 1
  }

  async ttl(key: string): Promise<number> {
    // Simplified implementation - returns -1 for no expiry, -2 for not exists
    return this.store.has(key) ? -1 : -2
  }

  /**
   * JSON operations (custom implementation)
   */
  async getJSON<T = any>(key: string): Promise<T | null> {
    const value = this.store.get(key)
    if (!value) return null
    
    try {
      return JSON.parse(value)
    } catch {
      return null
    }
  }

  async setJSON(key: string, value: any, ttl?: number): Promise<'OK'> {
    const jsonString = JSON.stringify(value)
    return this.set(key, jsonString, ttl)
  }

  /**
   * List operations
   */
  async lPush(key: string, ...values: string[]): Promise<number> {
    if (!this.listData.has(key)) {
      this.listData.set(key, [])
    }
    
    const list = this.listData.get(key)!
    list.unshift(...values)
    return list.length
  }

  async rPush(key: string, ...values: string[]): Promise<number> {
    if (!this.listData.has(key)) {
      this.listData.set(key, [])
    }
    
    const list = this.listData.get(key)!
    list.push(...values)
    return list.length
  }

  async lPop(key: string): Promise<string | null> {
    const list = this.listData.get(key)
    if (!list || list.length === 0) return null
    
    return list.shift() || null
  }

  async rPop(key: string): Promise<string | null> {
    const list = this.listData.get(key)
    if (!list || list.length === 0) return null
    
    return list.pop() || null
  }

  async lRange(key: string, start: number, end: number): Promise<string[]> {
    const list = this.listData.get(key)
    if (!list) return []
    
    return list.slice(start, end === -1 ? undefined : end + 1)
  }

  async lLen(key: string): Promise<number> {
    const list = this.listData.get(key)
    return list ? list.length : 0
  }

  async lTrim(key: string, start: number, end: number): Promise<'OK'> {
    const list = this.listData.get(key)
    if (list) {
      const newList = list.slice(start, end === -1 ? undefined : end + 1)
      this.listData.set(key, newList)
    }
    return 'OK'
  }

  /**
   * Hash operations
   */
  async hSet(key: string, field: string, value: string): Promise<number> {
    let hash = this.store.get(key)
    if (!hash || typeof hash !== 'object') {
      hash = {}
    }
    
    const isNew = !(field in hash)
    hash[field] = value
    this.store.set(key, hash)
    
    return isNew ? 1 : 0
  }

  async hGet(key: string, field: string): Promise<string | null> {
    const hash = this.store.get(key)
    if (!hash || typeof hash !== 'object') return null
    
    return hash[field] || null
  }

  async hGetAll(key: string): Promise<Record<string, string>> {
    const hash = this.store.get(key)
    if (!hash || typeof hash !== 'object') return {}
    
    return { ...hash }
  }

  async hDel(key: string, ...fields: string[]): Promise<number> {
    const hash = this.store.get(key)
    if (!hash || typeof hash !== 'object') return 0
    
    let deleted = 0
    for (const field of fields) {
      if (field in hash) {
        delete hash[field]
        deleted++
      }
    }
    
    return deleted
  }

  /**
   * Pub/Sub operations
   */
  async publish(channel: string, message: string): Promise<number> {
    const subscribers = this.subscribers.get(channel)
    if (!subscribers) return 0
    
    // Simulate async delivery
    setImmediate(() => {
      subscribers.forEach(callback => {
        try {
          callback(message)
        } catch (error) {
          console.error('Error in Redis subscriber callback:', error)
        }
      })
    })
    
    return subscribers.size
  }

  async publishJSON(channel: string, data: any): Promise<number> {
    return this.publish(channel, JSON.stringify(data))
  }

  subscribe(channel: string, callback: (message: string) => void): void {
    if (!this.subscribers.has(channel)) {
      this.subscribers.set(channel, new Set())
    }
    
    this.subscribers.get(channel)!.add(callback)
  }

  unsubscribe(channel: string, callback?: (message: string) => void): void {
    const subscribers = this.subscribers.get(channel)
    if (!subscribers) return
    
    if (callback) {
      subscribers.delete(callback)
    } else {
      subscribers.clear()
    }
    
    if (subscribers.size === 0) {
      this.subscribers.delete(channel)
    }
  }

  /**
   * Utility methods
   */
  async flushAll(): Promise<'OK'> {
    this.store.clear()
    this.listData.clear()
    this.subscribers.clear()
    this._clearAllExpirations()
    return 'OK'
  }

  async keys(pattern: string = '*'): Promise<string[]> {
    if (pattern === '*') {
      return Array.from(this.store.keys())
    }
    
    // Simple pattern matching for testing
    const regex = new RegExp(pattern.replace(/\*/g, '.*'))
    return Array.from(this.store.keys()).filter(key => regex.test(key))
  }

  /**
   * Connection methods (mocked)
   */
  async connect(): Promise<void> {
    // Mock connection
  }

  async disconnect(): Promise<void> {
    this._clearAllExpirations()
    this.store.clear()
    this.listData.clear()
    this.subscribers.clear()
  }

  async ping(): Promise<'PONG'> {
    return 'PONG'
  }

  /**
   * Private helper methods
   */
  private _setExpiration(key: string, ms: number): void {
    this._clearExpiration(key)
    
    const timer = setTimeout(() => {
      this.store.delete(key)
      this.listData.delete(key)
      this.expireTimers.delete(key)
    }, ms)
    
    this.expireTimers.set(key, timer)
  }

  private _clearExpiration(key: string): void {
    const timer = this.expireTimers.get(key)
    if (timer) {
      clearTimeout(timer)
      this.expireTimers.delete(key)
    }
  }

  private _clearAllExpirations(): void {
    for (const timer of this.expireTimers.values()) {
      clearTimeout(timer)
    }
    this.expireTimers.clear()
  }

  /**
   * Test helper methods
   */
  getStoreSnapshot(): Record<string, any> {
    return Object.fromEntries(this.store.entries())
  }

  getListSnapshot(): Record<string, any[]> {
    return Object.fromEntries(this.listData.entries())
  }

  getSubscriberCount(channel: string): number {
    return this.subscribers.get(channel)?.size || 0
  }

  simulateConnectionError(): void {
    this.emit('error', new Error('Simulated Redis connection error'))
  }

  simulateReconnect(): void {
    this.emit('connect')
  }
}

/**
 * Redis service mock factory
 */
export function createRedisMock(): RedisMock {
  return new RedisMock()
}

/**
 * Jest mock for Redis service
 */
export function mockRedisService(): {
  mock: RedisMock
  restore: () => void
} {
  const redisMock = createRedisMock()
  
  // Mock the Redis service
  jest.doMock('@/services/redis', () => ({
    RedisService: redisMock
  }))
  
  return {
    mock: redisMock,
    restore: () => {
      jest.dontMock('@/services/redis')
    }
  }
}

/**
 * Redis test utilities
 */
export class RedisTestHelper {
  private mock: RedisMock

  constructor(mock: RedisMock) {
    this.mock = mock
  }

  /**
   * Set up test data in Redis
   */
  async seedTestData(): Promise<void> {
    // Cache some test data
    await this.mock.setJSON('user:test123:portfolio', {
      totalValue: 10000,
      totalPnL: 1500,
      positions: 5
    })

    await this.mock.setJSON('token:SOL:price', {
      price: 120.50,
      timestamp: Date.now()
    })

    await this.mock.set('trading:enabled', 'true')
    
    // Add some list data
    await this.mock.lPush('recent_transactions:test123', 
      JSON.stringify({ hash: 'tx1', amount: 100 }),
      JSON.stringify({ hash: 'tx2', amount: 200 })
    )
  }

  /**
   * Assert Redis state
   */
  async assertRedisState(expectations: {
    keyExists?: string[]
    keyNotExists?: string[]
    listLength?: { key: string, length: number }[]
    values?: { key: string, value: any }[]
  }): Promise<void> {
    const { keyExists, keyNotExists, listLength, values } = expectations

    if (keyExists) {
      for (const key of keyExists) {
        const exists = await this.mock.exists(key)
        expect(exists).toBe(1)
      }
    }

    if (keyNotExists) {
      for (const key of keyNotExists) {
        const exists = await this.mock.exists(key)
        expect(exists).toBe(0)
      }
    }

    if (listLength) {
      for (const { key, length } of listLength) {
        const actualLength = await this.mock.lLen(key)
        expect(actualLength).toBe(length)
      }
    }

    if (values) {
      for (const { key, value } of values) {
        const actualValue = await this.mock.getJSON(key)
        expect(actualValue).toEqual(value)
      }
    }
  }

  /**
   * Wait for pub/sub message
   */
  async waitForMessage(channel: string, timeout: number = 5000): Promise<any> {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        this.mock.unsubscribe(channel, handler)
        reject(new Error(`No message received on channel ${channel} within ${timeout}ms`))
      }, timeout)

      const handler = (message: string) => {
        clearTimeout(timer)
        this.mock.unsubscribe(channel, handler)
        try {
          resolve(JSON.parse(message))
        } catch {
          resolve(message)
        }
      }

      this.mock.subscribe(channel, handler)
    })
  }

  /**
   * Simulate Redis operations with delays
   */
  async simulateSlowRedis(delayMs: number = 100): Promise<void> {
    const originalGet = this.mock.get.bind(this.mock)
    const originalSet = this.mock.set.bind(this.mock)
    
    this.mock.get = async (key: string) => {
      await new Promise(resolve => setTimeout(resolve, delayMs))
      return originalGet(key)
    }
    
    this.mock.set = async (key: string, value: string, ttl?: number) => {
      await new Promise(resolve => setTimeout(resolve, delayMs))
      return originalSet(key, value, ttl)
    }
  }

  /**
   * Get Redis statistics for testing
   */
  getStats(): {
    keyCount: number
    listCount: number
    subscriberCount: number
    totalSubscribers: number
  } {
    const store = this.mock.getStoreSnapshot()
    const lists = this.mock.getListSnapshot()
    const subscribers = Array.from((this.mock as any).subscribers.values())
    
    return {
      keyCount: Object.keys(store).length,
      listCount: Object.keys(lists).length,
      subscriberCount: subscribers.length,
      totalSubscribers: subscribers.reduce((sum: number, set: Set<any>) => sum + set.size, 0)
    }
  }
}