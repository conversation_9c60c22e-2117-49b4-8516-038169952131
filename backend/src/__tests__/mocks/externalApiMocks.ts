/**
 * External API mocks for testing (Jupiter, Helius, Price feeds, etc.)
 */

import { generateMockSolanaAddress, generateMockTransactionHash, generateMockDecimal } from '../utils/testUtils'

/**
 * Jupiter API Mock
 */
export class JupiterApiMock {
  private quoteResponseDelay: number = 100
  private swapResponseDelay: number = 200
  private shouldFailQuote: boolean = false
  private shouldFailSwap: boolean = false
  private customQuoteResponse: any = null
  private customSwapResponse: any = null

  /**
   * Mock quote endpoint
   */
  async getQuote(params: {
    inputMint: string
    outputMint: string
    amount: number
    slippageBps?: number
    userPublicKey?: string
    maxAccounts?: number
    onlyDirectRoutes?: boolean
  }): Promise<any> {
    await this.delay(this.quoteResponseDelay)

    if (this.shouldFailQuote) {
      throw new Error('Jupiter API error: Failed to get quote')
    }

    if (this.customQuoteResponse) {
      return this.customQuoteResponse
    }

    const { inputMint, outputMint, amount, slippageBps = 50 } = params
    const priceImpact = Math.random() * 2 // 0-2% price impact
    const outputAmount = amount * (100 + Math.random() * 10) // Simulate price difference

    return {
      inputMint,
      outputMint,
      inAmount: amount.toString(),
      outAmount: Math.floor(outputAmount).toString(),
      priceImpactPct: priceImpact,
      fees: {
        total: amount * 0.001, // 0.1% fee
        network: amount * 0.0005,
        jupiter: amount * 0.0005
      },
      routePlan: [
        {
          swapInfo: {
            ammKey: generateMockSolanaAddress(),
            label: 'Raydium',
            inputMint,
            outputMint,
            inAmount: amount.toString(),
            outAmount: Math.floor(outputAmount).toString(),
            feeAmount: Math.floor(amount * 0.001).toString(),
            feeMint: inputMint
          },
          percent: 100
        }
      ],
      slippageBps,
      marketInfos: [
        {
          id: generateMockSolanaAddress(),
          label: 'Raydium',
          inputMint,
          outputMint,
          notEnoughLiquidity: false,
          inAmount: amount.toString(),
          outAmount: Math.floor(outputAmount).toString(),
          priceImpactPct: priceImpact,
          lpFee: {
            amount: Math.floor(amount * 0.0005).toString(),
            mint: inputMint,
            pct: 0.05
          },
          platformFee: {
            amount: Math.floor(amount * 0.0005).toString(),
            mint: inputMint,
            pct: 0.05
          }
        }
      ]
    }
  }

  /**
   * Mock swap endpoint
   */
  async getSwapTransaction(params: {
    quoteResponse: any
    userPublicKey: string
    wrapAndUnwrapSol?: boolean
    dynamicComputeUnitLimit?: boolean
    prioritizationFeeLamports?: string | number
    useSharedAccounts?: boolean
  }): Promise<any> {
    await this.delay(this.swapResponseDelay)

    if (this.shouldFailSwap) {
      throw new Error('Jupiter API error: Failed to create swap transaction')
    }

    if (this.customSwapResponse) {
      return this.customSwapResponse
    }

    // Generate a mock base64 encoded transaction
    const mockTransaction = Buffer.from('mock-transaction-data').toString('base64')

    return {
      swapTransaction: mockTransaction,
      lastValidBlockHeight: *********,
      prioritizationFeeLamports: 5000
    }
  }

  /**
   * Health check mock
   */
  async healthCheck(): Promise<boolean> {
    await this.delay(50)
    return !this.shouldFailQuote && !this.shouldFailSwap
  }

  // Configuration methods for testing
  setQuoteDelay(ms: number): void {
    this.quoteResponseDelay = ms
  }

  setSwapDelay(ms: number): void {
    this.swapResponseDelay = ms
  }

  setQuoteFailure(shouldFail: boolean): void {
    this.shouldFailQuote = shouldFail
  }

  setSwapFailure(shouldFail: boolean): void {
    this.shouldFailSwap = shouldFail
  }

  setCustomQuoteResponse(response: any): void {
    this.customQuoteResponse = response
  }

  setCustomSwapResponse(response: any): void {
    this.customSwapResponse = response
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

/**
 * Helius RPC Mock
 */
export class HeliusRpcMock {
  private rpcDelay: number = 50
  private shouldFailRpc: boolean = false
  private blockHeight: number = *********
  private confirmedTransactions: Set<string> = new Set()

  /**
   * Mock getLatestBlockhash
   */
  async getLatestBlockhash(commitment: string = 'confirmed'): Promise<any> {
    await this.delay(this.rpcDelay)

    if (this.shouldFailRpc) {
      throw new Error('Helius RPC error: Failed to get latest blockhash')
    }

    return {
      value: {
        blockhash: generateMockSolanaAddress(),
        lastValidBlockHeight: this.blockHeight
      }
    }
  }

  /**
   * Mock sendRawTransaction
   */
  async sendRawTransaction(
    rawTransaction: Uint8Array | Buffer,
    options?: {
      skipPreflight?: boolean
      maxRetries?: number
      preflightCommitment?: string
    }
  ): Promise<string> {
    await this.delay(this.rpcDelay)

    if (this.shouldFailRpc) {
      throw new Error('Helius RPC error: Transaction failed')
    }

    const txHash = generateMockTransactionHash()
    
    // Simulate transaction confirmation after a delay
    setTimeout(() => {
      this.confirmedTransactions.add(txHash)
    }, 1000)

    return txHash
  }

  /**
   * Mock getSignatureStatus
   */
  async getSignatureStatus(signature: string): Promise<any> {
    await this.delay(this.rpcDelay)

    if (this.shouldFailRpc) {
      return { value: null }
    }

    const isConfirmed = this.confirmedTransactions.has(signature)

    return {
      value: isConfirmed ? {
        slot: this.blockHeight,
        confirmations: 15,
        err: null,
        status: { Ok: null }
      } : null
    }
  }

  /**
   * Mock getTransaction
   */
  async getTransaction(signature: string, options?: any): Promise<any> {
    await this.delay(this.rpcDelay)

    if (this.shouldFailRpc || !this.confirmedTransactions.has(signature)) {
      return { value: null }
    }

    return {
      value: {
        slot: this.blockHeight,
        transaction: {
          message: {
            accountKeys: [generateMockSolanaAddress(), generateMockSolanaAddress()],
            header: { numReadonlySignedAccounts: 0, numReadonlyUnsignedAccounts: 1, numRequiredSignatures: 1 },
            instructions: [],
            recentBlockhash: generateMockSolanaAddress()
          },
          signatures: [signature]
        },
        meta: {
          err: null,
          fee: 5000,
          innerInstructions: [],
          logMessages: ['Program log: Success'],
          postBalances: [**********, *********0],
          preBalances: [**********, **********],
          status: { Ok: null }
        },
        blockTime: Math.floor(Date.now() / 1000)
      }
    }
  }

  /**
   * Mock getAccountInfo
   */
  async getAccountInfo(address: string): Promise<any> {
    await this.delay(this.rpcDelay)

    if (this.shouldFailRpc) {
      return { value: null }
    }

    return {
      value: {
        data: ['', 'base64'],
        executable: false,
        lamports: Math.floor(Math.random() * ***********),
        owner: generateMockSolanaAddress(),
        rentEpoch: 300
      }
    }
  }

  /**
   * Mock getBalance
   */
  async getBalance(address: string): Promise<any> {
    await this.delay(this.rpcDelay)

    if (this.shouldFailRpc) {
      throw new Error('Helius RPC error: Failed to get balance')
    }

    return {
      value: Math.floor(Math.random() * ***********) // Random balance in lamports
    }
  }

  // Configuration methods
  setRpcDelay(ms: number): void {
    this.rpcDelay = ms
  }

  setRpcFailure(shouldFail: boolean): void {
    this.shouldFailRpc = shouldFail
  }

  setBlockHeight(height: number): void {
    this.blockHeight = height
  }

  confirmTransaction(signature: string): void {
    this.confirmedTransactions.add(signature)
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

/**
 * Price Service Mock
 */
export class PriceServiceMock {
  private priceData: Map<string, { price: number, timestamp: number }> = new Map()
  private priceDelay: number = 50
  private shouldFailPrice: boolean = false

  constructor() {
    // Initialize with some default prices
    this.setPriceData('So11111111111111111111111111111111111111112', 120.50) // SOL
    this.setPriceData('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', 1.00)   // USDC
    this.setPriceData('DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263', 0.000012) // BONK
  }

  async getTokenPrice(tokenAddress: string): Promise<number> {
    await this.delay(this.priceDelay)

    if (this.shouldFailPrice) {
      throw new Error('Price service error: Failed to get token price')
    }

    const priceData = this.priceData.get(tokenAddress)
    if (!priceData) {
      // Return random price for unknown tokens
      return Math.random() * 100
    }

    // Add some random variation (±2%)
    const variation = (Math.random() - 0.5) * 0.04
    return priceData.price * (1 + variation)
  }

  async getTokenPrices(tokenAddresses: string[]): Promise<Record<string, number>> {
    await this.delay(this.priceDelay)

    if (this.shouldFailPrice) {
      throw new Error('Price service error: Failed to get token prices')
    }

    const prices: Record<string, number> = {}
    for (const address of tokenAddresses) {
      prices[address] = await this.getTokenPrice(address)
    }

    return prices
  }

  async getHistoricalPrice(tokenAddress: string, timestamp: number): Promise<number> {
    await this.delay(this.priceDelay)

    if (this.shouldFailPrice) {
      throw new Error('Price service error: Failed to get historical price')
    }

    // Mock historical price with some variation
    const currentPrice = await this.getTokenPrice(tokenAddress)
    const ageHours = (Date.now() - timestamp) / (1000 * 60 * 60)
    const historicalVariation = (Math.random() - 0.5) * 0.1 * Math.min(ageHours / 24, 1)
    
    return currentPrice * (1 + historicalVariation)
  }

  // Configuration methods
  setPriceData(tokenAddress: string, price: number): void {
    this.priceData.set(tokenAddress, { price, timestamp: Date.now() })
  }

  setPriceDelay(ms: number): void {
    this.priceDelay = ms
  }

  setPriceFailure(shouldFail: boolean): void {
    this.shouldFailPrice = shouldFail
  }

  clearPriceData(): void {
    this.priceData.clear()
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

/**
 * WebSocket Mock for real-time updates
 */
export class WebSocketMock {
  private listeners: Map<string, Set<(data: any) => void>> = new Map()
  private isConnected: boolean = false
  private shouldFailConnection: boolean = false

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (this.shouldFailConnection) {
          reject(new Error('WebSocket connection failed'))
        } else {
          this.isConnected = true
          resolve()
        }
      }, 100)
    })
  }

  disconnect(): void {
    this.isConnected = false
    this.listeners.clear()
  }

  subscribe(channel: string, callback: (data: any) => void): void {
    if (!this.listeners.has(channel)) {
      this.listeners.set(channel, new Set())
    }
    this.listeners.get(channel)!.add(callback)
  }

  unsubscribe(channel: string, callback?: (data: any) => void): void {
    const channelListeners = this.listeners.get(channel)
    if (!channelListeners) return

    if (callback) {
      channelListeners.delete(callback)
    } else {
      channelListeners.clear()
    }

    if (channelListeners.size === 0) {
      this.listeners.delete(channel)
    }
  }

  // Simulate receiving data
  simulateMessage(channel: string, data: any): void {
    if (!this.isConnected) return

    const listeners = this.listeners.get(channel)
    if (listeners) {
      listeners.forEach(callback => {
        setTimeout(() => callback(data), 10)
      })
    }
  }

  // Configuration methods
  setConnectionFailure(shouldFail: boolean): void {
    this.shouldFailConnection = shouldFail
  }

  getConnectionStatus(): boolean {
    return this.isConnected
  }

  getListenerCount(channel: string): number {
    return this.listeners.get(channel)?.size || 0
  }
}

/**
 * Combined mock factory for all external services
 */
export class ExternalServiceMocks {
  public jupiter: JupiterApiMock
  public helius: HeliusRpcMock
  public priceService: PriceServiceMock
  public webSocket: WebSocketMock

  constructor() {
    this.jupiter = new JupiterApiMock()
    this.helius = new HeliusRpcMock()
    this.priceService = new PriceServiceMock()
    this.webSocket = new WebSocketMock()
  }

  /**
   * Configure all services for healthy responses
   */
  setHealthyState(): void {
    this.jupiter.setQuoteFailure(false)
    this.jupiter.setSwapFailure(false)
    this.helius.setRpcFailure(false)
    this.priceService.setPriceFailure(false)
    this.webSocket.setConnectionFailure(false)
  }

  /**
   * Configure all services for failure responses
   */
  setFailureState(): void {
    this.jupiter.setQuoteFailure(true)
    this.jupiter.setSwapFailure(true)
    this.helius.setRpcFailure(true)
    this.priceService.setPriceFailure(true)
    this.webSocket.setConnectionFailure(true)
  }

  /**
   * Set delays for all services (useful for timeout testing)
   */
  setDelays(ms: number): void {
    this.jupiter.setQuoteDelay(ms)
    this.jupiter.setSwapDelay(ms)
    this.helius.setRpcDelay(ms)
    this.priceService.setPriceDelay(ms)
  }

  /**
   * Reset all mocks to default state
   */
  reset(): void {
    this.setHealthyState()
    this.setDelays(50)
    this.priceService.clearPriceData()
    this.webSocket.disconnect()
  }
}

/**
 * Global mock instance for tests
 */
export const externalMocks = new ExternalServiceMocks()