import { Request, Response, NextFunction } from 'express'
import jwt from 'jsonwebtoken'
import { config } from '@/config/environment'
import { AppError, catchAsync } from '@/middleware/errorHandler'
import { DatabaseService } from '@/services/database'
import { logAuth } from '@/utils/logger'

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string
        email: string
        role: string
        isActive: boolean
      }
    }
  }
}

// JWT payload interface
interface JWTPayload {
  id: string
  email: string
  role: string
  iat: number
  exp: number
}

// Verify JWT token
const verifyToken = (token: string): Promise<JWTPayload> => {
  return new Promise((resolve, reject) => {
    jwt.verify(token, config.jwt.secret, (err, decoded) => {
      if (err) {
        reject(err)
      } else {
        resolve(decoded as JWTPayload)
      }
    })
  })
}

// Main authentication middleware
export const authMiddleware = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    // 1) Getting token and check if it's there
    let token: string | undefined

    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1]
    } else if (req.cookies?.jwt) {
      token = req.cookies.jwt
    }

    if (!token) {
      logAuth('Authentication failed - No token provided', undefined, {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        path: req.originalUrl,
      })
      
      return next(
        new AppError('You are not logged in! Please log in to get access.', 401, 'NO_TOKEN')
      )
    }

    // 2) Verification token
    let decoded: JWTPayload
    try {
      decoded = await verifyToken(token)
    } catch (error) {
      logAuth('Authentication failed - Invalid token', undefined, {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        path: req.originalUrl,
        error: error instanceof Error ? error.message : 'Unknown error',
      })
      
      return next(
        new AppError('Invalid token. Please log in again!', 401, 'INVALID_TOKEN')
      )
    }

    // 3) Check if user still exists
    const currentUser = await DatabaseService.user.findUnique({
      where: { id: decoded.id },
      select: {
        id: true,
        email: true,
        role: true,
        isActive: true,
        passwordChangedAt: true,
      },
    })

    if (!currentUser) {
      logAuth('Authentication failed - User not found', decoded.id, {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        path: req.originalUrl,
      })
      
      return next(
        new AppError('The user belonging to this token does no longer exist.', 401, 'USER_NOT_FOUND')
      )
    }

    // 4) Check if user is active
    if (!currentUser.isActive) {
      logAuth('Authentication failed - User inactive', decoded.id, {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        path: req.originalUrl,
      })
      
      return next(
        new AppError('Your account has been deactivated. Please contact support.', 401, 'USER_INACTIVE')
      )
    }

    // 5) Check if user changed password after the token was issued
    if (currentUser.passwordChangedAt) {
      const changedTimestamp = Math.floor(currentUser.passwordChangedAt.getTime() / 1000)
      
      if (decoded.iat < changedTimestamp) {
        logAuth('Authentication failed - Password changed after token issued', decoded.id, {
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          path: req.originalUrl,
        })
        
        return next(
          new AppError('User recently changed password! Please log in again.', 401, 'PASSWORD_CHANGED')
        )
      }
    }

    // Grant access to protected route
    req.user = {
      id: currentUser.id,
      email: currentUser.email,
      role: currentUser.role,
      isActive: currentUser.isActive,
    }

    logAuth('Authentication successful', currentUser.id, {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.originalUrl,
    })

    next()
  }
)

// Role-based authorization middleware
export const restrictTo = (...roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(
        new AppError('You are not logged in! Please log in to get access.', 401, 'NO_USER')
      )
    }

    if (!roles.includes(req.user.role)) {
      logAuth('Authorization failed - Insufficient permissions', req.user.id, {
        requiredRoles: roles,
        userRole: req.user.role,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        path: req.originalUrl,
      })
      
      return next(
        new AppError('You do not have permission to perform this action', 403, 'INSUFFICIENT_PERMISSIONS')
      )
    }

    next()
  }
}

// Optional authentication middleware (doesn't throw error if no token)
export const optionalAuth = catchAsync(
  async (req: Request, res: Response, next: NextFunction) => {
    let token: string | undefined

    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1]
    } else if (req.cookies?.jwt) {
      token = req.cookies.jwt
    }

    if (!token) {
      return next()
    }

    try {
      const decoded = await verifyToken(token)
      
      const currentUser = await DatabaseService.user.findUnique({
        where: { id: decoded.id },
        select: {
          id: true,
          email: true,
          role: true,
          isActive: true,
          passwordChangedAt: true,
        },
      })

      if (currentUser && currentUser.isActive) {
        // Check password change timestamp
        if (currentUser.passwordChangedAt) {
          const changedTimestamp = Math.floor(currentUser.passwordChangedAt.getTime() / 1000)
          
          if (decoded.iat >= changedTimestamp) {
            req.user = {
              id: currentUser.id,
              email: currentUser.email,
              role: currentUser.role,
              isActive: currentUser.isActive,
            }
          }
        } else {
          req.user = {
            id: currentUser.id,
            email: currentUser.email,
            role: currentUser.role,
            isActive: currentUser.isActive,
          }
        }
      }
    } catch (error) {
      // Silently fail for optional auth
    }

    next()
  }
)

// API key authentication middleware (for external services)
export const apiKeyAuth = (req: Request, res: Response, next: NextFunction) => {
  const apiKey = req.headers['x-api-key'] as string

  if (!apiKey) {
    return next(
      new AppError('API key is required', 401, 'NO_API_KEY')
    )
  }

  // In a real application, you would validate the API key against a database
  // For now, we'll use a simple environment variable check
  const validApiKey = process.env.API_KEY

  if (!validApiKey || apiKey !== validApiKey) {
    logAuth('API key authentication failed', undefined, {
      providedKey: apiKey.substring(0, 8) + '...',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.originalUrl,
    })
    
    return next(
      new AppError('Invalid API key', 401, 'INVALID_API_KEY')
    )
  }

  next()
}
