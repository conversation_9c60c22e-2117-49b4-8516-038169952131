import { Request, Response, NextFunction } from 'express'
import { logger } from '@/utils/logger'
import { RedisService } from '@/services/redis'
import { AppError } from '@/middleware/errorHandler'

export interface ErrorContext {
  userId?: string
  transactionId?: string
  endpoint: string
  method: string
  userAgent?: string
  ip?: string
  timestamp: number
  requestId: string
  sessionId?: string
  traceId?: string
}

export interface ErrorAnalysis {
  category: ErrorCategory
  severity: ErrorSeverity
  isRetryable: boolean
  recoveryStrategy: RecoveryStrategy
  userMessage: string
  technicalMessage: string
  errorCode: string
  httpStatus: number
  metadata: any
  recommendations: string[]
  relatedErrors: string[]
  estimatedRecoveryTime?: number
}

export type ErrorCategory = 
  | 'NETWORK'
  | 'RPC'
  | 'JUPITER'
  | 'WALLET'
  | 'MEV'
  | 'SLIPPAGE' 
  | 'VALIDATION'
  | 'AUTHENTICATION'
  | 'AUTHORIZATION'
  | 'RATE_LIMIT'
  | 'DATABASE'
  | 'REDIS'
  | 'WEBSOCKET'
  | 'TRANSACTION'
  | 'BUSINESS_LOGIC'
  | 'SYSTEM'
  | 'EXTERNAL_API'
  | 'UNKNOWN'

export type ErrorSeverity = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'

export type RecoveryStrategy = 
  | 'RETRY_IMMEDIATELY'
  | 'RETRY_WITH_BACKOFF'
  | 'RETRY_WITH_DIFFERENT_PARAMS'
  | 'ESCALATE_TO_MANUAL'
  | 'FAIL_FAST'
  | 'CIRCUIT_BREAKER'
  | 'FALLBACK_SERVICE'
  | 'USER_INTERVENTION_REQUIRED'
  | 'SYSTEM_MAINTENANCE_REQUIRED'

export interface ErrorPattern {
  pattern: RegExp
  category: ErrorCategory
  severity: ErrorSeverity
  recoveryStrategy: RecoveryStrategy
  userMessage: string
  recommendations: string[]
}

export interface CircuitBreakerState {
  isOpen: boolean
  failureCount: number
  lastFailureTime: number
  resetTimeout: number
  halfOpenAllowed: boolean
}

class EnhancedErrorHandler {
  private static instance: EnhancedErrorHandler
  private errorPatterns: ErrorPattern[]
  private circuitBreakers: Map<string, CircuitBreakerState> = new Map()
  private errorStats: Map<string, { count: number, lastOccurrence: number }> = new Map()
  private readonly CIRCUIT_BREAKER_THRESHOLD = 5
  private readonly CIRCUIT_BREAKER_TIMEOUT = 60000 // 1 minute
  private readonly ERROR_RATE_WINDOW = 300000 // 5 minutes

  private constructor() {
    this.initializeErrorPatterns()
    this.startErrorStatsCleanup()
  }

  public static getInstance(): EnhancedErrorHandler {
    if (!EnhancedErrorHandler.instance) {
      EnhancedErrorHandler.instance = new EnhancedErrorHandler()
    }
    return EnhancedErrorHandler.instance
  }

  /**
   * Initialize comprehensive error patterns
   */
  private initializeErrorPatterns(): void {
    this.errorPatterns = [
      // Network Errors
      {
        pattern: /ECONNREFUSED|ENOTFOUND|ETIMEDOUT|ECONNRESET/i,
        category: 'NETWORK',
        severity: 'HIGH',
        recoveryStrategy: 'RETRY_WITH_BACKOFF',
        userMessage: 'Network connection issue. Please try again in a few moments.',
        recommendations: ['Check network connectivity', 'Verify DNS resolution', 'Try again later']
      },
      {
        pattern: /Request timeout|Gateway timeout|504/i,
        category: 'NETWORK',
        severity: 'MEDIUM',
        recoveryStrategy: 'RETRY_WITH_BACKOFF',
        userMessage: 'Request timed out. Please try again.',
        recommendations: ['Retry with longer timeout', 'Check network congestion']
      },

      // RPC Errors
      {
        pattern: /All RPC endpoints failed|RPC_UNAVAILABLE|HELIUS_RPC_ERROR/i,
        category: 'RPC',
        severity: 'CRITICAL',
        recoveryStrategy: 'CIRCUIT_BREAKER',
        userMessage: 'Blockchain network is temporarily unavailable. Please try again later.',
        recommendations: ['Check RPC endpoint health', 'Switch to backup RPC', 'Wait for network recovery']
      },
      {
        pattern: /Invalid RPC response|RPC method not found/i,
        category: 'RPC',
        severity: 'HIGH',
        recoveryStrategy: 'FALLBACK_SERVICE',
        userMessage: 'Blockchain service error. Trying alternative connection.',
        recommendations: ['Validate RPC method', 'Check RPC version compatibility']
      },

      // Jupiter API Errors
      {
        pattern: /QUOTE_FAILED|Jupiter quote failed|No routes found/i,
        category: 'JUPITER',
        severity: 'MEDIUM',
        recoveryStrategy: 'RETRY_WITH_DIFFERENT_PARAMS',
        userMessage: 'Unable to find trading route. Try adjusting trade parameters.',
        recommendations: ['Increase slippage tolerance', 'Reduce trade size', 'Try different token pair']
      },
      {
        pattern: /JUPITER_UNAVAILABLE|Jupiter API error/i,
        category: 'JUPITER',
        severity: 'HIGH',
        recoveryStrategy: 'CIRCUIT_BREAKER',
        userMessage: 'Trading service temporarily unavailable. Please try again shortly.',
        recommendations: ['Wait for Jupiter API recovery', 'Check Jupiter API status']
      },

      // Wallet Errors
      {
        pattern: /Insufficient funds|Insufficient balance/i,
        category: 'WALLET',
        severity: 'LOW',
        recoveryStrategy: 'USER_INTERVENTION_REQUIRED',
        userMessage: 'Insufficient balance for this transaction.',
        recommendations: ['Add more funds to wallet', 'Reduce transaction amount']
      },
      {
        pattern: /Invalid signature|Signature verification failed/i,
        category: 'WALLET',
        severity: 'HIGH',
        recoveryStrategy: 'USER_INTERVENTION_REQUIRED',
        userMessage: 'Transaction signature is invalid. Please check your wallet.',
        recommendations: ['Reconnect wallet', 'Check wallet permissions', 'Verify wallet address']
      },

      // MEV Errors
      {
        pattern: /MEV_RISK_HIGH|MEV_RISK_CRITICAL|Sandwich attack detected/i,
        category: 'MEV',
        severity: 'MEDIUM',
        recoveryStrategy: 'RETRY_WITH_DIFFERENT_PARAMS',
        userMessage: 'High MEV risk detected - transaction parameters adjusted for protection.',
        recommendations: ['Increase slippage tolerance', 'Split large trades', 'Wait for better market conditions']
      },

      // Slippage Errors
      {
        pattern: /SLIPPAGE_EXCEEDED|Price impact too high|MARKET_CONDITIONS_UNFAVORABLE/i,
        category: 'SLIPPAGE',
        severity: 'MEDIUM',
        recoveryStrategy: 'RETRY_WITH_DIFFERENT_PARAMS',
        userMessage: 'Market conditions unfavorable. Consider adjusting trade parameters.',
        recommendations: ['Increase slippage tolerance', 'Reduce trade size', 'Wait for better liquidity']
      },

      // Validation Errors
      {
        pattern: /QUOTE_VALIDATION_FAILED|QUOTE_QUALITY_LOW|Invalid parameters/i,
        category: 'VALIDATION',
        severity: 'LOW',
        recoveryStrategy: 'RETRY_WITH_DIFFERENT_PARAMS',
        userMessage: 'Trade parameters need adjustment.',
        recommendations: ['Check input values', 'Adjust slippage settings', 'Verify token addresses']
      },

      // Transaction Errors
      {
        pattern: /Transaction failed|CONFIRMATION_TIMEOUT|CONFIRMATION_FAILED/i,
        category: 'TRANSACTION',
        severity: 'HIGH',
        recoveryStrategy: 'RETRY_WITH_BACKOFF',
        userMessage: 'Transaction failed to confirm. This may be due to network congestion.',
        recommendations: ['Wait and retry', 'Increase priority fee', 'Check transaction status']
      },

      // Database Errors
      {
        pattern: /Database connection failed|ECONNREFUSED.*5432|PostgreSQL/i,
        category: 'DATABASE',
        severity: 'CRITICAL',
        recoveryStrategy: 'SYSTEM_MAINTENANCE_REQUIRED',
        userMessage: 'Service temporarily unavailable. Please try again later.',
        recommendations: ['Check database connectivity', 'Verify database health', 'Restart database service']
      },

      // Redis Errors
      {
        pattern: /Redis connection failed|ECONNREFUSED.*6379/i,
        category: 'REDIS',
        severity: 'HIGH',
        recoveryStrategy: 'FALLBACK_SERVICE',
        userMessage: 'Some features may be temporarily limited.',
        recommendations: ['Check Redis connectivity', 'Use fallback caching', 'Restart Redis service']
      },

      // Rate Limiting
      {
        pattern: /Rate limit exceeded|Too many requests|429/i,
        category: 'RATE_LIMIT',
        severity: 'MEDIUM',
        recoveryStrategy: 'RETRY_WITH_BACKOFF',
        userMessage: 'Too many requests. Please wait a moment and try again.',
        recommendations: ['Implement exponential backoff', 'Reduce request frequency', 'Check rate limit headers']
      },

      // Authentication Errors
      {
        pattern: /Unauthorized|Invalid token|Authentication failed|401/i,
        category: 'AUTHENTICATION',
        severity: 'MEDIUM',
        recoveryStrategy: 'USER_INTERVENTION_REQUIRED',
        userMessage: 'Authentication required. Please log in again.',
        recommendations: ['Refresh authentication token', 'Re-login', 'Check session validity']
      },

      // Authorization Errors
      {
        pattern: /Forbidden|Access denied|Insufficient permissions|403/i,
        category: 'AUTHORIZATION',
        severity: 'MEDIUM',
        recoveryStrategy: 'USER_INTERVENTION_REQUIRED',
        userMessage: 'You don\'t have permission to perform this action.',
        recommendations: ['Check user permissions', 'Contact administrator', 'Verify account status']
      }
    ]
  }

  /**
   * Main error analysis and handling
   */
  public async analyzeError(
    error: Error | AppError | any,
    context: ErrorContext
  ): Promise<ErrorAnalysis> {
    try {
      const errorMessage = this.extractErrorMessage(error)
      const stackTrace = error.stack || 'No stack trace available'
      
      // Detect error pattern
      const pattern = this.detectErrorPattern(errorMessage, stackTrace)
      
      // Get base analysis from pattern or default
      const baseAnalysis = pattern ? {
        category: pattern.category,
        severity: pattern.severity,
        recoveryStrategy: pattern.recoveryStrategy,
        userMessage: pattern.userMessage,
        recommendations: pattern.recommendations
      } : this.getDefaultAnalysis(error)

      // Enhance analysis with context
      const enhancedAnalysis = await this.enhanceAnalysis(baseAnalysis, error, context)

      // Update error statistics
      await this.updateErrorStats(enhancedAnalysis.category, context)

      // Check circuit breaker
      const circuitBreakerKey = `${context.endpoint}:${enhancedAnalysis.category}`
      const shouldTriggerCircuitBreaker = this.shouldTriggerCircuitBreaker(circuitBreakerKey)
      
      if (shouldTriggerCircuitBreaker) {
        enhancedAnalysis.recoveryStrategy = 'CIRCUIT_BREAKER'
        enhancedAnalysis.userMessage = 'Service temporarily unavailable due to multiple failures. Please try again later.'
        this.openCircuitBreaker(circuitBreakerKey)
      }

      // Log comprehensive error information
      await this.logError(error, enhancedAnalysis, context)

      // Store error for pattern analysis
      await this.storeErrorForAnalysis(error, enhancedAnalysis, context)

      return enhancedAnalysis

    } catch (analysisError) {
      logger.error('Error analysis failed:', analysisError)
      return this.getFallbackAnalysis(error)
    }
  }

  /**
   * Express middleware for handling errors
   */
  public expressMiddleware() {
    return async (error: Error | AppError | any, req: Request, res: Response, next: NextFunction) => {
      try {
        const context: ErrorContext = {
          userId: req.user?.id,
          endpoint: req.path,
          method: req.method,
          userAgent: req.get('User-Agent'),
          ip: req.ip,
          timestamp: Date.now(),
          requestId: req.headers['x-request-id'] as string || this.generateRequestId(),
          sessionId: req.session?.id,
          traceId: req.headers['x-trace-id'] as string
        }

        const analysis = await this.analyzeError(error, context)

        // Send appropriate response
        res.status(analysis.httpStatus).json({
          success: false,
          error: {
            code: analysis.errorCode,
            message: analysis.userMessage,
            category: analysis.category,
            severity: analysis.severity,
            isRetryable: analysis.isRetryable,
            recommendations: analysis.recommendations,
            requestId: context.requestId,
            timestamp: context.timestamp
          },
          ...(process.env.NODE_ENV === 'development' && {
            debug: {
              technicalMessage: analysis.technicalMessage,
              stackTrace: error.stack,
              metadata: analysis.metadata
            }
          })
        })

        // Emit error event for real-time monitoring
        if (context.userId) {
          await RedisService.publishJSON('error_occurred', {
            userId: context.userId,
            category: analysis.category,
            message: analysis.userMessage,
            severity: analysis.severity,
            requestId: context.requestId,
            timestamp: context.timestamp
          }).catch(err => logger.debug('Failed to publish error event:', err))
        }

      } catch (handlerError) {
        logger.error('Error handler middleware failed:', handlerError)
        
        // Fallback response
        res.status(500).json({
          success: false,
          error: {
            code: 'INTERNAL_ERROR',
            message: 'An internal error occurred. Please try again later.',
            requestId: this.generateRequestId(),
            timestamp: Date.now()
          }
        })
      }
    }
  }

  /**
   * Detect error pattern from message and stack trace
   */
  private detectErrorPattern(message: string, stackTrace: string): ErrorPattern | null {
    const combinedText = `${message} ${stackTrace}`
    
    for (const pattern of this.errorPatterns) {
      if (pattern.pattern.test(combinedText)) {
        return pattern
      }
    }
    
    return null
  }

  /**
   * Get default analysis for unrecognized errors
   */
  private getDefaultAnalysis(error: any): Partial<ErrorAnalysis> {
    let category: ErrorCategory = 'UNKNOWN'
    let severity: ErrorSeverity = 'MEDIUM'
    let recoveryStrategy: RecoveryStrategy = 'RETRY_WITH_BACKOFF'

    // Try to categorize based on error properties
    if (error.code) {
      if (error.code.includes('NETWORK') || error.code.includes('TIMEOUT')) {
        category = 'NETWORK'
      } else if (error.code.includes('DB') || error.code.includes('DATABASE')) {
        category = 'DATABASE'
        severity = 'HIGH'
      } else if (error.code.includes('AUTH')) {
        category = 'AUTHENTICATION'
        recoveryStrategy = 'USER_INTERVENTION_REQUIRED'
      }
    }

    return {
      category,
      severity,
      recoveryStrategy,
      userMessage: 'An unexpected error occurred. Please try again.',
      recommendations: ['Try again', 'Contact support if problem persists']
    }
  }

  /**
   * Enhance analysis with additional context and intelligence
   */
  private async enhanceAnalysis(
    baseAnalysis: Partial<ErrorAnalysis>,
    error: any,
    context: ErrorContext
  ): Promise<ErrorAnalysis> {
    const errorCode = this.generateErrorCode(baseAnalysis.category!, context)
    const httpStatus = this.mapToHttpStatus(baseAnalysis.category!, baseAnalysis.severity!)
    
    // Check for related errors
    const relatedErrors = await this.findRelatedErrors(baseAnalysis.category!, context)
    
    // Determine if error is retryable
    const isRetryable = this.isRetryableError(baseAnalysis.recoveryStrategy!)
    
    // Estimate recovery time
    const estimatedRecoveryTime = this.estimateRecoveryTime(baseAnalysis.recoveryStrategy!)

    return {
      category: baseAnalysis.category!,
      severity: baseAnalysis.severity!,
      isRetryable,
      recoveryStrategy: baseAnalysis.recoveryStrategy!,
      userMessage: baseAnalysis.userMessage!,
      technicalMessage: this.extractErrorMessage(error),
      errorCode,
      httpStatus,
      metadata: this.extractErrorMetadata(error, context),
      recommendations: baseAnalysis.recommendations!,
      relatedErrors,
      estimatedRecoveryTime
    }
  }

  /**
   * Circuit breaker logic
   */
  private shouldTriggerCircuitBreaker(key: string): boolean {
    const state = this.circuitBreakers.get(key)
    if (!state) return false

    return state.failureCount >= this.CIRCUIT_BREAKER_THRESHOLD
  }

  private openCircuitBreaker(key: string): void {
    const now = Date.now()
    this.circuitBreakers.set(key, {
      isOpen: true,
      failureCount: this.CIRCUIT_BREAKER_THRESHOLD,
      lastFailureTime: now,
      resetTimeout: now + this.CIRCUIT_BREAKER_TIMEOUT,
      halfOpenAllowed: false
    })

    // Schedule circuit breaker reset
    setTimeout(() => {
      const state = this.circuitBreakers.get(key)
      if (state) {
        state.halfOpenAllowed = true
      }
    }, this.CIRCUIT_BREAKER_TIMEOUT)

    logger.warn(`Circuit breaker opened for: ${key}`)
  }

  /**
   * Update error statistics
   */
  private async updateErrorStats(category: ErrorCategory, context: ErrorContext): Promise<void> {
    const key = `${context.endpoint}:${category}`
    const now = Date.now()
    
    // Update in-memory stats
    const current = this.errorStats.get(key) || { count: 0, lastOccurrence: 0 }
    this.errorStats.set(key, {
      count: current.count + 1,
      lastOccurrence: now
    })

    // Update circuit breaker state
    const cbState = this.circuitBreakers.get(key) || {
      isOpen: false,
      failureCount: 0,
      lastFailureTime: 0,
      resetTimeout: 0,
      halfOpenAllowed: false
    }
    
    cbState.failureCount++
    cbState.lastFailureTime = now
    this.circuitBreakers.set(key, cbState)

    // Store in Redis for persistent tracking
    try {
      const redisKey = `error_stats:${key}`
      await RedisService.setJSON(redisKey, {
        count: current.count + 1,
        lastOccurrence: now,
        category,
        endpoint: context.endpoint
      }, 3600) // 1 hour TTL
    } catch (error) {
      logger.debug('Failed to store error stats in Redis:', error)
    }
  }

  /**
   * Log comprehensive error information
   */
  private async logError(
    error: any,
    analysis: ErrorAnalysis,
    context: ErrorContext
  ): Promise<void> {
    const logData = {
      category: analysis.category,
      severity: analysis.severity,
      errorCode: analysis.errorCode,
      userMessage: analysis.userMessage,
      technicalMessage: analysis.technicalMessage,
      recoveryStrategy: analysis.recoveryStrategy,
      context: {
        userId: context.userId,
        endpoint: context.endpoint,
        method: context.method,
        ip: context.ip,
        userAgent: context.userAgent,
        requestId: context.requestId
      },
      metadata: analysis.metadata,
      stackTrace: error.stack,
      timestamp: context.timestamp
    }

    // Log based on severity
    switch (analysis.severity) {
      case 'CRITICAL':
        logger.error('CRITICAL ERROR:', logData)
        break
      case 'HIGH':
        logger.error('HIGH SEVERITY ERROR:', logData)
        break
      case 'MEDIUM':
        logger.warn('MEDIUM SEVERITY ERROR:', logData)
        break
      case 'LOW':
        logger.info('LOW SEVERITY ERROR:', logData)
        break
    }
  }

  /**
   * Store error for pattern analysis and learning
   */
  private async storeErrorForAnalysis(
    error: any,
    analysis: ErrorAnalysis,
    context: ErrorContext
  ): Promise<void> {
    try {
      const errorData = {
        message: this.extractErrorMessage(error),
        stackTrace: error.stack,
        analysis,
        context,
        timestamp: Date.now()
      }

      const key = `error_analysis:${context.requestId}`
      await RedisService.setJSON(key, errorData, 86400) // 24 hours TTL
    } catch (error) {
      logger.debug('Failed to store error for analysis:', error)
    }
  }

  /**
   * Helper methods
   */
  private extractErrorMessage(error: any): string {
    if (typeof error === 'string') return error
    if (error.message) return error.message
    if (error.error && typeof error.error === 'string') return error.error
    return 'Unknown error occurred'
  }

  private generateErrorCode(category: ErrorCategory, context: ErrorContext): string {
    const timestamp = Date.now().toString().slice(-6)
    const endpoint = context.endpoint.replace(/[^a-zA-Z0-9]/g, '').toUpperCase().slice(0, 3)
    return `${category}_${endpoint}_${timestamp}`
  }

  private mapToHttpStatus(category: ErrorCategory, severity: ErrorSeverity): number {
    const statusMap: Record<ErrorCategory, number> = {
      NETWORK: 503,
      RPC: 503,
      JUPITER: 503,
      WALLET: 400,
      MEV: 400,
      SLIPPAGE: 400,
      VALIDATION: 400,
      AUTHENTICATION: 401,
      AUTHORIZATION: 403,
      RATE_LIMIT: 429,
      DATABASE: 503,
      REDIS: 503,
      WEBSOCKET: 503,
      TRANSACTION: 400,
      BUSINESS_LOGIC: 400,
      SYSTEM: 500,
      EXTERNAL_API: 503,
      UNKNOWN: 500
    }

    let baseStatus = statusMap[category] || 500
    
    // Adjust based on severity
    if (severity === 'CRITICAL' && baseStatus < 500) {
      baseStatus = 500
    }
    
    return baseStatus
  }

  private async findRelatedErrors(category: ErrorCategory, context: ErrorContext): Promise<string[]> {
    try {
      // Look for similar errors in the last 5 minutes
      const pattern = `error_stats:${context.endpoint}:*`
      // In a real implementation, you'd scan Redis for matching keys
      return []
    } catch (error) {
      return []
    }
  }

  private isRetryableError(strategy: RecoveryStrategy): boolean {
    return [
      'RETRY_IMMEDIATELY',
      'RETRY_WITH_BACKOFF',
      'RETRY_WITH_DIFFERENT_PARAMS',
      'FALLBACK_SERVICE'
    ].includes(strategy)
  }

  private estimateRecoveryTime(strategy: RecoveryStrategy): number | undefined {
    const timeMap: Record<RecoveryStrategy, number> = {
      RETRY_IMMEDIATELY: 1000,
      RETRY_WITH_BACKOFF: 5000,
      RETRY_WITH_DIFFERENT_PARAMS: 2000,
      ESCALATE_TO_MANUAL: 300000, // 5 minutes
      FAIL_FAST: 0,
      CIRCUIT_BREAKER: 60000, // 1 minute
      FALLBACK_SERVICE: 3000,
      USER_INTERVENTION_REQUIRED: undefined,
      SYSTEM_MAINTENANCE_REQUIRED: undefined
    }

    return timeMap[strategy]
  }

  private extractErrorMetadata(error: any, context: ErrorContext): any {
    const metadata: any = {
      errorType: error.constructor.name,
      hasStack: !!error.stack,
      endpoint: context.endpoint,
      method: context.method,
      timestamp: context.timestamp
    }

    // Extract additional metadata from AppError
    if (error instanceof AppError) {
      metadata.appErrorCode = error.code
      metadata.appErrorStatus = error.statusCode
    }

    // Extract metadata from specific error types
    if (error.code) metadata.systemErrorCode = error.code
    if (error.errno) metadata.errno = error.errno
    if (error.syscall) metadata.syscall = error.syscall
    if (error.hostname) metadata.hostname = error.hostname
    if (error.port) metadata.port = error.port

    return metadata
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private getFallbackAnalysis(error: any): ErrorAnalysis {
    return {
      category: 'UNKNOWN',
      severity: 'MEDIUM',
      isRetryable: true,
      recoveryStrategy: 'RETRY_WITH_BACKOFF',
      userMessage: 'An unexpected error occurred. Please try again.',
      technicalMessage: this.extractErrorMessage(error),
      errorCode: `UNKNOWN_${Date.now()}`,
      httpStatus: 500,
      metadata: {},
      recommendations: ['Try again', 'Contact support if problem persists'],
      relatedErrors: []
    }
  }

  /**
   * Cleanup old error statistics
   */
  private startErrorStatsCleanup(): void {
    setInterval(() => {
      const cutoff = Date.now() - this.ERROR_RATE_WINDOW
      
      for (const [key, stats] of this.errorStats.entries()) {
        if (stats.lastOccurrence < cutoff) {
          this.errorStats.delete(key)
        }
      }

      // Clean up circuit breakers
      for (const [key, state] of this.circuitBreakers.entries()) {
        if (state.isOpen && Date.now() > state.resetTimeout) {
          state.isOpen = false
          state.failureCount = 0
          state.halfOpenAllowed = false
        }
      }
    }, 60000) // Every minute
  }

  /**
   * Get error statistics
   */
  public getErrorStats(): {
    totalErrors: number
    errorsByCategory: Record<string, number>
    circuitBreakers: { [key: string]: CircuitBreakerState }
  } {
    const errorsByCategory: Record<string, number> = {}
    let totalErrors = 0

    for (const [key, stats] of this.errorStats.entries()) {
      const [, category] = key.split(':')
      errorsByCategory[category] = (errorsByCategory[category] || 0) + stats.count
      totalErrors += stats.count
    }

    return {
      totalErrors,
      errorsByCategory,
      circuitBreakers: Object.fromEntries(this.circuitBreakers)
    }
  }

  /**
   * Health check
   */
  public healthCheck(): { healthy: boolean, stats: any } {
    const stats = this.getErrorStats()
    const criticalErrors = stats.errorsByCategory['CRITICAL'] || 0
    const openCircuitBreakers = Object.values(stats.circuitBreakers).filter(cb => cb.isOpen).length

    return {
      healthy: criticalErrors < 10 && openCircuitBreakers < 3,
      stats: {
        ...stats,
        openCircuitBreakers,
        criticalErrors
      }
    }
  }
}

// Export singleton instance
export const enhancedErrorHandler = EnhancedErrorHandler.getInstance()