import { Request, Response, NextFunction } from 'express'
import { z, ZodSchema, ZodError } from 'zod'
import { AppError } from '@/middleware/errorHandler'

// Validation target types
type ValidationTarget = 'body' | 'query' | 'params' | 'headers'

// Validation middleware factory
export const validateRequest = (
  schema: ZodSchema,
  target: ValidationTarget = 'body'
) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const dataToValidate = req[target]
      const validatedData = schema.parse(dataToValidate)
      
      // Replace the original data with validated data
      req[target] = validatedData
      
      next()
    } catch (error) {
      if (error instanceof ZodError) {
        const errorMessage = error.issues
          .map(issue => `${issue.path.join('.')}: ${issue.message}`)
          .join(', ')
        
        next(new AppError(`Validation error in ${target}: ${errorMessage}`, 400, 'VALIDATION_ERROR'))
      } else {
        next(error)
      }
    }
  }
}

// Common validation schemas
export const commonSchemas = {
  // Pagination
  pagination: z.object({
    page: z.string().optional().transform(val => val ? parseInt(val, 10) : 1),
    limit: z.string().optional().transform(val => val ? parseInt(val, 10) : 10),
    sortBy: z.string().optional(),
    sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
  }),

  // UUID parameter
  uuidParam: z.object({
    id: z.string().uuid('Invalid UUID format'),
  }),

  // Solana address
  solanaAddress: z.string().regex(
    /^[1-9A-HJ-NP-Za-km-z]{32,44}$/,
    'Invalid Solana address format'
  ),

  // Email
  email: z.string().email('Invalid email format'),

  // Password
  password: z.string()
    .min(8, 'Password must be at least 8 characters long')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),

  // Amount (for trading)
  amount: z.string().regex(/^\d+(\.\d+)?$/, 'Invalid amount format').transform(val => parseFloat(val)),

  // Slippage (in basis points)
  slippage: z.number().min(1).max(10000, 'Slippage must be between 1 and 10000 basis points'),

  // Priority fee (in lamports)
  priorityFee: z.number().min(0).max(1000000, 'Priority fee must be between 0 and 1,000,000 lamports'),
}

// Auth validation schemas
export const authSchemas = {
  register: z.object({
    email: commonSchemas.email,
    password: commonSchemas.password,
    confirmPassword: z.string(),
    firstName: z.string().min(1, 'First name is required').max(50, 'First name too long'),
    lastName: z.string().min(1, 'Last name is required').max(50, 'Last name too long'),
    acceptTerms: z.boolean().refine(val => val === true, 'You must accept the terms and conditions'),
  }).refine(data => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  }),

  login: z.object({
    email: commonSchemas.email,
    password: z.string().min(1, 'Password is required'),
    rememberMe: z.boolean().optional(),
  }),

  forgotPassword: z.object({
    email: commonSchemas.email,
  }),

  resetPassword: z.object({
    token: z.string().min(1, 'Reset token is required'),
    password: commonSchemas.password,
    confirmPassword: z.string(),
  }).refine(data => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  }),

  changePassword: z.object({
    currentPassword: z.string().min(1, 'Current password is required'),
    newPassword: commonSchemas.password,
    confirmPassword: z.string(),
  }).refine(data => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  }),
}

// Trading validation schemas
export const tradingSchemas = {
  swapQuote: z.object({
    inputMint: commonSchemas.solanaAddress,
    outputMint: commonSchemas.solanaAddress,
    amount: commonSchemas.amount,
    slippageBps: commonSchemas.slippage.optional(),
  }),

  executeSwap: z.object({
    inputMint: commonSchemas.solanaAddress,
    outputMint: commonSchemas.solanaAddress,
    amount: commonSchemas.amount,
    slippageBps: commonSchemas.slippage.optional(),
    priorityFee: commonSchemas.priorityFee.optional(),
    quoteResponse: z.object({
      inputMint: z.string(),
      inAmount: z.string(),
      outputMint: z.string(),
      outAmount: z.string(),
      otherAmountThreshold: z.string(),
      swapMode: z.string(),
      slippageBps: z.number(),
      platformFee: z.any().optional(),
      priceImpactPct: z.string(),
      routePlan: z.array(z.any()),
    }),
  }),

  createPosition: z.object({
    tokenAddress: commonSchemas.solanaAddress,
    amount: commonSchemas.amount,
    entryPrice: commonSchemas.amount,
    positionType: z.enum(['LONG', 'SHORT']),
    leverage: z.number().min(1).max(10).optional(),
    stopLoss: commonSchemas.amount.optional(),
    takeProfit: commonSchemas.amount.optional(),
  }),

  updatePosition: z.object({
    stopLoss: commonSchemas.amount.optional(),
    takeProfit: commonSchemas.amount.optional(),
    amount: commonSchemas.amount.optional(),
  }),
}

// Portfolio validation schemas
export const portfolioSchemas = {
  getPortfolio: z.object({
    includeHistory: z.string().optional().transform(val => val === 'true'),
    timeframe: z.enum(['1h', '24h', '7d', '30d', '90d', '1y']).optional(),
  }),

  addWatchlist: z.object({
    tokenAddress: commonSchemas.solanaAddress,
    name: z.string().min(1, 'Token name is required').max(100),
    symbol: z.string().min(1, 'Token symbol is required').max(20),
  }),
}

// Alert validation schemas
export const alertSchemas = {
  createAlert: z.object({
    tokenAddress: commonSchemas.solanaAddress,
    alertType: z.enum(['PRICE_ABOVE', 'PRICE_BELOW', 'VOLUME_SPIKE', 'PRICE_CHANGE']),
    threshold: commonSchemas.amount,
    message: z.string().max(500).optional(),
    isActive: z.boolean().optional().default(true),
  }),

  updateAlert: z.object({
    threshold: commonSchemas.amount.optional(),
    message: z.string().max(500).optional(),
    isActive: z.boolean().optional(),
  }),
}

// User validation schemas
export const userSchemas = {
  updateProfile: z.object({
    firstName: z.string().min(1).max(50).optional(),
    lastName: z.string().min(1).max(50).optional(),
    avatar: z.string().url().optional(),
    timezone: z.string().optional(),
    language: z.string().optional(),
  }),

  updateSettings: z.object({
    defaultSlippage: commonSchemas.slippage.optional(),
    defaultPriorityFee: commonSchemas.priorityFee.optional(),
    autoApprove: z.boolean().optional(),
    emailNotifications: z.boolean().optional(),
    pushNotifications: z.boolean().optional(),
    tradingNotifications: z.boolean().optional(),
  }),

  connectWallet: z.object({
    walletAddress: commonSchemas.solanaAddress,
    signature: z.string().min(1, 'Signature is required'),
    message: z.string().min(1, 'Message is required'),
  }),
}

// Validation helpers
export const validatePagination = validateRequest(commonSchemas.pagination, 'query')
export const validateUuidParam = validateRequest(commonSchemas.uuidParam, 'params')

// Combine multiple validations
export const validateMultiple = (...validations: Array<(req: Request, res: Response, next: NextFunction) => void>) => {
  return (req: Request, res: Response, next: NextFunction) => {
    let index = 0
    
    const runNext = (error?: any) => {
      if (error) {
        return next(error)
      }
      
      if (index >= validations.length) {
        return next()
      }
      
      const validation = validations[index++]
      validation(req, res, runNext)
    }
    
    runNext()
  }
}
