import { Request, Response, NextFunction } from 'express'
import { ZodError } from 'zod'
import { Prisma } from '@prisma/client'
import { logger, logError } from '@/utils/logger'
import { config } from '@/config/environment'

// Custom error class
export class AppError extends Error {
  public statusCode: number
  public isOperational: boolean
  public code?: string

  constructor(message: string, statusCode: number = 500, code?: string) {
    super(message)
    this.statusCode = statusCode
    this.isOperational = true
    this.code = code

    Error.captureStackTrace(this, this.constructor)
  }
}

// Error response interface
interface ErrorResponse {
  error: string
  message: string
  statusCode: number
  timestamp: string
  path: string
  code?: string
  details?: any
  stack?: string
}

// Handle Zod validation errors
const handleZodError = (error: ZodError): AppError => {
  const message = error.issues
    .map(issue => `${issue.path.join('.')}: ${issue.message}`)
    .join(', ')
  
  return new AppError(`Validation error: ${message}`, 400, 'VALIDATION_ERROR')
}

// Handle Prisma errors
const handlePrismaError = (error: Prisma.PrismaClientKnownRequestError): AppError => {
  switch (error.code) {
    case 'P2002':
      return new AppError(
        `Duplicate field value: ${error.meta?.target}`,
        409,
        'DUPLICATE_FIELD'
      )
    case 'P2014':
      return new AppError(
        'The change you are trying to make would violate the required relation',
        400,
        'RELATION_VIOLATION'
      )
    case 'P2003':
      return new AppError(
        'Foreign key constraint failed',
        400,
        'FOREIGN_KEY_CONSTRAINT'
      )
    case 'P2025':
      return new AppError(
        'Record not found',
        404,
        'RECORD_NOT_FOUND'
      )
    default:
      return new AppError(
        'Database operation failed',
        500,
        'DATABASE_ERROR'
      )
  }
}

// Handle JWT errors
const handleJWTError = (): AppError => {
  return new AppError('Invalid token. Please log in again!', 401, 'INVALID_TOKEN')
}

const handleJWTExpiredError = (): AppError => {
  return new AppError('Your token has expired! Please log in again.', 401, 'TOKEN_EXPIRED')
}

// Send error response in development
const sendErrorDev = (err: AppError, req: Request, res: Response): void => {
  const errorResponse: ErrorResponse = {
    error: err.name,
    message: err.message,
    statusCode: err.statusCode,
    timestamp: new Date().toISOString(),
    path: req.originalUrl,
    code: err.code,
    stack: err.stack,
  }

  res.status(err.statusCode).json(errorResponse)
}

// Send error response in production
const sendErrorProd = (err: AppError, req: Request, res: Response): void => {
  // Operational, trusted error: send message to client
  if (err.isOperational) {
    const errorResponse: ErrorResponse = {
      error: 'Error',
      message: err.message,
      statusCode: err.statusCode,
      timestamp: new Date().toISOString(),
      path: req.originalUrl,
      code: err.code,
    }

    res.status(err.statusCode).json(errorResponse)
  } else {
    // Programming or other unknown error: don't leak error details
    logError('Unknown error occurred', err, {
      url: req.originalUrl,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    })

    const errorResponse: ErrorResponse = {
      error: 'Internal Server Error',
      message: 'Something went wrong!',
      statusCode: 500,
      timestamp: new Date().toISOString(),
      path: req.originalUrl,
    }

    res.status(500).json(errorResponse)
  }
}

// Main error handling middleware
export const errorHandler = (
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let error = { ...err } as AppError
  error.message = err.message

  // Set default values
  if (!error.statusCode) {
    error.statusCode = 500
  }
  if (!error.isOperational) {
    error.isOperational = false
  }

  // Handle specific error types
  if (err instanceof ZodError) {
    error = handleZodError(err)
  } else if (err instanceof Prisma.PrismaClientKnownRequestError) {
    error = handlePrismaError(err)
  } else if (err.name === 'JsonWebTokenError') {
    error = handleJWTError()
  } else if (err.name === 'TokenExpiredError') {
    error = handleJWTExpiredError()
  } else if (err.name === 'CastError') {
    error = new AppError('Invalid ID format', 400, 'INVALID_ID')
  }

  // Log error
  logError('Error handled by error middleware', error, {
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    body: req.body,
    params: req.params,
    query: req.query,
  })

  // Send error response
  if (config.nodeEnv === 'development') {
    sendErrorDev(error, req, res)
  } else {
    sendErrorProd(error, req, res)
  }
}

// Async error wrapper
export const catchAsync = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next)
  }
}

// 404 handler
export const notFound = (req: Request, res: Response, next: NextFunction): void => {
  const error = new AppError(`Not found - ${req.originalUrl}`, 404, 'NOT_FOUND')
  next(error)
}

// Unhandled promise rejection handler
process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
  logError('Unhandled Promise Rejection', new Error(reason), { promise })
  
  // Close server gracefully
  process.exit(1)
})

// Uncaught exception handler
process.on('uncaughtException', (error: Error) => {
  logError('Uncaught Exception', error)
  
  // Close server gracefully
  process.exit(1)
})
