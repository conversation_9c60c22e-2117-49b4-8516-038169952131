import { Router } from 'express'
import { WalletService } from '@/services/walletService'
import { catchAsync, AppError } from '@/middleware/errorHandler'
import { logger } from '@/utils/logger'

const router = Router()

/**
 * GET /api/wallet/balance
 * Get wallet balance including SOL and all tokens above minimum value
 */
router.get('/balance',
  catchAsync(async (req, res) => {
    const { minValue } = req.query
    const minValueUSD = minValue ? parseFloat(minValue as string) : 0.50

    try {
      const balance = await WalletService.getWalletBalance(minValueUSD)
      
      res.json({
        success: true,
        data: balance
      })
    } catch (error) {
      logger.error('Failed to get wallet balance:', error)
      throw new AppError('Failed to fetch wallet balance', 500, 'WALLET_BALANCE_FAILED')
    }
  })
)

/**
 * GET /api/wallet/balance/sol
 * Get SOL balance only
 */
router.get('/balance/sol',
  catchAsync(async (req, res) => {
    try {
      const solBalance = await WalletService.getSOLBalance()
      
      res.json({
        success: true,
        data: {
          balance: solBalance,
          symbol: 'SOL',
          address: 'So11111111111111111111111111111111111111112'
        }
      })
    } catch (error) {
      logger.error('Failed to get SOL balance:', error)
      throw new AppError('Failed to fetch SOL balance', 500, 'SOL_BALANCE_FAILED')
    }
  })
)

/**
 * GET /api/wallet/balance/token/:address
 * Get specific token balance
 */
router.get('/balance/token/:address',
  catchAsync(async (req, res) => {
    const { address } = req.params
    
    if (!WalletService.isValidAddress(address)) {
      throw new AppError('Invalid token address', 400, 'INVALID_ADDRESS')
    }

    try {
      const balance = await WalletService.getTokenBalance(address)
      
      res.json({
        success: true,
        data: {
          address,
          balance
        }
      })
    } catch (error) {
      logger.error('Failed to get token balance:', error)
      throw new AppError('Failed to fetch token balance', 500, 'TOKEN_BALANCE_FAILED')
    }
  })
)

/**
 * GET /api/wallet/tokens
 * Get all token balances above minimum value
 */
router.get('/tokens',
  catchAsync(async (req, res) => {
    const { minValue } = req.query
    const minValueUSD = minValue ? parseFloat(minValue as string) : 0.50

    try {
      const tokens = await WalletService.getTokenBalances(minValueUSD)
      
      res.json({
        success: true,
        data: tokens
      })
    } catch (error) {
      logger.error('Failed to get token balances:', error)
      throw new AppError('Failed to fetch token balances', 500, 'TOKEN_BALANCES_FAILED')
    }
  })
)

/**
 * GET /api/wallet/history
 * Get wallet transaction history
 */
router.get('/history',
  catchAsync(async (req, res) => {
    const { limit } = req.query
    const txLimit = limit ? parseInt(limit as string) : 50

    if (txLimit > 100) {
      throw new AppError('Limit cannot exceed 100', 400, 'LIMIT_TOO_LARGE')
    }

    try {
      const history = await WalletService.getTransactionHistory(txLimit)
      
      res.json({
        success: true,
        data: history
      })
    } catch (error) {
      logger.error('Failed to get transaction history:', error)
      throw new AppError('Failed to fetch transaction history', 500, 'HISTORY_FAILED')
    }
  })
)

/**
 * GET /api/wallet/validate/:address
 * Validate a wallet/token address
 */
router.get('/validate/:address',
  catchAsync(async (req, res) => {
    const { address } = req.params
    
    const isValid = WalletService.isValidAddress(address)
    
    res.json({
      success: true,
      data: {
        address,
        valid: isValid
      }
    })
  })
)

/**
 * GET /api/wallet/health
 * Wallet service health check
 */
router.get('/health',
  catchAsync(async (req, res) => {
    try {
      const healthy = await WalletService.healthCheck()
      
      res.json({
        success: true,
        data: {
          healthy,
          timestamp: Date.now()
        }
      })
    } catch (error) {
      logger.error('Wallet health check failed:', error)
      throw new AppError('Wallet service unhealthy', 503, 'WALLET_UNHEALTHY')
    }
  })
)

export default router