import { Router } from 'express'
import { DatabaseService } from '@/services/database'
import { NotificationService } from '@/services/notificationService'
import { PriceService } from '@/services/priceService'
import { catchAsync, AppError } from '@/middleware/errorHandler'
import { validateRequest } from '@/middleware/validation'
import { logAlert } from '@/utils/logger'
import {
  AlertQuerySchema,
  AlertConfigSchema,
  PaginationSchema
} from '@memetrader-pro/shared'
import { AlertType, AlertPriority } from '@memetrader-pro/shared'

const router = Router()

/**
 * GET /api/alerts
 * Get user alerts with filtering and pagination
 */
router.get('/',
  validateRequest({ query: AlertQuerySchema }),
  catchAsync(async (req, res) => {
    const userId = req.user!.id
    const { 
      limit = 20, 
      offset = 0, 
      read, 
      priority, 
      type 
    } = req.query

    logAlert('Alerts requested', userId, { limit, offset, read, priority, type })

    const { alerts, total } = await NotificationService.getUserAlerts(userId, {
      limit,
      offset,
      unreadOnly: read === false,
      priority,
      type
    })

    res.json({
      success: true,
      data: {
        alerts,
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total
        },
        summary: {
          unreadCount: await DatabaseService.client.alert.count({
            where: { userId, read: false }
          }),
          criticalCount: await DatabaseService.client.alert.count({
            where: { userId, priority: AlertPriority.CRITICAL, read: false }
          })
        }
      }
    })
  })
)

/**
 * GET /api/alerts/:id
 * Get specific alert details
 */
router.get('/:id',
  catchAsync(async (req, res) => {
    const { id } = req.params
    const userId = req.user!.id

    const alert = await DatabaseService.client.alert.findFirst({
      where: { id, userId }
    })

    if (!alert) {
      throw new AppError('Alert not found', 404, 'ALERT_NOT_FOUND')
    }

    res.json({
      success: true,
      data: alert
    })
  })
)

/**
 * PUT /api/alerts/:id/read
 * Mark alert as read
 */
router.put('/:id/read',
  catchAsync(async (req, res) => {
    const { id } = req.params
    const userId = req.user!.id

    await NotificationService.markAlertAsRead(id, userId)

    res.json({
      success: true,
      message: 'Alert marked as read'
    })
  })
)

/**
 * PUT /api/alerts/mark-all-read
 * Mark all alerts as read
 */
router.put('/mark-all-read',
  catchAsync(async (req, res) => {
    const userId = req.user!.id

    await DatabaseService.client.alert.updateMany({
      where: { userId, read: false },
      data: { read: true }
    })

    logAlert('All alerts marked as read', userId)

    res.json({
      success: true,
      message: 'All alerts marked as read'
    })
  })
)

/**
 * DELETE /api/alerts/:id
 * Delete alert
 */
router.delete('/:id',
  catchAsync(async (req, res) => {
    const { id } = req.params
    const userId = req.user!.id

    const alert = await DatabaseService.client.alert.findFirst({
      where: { id, userId }
    })

    if (!alert) {
      throw new AppError('Alert not found', 404, 'ALERT_NOT_FOUND')
    }

    await DatabaseService.client.alert.delete({
      where: { id }
    })

    logAlert('Alert deleted', userId, { alertId: id })

    res.json({
      success: true,
      message: 'Alert deleted successfully'
    })
  })
)

/**
 * DELETE /api/alerts/bulk
 * Bulk delete alerts
 */
router.delete('/bulk',
  catchAsync(async (req, res) => {
    const userId = req.user!.id
    const { alertIds, filter } = req.body

    let where: any = { userId }

    if (alertIds && Array.isArray(alertIds)) {
      where.id = { in: alertIds }
    } else if (filter) {
      // Apply filters for bulk operations
      if (filter.read !== undefined) where.read = filter.read
      if (filter.priority) where.priority = filter.priority
      if (filter.type) where.type = filter.type
    }

    const deleteResult = await DatabaseService.client.alert.deleteMany({
      where
    })

    logAlert('Bulk alert deletion', userId, { 
      deletedCount: deleteResult.count,
      filter: filter || { alertIds: alertIds?.length }
    })

    res.json({
      success: true,
      message: `${deleteResult.count} alerts deleted successfully`
    })
  })
)

/**
 * POST /api/alerts/price
 * Create price alert
 */
router.post('/price',
  catchAsync(async (req, res) => {
    const userId = req.user!.id
    const { 
      tokenAddress,
      type, // 'ABOVE' | 'BELOW' | 'CHANGE_PERCENT'
      targetValue,
      message
    } = req.body

    if (!tokenAddress || !type || targetValue === undefined) {
      throw new AppError('Missing required parameters', 400, 'MISSING_PARAMETERS')
    }

    logAlert('Creating price alert', userId, { tokenAddress, type, targetValue })

    const alertId = await PriceService.createPriceAlert(
      userId,
      tokenAddress,
      type,
      targetValue
    )

    res.json({
      success: true,
      data: { alertId },
      message: 'Price alert created successfully'
    })
  })
)

/**
 * GET /api/alerts/price
 * Get user's price alerts
 */
router.get('/price/list',
  catchAsync(async (req, res) => {
    const userId = req.user!.id

    const priceAlerts = await DatabaseService.client.alert.findMany({
      where: {
        userId,
        type: AlertType.PRICE_ALERT,
        dismissed: false
      },
      orderBy: { timestamp: 'desc' }
    })

    res.json({
      success: true,
      data: priceAlerts
    })
  })
)

/**
 * DELETE /api/alerts/price/:id
 * Delete price alert
 */
router.delete('/price/:id',
  catchAsync(async (req, res) => {
    const { id } = req.params
    const userId = req.user!.id

    const alert = await DatabaseService.client.alert.findFirst({
      where: { 
        id, 
        userId, 
        type: AlertType.PRICE_ALERT 
      }
    })

    if (!alert) {
      throw new AppError('Price alert not found', 404, 'ALERT_NOT_FOUND')
    }

    await DatabaseService.client.alert.update({
      where: { id },
      data: { dismissed: true }
    })

    logAlert('Price alert dismissed', userId, { alertId: id })

    res.json({
      success: true,
      message: 'Price alert dismissed'
    })
  })
)

/**
 * GET /api/alerts/config
 * Get user alert configuration
 */
router.get('/config',
  catchAsync(async (req, res) => {
    const userId = req.user!.id

    const userPrefs = await DatabaseService.client.userPreferences.findUnique({
      where: { userId }
    })

    const config = {
      enabled: userPrefs?.alertsEnabled ?? true,
      channels: {
        email: userPrefs?.emailAlerts ?? false,
        desktop: userPrefs?.desktopAlerts ?? true,
        sound: userPrefs?.soundAlerts ?? true
      },
      quietHours: {
        enabled: userPrefs?.quietHoursEnabled ?? false,
        start: userPrefs?.quietHoursStart || '22:00',
        end: userPrefs?.quietHoursEnd || '08:00',
        highPriorityOverride: true
      },
      filters: {
        minPriority: AlertPriority.LOW,
        categories: Object.values(AlertType),
        tokens: []
      }
    }

    res.json({
      success: true,
      data: config
    })
  })
)

/**
 * PUT /api/alerts/config
 * Update user alert configuration
 */
router.put('/config',
  validateRequest({ body: AlertConfigSchema }),
  catchAsync(async (req, res) => {
    const userId = req.user!.id
    const config = req.body

    logAlert('Updating alert configuration', userId, config)

    // Update user preferences
    await DatabaseService.client.userPreferences.upsert({
      where: { userId },
      update: {
        alertsEnabled: config.enabled,
        emailAlerts: config.channels.find(c => c.type === 'email')?.enabled ?? false,
        desktopAlerts: config.channels.find(c => c.type === 'desktop')?.enabled ?? true,
        soundAlerts: config.channels.find(c => c.type === 'sound')?.enabled ?? true,
        quietHoursEnabled: config.quietHours.enabled,
        quietHoursStart: config.quietHours.start,
        quietHoursEnd: config.quietHours.end
      },
      create: {
        userId,
        alertsEnabled: config.enabled,
        emailAlerts: config.channels.find(c => c.type === 'email')?.enabled ?? false,
        desktopAlerts: config.channels.find(c => c.type === 'desktop')?.enabled ?? true,
        soundAlerts: config.channels.find(c => c.type === 'sound')?.enabled ?? true,
        quietHoursEnabled: config.quietHours.enabled,
        quietHoursStart: config.quietHours.start,
        quietHoursEnd: config.quietHours.end
      }
    })

    res.json({
      success: true,
      message: 'Alert configuration updated successfully'
    })
  })
)

/**
 * GET /api/alerts/statistics
 * Get alert statistics and analytics
 */
router.get('/statistics',
  catchAsync(async (req, res) => {
    const userId = req.user!.id
    const { period = '7d' } = req.query

    // Calculate date range
    let startDate: Date
    switch (period) {
      case '1d':
        startDate = new Date(Date.now() - 24 * 60 * 60 * 1000)
        break
      case '7d':
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        break
      default:
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    }

    // Get alert counts by type and priority
    const [
      totalAlerts,
      unreadAlerts,
      alertsByType,
      alertsByPriority,
      recentAlerts
    ] = await Promise.all([
      DatabaseService.client.alert.count({
        where: { userId, timestamp: { gte: startDate } }
      }),
      DatabaseService.client.alert.count({
        where: { userId, read: false }
      }),
      DatabaseService.client.alert.groupBy({
        by: ['type'],
        where: { userId, timestamp: { gte: startDate } },
        _count: { type: true }
      }),
      DatabaseService.client.alert.groupBy({
        by: ['priority'],
        where: { userId, timestamp: { gte: startDate } },
        _count: { priority: true }
      }),
      DatabaseService.client.alert.findMany({
        where: { userId },
        orderBy: { timestamp: 'desc' },
        take: 10,
        select: {
          id: true,
          type: true,
          priority: true,
          title: true,
          timestamp: true,
          read: true
        }
      })
    ])

    // Format statistics
    const typeBreakdown = alertsByType.reduce((acc, item) => {
      acc[item.type] = item._count.type
      return acc
    }, {} as Record<string, number>)

    const priorityBreakdown = alertsByPriority.reduce((acc, item) => {
      acc[item.priority] = item._count.priority
      return acc
    }, {} as Record<string, number>)

    res.json({
      success: true,
      data: {
        summary: {
          total: totalAlerts,
          unread: unreadAlerts,
          readRate: totalAlerts > 0 ? ((totalAlerts - unreadAlerts) / totalAlerts) * 100 : 0
        },
        breakdown: {
          byType: typeBreakdown,
          byPriority: priorityBreakdown
        },
        recent: recentAlerts,
        period
      }
    })
  })
)

/**
 * POST /api/alerts/test
 * Send test notification (for testing purposes)
 */
router.post('/test',
  catchAsync(async (req, res) => {
    const userId = req.user!.id
    const { type = 'SYSTEM_ALERT', priority = 'MEDIUM', message = 'Test notification' } = req.body

    logAlert('Sending test notification', userId, { type, priority })

    const alertId = await NotificationService.sendNotification(
      userId,
      type,
      priority,
      'Test Notification',
      message,
      { test: true, timestamp: Date.now() }
    )

    res.json({
      success: true,
      data: { alertId },
      message: 'Test notification sent successfully'
    })
  })
)

/**
 * GET /api/alerts/export
 * Export alerts to CSV
 */
router.get('/export',
  catchAsync(async (req, res) => {
    const userId = req.user!.id
    const { format = 'csv', startDate, endDate } = req.query

    const where: any = { userId }
    if (startDate) where.timestamp = { gte: new Date(startDate as string) }
    if (endDate) where.timestamp = { ...where.timestamp, lte: new Date(endDate as string) }

    const alerts = await DatabaseService.client.alert.findMany({
      where,
      orderBy: { timestamp: 'desc' }
    })

    if (format === 'csv') {
      // Generate CSV
      const csvHeaders = ['ID', 'Type', 'Priority', 'Title', 'Message', 'Read', 'Timestamp']
      const csvRows = alerts.map(alert => [
        alert.id,
        alert.type,
        alert.priority,
        `"${alert.title.replace(/"/g, '""')}"`,
        `"${alert.message.replace(/"/g, '""')}"`,
        alert.read ? 'Yes' : 'No',
        alert.timestamp.toISOString()
      ])

      const csvContent = [csvHeaders, ...csvRows]
        .map(row => row.join(','))
        .join('\n')

      res.setHeader('Content-Type', 'text/csv')
      res.setHeader('Content-Disposition', `attachment; filename="alerts-${Date.now()}.csv"`)
      res.send(csvContent)
    } else {
      res.json({
        success: true,
        data: alerts
      })
    }
  })
)

/**
 * GET /api/alerts/health
 * Alerts service health check
 */
router.get('/health',
  catchAsync(async (req, res) => {
    const notificationHealth = await NotificationService.healthCheck()
    const priceHealth = await PriceService.healthCheck()

    const overallHealth = notificationHealth && priceHealth

    res.json({
      success: true,
      data: {
        overall: overallHealth,
        services: {
          notifications: notificationHealth,
          price: priceHealth
        }
      }
    })
  })
)

export default router