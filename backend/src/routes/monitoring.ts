/**
 * Monitoring and Health Check Routes
 * 
 * Provides comprehensive monitoring for all managed services including
 * rate limiting, API health, and performance metrics.
 */

import { Router, Request, Response } from 'express'
import { logger } from '@/utils/logger'
import { managedJupiterService } from '@/services/managedJupiterService'
import { managedHeliusService } from '@/services/managedHeliusService'
import { rateLimitManager } from '@/services/rateLimitManager'
import { heliusLaserStreamService } from '@/services/heliusLaserStreamService'
import { realtimeMonitoringService } from '@/services/realtimeMonitoringService'
import { TradingService } from '@/services/tradingService'

const router = Router()

/**
 * Comprehensive system health check
 */
router.get('/health', async (req: Request, res: Response) => {
  try {
    const healthChecks = await Promise.allSettled([
      managedJupiterService.healthCheck(),
      managedHeliusService.healthCheck(),
      rateLimitManager.healthCheck(),
      heliusLaserStreamService.healthCheck(),
      realtimeMonitoringService.healthCheck(),
      TradingService.healthCheck()
    ])

    const health = {
      timestamp: new Date().toISOString(),
      overall: 'healthy',
      services: {
        jupiter: healthChecks[0].status === 'fulfilled' ? healthChecks[0].value : false,
        helius: healthChecks[1].status === 'fulfilled' ? healthChecks[1].value : false,
        rateLimiter: healthChecks[2].status === 'fulfilled' ? healthChecks[2].value : false,
        laserStream: healthChecks[3].status === 'fulfilled' ? healthChecks[3].value : false,
        realtimeMonitoring: healthChecks[4].status === 'fulfilled' ? healthChecks[4].value : false,
        trading: healthChecks[5].status === 'fulfilled' ? healthChecks[5].value : false
      },
      errors: healthChecks
        .filter(check => check.status === 'rejected')
        .map(check => (check as PromiseRejectedResult).reason?.message || 'Unknown error')
    }

    // Determine overall health
    const healthyServices = Object.values(health.services).filter(status => status === true).length
    const totalServices = Object.keys(health.services).length
    
    if (healthyServices === totalServices) {
      health.overall = 'healthy'
    } else if (healthyServices >= totalServices * 0.8) {
      health.overall = 'degraded'
    } else {
      health.overall = 'unhealthy'
    }

    const statusCode = health.overall === 'healthy' ? 200 : 
                      health.overall === 'degraded' ? 207 : 503

    res.status(statusCode).json(health)

  } catch (error) {
    logger.error('Health check failed:', error)
    res.status(500).json({
      timestamp: new Date().toISOString(),
      overall: 'error',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

/**
 * Rate limiting statistics
 */
router.get('/rate-limits', async (req: Request, res: Response) => {
  try {
    const stats = rateLimitManager.getStats()
    
    res.json({
      timestamp: new Date().toISOString(),
      rateLimits: stats
    })

  } catch (error) {
    logger.error('Rate limit stats failed:', error)
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

/**
 * Jupiter service statistics
 */
router.get('/jupiter/stats', async (req: Request, res: Response) => {
  try {
    const stats = managedJupiterService.getStats()
    
    res.json({
      timestamp: new Date().toISOString(),
      jupiter: stats
    })

  } catch (error) {
    logger.error('Jupiter stats failed:', error)
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

/**
 * Helius service statistics
 */
router.get('/helius/stats', async (req: Request, res: Response) => {
  try {
    const stats = managedHeliusService.getStats()
    
    res.json({
      timestamp: new Date().toISOString(),
      helius: stats
    })

  } catch (error) {
    logger.error('Helius stats failed:', error)
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

/**
 * LaserStream service status
 */
router.get('/laserstream/status', async (req: Request, res: Response) => {
  try {
    const status = heliusLaserStreamService.getStatus()
    
    res.json({
      timestamp: new Date().toISOString(),
      laserStream: status
    })

  } catch (error) {
    logger.error('LaserStream status failed:', error)
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

/**
 * Real-time monitoring service status
 */
router.get('/realtime/status', async (req: Request, res: Response) => {
  try {
    const status = realtimeMonitoringService.getServiceStatus()
    
    res.json({
      timestamp: new Date().toISOString(),
      realtimeMonitoring: status
    })

  } catch (error) {
    logger.error('Real-time monitoring status failed:', error)
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

/**
 * Trading service comprehensive statistics
 */
router.get('/trading/stats', async (req: Request, res: Response) => {
  try {
    const stats = TradingService.getStats()
    
    res.json({
      timestamp: new Date().toISOString(),
      trading: stats
    })

  } catch (error) {
    logger.error('Trading stats failed:', error)
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

/**
 * Clear all service caches
 */
router.post('/cache/clear', async (req: Request, res: Response) => {
  try {
    // Clear Jupiter cache
    managedJupiterService.clearCache()
    
    // Clear Helius cache
    managedHeliusService.clearCache()
    
    logger.info('All service caches cleared')
    
    res.json({
      timestamp: new Date().toISOString(),
      message: 'All service caches cleared successfully'
    })

  } catch (error) {
    logger.error('Cache clear failed:', error)
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

/**
 * System performance metrics
 */
router.get('/metrics', async (req: Request, res: Response) => {
  try {
    const metrics = {
      timestamp: new Date().toISOString(),
      system: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        cpu: process.cpuUsage()
      },
      services: {
        jupiter: managedJupiterService.getStats(),
        helius: managedHeliusService.getStats(),
        rateLimiter: rateLimitManager.getStats(),
        laserStream: heliusLaserStreamService.getStatus(),
        realtimeMonitoring: realtimeMonitoringService.getServiceStatus()
      }
    }
    
    res.json(metrics)

  } catch (error) {
    logger.error('Metrics collection failed:', error)
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

/**
 * Test specific service endpoints
 */
router.post('/test/:service', async (req: Request, res: Response) => {
  try {
    const { service } = req.params
    const { params } = req.body
    
    let result: any
    
    switch (service) {
      case 'jupiter-quote':
        if (!params.inputMint || !params.outputMint || !params.amount) {
          return res.status(400).json({ error: 'Missing required parameters: inputMint, outputMint, amount' })
        }
        result = await managedJupiterService.getQuote(params, 'low')
        break
        
      case 'helius-priority-fee':
        if (!params.accountKeys) {
          return res.status(400).json({ error: 'Missing required parameter: accountKeys' })
        }
        result = await managedHeliusService.getPriorityFeeEstimate(params, 'low')
        break
        
      case 'helius-rpc':
        if (!params.method) {
          return res.status(400).json({ error: 'Missing required parameter: method' })
        }
        result = await managedHeliusService.rpcCall(params.method, params.params || [], 'low', 5000)
        break
        
      default:
        return res.status(400).json({ error: `Unknown service: ${service}` })
    }
    
    res.json({
      timestamp: new Date().toISOString(),
      service,
      result
    })

  } catch (error) {
    logger.error(`Service test failed for ${req.params.service}:`, error)
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

export default router