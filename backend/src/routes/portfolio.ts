import { Router } from 'express'
import { DatabaseService } from '@/services/database'
import { RiskService } from '@/services/riskService'
import { PriceService } from '@/services/priceService'
import { catchAsync, AppError } from '@/middleware/errorHandler'
import { validateRequest } from '@/middleware/validation'
import { logPortfolio } from '@/utils/logger'
import {
  PositionQuerySchema,
  RebalanceParamsSchema,
  PaginationSchema
} from '@memetrader-pro/shared'
import { PositionStatus } from '@memetrader-pro/shared'

const router = Router()

/**
 * GET /api/portfolio/overview
 * Get portfolio overview and metrics
 */
router.get('/overview',
  catchAsync(async (req, res) => {
    const userId = req.user!.id

    logPortfolio('Portfolio overview requested', userId)

    // Get portfolio risk metrics
    const riskMetrics = await RiskService.calculatePortfolioRisk(userId)

    // Get portfolio record
    let portfolio = await DatabaseService.client.portfolio.findFirst({
      where: { userId },
      orderBy: { lastUpdated: 'desc' }
    })

    if (!portfolio) {
      // Create initial portfolio record
      portfolio = await DatabaseService.client.portfolio.create({
        data: {
          userId,
          totalValue: 0,
          totalValueUsd: 0,
          totalPnl: 0,
          totalPnlUsd: 0,
          totalPnlPercent: 0,
          riskScore: 0,
          exposurePercent: 0,
          maxDrawdown: 0,
          winRate: 0,
          totalTrades: 0,
          profitFactor: 0,
          sharpeRatio: 0
        }
      })
    }

    // Update portfolio with current risk metrics
    await DatabaseService.client.portfolio.update({
      where: { id: portfolio.id },
      data: {
        totalValue: riskMetrics.totalValue,
        totalValueUsd: riskMetrics.totalValueUsd,
        riskScore: riskMetrics.riskScore,
        exposurePercent: riskMetrics.exposurePercentage,
        maxDrawdown: riskMetrics.maxDrawdown,
        lastUpdated: new Date()
      }
    })

    res.json({
      success: true,
      data: {
        overview: {
          totalValue: riskMetrics.totalValue,
          totalValueUsd: riskMetrics.totalValueUsd,
          totalPnl: parseFloat(portfolio.totalPnl.toString()),
          totalPnlUsd: parseFloat(portfolio.totalPnlUsd.toString()),
          totalPnlPercent: portfolio.totalPnlPercent,
          riskScore: riskMetrics.riskScore,
          exposurePercent: riskMetrics.exposurePercentage,
          maxDrawdown: riskMetrics.maxDrawdown
        },
        performance: {
          winRate: portfolio.winRate,
          totalTrades: portfolio.totalTrades,
          profitFactor: portfolio.profitFactor,
          sharpeRatio: portfolio.sharpeRatio
        },
        riskBreakdown: {
          concentration: riskMetrics.concentration,
          correlationRisk: riskMetrics.correlationRisk,
          liquidityRisk: riskMetrics.liquidityRisk,
          volatilityScore: riskMetrics.volatilityScore
        }
      }
    })
  })
)

/**
 * GET /api/portfolio/positions
 * Get user's positions with filtering and pagination
 */
router.get('/positions',
  validateRequest({ query: PositionQuerySchema }),
  catchAsync(async (req, res) => {
    const userId = req.user!.id
    const { status, limit = 20, offset = 0 } = req.query

    logPortfolio('Positions requested', userId, { status, limit, offset })

    const where: any = { userId }
    if (status) {
      where.status = status
    }

    const [positions, total] = await Promise.all([
      DatabaseService.client.position.findMany({
        where,
        include: {
          strategy: {
            select: {
              id: true,
              type: true,
              customName: true,
              executionState: true,
              stopLoss: true,
              profitTargets: true
            }
          }
        },
        orderBy: { entryTimestamp: 'desc' },
        skip: offset,
        take: limit
      }),
      DatabaseService.client.position.count({ where })
    ])

    // Enrich positions with current market data
    const enrichedPositions = await Promise.all(
      positions.map(async (position) => {
        const marketData = await PriceService.getTokenMarketData(position.tokenAddress)
        
        return {
          ...position,
          currentPrice: marketData?.price || parseFloat(position.currentPrice.toString()),
          marketData: marketData ? {
            price: marketData.price,
            change24h: marketData.change24h,
            volume24h: marketData.volume24h,
            marketCap: marketData.marketCap,
            liquidity: marketData.liquidity,
            holders: marketData.holders
          } : null,
          // Calculate real-time P&L
          pnl: marketData ? 
            (marketData.price - parseFloat(position.entryPrice.toString())) * parseFloat(position.quantity.toString()) :
            parseFloat(position.pnl.toString()),
          pnlPercent: marketData ?
            ((marketData.price / parseFloat(position.entryPrice.toString())) - 1) * 100 :
            position.pnlPercent,
          age: Date.now() - position.entryTimestamp.getTime()
        }
      })
    )

    res.json({
      success: true,
      data: {
        positions: enrichedPositions,
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total
        }
      }
    })
  })
)

/**
 * GET /api/portfolio/positions/:id
 * Get detailed position information
 */
router.get('/positions/:id',
  catchAsync(async (req, res) => {
    const { id } = req.params
    const userId = req.user!.id

    const position = await DatabaseService.client.position.findFirst({
      where: { id, userId },
      include: {
        strategy: true,
        transactions: {
          orderBy: { timestamp: 'desc' },
          take: 10
        }
      }
    })

    if (!position) {
      throw new AppError('Position not found', 404, 'POSITION_NOT_FOUND')
    }

    // Get risk analysis for this position
    const riskAnalysis = await RiskService.analyzePositionRisk(id)

    // Get current market data
    const marketData = await PriceService.getTokenMarketData(position.tokenAddress)

    res.json({
      success: true,
      data: {
        position: {
          ...position,
          marketData,
          riskAnalysis
        }
      }
    })
  })
)

/**
 * PUT /api/portfolio/positions/:id/close
 * Close a position (emergency exit)
 */
router.put('/positions/:id/close',
  catchAsync(async (req, res) => {
    const { id } = req.params
    const userId = req.user!.id
    const { percentage = 100, reason } = req.body

    logPortfolio('Position close requested', userId, { positionId: id, percentage, reason })

    const position = await DatabaseService.client.position.findFirst({
      where: { id, userId, status: PositionStatus.ACTIVE }
    })

    if (!position) {
      throw new AppError('Active position not found', 404, 'POSITION_NOT_FOUND')
    }

    // Calculate sell quantity
    const sellQuantity = (parseFloat(position.quantity.toString()) * percentage) / 100

    // Execute emergency sell via TradingService
    const { TradingService } = await import('@/services/tradingService')
    const result = await TradingService.executeTrade(
      {
        tokenIn: position.tokenAddress,
        tokenOut: 'So11111111111111111111111111111111111111112', // SOL
        amount: sellQuantity,
        slippage: 5, // 5% emergency slippage
        preset: 'DEFAULT' as const
      },
      req.user!.walletAddress,
      userId
    )

    if (result.success) {
      // Update position
      const newQuantity = parseFloat(position.quantity.toString()) - sellQuantity
      const newStatus = newQuantity <= 0 ? PositionStatus.CLOSED : PositionStatus.PARTIAL

      await DatabaseService.client.position.update({
        where: { id },
        data: {
          quantity: newQuantity,
          status: newStatus,
          exitTimestamp: newQuantity <= 0 ? new Date() : null,
          updatedAt: new Date()
        }
      })

      // Deactivate strategy if position is fully closed
      if (newQuantity <= 0 && position.strategyId) {
        const { StrategyService } = await import('@/services/strategyService')
        await StrategyService.deactivateStrategy(position.strategyId)
      }

      res.json({
        success: true,
        data: {
          transactionHash: result.transactionHash,
          amountSold: sellQuantity,
          newQuantity,
          status: newStatus
        }
      })
    } else {
      throw new AppError(`Failed to close position: ${result.error}`, 500, 'POSITION_CLOSE_FAILED')
    }
  })
)

/**
 * GET /api/portfolio/allocation
 * Get portfolio allocation breakdown
 */
router.get('/allocation',
  catchAsync(async (req, res) => {
    const userId = req.user!.id

    const positions = await DatabaseService.client.position.findMany({
      where: { userId, status: PositionStatus.ACTIVE }
    })

    // Calculate allocation by token
    const tokenAllocations: Record<string, {
      tokenAddress: string
      tokenSymbol: string
      value: number
      percentage: number
      quantity: number
      avgEntryPrice: number
    }> = {}

    let totalValue = 0

    for (const position of positions) {
      const marketData = await PriceService.getTokenMarketData(position.tokenAddress)
      const currentPrice = marketData?.price || parseFloat(position.currentPrice.toString())
      const quantity = parseFloat(position.quantity.toString())
      const value = currentPrice * quantity

      totalValue += value

      if (tokenAllocations[position.tokenAddress]) {
        tokenAllocations[position.tokenAddress].value += value
        tokenAllocations[position.tokenAddress].quantity += quantity
      } else {
        tokenAllocations[position.tokenAddress] = {
          tokenAddress: position.tokenAddress,
          tokenSymbol: position.tokenSymbol,
          value,
          percentage: 0, // Will be calculated below
          quantity,
          avgEntryPrice: parseFloat(position.entryPrice.toString())
        }
      }
    }

    // Calculate percentages
    Object.values(tokenAllocations).forEach(allocation => {
      allocation.percentage = totalValue > 0 ? (allocation.value / totalValue) * 100 : 0
    })

    // Get top performers
    const sortedAllocations = Object.values(tokenAllocations)
      .sort((a, b) => b.value - a.value)

    res.json({
      success: true,
      data: {
        totalValue,
        allocations: sortedAllocations,
        topHoldings: sortedAllocations.slice(0, 5),
        diversificationScore: sortedAllocations.length > 0 ? 
          Math.min(100, (sortedAllocations.length * 20)) : 0
      }
    })
  })
)

/**
 * GET /api/portfolio/performance
 * Get portfolio performance metrics and charts
 */
router.get('/performance',
  catchAsync(async (req, res) => {
    const userId = req.user!.id
    const { period = '7d' } = req.query

    logPortfolio('Performance metrics requested', userId, { period })

    // Get date range based on period
    let startDate: Date
    switch (period) {
      case '1d':
        startDate = new Date(Date.now() - 24 * 60 * 60 * 1000)
        break
      case '7d':
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        break
      case '90d':
        startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)
        break
      default:
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    }

    // Get transactions in the period
    const transactions = await DatabaseService.client.transaction.findMany({
      where: {
        userId,
        timestamp: { gte: startDate }
      },
      orderBy: { timestamp: 'asc' }
    })

    // Calculate daily P&L
    const dailyPnL: Record<string, number> = {}
    const dailyTrades: Record<string, number> = {}

    transactions.forEach(tx => {
      const date = tx.timestamp.toISOString().split('T')[0]
      
      // Simplified P&L calculation
      if (tx.type === 'SELL') {
        const pnl = parseFloat(tx.amountOut.toString()) - parseFloat(tx.amountIn.toString())
        dailyPnL[date] = (dailyPnL[date] || 0) + pnl
      }
      
      dailyTrades[date] = (dailyTrades[date] || 0) + 1
    })

    // Calculate performance metrics
    const totalPnL = Object.values(dailyPnL).reduce((sum, pnl) => sum + pnl, 0)
    const totalTrades = transactions.length
    const winningTrades = transactions.filter(tx => 
      tx.type === 'SELL' && parseFloat(tx.amountOut.toString()) > parseFloat(tx.amountIn.toString())
    ).length
    const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0

    // Create chart data
    const chartData = Object.entries(dailyPnL).map(([date, pnl]) => ({
      date,
      pnl,
      cumulativePnl: Object.entries(dailyPnL)
        .filter(([d]) => d <= date)
        .reduce((sum, [, p]) => sum + p, 0),
      trades: dailyTrades[date] || 0
    }))

    res.json({
      success: true,
      data: {
        summary: {
          totalPnL,
          totalTrades,
          winRate,
          avgTradeSize: totalTrades > 0 ? totalPnL / totalTrades : 0,
          bestDay: Math.max(...Object.values(dailyPnL), 0),
          worstDay: Math.min(...Object.values(dailyPnL), 0)
        },
        chartData,
        period
      }
    })
  })
)

/**
 * POST /api/portfolio/rebalance
 * Rebalance portfolio according to target allocations
 */
router.post('/rebalance',
  validateRequest({ body: RebalanceParamsSchema }),
  catchAsync(async (req, res) => {
    const userId = req.user!.id
    const { targetAllocations, maxSlippage, priorityFee } = req.body

    logPortfolio('Portfolio rebalance requested', userId, { targetAllocations })

    // Get current positions
    const positions = await DatabaseService.client.position.findMany({
      where: { userId, status: PositionStatus.ACTIVE }
    })

    // Calculate current allocations
    let totalValue = 0
    const currentAllocations: Record<string, number> = {}

    for (const position of positions) {
      const marketData = await PriceService.getTokenMarketData(position.tokenAddress)
      const currentPrice = marketData?.price || parseFloat(position.currentPrice.toString())
      const value = currentPrice * parseFloat(position.quantity.toString())
      
      totalValue += value
      currentAllocations[position.tokenAddress] = value
    }

    // Calculate rebalancing trades needed
    const trades: Array<{
      tokenAddress: string
      action: 'buy' | 'sell'
      amount: number
      currentAllocation: number
      targetAllocation: number
    }> = []

    Object.entries(targetAllocations).forEach(([tokenAddress, targetPercent]) => {
      const targetValue = (totalValue * targetPercent) / 100
      const currentValue = currentAllocations[tokenAddress] || 0
      const difference = targetValue - currentValue

      if (Math.abs(difference) > totalValue * 0.01) { // Only rebalance if difference > 1%
        trades.push({
          tokenAddress,
          action: difference > 0 ? 'buy' : 'sell',
          amount: Math.abs(difference),
          currentAllocation: (currentValue / totalValue) * 100,
          targetAllocation: targetPercent
        })
      }
    })

    // For demo purposes, we'll just return the rebalancing plan
    // In production, you would execute these trades
    res.json({
      success: true,
      data: {
        rebalancingPlan: trades,
        totalValue,
        estimatedFees: trades.length * 0.001, // Estimated fees
        message: trades.length > 0 
          ? `${trades.length} trades needed for rebalancing`
          : 'Portfolio is already well-balanced'
      }
    })
  })
)

/**
 * GET /api/portfolio/risk
 * Get detailed risk analysis
 */
router.get('/risk',
  catchAsync(async (req, res) => {
    const userId = req.user!.id

    const riskMetrics = await RiskService.calculatePortfolioRisk(userId)

    // Get risk breakdown by position
    const positions = await DatabaseService.client.position.findMany({
      where: { userId, status: PositionStatus.ACTIVE }
    })

    const positionRisks = await Promise.all(
      positions.map(async (position) => {
        const riskAnalysis = await RiskService.analyzePositionRisk(position.id)
        return {
          positionId: position.id,
          tokenSymbol: position.tokenSymbol,
          riskLevel: riskAnalysis.riskLevel,
          riskScore: riskAnalysis.riskScore,
          recommendations: riskAnalysis.recommendations
        }
      })
    )

    res.json({
      success: true,
      data: {
        overall: riskMetrics,
        positionRisks,
        riskLimits: {
          maxExposure: 90,
          maxConcentration: 25,
          maxDrawdown: 20
        },
        warnings: positionRisks
          .filter(p => p.riskLevel === 'HIGH' || p.riskLevel === 'EXTREME')
          .map(p => `High risk detected in ${p.tokenSymbol}`)
      }
    })
  })
)

/**
 * GET /api/portfolio/health
 * Portfolio service health check
 */
router.get('/health',
  catchAsync(async (req, res) => {
    const riskHealth = await RiskService.healthCheck()
    const priceHealth = await PriceService.healthCheck()

    const overallHealth = riskHealth && priceHealth

    res.json({
      success: true,
      data: {
        overall: overallHealth,
        services: {
          risk: riskHealth,
          price: priceHealth
        }
      }
    })
  })
)

export default router