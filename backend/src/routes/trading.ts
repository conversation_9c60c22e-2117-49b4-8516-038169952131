import { Router } from 'express'
import { TradingService } from '@/services/tradingService'
import { RiskService } from '@/services/riskService'
import { StrategyService } from '@/services/strategyService'
import { heliusWebSocketService } from '@/services/heliusWebSocket'
import { positionMonitorService } from '@/services/positionMonitor'
import { RedisService } from '@/services/redis'
import { catchAsync, AppError } from '@/middleware/errorHandler'
import { validateRequest } from '@/middleware/validation'
import { logTrading } from '@/utils/logger'
import {
  TradeParamsSchema,
  QuoteParamsSchema,
  SimulationParamsSchema,
  ExitStrategySchema,
  CustomStrategySchema,
  TradingPresetSchema
} from '@memetrader-pro/shared'

const router = Router()

/**
 * POST /api/trading/quote
 * Get trading quote from Jupiter with real-time price data
 */
router.post('/quote', 
  validateRequest({ body: QuoteParamsSchema }),
  catchAsync(async (req, res) => {
    const { tokenIn, tokenOut, amount, slippage } = req.body
    const userId = req.user!.id

    logTrading('Quote requested', userId, { tokenIn, tokenOut, amount })

    // Get current market prices
    const [tokenInPrice, tokenOutPrice] = await Promise.all([
      RedisService.getJSON(`price:${tokenIn}`),
      RedisService.getJSON(`price:${tokenOut}`)
    ])

    const quote = await TradingService.getQuote({
      inputMint: tokenIn,
      outputMint: tokenOut,
      amount,
      slippageBps: slippage ? Math.floor(slippage * 100) : undefined,
      userPublicKey: req.user!.walletAddress
    })

    // Enhance quote with real-time market data
    const enhancedQuote = {
      ...quote,
      marketData: {
        tokenInPrice: tokenInPrice?.price,
        tokenOutPrice: tokenOutPrice?.price,
        tokenInChange24h: tokenInPrice?.change24h,
        tokenOutChange24h: tokenOutPrice?.change24h,
        timestamp: Date.now()
      }
    }

    res.json({
      success: true,
      data: enhancedQuote
    })
  })
)

/**
 * POST /api/trading/quote/live
 * Get live trading quote that auto-refreshes
 */
router.post('/quote/live',
  validateRequest({ body: QuoteParamsSchema }),
  catchAsync(async (req, res) => {
    const { tokenIn, tokenOut, amount, slippage } = req.body
    const userId = req.user!.id

    // Subscribe to price updates for both tokens
    await heliusWebSocketService.subscribeToTokenPrices([tokenIn, tokenOut], userId)

    // Get initial quote
    const quote = await TradingService.getQuote({
      inputMint: tokenIn,
      outputMint: tokenOut,
      amount,
      slippageBps: slippage ? Math.floor(slippage * 100) : undefined,
      userPublicKey: req.user!.walletAddress
    })

    // Cache quote parameters for auto-refresh
    await RedisService.setJSON(`live_quote:${userId}`, {
      tokenIn,
      tokenOut,
      amount,
      slippage,
      walletAddress: req.user!.walletAddress,
      timestamp: Date.now()
    }, 300) // 5 minutes

    res.json({
      success: true,
      data: {
        ...quote,
        isLive: true,
        refreshInterval: 15000, // 15 seconds
        subscriptionActive: true
      }
    })
  })
)

/**
 * POST /api/trading/simulate
 * Simulate trade execution
 */
router.post('/simulate',
  validateRequest({ body: SimulationParamsSchema }),
  catchAsync(async (req, res) => {
    const simulationParams = req.body
    const userId = req.user!.id

    logTrading('Trade simulation requested', userId, simulationParams)

    const result = await TradingService.simulateTrade(simulationParams)

    res.json({
      success: true,
      data: result
    })
  })
)

/**
 * POST /api/trading/execute
 * Execute trade with strategy validation
 */
router.post('/execute',
  validateRequest({ body: TradeParamsSchema }),
  catchAsync(async (req, res) => {
    const tradeParams = req.body
    const userId = req.user!.id
    const walletAddress = req.user!.walletAddress

    logTrading('Trade execution requested', userId, tradeParams)

    // 1. Validate risk limits before execution
    const portfolioValue = 10000 // TODO: Get actual portfolio value
    const riskValidation = await RiskService.validateNewPosition(
      userId,
      tradeParams.tokenOut,
      tradeParams.amount,
      portfolioValue
    )

    if (!riskValidation.allowed) {
      throw new AppError(
        `Trade blocked: ${riskValidation.violations.join(', ')}`,
        400,
        'RISK_VIOLATION'
      )
    }

    // 2. Check if exit strategy is required (enforce strategy requirement)
    if (!tradeParams.strategyId) {
      throw new AppError(
        'Exit strategy is required for all trades',
        400,
        'STRATEGY_REQUIRED'
      )
    }

    // 3. Execute the trade
    const result = await TradingService.executeTrade(
      tradeParams,
      walletAddress,
      userId
    )

    if (result.success && tradeParams.strategyId) {
      // Activate strategy for the new position
      try {
        await StrategyService.activateStrategy(tradeParams.strategyId, userId)
      } catch (error) {
        logTrading('Failed to activate strategy after trade', userId, {
          strategyId: tradeParams.strategyId,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    res.json({
      success: true,
      data: result,
      warnings: riskValidation.warnings
    })
  })
)

/**
 * GET /api/trading/presets
 * Get available trading presets
 */
router.get('/presets',
  catchAsync(async (req, res) => {
    const presets = await TradingService.getTradingPresets()

    res.json({
      success: true,
      data: presets
    })
  })
)

/**
 * PUT /api/trading/presets/:id
 * Update trading preset (admin only)
 */
router.put('/presets/:id',
  validateRequest({ body: TradingPresetSchema }),
  catchAsync(async (req, res) => {
    const { id } = req.params
    const presetData = req.body

    // Only allow admin users to modify presets
    if (req.user!.role !== 'admin') {
      throw new AppError('Insufficient permissions', 403, 'INSUFFICIENT_PERMISSIONS')
    }

    await TradingService.updateTradingPreset(id, presetData)

    res.json({
      success: true,
      message: 'Trading preset updated successfully'
    })
  })
)

/**
 * POST /api/trading/strategies
 * Create exit strategy
 */
router.post('/strategies',
  validateRequest({ body: ExitStrategySchema }),
  catchAsync(async (req, res) => {
    const strategyConfig = req.body
    const userId = req.user!.id

    logTrading('Creating exit strategy', userId, { type: strategyConfig.type })

    const strategyId = await StrategyService.createExitStrategy(
      userId,
      strategyConfig.type,
      {
        stopLoss: strategyConfig.stopLoss,
        profitTargets: strategyConfig.profitTargets,
        moonBag: strategyConfig.moonBag,
        customName: strategyConfig.customName
      }
    )

    res.json({
      success: true,
      data: { strategyId }
    })
  })
)

/**
 * GET /api/trading/strategies
 * Get user's exit strategies
 */
router.get('/strategies',
  catchAsync(async (req, res) => {
    const userId = req.user!.id
    const { active } = req.query

    const strategies = await StrategyService.getUserStrategies(userId, {
      activeOnly: active === 'true'
    })

    res.json({
      success: true,
      data: strategies
    })
  })
)

/**
 * POST /api/trading/strategies/custom
 * Create custom strategy template
 */
router.post('/strategies/custom',
  validateRequest({ body: CustomStrategySchema }),
  catchAsync(async (req, res) => {
    const { name, description, config, prdCompliant } = req.body
    const userId = req.user!.id

    logTrading('Creating custom strategy template', userId, { name })

    const strategyId = await StrategyService.createCustomStrategy(
      userId,
      name,
      description,
      config
    )

    res.json({
      success: true,
      data: { strategyId }
    })
  })
)

/**
 * GET /api/trading/strategies/custom
 * Get user's custom strategy templates
 */
router.get('/strategies/custom',
  catchAsync(async (req, res) => {
    const userId = req.user!.id

    const customStrategies = await StrategyService.getCustomStrategies(userId)

    res.json({
      success: true,
      data: customStrategies
    })
  })
)

/**
 * GET /api/trading/strategies/templates
 * Get PRD-compliant and default strategy templates
 */
router.get('/strategies/templates',
  catchAsync(async (req, res) => {
    const prdTemplate = StrategyService.getPRDCompliantTemplate()
    
    const templates = {
      prdCompliant: prdTemplate,
      aggressive: {
        stopLoss: { percentage: 20, trailing: false, emergencySlippage: 15 },
        profitTargets: [
          { percentage: 25, sellPercentage: 25, priority: 1 },
          { percentage: 75, sellPercentage: 50, priority: 2 }
        ],
        moonBag: { percentage: 25, exitTarget: 300, enabled: true },
        locked: false
      },
      conservative: {
        stopLoss: { percentage: 10, trailing: true, emergencySlippage: 5 },
        profitTargets: [
          { percentage: 20, sellPercentage: 30, priority: 1 },
          { percentage: 40, sellPercentage: 30, priority: 2 },
          { percentage: 60, sellPercentage: 40, priority: 3 }
        ],
        moonBag: { percentage: 0, exitTarget: 0, enabled: false },
        locked: false
      }
    }

    res.json({
      success: true,
      data: templates
    })
  })
)

/**
 * PUT /api/trading/strategies/:id/activate
 * Activate strategy for monitoring
 */
router.put('/strategies/:id/activate',
  catchAsync(async (req, res) => {
    const { id: strategyId } = req.params
    const userId = req.user!.id

    await StrategyService.activateStrategy(strategyId, userId)

    res.json({
      success: true,
      message: 'Strategy activated successfully'
    })
  })
)

/**
 * PUT /api/trading/strategies/:id/deactivate
 * Deactivate strategy
 */
router.put('/strategies/:id/deactivate',
  catchAsync(async (req, res) => {
    const { id: strategyId } = req.params
    const userId = req.user!.id

    await StrategyService.deactivateStrategy(strategyId)

    res.json({
      success: true,
      message: 'Strategy deactivated successfully'
    })
  })
)

/**
 * GET /api/trading/strategies/:id/analytics
 * Get strategy performance analytics
 */
router.get('/strategies/:id/analytics',
  catchAsync(async (req, res) => {
    const { id: strategyId } = req.params
    const userId = req.user!.id

    const analytics = await StrategyService.getStrategyAnalytics(strategyId, userId)

    res.json({
      success: true,
      data: analytics
    })
  })
)

/**
 * POST /api/trading/position-size
 * Calculate optimal position size
 */
router.post('/position-size',
  catchAsync(async (req, res) => {
    const { tokenAddress, riskTolerance, portfolioValue, customRiskParams } = req.body
    const userId = req.user!.id

    if (!tokenAddress || !riskTolerance || !portfolioValue) {
      throw new AppError('Missing required parameters', 400, 'MISSING_PARAMETERS')
    }

    const calculation = await RiskService.calculatePositionSize(
      userId,
      tokenAddress,
      riskTolerance,
      portfolioValue,
      customRiskParams
    )

    res.json({
      success: true,
      data: calculation
    })
  })
)

/**
 * POST /api/trading/risk-analysis
 * Analyze risk for a trade
 */
router.post('/risk-analysis',
  catchAsync(async (req, res) => {
    const { tokenAddress, positionSize, portfolioValue } = req.body
    const userId = req.user!.id

    if (!tokenAddress || !positionSize || !portfolioValue) {
      throw new AppError('Missing required parameters', 400, 'MISSING_PARAMETERS')
    }

    const analysis = await RiskService.validateNewPosition(
      userId,
      tokenAddress,
      positionSize,
      portfolioValue
    )

    res.json({
      success: true,
      data: analysis
    })
  })
)

/**
 * GET /api/trading/health
 * Trading service health check
 */
router.get('/health',
  catchAsync(async (req, res) => {
    const [tradingHealth, strategyHealth, riskHealth] = await Promise.all([
      TradingService.healthCheck(),
      StrategyService.healthCheck(),
      RiskService.healthCheck()
    ])

    const overallHealth = tradingHealth && strategyHealth && riskHealth

    res.json({
      success: true,
      data: {
        overall: overallHealth,
        services: {
          trading: tradingHealth,
          strategy: strategyHealth,
          risk: riskHealth
        }
      }
    })
  })
)

export default router