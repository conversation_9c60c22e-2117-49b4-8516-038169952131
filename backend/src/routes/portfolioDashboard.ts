import { Router, Request, Response } from 'express'
import { query, param, validationResult } from 'express-validator'
import { authMiddleware } from '@/middleware/auth'
import { PortfolioService } from '@/services/portfolioService'
import { logger } from '@/utils/logger'
import { AppError } from '@/middleware/errorHandler'

const router = Router()

// Apply authentication to all routes
router.use(authMiddleware)

/**
 * GET /api/portfolio/dashboard - Get comprehensive portfolio dashboard data
 */
router.get('/dashboard', [
  query('forceRefresh').optional().isBoolean().withMessage('Force refresh must be boolean')
], async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      })
    }

    const userId = req.user?.id
    if (!userId) {
      throw new AppError('User not authenticated', 401, 'UNAUTHORIZED')
    }

    const forceRefresh = req.query.forceRefresh === 'true'

    // Get comprehensive portfolio summary
    const portfolioSummary = await PortfolioService.getPortfolioSummary(userId, forceRefresh)

    res.json({
      success: true,
      data: {
        portfolio: portfolioSummary,
        realTimeUpdates: PortfolioService.getConfig().realTimeUpdatesEnabled,
        lastUpdated: portfolioSummary.lastUpdated,
        priceLastUpdated: portfolioSummary.priceLastUpdated
      }
    })

  } catch (error) {
    logger.error('Portfolio dashboard fetch failed:', error)
    
    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        error: {
          code: error.code,
          message: error.message
        }
      })
    }

    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fetch portfolio dashboard'
      }
    })
  }
})

/**
 * GET /api/portfolio/summary - Get portfolio summary (lightweight version)
 */
router.get('/summary', async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id
    if (!userId) {
      throw new AppError('User not authenticated', 401, 'UNAUTHORIZED')
    }

    const portfolioSummary = await PortfolioService.getPortfolioSummary(userId)

    // Return lightweight summary
    const summary = {
      totalValue: portfolioSummary.totalValue,
      totalPnL: portfolioSummary.totalPnL,
      totalPnLPercent: portfolioSummary.totalPnLPercent,
      todaysPnL: portfolioSummary.todaysPnL,
      todaysPnLPercent: portfolioSummary.todaysPnLPercent,
      positionCount: portfolioSummary.positionCount,
      activePositions: portfolioSummary.activePositions,
      riskLevel: portfolioSummary.riskMetrics.portfolioRisk,
      riskScore: portfolioSummary.riskMetrics.riskScore,
      lastUpdated: portfolioSummary.lastUpdated
    }

    res.json({
      success: true,
      data: summary
    })

  } catch (error) {
    logger.error('Portfolio summary fetch failed:', error)
    
    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        error: {
          code: error.code,
          message: error.message
        }
      })
    }

    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fetch portfolio summary'
      }
    })
  }
})

/**
 * GET /api/portfolio/positions - Get all portfolio positions
 */
router.get('/positions', [
  query('status').optional().isIn(['ACTIVE', 'CLOSED', 'PARTIAL']).withMessage('Invalid position status'),
  query('sortBy').optional().isIn(['totalValue', 'totalPnL', 'totalPnLPercent', 'holdingPeriod', 'riskLevel']).withMessage('Invalid sort field'),
  query('sortOrder').optional().isIn(['asc', 'desc']).withMessage('Sort order must be asc or desc'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
], async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      })
    }

    const userId = req.user?.id
    if (!userId) {
      throw new AppError('User not authenticated', 401, 'UNAUTHORIZED')
    }

    const portfolioSummary = await PortfolioService.getPortfolioSummary(userId)
    let positions = portfolioSummary.positions

    // Apply filters
    if (req.query.status) {
      positions = positions.filter(pos => pos.status === req.query.status)
    }

    // Apply sorting
    const sortBy = (req.query.sortBy as string) || 'totalValue'
    const sortOrder = (req.query.sortOrder as string) || 'desc'
    
    positions.sort((a, b) => {
      const aVal = a[sortBy as keyof typeof a] as number
      const bVal = b[sortBy as keyof typeof b] as number
      
      if (sortOrder === 'asc') {
        return aVal - bVal
      } else {
        return bVal - aVal
      }
    })

    // Apply limit
    const limit = parseInt(req.query.limit as string) || positions.length
    positions = positions.slice(0, limit)

    res.json({
      success: true,
      data: {
        positions,
        total: portfolioSummary.positions.length,
        filtered: positions.length
      }
    })

  } catch (error) {
    logger.error('Portfolio positions fetch failed:', error)
    
    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        error: {
          code: error.code,
          message: error.message
        }
      })
    }

    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fetch portfolio positions'
      }
    })
  }
})

/**
 * GET /api/portfolio/positions/:positionId - Get detailed position information
 */
router.get('/positions/:positionId', [
  param('positionId').isUUID().withMessage('Invalid position ID format')
], async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      })
    }

    const userId = req.user?.id
    const positionId = req.params.positionId

    if (!userId) {
      throw new AppError('User not authenticated', 401, 'UNAUTHORIZED')
    }

    const position = await PortfolioService.getPositionDetails(positionId, userId)

    if (!position) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'POSITION_NOT_FOUND',
          message: 'Position not found'
        }
      })
    }

    res.json({
      success: true,
      data: position
    })

  } catch (error) {
    logger.error('Position details fetch failed:', error)
    
    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        error: {
          code: error.code,
          message: error.message
        }
      })
    }

    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fetch position details'
      }
    })
  }
})

/**
 * GET /api/portfolio/analytics - Get portfolio analytics for charts and insights
 */
router.get('/analytics', [
  query('period').optional().isIn(['day', 'week', 'month', 'quarter', 'year', 'all']).withMessage('Invalid period'),
  query('includeComparison').optional().isBoolean().withMessage('Include comparison must be boolean')
], async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      })
    }

    const userId = req.user?.id
    if (!userId) {
      throw new AppError('User not authenticated', 401, 'UNAUTHORIZED')
    }

    const period = (req.query.period as any) || 'month'
    const includeComparison = req.query.includeComparison === 'true'

    const analytics = await PortfolioService.getPortfolioAnalytics(userId, period)

    // Add benchmark comparison if requested
    let benchmarkData = null
    if (includeComparison) {
      // Would implement benchmark comparison (e.g., SPY, BTC, SOL)
      benchmarkData = {
        benchmark: 'SOL',
        period,
        return: 0, // Would calculate
        correlation: 0 // Would calculate
      }
    }

    res.json({
      success: true,
      data: {
        analytics,
        benchmark: benchmarkData,
        period,
        generatedAt: new Date()
      }
    })

  } catch (error) {
    logger.error('Portfolio analytics fetch failed:', error)
    
    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        error: {
          code: error.code,
          message: error.message
        }
      })
    }

    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fetch portfolio analytics'
      }
    })
  }
})

/**
 * GET /api/portfolio/performance - Get detailed performance metrics
 */
router.get('/performance', [
  query('timeframe').optional().isIn(['1D', '7D', '30D', '90D', '1Y', 'ALL']).withMessage('Invalid timeframe')
], async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      })
    }

    const userId = req.user?.id
    if (!userId) {
      throw new AppError('User not authenticated', 401, 'UNAUTHORIZED')
    }

    const portfolioSummary = await PortfolioService.getPortfolioSummary(userId)
    const timeframe = req.query.timeframe || '30D'

    // Get performance analytics
    const period = this.mapTimeframeToPeriod(timeframe as string)
    const analytics = await PortfolioService.getPortfolioAnalytics(userId, period)

    const performanceData = {
      overview: {
        totalReturn: portfolioSummary.performance.totalReturn,
        totalReturnPercent: portfolioSummary.performance.totalReturnPercent,
        todaysReturn: portfolioSummary.todaysPnL,
        todaysReturnPercent: portfolioSummary.todaysPnLPercent,
        winRate: portfolioSummary.performance.winRate,
        profitFactor: portfolioSummary.performance.profitFactor,
        sharpeRatio: portfolioSummary.performance.sharpeRatio,
        maxDrawdown: portfolioSummary.performance.maxDrawdown
      },
      
      risk: {
        portfolioRisk: portfolioSummary.riskMetrics.portfolioRisk,
        riskScore: portfolioSummary.riskMetrics.riskScore,
        volatility: portfolioSummary.performance.volatility,
        beta: portfolioSummary.performance.beta,
        var95: portfolioSummary.riskMetrics.var95,
        concentrationRisk: portfolioSummary.riskMetrics.concentrationRisk
      },
      
      diversification: {
        positionCount: portfolioSummary.positionCount,
        concentration: portfolioSummary.diversification.concentration,
        largestPosition: portfolioSummary.diversification.largestPosition,
        top5Concentration: portfolioSummary.diversification.top5Concentration,
        assetAllocation: portfolioSummary.diversification.assetAllocation
      },
      
      trading: {
        totalTrades: portfolioSummary.performance.totalTrades,
        averageTradeSize: portfolioSummary.performance.averageTradeSize,
        tradingFrequency: portfolioSummary.performance.tradingFrequency,
        avgWin: portfolioSummary.performance.avgWin,
        avgLoss: portfolioSummary.performance.avgLoss
      },
      
      charts: {
        performanceChart: analytics.performanceChart,
        riskMetrics: analytics.riskMetrics,
        topPerformers: analytics.topPerformers,
        topLosers: analytics.topLosers
      },
      
      timeframe,
      lastUpdated: portfolioSummary.lastUpdated
    }

    res.json({
      success: true,
      data: performanceData
    })

  } catch (error) {
    logger.error('Portfolio performance fetch failed:', error)
    
    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        error: {
          code: error.code,
          message: error.message
        }
      })
    }

    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fetch portfolio performance'
      }
    })
  }
})

/**
 * GET /api/portfolio/alerts - Get portfolio alerts and warnings
 */
router.get('/alerts', [
  query('severity').optional().isIn(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).withMessage('Invalid severity level'),
  query('acknowledged').optional().isBoolean().withMessage('Acknowledged must be boolean'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
], async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      })
    }

    const userId = req.user?.id
    if (!userId) {
      throw new AppError('User not authenticated', 401, 'UNAUTHORIZED')
    }

    const portfolioSummary = await PortfolioService.getPortfolioSummary(userId)
    let alerts = portfolioSummary.alerts

    // Apply filters
    if (req.query.severity) {
      alerts = alerts.filter(alert => alert.severity === req.query.severity)
    }

    if (req.query.acknowledged !== undefined) {
      const acknowledged = req.query.acknowledged === 'true'
      alerts = alerts.filter(alert => alert.acknowledged === acknowledged)
    }

    // Apply limit
    const limit = parseInt(req.query.limit as string) || alerts.length
    alerts = alerts.slice(0, limit)

    // Get alert summary
    const alertSummary = {
      total: portfolioSummary.alerts.length,
      unacknowledged: portfolioSummary.alerts.filter(a => !a.acknowledged).length,
      bySeverity: {
        critical: portfolioSummary.alerts.filter(a => a.severity === 'CRITICAL').length,
        high: portfolioSummary.alerts.filter(a => a.severity === 'HIGH').length,
        medium: portfolioSummary.alerts.filter(a => a.severity === 'MEDIUM').length,
        low: portfolioSummary.alerts.filter(a => a.severity === 'LOW').length
      }
    }

    res.json({
      success: true,
      data: {
        alerts,
        summary: alertSummary,
        filtered: alerts.length
      }
    })

  } catch (error) {
    logger.error('Portfolio alerts fetch failed:', error)
    
    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        error: {
          code: error.code,
          message: error.message
        }
      })
    }

    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fetch portfolio alerts'
      }
    })
  }
})

/**
 * POST /api/portfolio/refresh - Force refresh portfolio data
 */
router.post('/refresh', async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id
    if (!userId) {
      throw new AppError('User not authenticated', 401, 'UNAUTHORIZED')
    }

    // Force refresh portfolio data
    const portfolioSummary = await PortfolioService.getPortfolioSummary(userId, true)
    
    // Create new snapshot
    await PortfolioService.createPortfolioSnapshot(userId)

    res.json({
      success: true,
      message: 'Portfolio data refreshed successfully',
      data: {
        totalValue: portfolioSummary.totalValue,
        totalPnL: portfolioSummary.totalPnL,
        totalPnLPercent: portfolioSummary.totalPnLPercent,
        lastUpdated: portfolioSummary.lastUpdated
      }
    })

  } catch (error) {
    logger.error('Portfolio refresh failed:', error)
    
    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        error: {
          code: error.code,
          message: error.message
        }
      })
    }

    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to refresh portfolio data'
      }
    })
  }
})

/**
 * GET /api/portfolio/export - Export portfolio data
 */
router.get('/export', [
  query('format').optional().isIn(['json', 'csv']).withMessage('Format must be json or csv'),
  query('includePositions').optional().isBoolean().withMessage('Include positions must be boolean'),
  query('includeAnalytics').optional().isBoolean().withMessage('Include analytics must be boolean')
], async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      })
    }

    const userId = req.user?.id
    if (!userId) {
      throw new AppError('User not authenticated', 401, 'UNAUTHORIZED')
    }

    const format = (req.query.format as string) || 'json'
    const includePositions = req.query.includePositions !== 'false'
    const includeAnalytics = req.query.includeAnalytics === 'true'

    // Get portfolio data
    const portfolioSummary = await PortfolioService.getPortfolioSummary(userId)
    
    let exportData: any = {
      summary: {
        totalValue: portfolioSummary.totalValue,
        totalPnL: portfolioSummary.totalPnL,
        totalPnLPercent: portfolioSummary.totalPnLPercent,
        positionCount: portfolioSummary.positionCount,
        riskLevel: portfolioSummary.riskMetrics.portfolioRisk,
        lastUpdated: portfolioSummary.lastUpdated
      }
    }

    if (includePositions) {
      exportData.positions = portfolioSummary.positions.map(pos => ({
        tokenSymbol: pos.tokenSymbol,
        tokenName: pos.tokenName,
        quantity: pos.quantity,
        currentPrice: pos.currentPrice,
        totalValue: pos.totalValue,
        totalPnL: pos.totalPnL,
        totalPnLPercent: pos.totalPnLPercent,
        weight: pos.weight,
        riskLevel: pos.riskLevel,
        holdingPeriod: pos.holdingPeriod,
        status: pos.status
      }))
    }

    if (includeAnalytics) {
      exportData.analytics = {
        performance: portfolioSummary.performance,
        diversification: portfolioSummary.diversification,
        riskMetrics: portfolioSummary.riskMetrics
      }
    }

    if (format === 'csv') {
      // Convert to CSV format
      const csvData = this.convertToCSV(exportData)
      const filename = `portfolio_${userId}_${new Date().toISOString().split('T')[0]}.csv`
      
      res.setHeader('Content-Type', 'text/csv')
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`)
      res.send(csvData)
    } else {
      // JSON format
      const filename = `portfolio_${userId}_${new Date().toISOString().split('T')[0]}.json`
      
      res.setHeader('Content-Type', 'application/json')
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`)
      res.json({
        success: true,
        data: exportData,
        exportedAt: new Date(),
        format
      })
    }

  } catch (error) {
    logger.error('Portfolio export failed:', error)
    
    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        error: {
          code: error.code,
          message: error.message
        }
      })
    }

    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to export portfolio data'
      }
    })
  }
})

/**
 * Helper method to map timeframe to period
 */
function mapTimeframeToPeriod(timeframe: string): 'day' | 'week' | 'month' | 'quarter' | 'year' | 'all' {
  switch (timeframe) {
    case '1D': return 'day'
    case '7D': return 'week'
    case '30D': return 'month'
    case '90D': return 'quarter'
    case '1Y': return 'year'
    case 'ALL': return 'all'
    default: return 'month'
  }
}

/**
 * Helper method to convert portfolio data to CSV
 */
function convertToCSV(data: any): string {
  const headers = ['Metric', 'Value']
  const rows = [
    ['Total Value', data.summary.totalValue],
    ['Total P&L', data.summary.totalPnL],
    ['Total P&L %', data.summary.totalPnLPercent],
    ['Position Count', data.summary.positionCount],
    ['Risk Level', data.summary.riskLevel],
    ['Last Updated', data.summary.lastUpdated]
  ]

  if (data.positions) {
    rows.push(['', ''], ['POSITIONS', ''])
    rows.push(['Token', 'Quantity', 'Current Price', 'Total Value', 'P&L', 'P&L %'])
    
    data.positions.forEach((pos: any) => {
      rows.push([
        pos.tokenSymbol,
        pos.quantity,
        pos.currentPrice,
        pos.totalValue,
        pos.totalPnL,
        pos.totalPnLPercent
      ])
    })
  }

  return [
    headers.join(','),
    ...rows.map(row => row.map(cell => `"${cell}"`).join(','))
  ].join('\n')
}

export default router