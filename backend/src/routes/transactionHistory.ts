import { Router } from 'express'
import { TransactionRecordingService } from '@/services/transactionRecordingService'
import { catchAsync, AppError } from '@/middleware/errorHandler'
import { validateRequest } from '@/middleware/validation'
import { logger } from '@/utils/logger'
import { z } from 'zod'

const router = Router()

// Validation schemas
const TransactionQuerySchema = z.object({
  type: z.array(z.string()).optional(),
  status: z.array(z.string()).optional(),
  tokenIn: z.string().optional(),
  tokenOut: z.string().optional(),
  strategyId: z.string().optional(),
  presetUsed: z.array(z.string()).optional(),
  mevProtected: z.boolean().optional(),
  dateFrom: z.string().transform(str => new Date(str)).optional(),
  dateTo: z.string().transform(str => new Date(str)).optional(),
  minAmount: z.number().optional(),
  maxAmount: z.number().optional(),
  minValue: z.number().optional(),
  maxValue: z.number().optional(),
  minSlippage: z.number().optional(),
  maxSlippage: z.number().optional(),
  profitableOnly: z.boolean().optional(),
  limit: z.number().min(1).max(100).default(50),
  offset: z.number().min(0).default(0),
  sortBy: z.enum(['createdAt', 'confirmedAt', 'amountIn', 'amountOut', 'price', 'executionTime']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  searchTerm: z.string().optional(),
  includeArchived: z.boolean().default(false)
})

/**
 * GET /api/transactions
 * Get user's transaction history with advanced filtering
 */
router.get('/',
  catchAsync(async (req, res) => {
    const userId = req.user!.id
    
    // Parse and validate query parameters
    const queryParams = TransactionQuerySchema.parse({
      ...req.query,
      // Convert string arrays from query params
      type: req.query.type ? (Array.isArray(req.query.type) ? req.query.type : [req.query.type]) : undefined,
      status: req.query.status ? (Array.isArray(req.query.status) ? req.query.status : [req.query.status]) : undefined,
      presetUsed: req.query.presetUsed ? (Array.isArray(req.query.presetUsed) ? req.query.presetUsed : [req.query.presetUsed]) : undefined
    })

    const result = await TransactionRecordingService.queryTransactions({
      ...queryParams,
      userId // Always filter by current user
    })

    res.json({
      success: true,
      data: result
    })
  })
)

/**
 * GET /api/transactions/stats
 * Get comprehensive transaction statistics
 */
router.get('/stats',
  catchAsync(async (req, res) => {
    const userId = req.user!.id
    const { dateFrom, dateTo } = req.query

    const stats = await TransactionRecordingService.getTransactionStats(
      userId,
      dateFrom ? new Date(dateFrom as string) : undefined,
      dateTo ? new Date(dateTo as string) : undefined
    )

    res.json({
      success: true,
      data: stats
    })
  })
)

/**
 * GET /api/transactions/:hash
 * Get specific transaction by hash
 */
router.get('/:hash',
  catchAsync(async (req, res) => {
    const { hash } = req.params
    const userId = req.user!.id

    if (!hash.match(/^[A-Za-z0-9]{88}$/)) {
      throw new AppError('Invalid transaction hash format', 400, 'INVALID_HASH')
    }

    const transaction = await TransactionRecordingService.getTransactionByHash(hash)

    if (!transaction) {
      throw new AppError('Transaction not found', 404, 'TRANSACTION_NOT_FOUND')
    }

    // Ensure user can only see their own transactions
    if (transaction.userId !== userId) {
      throw new AppError('Access denied', 403, 'ACCESS_DENIED')
    }

    res.json({
      success: true,
      data: transaction
    })
  })
)

/**
 * GET /api/transactions/health
 * Transaction service health check
 */
router.get('/health',
  catchAsync(async (req, res) => {
    const healthy = await TransactionRecordingService.healthCheck()
    const config = TransactionRecordingService.getConfig()

    res.status(healthy ? 200 : 503).json({
      success: healthy,
      data: {
        healthy,
        config: {
          enabled: config.enabled,
          batchSize: config.batchSize,
          batchInterval: config.batchInterval
        },
        timestamp: Date.now()
      }
    })
  })
)

export default router