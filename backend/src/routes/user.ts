import { Router } from 'express'
import { DatabaseService } from '@/services/database'
import { catchAsync, AppError } from '@/middleware/errorHandler'
import { validateRequest } from '@/middleware/validation'
import { logUser } from '@/utils/logger'
import { z } from 'zod'
import { RiskLevel, PresetType } from '@memetrader-pro/shared'

const router = Router()

// Validation schemas
const updateProfileSchema = z.object({
  email: z.string().email('Invalid email format').optional(),
  walletAddress: z.string().regex(/^[1-9A-HJ-NP-Za-km-z]{32,44}$/, 'Invalid Solana wallet address').optional(),
})

const updatePreferencesSchema = z.object({
  defaultPreset: z.nativeEnum(PresetType).optional(),
  defaultSlippage: z.number().min(0.1).max(50).optional(),
  defaultPriorityFee: z.number().min(0).max(1).optional(),
  riskTolerance: z.nativeEnum(RiskLevel).optional(),
  maxPositionSize: z.number().min(0.1).max(100).optional(),
  alertsEnabled: z.boolean().optional(),
  emailAlerts: z.boolean().optional(),
  desktopAlerts: z.boolean().optional(),
  soundAlerts: z.boolean().optional(),
  quietHoursEnabled: z.boolean().optional(),
  quietHoursStart: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
  quietHoursEnd: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
  theme: z.enum(['light', 'dark']).optional(),
  currency: z.enum(['USD', 'EUR', 'SOL']).optional(),
  timezone: z.string().optional(),
})

/**
 * GET /api/user/profile
 * Get user profile
 */
router.get('/profile',
  catchAsync(async (req, res) => {
    const userId = req.user!.id

    const user = await DatabaseService.client.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        walletAddress: true,
        role: true,
        isActive: true,
        createdAt: true,
        updatedAt: true
      }
    })

    if (!user) {
      throw new AppError('User not found', 404, 'USER_NOT_FOUND')
    }

    res.json({
      success: true,
      data: user
    })
  })
)

/**
 * PUT /api/user/profile
 * Update user profile
 */
router.put('/profile',
  validateRequest({ body: updateProfileSchema }),
  catchAsync(async (req, res) => {
    const userId = req.user!.id
    const updates = req.body

    logUser('Profile update requested', userId, updates)

    // Check if email is being changed and if it's already taken
    if (updates.email) {
      const existingUser = await DatabaseService.client.user.findFirst({
        where: {
          email: updates.email,
          id: { not: userId }
        }
      })

      if (existingUser) {
        throw new AppError('Email already in use', 400, 'EMAIL_IN_USE')
      }
    }

    // Check if wallet address is being changed and if it's already taken
    if (updates.walletAddress) {
      const existingUser = await DatabaseService.client.user.findFirst({
        where: {
          walletAddress: updates.walletAddress,
          id: { not: userId }
        }
      })

      if (existingUser) {
        throw new AppError('Wallet address already in use', 400, 'WALLET_IN_USE')
      }
    }

    // Update user profile
    const updatedUser = await DatabaseService.client.user.update({
      where: { id: userId },
      data: {
        ...updates,
        updatedAt: new Date()
      },
      select: {
        id: true,
        email: true,
        walletAddress: true,
        role: true,
        isActive: true,
        createdAt: true,
        updatedAt: true
      }
    })

    logUser('Profile updated successfully', userId)

    res.json({
      success: true,
      data: updatedUser,
      message: 'Profile updated successfully'
    })
  })
)

/**
 * GET /api/user/preferences
 * Get user preferences
 */
router.get('/preferences',
  catchAsync(async (req, res) => {
    const userId = req.user!.id

    let preferences = await DatabaseService.client.userPreferences.findUnique({
      where: { userId }
    })

    if (!preferences) {
      // Create default preferences if they don't exist
      preferences = await DatabaseService.client.userPreferences.create({
        data: {
          userId,
          defaultPreset: PresetType.DEFAULT,
          defaultSlippage: 1.0,
          defaultPriorityFee: 0.001,
          riskTolerance: RiskLevel.MEDIUM,
          maxPositionSize: 5.0,
          alertsEnabled: true,
          emailAlerts: false,
          desktopAlerts: true,
          soundAlerts: true,
          quietHoursEnabled: false,
          quietHoursStart: '22:00',
          quietHoursEnd: '08:00',
          theme: 'dark',
          currency: 'USD',
          timezone: 'UTC'
        }
      })
    }

    res.json({
      success: true,
      data: preferences
    })
  })
)

/**
 * PUT /api/user/preferences
 * Update user preferences
 */
router.put('/preferences',
  validateRequest({ body: updatePreferencesSchema }),
  catchAsync(async (req, res) => {
    const userId = req.user!.id
    const updates = req.body

    logUser('Preferences update requested', userId, updates)

    const updatedPreferences = await DatabaseService.client.userPreferences.upsert({
      where: { userId },
      update: {
        ...updates,
        updatedAt: new Date()
      },
      create: {
        userId,
        defaultPreset: PresetType.DEFAULT,
        defaultSlippage: 1.0,
        defaultPriorityFee: 0.001,
        riskTolerance: RiskLevel.MEDIUM,
        maxPositionSize: 5.0,
        alertsEnabled: true,
        emailAlerts: false,
        desktopAlerts: true,
        soundAlerts: true,
        quietHoursEnabled: false,
        quietHoursStart: '22:00',
        quietHoursEnd: '08:00',
        theme: 'dark',
        currency: 'USD',
        timezone: 'UTC',
        ...updates
      }
    })

    logUser('Preferences updated successfully', userId)

    res.json({
      success: true,
      data: updatedPreferences,
      message: 'Preferences updated successfully'
    })
  })
)

/**
 * GET /api/user/dashboard
 * Get user dashboard data
 */
router.get('/dashboard',
  catchAsync(async (req, res) => {
    const userId = req.user!.id

    logUser('Dashboard data requested', userId)

    // Get user statistics
    const [
      activePositions,
      totalTransactions,
      totalAlerts,
      unreadAlerts,
      activeStrategies,
      recentTransactions,
      portfolio
    ] = await Promise.all([
      DatabaseService.client.position.count({
        where: { userId, status: 'ACTIVE' }
      }),
      DatabaseService.client.transaction.count({
        where: { userId }
      }),
      DatabaseService.client.alert.count({
        where: { userId }
      }),
      DatabaseService.client.alert.count({
        where: { userId, read: false }
      }),
      DatabaseService.client.exitStrategy.count({
        where: { userId, executionState: 'ACTIVE' }
      }),
      DatabaseService.client.transaction.findMany({
        where: { userId },
        orderBy: { timestamp: 'desc' },
        take: 5,
        select: {
          id: true,
          type: true,
          tokenInSymbol: true,
          tokenOutSymbol: true,
          amountIn: true,
          amountOut: true,
          timestamp: true
        }
      }),
      DatabaseService.client.portfolio.findFirst({
        where: { userId },
        orderBy: { lastUpdated: 'desc' }
      })
    ])

    // Calculate today's activity
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    const todayTransactions = await DatabaseService.client.transaction.count({
      where: {
        userId,
        timestamp: { gte: today }
      }
    })

    const todayAlerts = await DatabaseService.client.alert.count({
      where: {
        userId,
        timestamp: { gte: today }
      }
    })

    const dashboardData = {
      overview: {
        activePositions,
        totalTransactions,
        totalAlerts,
        unreadAlerts,
        activeStrategies
      },
      portfolio: portfolio ? {
        totalValue: parseFloat(portfolio.totalValue.toString()),
        totalValueUsd: parseFloat(portfolio.totalValueUsd.toString()),
        totalPnl: parseFloat(portfolio.totalPnl.toString()),
        totalPnlPercent: portfolio.totalPnlPercent,
        riskScore: portfolio.riskScore,
        exposurePercent: portfolio.exposurePercent,
        winRate: portfolio.winRate
      } : null,
      activity: {
        todayTransactions,
        todayAlerts,
        recentTransactions
      },
      quickStats: {
        profitFactor: portfolio?.profitFactor || 0,
        sharpeRatio: portfolio?.sharpeRatio || 0,
        maxDrawdown: portfolio?.maxDrawdown || 0,
        totalTrades: portfolio?.totalTrades || 0
      }
    }

    res.json({
      success: true,
      data: dashboardData
    })
  })
)

/**
 * GET /api/user/stats
 * Get detailed user statistics
 */
router.get('/stats',
  catchAsync(async (req, res) => {
    const userId = req.user!.id
    const { period = '30d' } = req.query

    // Calculate date range
    let startDate: Date
    switch (period) {
      case '7d':
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        break
      case '90d':
        startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)
        break
      case '1y':
        startDate = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000)
        break
      default:
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    }

    // Get comprehensive statistics
    const [
      transactionStats,
      positionStats,
      strategyStats,
      alertStats
    ] = await Promise.all([
      // Transaction statistics
      DatabaseService.client.transaction.aggregate({
        where: {
          userId,
          timestamp: { gte: startDate }
        },
        _count: { id: true },
        _sum: {
          amountIn: true,
          amountOut: true
        }
      }),
      // Position statistics
      DatabaseService.client.position.groupBy({
        by: ['status'],
        where: {
          userId,
          entryTimestamp: { gte: startDate }
        },
        _count: { status: true }
      }),
      // Strategy statistics
      DatabaseService.client.exitStrategy.groupBy({
        by: ['type'],
        where: {
          userId,
          createdAt: { gte: startDate }
        },
        _count: { type: true }
      }),
      // Alert statistics
      DatabaseService.client.alert.groupBy({
        by: ['type', 'priority'],
        where: {
          userId,
          timestamp: { gte: startDate }
        },
        _count: { type: true }
      })
    ])

    // Calculate win rate
    const closedPositions = await DatabaseService.client.position.findMany({
      where: {
        userId,
        status: 'CLOSED',
        entryTimestamp: { gte: startDate }
      },
      select: {
        pnl: true,
        pnlPercent: true
      }
    })

    const winningPositions = closedPositions.filter(p => parseFloat(p.pnl.toString()) > 0)
    const winRate = closedPositions.length > 0 ? (winningPositions.length / closedPositions.length) * 100 : 0

    const stats = {
      period,
      trading: {
        totalTransactions: transactionStats._count.id,
        totalVolumeIn: parseFloat(transactionStats._sum.amountIn?.toString() || '0'),
        totalVolumeOut: parseFloat(transactionStats._sum.amountOut?.toString() || '0'),
        winRate,
        totalClosedPositions: closedPositions.length,
        winningPositions: winningPositions.length,
        avgPnL: closedPositions.length > 0 
          ? closedPositions.reduce((sum, p) => sum + parseFloat(p.pnl.toString()), 0) / closedPositions.length 
          : 0
      },
      positions: positionStats.reduce((acc, stat) => {
        acc[stat.status.toLowerCase()] = stat._count.status
        return acc
      }, {} as Record<string, number>),
      strategies: strategyStats.reduce((acc, stat) => {
        acc[stat.type.toLowerCase()] = stat._count.type
        return acc
      }, {} as Record<string, number>),
      alerts: {
        total: alertStats.reduce((sum, stat) => sum + stat._count.type, 0),
        byType: alertStats.reduce((acc, stat) => {
          acc[stat.type] = (acc[stat.type] || 0) + stat._count.type
          return acc
        }, {} as Record<string, number>)
      }
    }

    res.json({
      success: true,
      data: stats
    })
  })
)

/**
 * POST /api/user/export
 * Export user data
 */
router.post('/export',
  catchAsync(async (req, res) => {
    const userId = req.user!.id
    const { includeTransactions = true, includePositions = true, includeAlerts = false } = req.body

    logUser('Data export requested', userId, { includeTransactions, includePositions, includeAlerts })

    const exportData: any = {
      user: await DatabaseService.client.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          walletAddress: true,
          createdAt: true,
          preferences: true
        }
      }),
      exportedAt: new Date().toISOString()
    }

    if (includeTransactions) {
      exportData.transactions = await DatabaseService.client.transaction.findMany({
        where: { userId },
        orderBy: { timestamp: 'desc' }
      })
    }

    if (includePositions) {
      exportData.positions = await DatabaseService.client.position.findMany({
        where: { userId },
        include: {
          strategy: {
            select: {
              id: true,
              type: true,
              customName: true
            }
          }
        },
        orderBy: { entryTimestamp: 'desc' }
      })
    }

    if (includeAlerts) {
      exportData.alerts = await DatabaseService.client.alert.findMany({
        where: { userId },
        orderBy: { timestamp: 'desc' }
      })
    }

    res.json({
      success: true,
      data: exportData,
      message: 'Data exported successfully'
    })
  })
)

/**
 * DELETE /api/user/account
 * Delete user account (soft delete)
 */
router.delete('/account',
  catchAsync(async (req, res) => {
    const userId = req.user!.id
    const { confirmDelete } = req.body

    if (!confirmDelete) {
      throw new AppError('Account deletion must be confirmed', 400, 'DELETION_NOT_CONFIRMED')
    }

    logUser('Account deletion requested', userId)

    // Soft delete by deactivating account
    await DatabaseService.client.user.update({
      where: { id: userId },
      data: {
        isActive: false,
        updatedAt: new Date()
      }
    })

    // In production, you might want to:
    // 1. Close all active positions
    // 2. Cancel all pending orders
    // 3. Deactivate all strategies
    // 4. Clear sensitive data after retention period

    logUser('Account deactivated', userId)

    res.json({
      success: true,
      message: 'Account has been deactivated successfully'
    })
  })
)

/**
 * GET /api/user/activity
 * Get user activity log
 */
router.get('/activity',
  catchAsync(async (req, res) => {
    const userId = req.user!.id
    const { limit = 50, offset = 0, type } = req.query

    // Get recent transactions as activity
    const where: any = { userId }
    if (type) {
      where.type = type
    }

    const [transactions, total] = await Promise.all([
      DatabaseService.client.transaction.findMany({
        where,
        orderBy: { timestamp: 'desc' },
        skip: Number(offset),
        take: Number(limit),
        select: {
          id: true,
          type: true,
          tokenInSymbol: true,
          tokenOutSymbol: true,
          amountIn: true,
          amountOut: true,
          hash: true,
          presetUsed: true,
          timestamp: true
        }
      }),
      DatabaseService.client.transaction.count({ where })
    ])

    res.json({
      success: true,
      data: {
        activities: transactions.map(tx => ({
          id: tx.id,
          type: tx.type,
          description: `${tx.type} ${tx.tokenInSymbol} → ${tx.tokenOutSymbol}`,
          details: {
            amountIn: tx.amountIn,
            amountOut: tx.amountOut,
            hash: tx.hash,
            preset: tx.presetUsed
          },
          timestamp: tx.timestamp
        })),
        pagination: {
          total,
          limit: Number(limit),
          offset: Number(offset),
          hasMore: Number(offset) + Number(limit) < total
        }
      }
    })
  })
)

export default router