import { Router, Request, Response } from 'express'
import { DatabaseService } from '@/services/database'
import { RedisService } from '@/services/redis'
import { WebSocketService } from '@/services/websocket'
import { catchAsync } from '@/middleware/errorHandler'

const router = Router()

// Basic health check
router.get('/', catchAsync(async (req: Request, res: Response) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
  })
}))

// Detailed health check
router.get('/detailed', catchAsync(async (req: Request, res: Response) => {
  const checks = await Promise.allSettled([
    DatabaseService.healthCheck(),
    RedisService.healthCheck(),
  ])

  const dbHealth = checks[0].status === 'fulfilled' ? checks[0].value : false
  const redisHealth = checks[1].status === 'fulfilled' ? checks[1].value : false

  const wsConnections = WebSocketService.getConnectedClients()
  const wsAuthenticatedConnections = WebSocketService.getAuthenticatedClients()

  const overallHealth = dbHealth && redisHealth

  res.status(overallHealth ? 200 : 503).json({
    status: overallHealth ? 'healthy' : 'unhealthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    services: {
      database: {
        status: dbHealth ? 'healthy' : 'unhealthy',
        connected: dbHealth,
      },
      redis: {
        status: redisHealth ? 'healthy' : 'unhealthy',
        connected: redisHealth,
      },
      websocket: {
        status: 'healthy',
        connections: wsConnections,
        authenticatedConnections: wsAuthenticatedConnections,
      },
    },
    system: {
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      platform: process.platform,
      nodeVersion: process.version,
    },
  })
}))

// Readiness probe (for Kubernetes)
router.get('/ready', catchAsync(async (req: Request, res: Response) => {
  const dbHealth = await DatabaseService.healthCheck()
  const redisHealth = await RedisService.healthCheck()

  if (dbHealth && redisHealth) {
    res.status(200).json({
      status: 'ready',
      timestamp: new Date().toISOString(),
    })
  } else {
    res.status(503).json({
      status: 'not ready',
      timestamp: new Date().toISOString(),
      issues: {
        database: !dbHealth,
        redis: !redisHealth,
      },
    })
  }
}))

// Liveness probe (for Kubernetes)
router.get('/live', (req: Request, res: Response) => {
  res.status(200).json({
    status: 'alive',
    timestamp: new Date().toISOString(),
  })
})

export default router
