import { Router, Request, Response } from 'express'
import { DatabaseService } from '@/services/database'
import { RedisService } from '@/services/redis'
import { WebSocketService } from '@/services/websocket'
import { HealthMonitor } from '@/services/healthMonitor'
import { catchAsync } from '@/middleware/errorHandler'

const router = Router()

// Basic health check
router.get('/', catchAsync(async (req: Request, res: Response) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
  })
}))

// Comprehensive health check using HealthMonitor
router.get('/detailed', catchAsync(async (req: Request, res: Response) => {
  const healthSummary = HealthMonitor.getHealthSummary()
  
  // Add WebSocket information
  const wsConnections = WebSocketService.getConnectedClients()
  const wsAuthenticatedConnections = WebSocketService.getAuthenticatedClients()

  const statusCode = healthSummary.status === 'healthy' ? 200 : 
                    healthSummary.status === 'degraded' ? 206 : 503

  res.status(statusCode).json({
    ...healthSummary,
    websocket: {
      status: 'healthy',
      connections: wsConnections,
      authenticatedConnections: wsAuthenticatedConnections,
    },
    system: {
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      platform: process.platform,
      nodeVersion: process.version,
      pid: process.pid
    }
  })
}))

// System health summary (comprehensive monitoring view)
router.get('/system', catchAsync(async (req: Request, res: Response) => {
  const healthSummary = HealthMonitor.getHealthSummary()
  const statusCode = healthSummary.status === 'healthy' ? 200 : 
                    healthSummary.status === 'degraded' ? 206 : 503

  res.status(statusCode).json(healthSummary)
}))

// Individual service health check
router.get('/service/:serviceName', catchAsync(async (req: Request, res: Response) => {
  const { serviceName } = req.params
  const serviceStatus = await HealthMonitor.checkService(serviceName)

  if (!serviceStatus) {
    return res.status(404).json({
      error: 'Service not found',
      availableServices: HealthMonitor.getHealthSummary().services.map(s => s.name)
    })
  }

  const statusCode = serviceStatus.status === 'healthy' ? 200 : 
                    serviceStatus.status === 'degraded' ? 206 : 503

  res.status(statusCode).json(serviceStatus)
}))

// Readiness probe (for Kubernetes) - checks critical services only
router.get('/ready', catchAsync(async (req: Request, res: Response) => {
  const healthSummary = HealthMonitor.getHealthSummary()
  
  // Check critical services for readiness
  const criticalServices = ['database', 'redis', 'rpc_failover']
  const criticalStatuses = healthSummary.services.filter(s => 
    criticalServices.includes(s.name)
  )
  
  const allCriticalHealthy = criticalStatuses.every(s => s.status === 'healthy')
  const anyUnhealthy = criticalStatuses.some(s => s.status === 'unhealthy')

  if (allCriticalHealthy) {
    res.status(200).json({
      status: 'ready',
      timestamp: new Date().toISOString(),
      criticalServices: criticalStatuses.map(s => ({ name: s.name, status: s.status }))
    })
  } else {
    res.status(503).json({
      status: 'not ready',
      timestamp: new Date().toISOString(),
      criticalServices: criticalStatuses.map(s => ({ 
        name: s.name, 
        status: s.status,
        error: s.error 
      })),
      issues: criticalStatuses
        .filter(s => s.status !== 'healthy')
        .reduce((acc, s) => ({ ...acc, [s.name]: s.error || 'unhealthy' }), {})
    })
  }
}))

// Liveness probe (for Kubernetes)
router.get('/live', (req: Request, res: Response) => {
  res.status(200).json({
    status: 'alive',
    timestamp: new Date().toISOString(),
  })
})

export default router
