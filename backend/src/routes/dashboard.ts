import { Router } from 'express'
import { HealthMonitor } from '@/services/healthMonitor'
import { HeliusRPC } from '@/services/heliusRPC'
import { DatabaseService } from '@/services/database'
import { RedisService } from '@/services/redis'
import { WebSocketService } from '@/services/websocket'
import { logger } from '@/utils/logger'
import { catchAsync } from '@/middleware/errorHandler'

const router = Router()

/**
 * GET /api/dashboard/overview
 * Get comprehensive system overview for monitoring dashboard
 */
router.get('/overview', catchAsync(async (req, res) => {
  const healthSummary = HealthMonitor.getHealthSummary()
  const rpcHealth = HeliusRPC.getHealthSummary()
  const rpcEndpoints = HeliusRPC.getEndpointStatus()
  
  // WebSocket statistics
  const wsConnections = WebSocketService.getConnectedClients()
  const wsAuthenticatedConnections = WebSocketService.getAuthenticatedClients()

  // System metrics
  const memoryUsage = process.memoryUsage()
  const cpuUsage = process.cpuUsage()

  const overview = {
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    version: process.env.npm_package_version || '1.0.0',
    
    // Overall system status
    status: {
      overall: healthSummary.status,
      services: healthSummary.summary,
      critical_services: healthSummary.services
        .filter(s => ['database', 'redis', 'rpc_failover'].includes(s.name))
        .map(s => ({ name: s.name, status: s.status, responseTime: s.responseTime }))
    },

    // RPC system health
    rpc: {
      status: rpcHealth.healthyEndpoints > 0 ? 'healthy' : 'unhealthy',
      endpoints: {
        total: rpcHealth.totalEndpoints,
        healthy: rpcHealth.healthyEndpoints,
        unhealthy: rpcHealth.totalEndpoints - rpcHealth.healthyEndpoints
      },
      performance: {
        totalRequests: rpcHealth.totalRequests,
        successfulRequests: rpcHealth.successfulRequests,
        avgResponseTime: Math.round(rpcHealth.avgResponseTime),
        successRate: rpcHealth.totalRequests > 0 ? 
          Math.round((rpcHealth.successfulRequests / rpcHealth.totalRequests) * 100) : 0
      },
      endpoints_detail: rpcEndpoints.map(e => ({
        id: e.id,
        priority: e.priority,
        healthy: e.healthy,
        circuitBreakerOpen: e.circuitBreakerOpen,
        errorCount: e.errorCount,
        successRate: e.totalRequests > 0 ? 
          Math.round((e.successfulRequests / e.totalRequests) * 100) : 0,
        avgResponseTime: Math.round(e.avgResponseTime)
      }))
    },

    // WebSocket connections
    websocket: {
      status: 'active',
      connections: {
        total: wsConnections,
        authenticated: wsAuthenticatedConnections,
        anonymous: wsConnections - wsAuthenticatedConnections
      }
    },

    // System resources
    system: {
      memory: {
        used: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
        total: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
        usage_percent: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100),
        rss: Math.round(memoryUsage.rss / 1024 / 1024) // MB
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system
      },
      platform: process.platform,
      nodeVersion: process.version,
      pid: process.pid
    },

    // Service health details
    services: healthSummary.services.map(service => ({
      name: service.name,
      status: service.status,
      lastCheck: new Date(service.lastCheck).toISOString(),
      responseTime: service.responseTime,
      error: service.error,
      uptime: service.uptime
    }))
  }

  res.json(overview)
}))

/**
 * GET /api/dashboard/metrics
 * Get real-time metrics for monitoring
 */
router.get('/metrics', catchAsync(async (req, res) => {
  const healthSummary = HealthMonitor.getHealthSummary()
  const rpcHealth = HeliusRPC.getHealthSummary()
  
  // Calculate health scores
  const systemHealthScore = Math.round(
    (healthSummary.summary.healthy / healthSummary.summary.total) * 100
  )
  
  const rpcHealthScore = rpcHealth.totalEndpoints > 0 ? 
    Math.round((rpcHealth.healthyEndpoints / rpcHealth.totalEndpoints) * 100) : 0

  // Memory usage metrics
  const memoryUsage = process.memoryUsage()
  const memoryUsagePercent = Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100)

  const metrics = {
    timestamp: Date.now(),
    uptime_seconds: Math.round(process.uptime()),
    
    // Health scores (0-100)
    health_scores: {
      system: systemHealthScore,
      rpc: rpcHealthScore,
      memory: Math.max(0, 100 - memoryUsagePercent), // Invert so higher is better
      overall: Math.round((systemHealthScore + rpcHealthScore) / 2)
    },

    // Service counts
    service_counts: {
      total: healthSummary.summary.total,
      healthy: healthSummary.summary.healthy,
      degraded: healthSummary.summary.degraded,
      unhealthy: healthSummary.summary.unhealthy,
      unknown: healthSummary.summary.unknown
    },

    // RPC metrics
    rpc_metrics: {
      total_requests: rpcHealth.totalRequests,
      successful_requests: rpcHealth.successfulRequests,
      failed_requests: rpcHealth.totalRequests - rpcHealth.successfulRequests,
      success_rate_percent: rpcHealth.totalRequests > 0 ? 
        Math.round((rpcHealth.successfulRequests / rpcHealth.totalRequests) * 100) : 0,
      avg_response_time_ms: Math.round(rpcHealth.avgResponseTime),
      healthy_endpoints: rpcHealth.healthyEndpoints,
      total_endpoints: rpcHealth.totalEndpoints
    },

    // System resource metrics
    system_metrics: {
      memory_used_mb: Math.round(memoryUsage.heapUsed / 1024 / 1024),
      memory_total_mb: Math.round(memoryUsage.heapTotal / 1024 / 1024),
      memory_usage_percent: memoryUsagePercent,
      memory_rss_mb: Math.round(memoryUsage.rss / 1024 / 1024),
      memory_external_mb: Math.round(memoryUsage.external / 1024 / 1024)
    },

    // Connection metrics
    connection_metrics: {
      websocket_connections: WebSocketService.getConnectedClients(),
      websocket_authenticated: WebSocketService.getAuthenticatedClients()
    }
  }

  res.json(metrics)
}))

/**
 * GET /api/dashboard/alerts
 * Get recent health alerts and issues
 */
router.get('/alerts', catchAsync(async (req, res) => {
  try {
    // Get cached health alerts from Redis
    const cachedAlerts = await RedisService.getJSON('health:recent_alerts') || []
    
    // Get current unhealthy services
    const healthSummary = HealthMonitor.getHealthSummary()
    const currentIssues = healthSummary.services
      .filter(s => s.status !== 'healthy')
      .map(s => ({
        service: s.name,
        status: s.status,
        error: s.error,
        lastCheck: s.lastCheck,
        responseTime: s.responseTime,
        type: 'service_health'
      }))

    // Get RPC endpoint issues
    const rpcEndpoints = HeliusRPC.getEndpointStatus()
    const rpcIssues = rpcEndpoints
      .filter(e => !e.healthy || e.circuitBreakerOpen)
      .map(e => ({
        service: `rpc_endpoint_${e.id}`,
        status: e.healthy ? 'degraded' : 'unhealthy',
        error: e.lastError || (e.circuitBreakerOpen ? 'Circuit breaker open' : 'Endpoint unhealthy'),
        lastCheck: e.lastHealthCheck,
        responseTime: e.avgResponseTime,
        type: 'rpc_endpoint',
        details: {
          priority: e.priority,
          errorCount: e.errorCount,
          circuitBreakerOpen: e.circuitBreakerOpen
        }
      }))

    const alerts = {
      timestamp: new Date().toISOString(),
      current_issues: [...currentIssues, ...rpcIssues],
      recent_alerts: cachedAlerts.slice(0, 50), // Last 50 alerts
      summary: {
        total_current_issues: currentIssues.length + rpcIssues.length,
        service_issues: currentIssues.length,
        rpc_issues: rpcIssues.length,
        critical_issues: currentIssues.filter(i => i.status === 'unhealthy').length
      }
    }

    res.json(alerts)

  } catch (error) {
    logger.error('Failed to get dashboard alerts:', error)
    res.status(500).json({
      error: 'Failed to get alerts',
      timestamp: new Date().toISOString()
    })
  }
}))

/**
 * POST /api/dashboard/test-alert
 * Test the health monitoring alert system
 */
router.post('/test-alert', catchAsync(async (req, res) => {
  const { service = 'test_service', severity = 'warning' } = req.body

  try {
    // Trigger a test alert
    const testAlert = {
      service,
      status: 'test',
      error: 'This is a test alert',
      severity,
      timestamp: Date.now(),
      test: true
    }

    // Publish test alert
    await RedisService.publishJSON('health_alert', testAlert)
    
    // Add to recent alerts cache
    const recentAlerts = await RedisService.getJSON('health:recent_alerts') || []
    recentAlerts.unshift(testAlert)
    await RedisService.setJSON('health:recent_alerts', recentAlerts.slice(0, 100), 3600) // Keep 100 alerts for 1 hour

    res.json({
      success: true,
      message: 'Test alert sent',
      alert: testAlert
    })

  } catch (error) {
    logger.error('Failed to send test alert:', error)
    res.status(500).json({
      error: 'Failed to send test alert',
      details: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}))

export default router