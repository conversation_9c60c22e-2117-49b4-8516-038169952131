import { Router } from 'express'
import { ExitStrategyService } from '@/services/exitStrategyService'
import { catchAsync, AppError } from '@/middleware/errorHandler'
import { validateRequest } from '@/middleware/validation'
import { logger } from '@/utils/logger'
import { z } from 'zod'

const router = Router()

// Validation schemas
const CreateExitStrategySchema = z.object({
  positionId: z.string().min(1),
  name: z.string().min(1).max(100),
  type: z.enum(['STOP_LOSS', 'TAKE_PROFIT', 'TRAILING_STOP', 'MOON_BAG', 'LADDER', 'TIME_BASED', 'COMPOSITE']),
  
  // Strategy configurations (optional based on type)
  stopLoss: z.object({
    enabled: z.boolean(),
    type: z.enum(['FIXED_PRICE', 'PERCENTAGE', 'ATR_BASED', 'SUPPORT_LEVEL']),
    triggerPrice: z.number().optional(),
    triggerPercent: z.number().min(0).max(100).optional(),
    atrMultiplier: z.number().optional(),
    supportLevel: z.number().optional(),
    timeDelay: z.number().optional(),
    volumeConfirmation: z.boolean().optional(),
    partialExecution: z.boolean().optional(),
    partialPercent: z.number().min(1).max(100).optional()
  }).optional(),
  
  takeProfits: z.array(z.object({
    id: z.string(),
    enabled: z.boolean(),
    type: z.enum(['FIXED_PRICE', 'PERCENTAGE', 'RISK_REWARD_RATIO', 'RESISTANCE_LEVEL']),
    triggerPrice: z.number().optional(),
    triggerPercent: z.number().min(0).optional(),
    riskRewardRatio: z.number().min(0).optional(),
    resistanceLevel: z.number().optional(),
    sellPercent: z.number().min(1).max(100),
    executed: z.boolean().default(false),
    timeWindow: z.object({
      start: z.number().min(0).max(23),
      end: z.number().min(0).max(23)
    }).optional(),
    volumeThreshold: z.number().optional(),
    priceMovementConfirmation: z.boolean().optional()
  })).optional(),
  
  trailingStop: z.object({
    enabled: z.boolean(),
    type: z.enum(['PERCENTAGE', 'ATR_BASED', 'FIXED_AMOUNT']),
    trailPercent: z.number().min(0.1).max(50).optional(),
    trailAmount: z.number().optional(),
    atrMultiplier: z.number().optional(),
    highWaterMark: z.number(),
    currentStopPrice: z.number(),
    activationPrice: z.number().optional(),
    minProfit: z.number().optional(),
    accelerated: z.boolean().optional()
  }).optional(),
  
  moonBag: z.object({
    enabled: z.boolean(),
    reservePercent: z.number().min(1).max(95),
    triggerConditions: z.object({
      minProfitPercent: z.number().min(10),
      priceMultiple: z.number().optional(),
      timeHolding: z.number().optional(),
      volumeSpike: z.boolean().optional()
    }),
    neverSell: z.boolean(),
    sellRules: z.object({
      priceTargets: z.array(z.number()),
      sellPercentages: z.array(z.number())
    }).optional()
  }).optional(),
  
  ladder: z.object({
    enabled: z.boolean(),
    type: z.enum(['PROFIT_LADDER', 'LOSS_LADDER']),
    steps: z.array(z.object({
      triggerPercent: z.number(),
      sellPercent: z.number().min(1).max(100),
      executed: z.boolean().default(false)
    })),
    resetOnReversal: z.boolean()
  }).optional(),
  
  timeBased: z.object({
    enabled: z.boolean(),
    type: z.enum(['SCHEDULED_EXIT', 'HOLDING_PERIOD', 'MARKET_HOURS']),
    scheduledTime: z.string().transform(str => new Date(str)).optional(),
    holdingPeriod: z.number().optional(),
    marketHours: z.object({
      exitBeforeClose: z.boolean(),
      minutesBeforeClose: z.number(),
      weekendsOnly: z.boolean()
    }).optional()
  }).optional(),
  
  // Execution settings
  executionMode: z.enum(['AUTOMATIC', 'ALERT_ONLY', 'MANUAL_CONFIRM']).default('AUTOMATIC'),
  slippageTolerance: z.number().min(0.1).max(10).default(2.0),
  presetToUse: z.enum(['DEFAULT', 'AGGRESSIVE', 'CONSERVATIVE', 'MEV_PROTECTED']).default('DEFAULT'),
  mevProtection: z.boolean().default(true),
  
  // Risk management
  maxLossPercent: z.number().min(0).max(100).optional(),
  minProfitPercent: z.number().min(0).optional(),
  cooldownPeriod: z.number().optional(),
  maxExecutionsPerDay: z.number().min(1).max(100).optional()
})

const UpdateExitStrategySchema = CreateExitStrategySchema.partial()

/**
 * POST /api/exit-strategies
 * Create a new exit strategy
 */
router.post('/',
  validateRequest({ body: CreateExitStrategySchema }),
  catchAsync(async (req, res) => {
    const userId = req.user!.id
    
    const strategyData = {
      ...req.body,
      userId,
      status: 'ACTIVE' as const
    }

    const strategy = await ExitStrategyService.createExitStrategy(strategyData)

    logger.info('Exit strategy created via API', {
      strategyId: strategy.id,
      userId,
      type: strategy.type
    })

    res.status(201).json({
      success: true,
      data: strategy
    })
  })
)

/**
 * GET /api/exit-strategies
 * Get all user's exit strategies
 */
router.get('/',
  catchAsync(async (req, res) => {
    // For development - return mock data instead of requiring authentication
    if (process.env.NODE_ENV === 'development') {
      res.json({
        success: true,
        data: {
          strategies: [
            {
              id: 'strategy_1',
              userId: 'dev_user',
              positionId: 'pos_1',
              name: 'Conservative',
              type: 'COMPOSITE',
              status: 'ACTIVE',
              executionState: 'MONITORING',
              executionMode: 'AUTOMATIC',
              slippageTolerance: 0.5,
              presetToUse: 'DEFAULT',
              mevProtection: false,
              createdAt: '2025-08-01T10:00:00Z',
              updatedAt: '2025-08-03T15:30:00Z',
              executionCount: 0,
              totalRealized: 0,
              stopLoss: {
                enabled: true,
                type: 'PERCENTAGE',
                triggerPercent: 10
              },
              takeProfits: [
                { id: 'tp1', enabled: true, type: 'PERCENTAGE', triggerPercent: 25, sellPercent: 20, executed: false },
                { id: 'tp2', enabled: true, type: 'PERCENTAGE', triggerPercent: 50, sellPercent: 30, executed: false }
              ]
            },
            {
              id: 'strategy_2',
              userId: 'dev_user',
              positionId: 'pos_2',
              name: 'Aggressive TP',
              type: 'COMPOSITE',
              status: 'PAUSED',
              executionState: 'PAUSED',
              executionMode: 'AUTOMATIC',
              slippageTolerance: 0.5,
              presetToUse: 'AGGRESSIVE',
              mevProtection: true,
              createdAt: '2025-08-02T14:00:00Z',
              updatedAt: '2025-08-03T15:30:00Z',
              executionCount: 2,
              totalRealized: 150.75,
              stopLoss: {
                enabled: true,
                type: 'PERCENTAGE',
                triggerPercent: 15
              },
              takeProfits: [
                { id: 'tp1', enabled: true, type: 'PERCENTAGE', triggerPercent: 50, sellPercent: 15, executed: false },
                { id: 'tp2', enabled: true, type: 'PERCENTAGE', triggerPercent: 100, sellPercent: 25, executed: false }
              ]
            }
          ]
        }
      })
      return
    }

    const userId = req.user!.id
    const { status } = req.query

    const strategies = await ExitStrategyService.getUserStrategies(userId)
    
    // Filter by status if provided
    const filteredStrategies = status ? 
      strategies.filter(s => s.status === status) : 
      strategies

    res.json({
      success: true,
      data: {
        strategies: filteredStrategies,
        total: filteredStrategies.length
      }
    })
  })
)

/**
 * GET /api/exit-strategies/:id
 * Get specific exit strategy details
 */
router.get('/:id',
  catchAsync(async (req, res) => {
    const { id } = req.params
    const userId = req.user!.id

    const strategy = await ExitStrategyService.getStrategy(id, userId)

    if (!strategy) {
      throw new AppError('Exit strategy not found', 404, 'STRATEGY_NOT_FOUND')
    }

    res.json({
      success: true,
      data: strategy
    })
  })
)

/**
 * PUT /api/exit-strategies/:id
 * Update an existing exit strategy
 */
router.put('/:id',
  validateRequest({ body: UpdateExitStrategySchema }),
  catchAsync(async (req, res) => {
    const { id } = req.params
    const userId = req.user!.id

    // Verify ownership
    const existingStrategy = await ExitStrategyService.getStrategy(id, userId)
    if (!existingStrategy) {
      throw new AppError('Exit strategy not found', 404, 'STRATEGY_NOT_FOUND')
    }

    const updatedStrategy = await ExitStrategyService.updateExitStrategy(id, req.body)

    logger.info('Exit strategy updated via API', {
      strategyId: id,
      userId,
      updates: Object.keys(req.body)
    })

    res.json({
      success: true,
      data: updatedStrategy
    })
  })
)

/**
 * DELETE /api/exit-strategies/:id
 * Delete an exit strategy
 */
router.delete('/:id',
  catchAsync(async (req, res) => {
    const { id } = req.params
    const userId = req.user!.id

    await ExitStrategyService.deleteExitStrategy(id, userId)

    logger.info('Exit strategy deleted via API', { strategyId: id, userId })

    res.json({
      success: true,
      message: 'Exit strategy deleted successfully'
    })
  })
)

/**
 * POST /api/exit-strategies/:id/pause
 * Pause an exit strategy
 */
router.post('/:id/pause',
  catchAsync(async (req, res) => {
    // For development - return mock success response
    if (process.env.NODE_ENV === 'development') {
      const { id } = req.params
      res.json({
        success: true,
        data: {
          id,
          status: 'PAUSED',
          executionState: 'PAUSED',
          updatedAt: new Date().toISOString()
        },
        message: 'Exit strategy paused successfully (development mode)'
      })
      return
    }

    const { id } = req.params
    const userId = req.user!.id

    const strategy = await ExitStrategyService.getStrategy(id, userId)
    if (!strategy) {
      throw new AppError('Exit strategy not found', 404, 'STRATEGY_NOT_FOUND')
    }

    const updatedStrategy = await ExitStrategyService.updateExitStrategy(id, {
      status: 'PAUSED'
    })

    logger.info('Exit strategy paused via API', { strategyId: id, userId })

    res.json({
      success: true,
      data: updatedStrategy,
      message: 'Exit strategy paused successfully'
    })
  })
)

/**
 * POST /api/exit-strategies/:id/resume
 * Resume a paused exit strategy
 */
router.post('/:id/resume',
  catchAsync(async (req, res) => {
    // For development - return mock success response
    if (process.env.NODE_ENV === 'development') {
      const { id } = req.params
      res.json({
        success: true,
        data: {
          id,
          status: 'ACTIVE',
          executionState: 'MONITORING',
          updatedAt: new Date().toISOString()
        },
        message: 'Exit strategy resumed successfully (development mode)'
      })
      return
    }

    const { id } = req.params
    const userId = req.user!.id

    const strategy = await ExitStrategyService.getStrategy(id, userId)
    if (!strategy) {
      throw new AppError('Exit strategy not found', 404, 'STRATEGY_NOT_FOUND')
    }

    const updatedStrategy = await ExitStrategyService.updateExitStrategy(id, {
      status: 'ACTIVE'
    })

    logger.info('Exit strategy resumed via API', { strategyId: id, userId })

    res.json({
      success: true,
      data: updatedStrategy,
      message: 'Exit strategy resumed successfully'
    })
  })
)

/**
 * GET /api/exit-strategies/:id/alerts
 * Get alerts for specific strategy
 */
router.get('/:id/alerts',
  catchAsync(async (req, res) => {
    const { id } = req.params
    const userId = req.user!.id
    const { acknowledged } = req.query

    const strategy = await ExitStrategyService.getStrategy(id, userId)
    if (!strategy) {
      throw new AppError('Exit strategy not found', 404, 'STRATEGY_NOT_FOUND')
    }

    let alerts = strategy.executionState.alertHistory
    
    // Filter by acknowledged status if specified
    if (acknowledged !== undefined) {
      const isAcknowledged = acknowledged === 'true'
      alerts = alerts.filter(alert => alert.acknowledged === isAcknowledged)
    }

    res.json({
      success: true,
      data: {
        alerts,
        pending: strategy.executionState.pendingAlerts.length,
        total: alerts.length
      }
    })
  })
)

/**
 * POST /api/exit-strategies/:id/alerts/:alertId/acknowledge
 * Acknowledge a strategy alert
 */
router.post('/:id/alerts/:alertId/acknowledge',
  catchAsync(async (req, res) => {
    const { id, alertId } = req.params
    const userId = req.user!.id

    const strategy = await ExitStrategyService.getStrategy(id, userId)
    if (!strategy) {
      throw new AppError('Exit strategy not found', 404, 'STRATEGY_NOT_FOUND')
    }

    // Find and acknowledge the alert
    const alert = strategy.executionState.alertHistory.find(a => a.id === alertId)
    if (!alert) {
      throw new AppError('Alert not found', 404, 'ALERT_NOT_FOUND')
    }

    alert.acknowledged = true

    // Remove from pending alerts
    const pendingIndex = strategy.executionState.pendingAlerts.findIndex(a => a.id === alertId)
    if (pendingIndex !== -1) {
      strategy.executionState.pendingAlerts.splice(pendingIndex, 1)
    }

    // Update the strategy
    await ExitStrategyService.updateExitStrategy(id, {
      executionState: strategy.executionState
    })

    res.json({
      success: true,
      message: 'Alert acknowledged successfully'
    })
  })
)

/**
 * GET /api/exit-strategies/:id/executions
 * Get execution history for specific strategy
 */
router.get('/:id/executions',
  catchAsync(async (req, res) => {
    const { id } = req.params
    const userId = req.user!.id
    const { limit = 50, offset = 0 } = req.query

    const strategy = await ExitStrategyService.getStrategy(id, userId)
    if (!strategy) {
      throw new AppError('Exit strategy not found', 404, 'STRATEGY_NOT_FOUND')
    }

    const executions = strategy.executionState.executionHistory
      .slice(parseInt(offset as string), parseInt(offset as string) + parseInt(limit as string))

    res.json({
      success: true,
      data: {
        executions,
        total: strategy.executionState.executionHistory.length,
        summary: {
          totalExecutions: strategy.executionCount,
          totalRealized: strategy.totalRealized,
          successRate: strategy.executionState.executionHistory.length > 0 ?
            (strategy.executionState.executionHistory.filter(e => e.success).length / strategy.executionState.executionHistory.length) * 100 : 0
        }
      }
    })
  })
)

/**
 * POST /api/exit-strategies/test
 * Test strategy configuration (paper trading)
 */
router.post('/test',
  validateRequest({ body: CreateExitStrategySchema }),
  catchAsync(async (req, res) => {
    const userId = req.user!.id

    // Create a temporary strategy for testing
    const testStrategy = {
      ...req.body,
      userId,
      status: 'ACTIVE' as const,
      name: `[TEST] ${req.body.name}`
    }

    // Set service to paper trading mode temporarily
    const originalConfig = ExitStrategyService.getConfig()
    ExitStrategyService.updateConfig({ paperTradingMode: true })

    try {
      const strategy = await ExitStrategyService.createExitStrategy(testStrategy)

      // Run a single monitoring cycle for testing
      await ExitStrategyService.executeMonitoringCycle()

      // Clean up test strategy
      await ExitStrategyService.deleteExitStrategy(strategy.id, userId)

      res.json({
        success: true,
        data: {
          message: 'Strategy configuration is valid and tested successfully',
          testResults: {
            configurationValid: true,
            monitoringWorking: true,
            executionReady: true
          }
        }
      })

    } finally {
      // Restore original configuration
      ExitStrategyService.updateConfig({ paperTradingMode: originalConfig.paperTradingMode })
    }
  })
)

/**
 * GET /api/exit-strategies/health
 * Exit strategy service health check
 */
router.get('/health',
  catchAsync(async (req, res) => {
    const healthy = await ExitStrategyService.healthCheck()
    const config = ExitStrategyService.getConfig()

    res.status(healthy ? 200 : 503).json({
      success: healthy,
      data: {
        healthy,
        config: {
          enabled: config.enabled,
          monitoringInterval: config.monitoringInterval,
          maxConcurrentExecutions: config.maxConcurrentExecutions,
          riskManagementEnabled: config.riskManagementEnabled,
          emergencyStopEnabled: config.emergencyStopEnabled,
          paperTradingMode: config.paperTradingMode
        },
        timestamp: Date.now()
      }
    })
  })
)

export default router