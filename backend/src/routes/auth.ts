import { Router } from 'express'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { DatabaseService } from '@/services/database'
import { RedisService } from '@/services/redis'
import { config } from '@/config/environment'
import { catchAsync, AppError } from '@/middleware/errorHandler'
import { validateRequest } from '@/middleware/validation'
import { authMiddleware, optionalAuth } from '@/middleware/auth'
import { logAuth } from '@/utils/logger'
import { z } from 'zod'

const router = Router()

// Validation schemas
const registerSchema = z.object({
  email: z.string().email('Invalid email format'),
  walletAddress: z.string().regex(/^[1-9A-HJ-NP-Za-km-z]{32,44}$/, 'Invalid Solana wallet address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
})

const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required'),
})

const refreshTokenSchema = z.object({
  refreshToken: z.string().min(1, 'Refresh token is required'),
})

const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string().min(8, 'New password must be at least 8 characters'),
})

const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email format'),
})

const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  newPassword: z.string().min(8, 'New password must be at least 8 characters'),
})

/**
 * Generate JWT tokens
 */
const generateTokens = (userId: string, email: string, role: string) => {
  const accessToken = jwt.sign(
    { id: userId, email, role },
    config.jwt.secret,
    { expiresIn: config.jwt.expiresIn }
  )

  const refreshToken = jwt.sign(
    { id: userId, email, role, type: 'refresh' },
    config.jwt.refreshSecret,
    { expiresIn: config.jwt.refreshExpiresIn }
  )

  return { accessToken, refreshToken }
}

/**
 * Hash password
 */
const hashPassword = async (password: string): Promise<string> => {
  return bcrypt.hash(password, config.security.bcryptRounds)
}

/**
 * Verify password
 */
const verifyPassword = async (password: string, hashedPassword: string): Promise<boolean> => {
  return bcrypt.compare(password, hashedPassword)
}

/**
 * POST /api/auth/register
 * Register new user
 */
router.post('/register',
  validateRequest({ body: registerSchema }),
  catchAsync(async (req, res) => {
    const { email, walletAddress, password } = req.body

    logAuth('Registration attempt', undefined, { email, walletAddress })

    // Check if user already exists
    const existingUser = await DatabaseService.client.user.findFirst({
      where: {
        OR: [
          { email },
          { walletAddress }
        ]
      }
    })

    if (existingUser) {
      if (existingUser.email === email) {
        throw new AppError('Email already registered', 400, 'EMAIL_EXISTS')
      }
      if (existingUser.walletAddress === walletAddress) {
        throw new AppError('Wallet address already registered', 400, 'WALLET_EXISTS')
      }
    }

    // Hash password
    const passwordHash = await hashPassword(password)

    // Create user
    const user = await DatabaseService.client.user.create({
      data: {
        email,
        walletAddress,
        passwordHash,
        role: 'user',
        isActive: true
      }
    })

    // Create user preferences
    await DatabaseService.client.userPreferences.create({
      data: {
        userId: user.id,
        defaultPreset: 'DEFAULT',
        defaultSlippage: 1.0,
        defaultPriorityFee: 0.001,
        riskTolerance: 'MEDIUM',
        maxPositionSize: 5.0,
        alertsEnabled: true,
        emailAlerts: false,
        desktopAlerts: true,
        soundAlerts: true,
        theme: 'dark',
        currency: 'USD',
        timezone: 'UTC'
      }
    })

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user.id, user.email, user.role)

    // Store refresh token in Redis
    await RedisService.setJSON(`refresh_token:${user.id}`, {
      token: refreshToken,
      createdAt: new Date(),
      userAgent: req.get('User-Agent'),
      ip: req.ip
    }, 30 * 24 * 60 * 60) // 30 days

    logAuth('User registered successfully', user.id, { email })

    res.status(201).json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          walletAddress: user.walletAddress,
          role: user.role,
          isActive: user.isActive,
          createdAt: user.createdAt
        },
        tokens: {
          accessToken,
          refreshToken
        }
      },
      message: 'User registered successfully'
    })
  })
)

/**
 * POST /api/auth/login
 * User login
 */
router.post('/login',
  validateRequest({ body: loginSchema }),
  catchAsync(async (req, res) => {
    const { email, password } = req.body

    logAuth('Login attempt', undefined, { email })

    // Find user
    const user = await DatabaseService.client.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        walletAddress: true,
        role: true,
        isActive: true,
        passwordHash: true,
        createdAt: true
      }
    })

    if (!user) {
      throw new AppError('Invalid email or password', 401, 'INVALID_CREDENTIALS')
    }

    if (!user.isActive) {
      throw new AppError('Account is deactivated', 401, 'ACCOUNT_DEACTIVATED')
    }

    // Verify password
    const isValidPassword = await verifyPassword(password, user.passwordHash!)
    if (!isValidPassword) {
      throw new AppError('Invalid email or password', 401, 'INVALID_CREDENTIALS')
    }

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user.id, user.email, user.role)

    // Store refresh token in Redis
    await RedisService.setJSON(`refresh_token:${user.id}`, {
      token: refreshToken,
      createdAt: new Date(),
      userAgent: req.get('User-Agent'),
      ip: req.ip
    }, 30 * 24 * 60 * 60) // 30 days

    // Update last login
    await DatabaseService.client.user.update({
      where: { id: user.id },
      data: { updatedAt: new Date() }
    })

    logAuth('User logged in successfully', user.id, { email })

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          walletAddress: user.walletAddress,
          role: user.role,
          isActive: user.isActive,
          createdAt: user.createdAt
        },
        tokens: {
          accessToken,
          refreshToken
        }
      },
      message: 'Login successful'
    })
  })
)

/**
 * POST /api/auth/refresh
 * Refresh access token
 */
router.post('/refresh',
  validateRequest({ body: refreshTokenSchema }),
  catchAsync(async (req, res) => {
    const { refreshToken } = req.body

    try {
      // Verify refresh token
      const decoded = jwt.verify(refreshToken, config.jwt.refreshSecret) as any

      if (decoded.type !== 'refresh') {
        throw new AppError('Invalid token type', 401, 'INVALID_TOKEN')
      }

      // Check if token exists in Redis
      const storedToken = await RedisService.getJSON(`refresh_token:${decoded.id}`)
      if (!storedToken || storedToken.token !== refreshToken) {
        throw new AppError('Invalid refresh token', 401, 'INVALID_REFRESH_TOKEN')
      }

      // Get user
      const user = await DatabaseService.client.user.findUnique({
        where: { id: decoded.id },
        select: {
          id: true,
          email: true,
          walletAddress: true,
          role: true,
          isActive: true
        }
      })

      if (!user || !user.isActive) {
        throw new AppError('User not found or inactive', 401, 'USER_INACTIVE')
      }

      // Generate new tokens
      const { accessToken, refreshToken: newRefreshToken } = generateTokens(
        user.id,
        user.email,
        user.role
      )

      // Update refresh token in Redis
      await RedisService.setJSON(`refresh_token:${user.id}`, {
        token: newRefreshToken,
        createdAt: new Date(),
        userAgent: req.get('User-Agent'),
        ip: req.ip
      }, 30 * 24 * 60 * 60) // 30 days

      logAuth('Token refreshed successfully', user.id)

      res.json({
        success: true,
        data: {
          tokens: {
            accessToken,
            refreshToken: newRefreshToken
          }
        },
        message: 'Token refreshed successfully'
      })

    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        throw new AppError('Invalid refresh token', 401, 'INVALID_REFRESH_TOKEN')
      }
      throw error
    }
  })
)

/**
 * POST /api/auth/logout
 * User logout
 */
router.post('/logout',
  authMiddleware,
  catchAsync(async (req, res) => {
    const userId = req.user!.id

    // Remove refresh token from Redis
    await RedisService.del(`refresh_token:${userId}`)

    logAuth('User logged out', userId)

    res.json({
      success: true,
      message: 'Logged out successfully'
    })
  })
)

/**
 * GET /api/auth/me
 * Get current user profile
 */
router.get('/me',
  authMiddleware,
  catchAsync(async (req, res) => {
    const userId = req.user!.id

    const user = await DatabaseService.client.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        walletAddress: true,
        role: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
        preferences: true
      }
    })

    if (!user) {
      throw new AppError('User not found', 404, 'USER_NOT_FOUND')
    }

    res.json({
      success: true,
      data: user
    })
  })
)

/**
 * PUT /api/auth/change-password
 * Change user password
 */
router.put('/change-password',
  authMiddleware,
  validateRequest({ body: changePasswordSchema }),
  catchAsync(async (req, res) => {
    const { currentPassword, newPassword } = req.body
    const userId = req.user!.id

    logAuth('Password change attempt', userId)

    // Get user with password hash
    const user = await DatabaseService.client.user.findUnique({
      where: { id: userId },
      select: { id: true, passwordHash: true }
    })

    if (!user || !user.passwordHash) {
      throw new AppError('User not found', 404, 'USER_NOT_FOUND')
    }

    // Verify current password
    const isValidPassword = await verifyPassword(currentPassword, user.passwordHash)
    if (!isValidPassword) {
      throw new AppError('Current password is incorrect', 400, 'INVALID_CURRENT_PASSWORD')
    }

    // Hash new password
    const newPasswordHash = await hashPassword(newPassword)

    // Update password
    await DatabaseService.client.user.update({
      where: { id: userId },
      data: {
        passwordHash: newPasswordHash,
        passwordChangedAt: new Date(),
        updatedAt: new Date()
      }
    })

    // Invalidate all refresh tokens
    await RedisService.del(`refresh_token:${userId}`)

    logAuth('Password changed successfully', userId)

    res.json({
      success: true,
      message: 'Password changed successfully. Please log in again.'
    })
  })
)

/**
 * POST /api/auth/forgot-password
 * Initiate password reset
 */
router.post('/forgot-password',
  validateRequest({ body: forgotPasswordSchema }),
  catchAsync(async (req, res) => {
    const { email } = req.body

    logAuth('Password reset requested', undefined, { email })

    const user = await DatabaseService.client.user.findUnique({
      where: { email },
      select: { id: true, email: true, isActive: true }
    })

    if (!user || !user.isActive) {
      // Don't reveal if email exists or not
      res.json({
        success: true,
        message: 'If an account with that email exists, a password reset link has been sent.'
      })
      return
    }

    // Generate reset token
    const resetToken = jwt.sign(
      { id: user.id, email: user.email, type: 'password-reset' },
      config.jwt.secret,
      { expiresIn: '1h' }
    )

    // Store reset token in Redis with 1 hour expiry
    await RedisService.setJSON(`password_reset:${user.id}`, {
      token: resetToken,
      createdAt: new Date(),
      email: user.email
    }, 3600) // 1 hour

    // In production, send email with reset link
    // For now, we'll just log it
    logAuth('Password reset token generated', user.id, { resetToken })

    res.json({
      success: true,
      message: 'If an account with that email exists, a password reset link has been sent.',
      // In development, include the token for testing
      ...(config.nodeEnv === 'development' && { resetToken })
    })
  })
)

/**
 * POST /api/auth/reset-password
 * Reset password with token
 */
router.post('/reset-password',
  validateRequest({ body: resetPasswordSchema }),
  catchAsync(async (req, res) => {
    const { token, newPassword } = req.body

    try {
      // Verify reset token
      const decoded = jwt.verify(token, config.jwt.secret) as any

      if (decoded.type !== 'password-reset') {
        throw new AppError('Invalid token type', 400, 'INVALID_TOKEN')
      }

      // Check if token exists in Redis
      const storedToken = await RedisService.getJSON(`password_reset:${decoded.id}`)
      if (!storedToken || storedToken.token !== token) {
        throw new AppError('Invalid or expired reset token', 400, 'INVALID_RESET_TOKEN')
      }

      // Get user
      const user = await DatabaseService.client.user.findUnique({
        where: { id: decoded.id },
        select: { id: true, email: true, isActive: true }
      })

      if (!user || !user.isActive) {
        throw new AppError('User not found or inactive', 400, 'USER_NOT_FOUND')
      }

      // Hash new password
      const passwordHash = await hashPassword(newPassword)

      // Update password
      await DatabaseService.client.user.update({
        where: { id: user.id },
        data: {
          passwordHash,
          passwordChangedAt: new Date(),
          updatedAt: new Date()
        }
      })

      // Remove reset token
      await RedisService.del(`password_reset:${user.id}`)

      // Invalidate all refresh tokens
      await RedisService.del(`refresh_token:${user.id}`)

      logAuth('Password reset successfully', user.id)

      res.json({
        success: true,
        message: 'Password reset successfully. Please log in with your new password.'
      })

    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        throw new AppError('Invalid or expired reset token', 400, 'INVALID_RESET_TOKEN')
      }
      throw error
    }
  })
)

/**
 * GET /api/auth/verify-token
 * Verify if token is valid (optional auth)
 */
router.get('/verify-token',
  optionalAuth,
  catchAsync(async (req, res) => {
    res.json({
      success: true,
      data: {
        valid: !!req.user,
        user: req.user || null
      }
    })
  })
)

/**
 * POST /api/auth/logout-all
 * Logout from all devices
 */
router.post('/logout-all',
  authMiddleware,
  catchAsync(async (req, res) => {
    const userId = req.user!.id

    // Remove all refresh tokens for this user
    const keys = await RedisService.mainClient.keys(`refresh_token:${userId}`)
    if (keys.length > 0) {
      await RedisService.mainClient.del(...keys)
    }

    // Update password changed timestamp to invalidate all JWTs
    await DatabaseService.client.user.update({
      where: { id: userId },
      data: { passwordChangedAt: new Date() }
    })

    logAuth('User logged out from all devices', userId)

    res.json({
      success: true,
      message: 'Logged out from all devices successfully'
    })
  })
)

/**
 * GET /api/auth/sessions
 * Get active sessions
 */
router.get('/sessions',
  authMiddleware,
  catchAsync(async (req, res) => {
    const userId = req.user!.id

    const keys = await RedisService.mainClient.keys(`refresh_token:${userId}`)
    const sessions = []

    for (const key of keys) {
      const session = await RedisService.getJSON(key)
      if (session) {
        sessions.push({
          createdAt: session.createdAt,
          userAgent: session.userAgent,
          ip: session.ip
        })
      }
    }

    res.json({
      success: true,
      data: { sessions }
    })
  })
)

export default router