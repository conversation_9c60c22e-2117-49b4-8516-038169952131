import { Router } from 'express'
import { He<PERSON>RPC } from '@/services/heliusRPC'
import { logger } from '@/utils/logger'
import { AppError } from '@/middleware/errorHandler'

const router = Router()

/**
 * GET /api/rpc/health
 * Get comprehensive RPC system health status
 */
router.get('/health', async (req, res) => {
  try {
    const healthSummary = HeliusRPC.getHealthSummary()
    const endpointStatus = HeliusRPC.getEndpointStatus()
    
    const healthData = {
      status: healthSummary.healthyEndpoints > 0 ? 'healthy' : 'degraded',
      timestamp: new Date().toISOString(),
      summary: healthSummary,
      endpoints: endpointStatus.map(endpoint => ({
        id: endpoint.id,
        url: endpoint.url.replace(/api-key=[^&]+/, 'api-key=***'), // Hide API key
        priority: endpoint.priority,
        weight: endpoint.weight,
        healthy: endpoint.healthy,
        circuitBreakerOpen: endpoint.circuitBreakerOpen,
        errorCount: endpoint.errorCount,
        totalRequests: endpoint.totalRequests,
        successfulRequests: endpoint.successfulRequests,
        successRate: endpoint.totalRequests > 0 ? 
          Math.round((endpoint.successfulRequests / endpoint.totalRequests) * 100) : 0,
        avgResponseTime: Math.round(endpoint.avgResponseTime),
        lastHealthCheck: endpoint.lastHealthCheck,
        lastError: endpoint.lastError
      }))
    }

    const statusCode = healthData.status === 'healthy' ? 200 : 503
    res.status(statusCode).json(healthData)

  } catch (error) {
    logger.error('Failed to get RPC health status:', error)
    throw new AppError('Failed to get RPC health status', 500, 'RPC_HEALTH_FAILED')
  }
})

/**
 * GET /api/rpc/endpoints
 * Get detailed endpoint status information
 */
router.get('/endpoints', async (req, res) => {
  try {
    const endpointStatus = HeliusRPC.getEndpointStatus()
    
    const endpointsData = endpointStatus.map(endpoint => ({
      id: endpoint.id,
      url: endpoint.url.replace(/api-key=[^&]+/, 'api-key=***'),
      priority: endpoint.priority,
      weight: endpoint.weight,
      maxRPS: endpoint.maxRPS,
      timeout: endpoint.timeout,
      retryAttempts: endpoint.retryAttempts,
      healthy: endpoint.healthy,
      circuitBreakerOpen: endpoint.circuitBreakerOpen,
      errorCount: endpoint.errorCount,
      totalRequests: endpoint.totalRequests,
      successfulRequests: endpoint.successfulRequests,
      avgResponseTime: endpoint.avgResponseTime,
      lastHealthCheck: endpoint.lastHealthCheck,
      lastError: endpoint.lastError,
      metrics: {
        successRate: endpoint.totalRequests > 0 ? 
          Math.round((endpoint.successfulRequests / endpoint.totalRequests) * 10000) / 100 : 0,
        errorRate: endpoint.totalRequests > 0 ? 
          Math.round((endpoint.errorCount / endpoint.totalRequests) * 10000) / 100 : 0,
        avgResponseTimeMs: Math.round(endpoint.avgResponseTime),
        healthScore: endpoint.healthy ? 
          Math.max(0, 100 - (endpoint.errorCount * 10) - (endpoint.avgResponseTime / 50)) : 0
      }
    }))

    res.json({
      endpoints: endpointsData,
      summary: {
        total: endpointsData.length,
        healthy: endpointsData.filter(e => e.healthy).length,
        degraded: endpointsData.filter(e => !e.healthy && !e.circuitBreakerOpen).length,
        failed: endpointsData.filter(e => e.circuitBreakerOpen).length
      }
    })

  } catch (error) {
    logger.error('Failed to get RPC endpoints status:', error)
    throw new AppError('Failed to get RPC endpoints status', 500, 'RPC_ENDPOINTS_FAILED')
  }
})

/**
 * POST /api/rpc/test
 * Test RPC functionality with a simple request
 */
router.post('/test', async (req, res) => {
  try {
    const { method = 'getSlot', params = [] } = req.body

    // Validate allowed test methods
    const allowedMethods = ['getSlot', 'getVersion', 'getBlockHeight', 'getHealth']
    if (!allowedMethods.includes(method)) {
      throw new AppError(`Test method ${method} not allowed`, 400, 'INVALID_TEST_METHOD')
    }

    const startTime = Date.now()
    
    // Import the rpcRequest function
    const { rpcRequest } = await import('@/services/heliusRPC')
    const result = await rpcRequest(method, params)
    
    const responseTime = Date.now() - startTime

    res.json({
      success: true,
      method,
      params,
      result,
      responseTime,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    logger.error('RPC test failed:', error)
    
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    })
  }
})

/**
 * POST /api/rpc/reset-circuit-breakers
 * Reset all circuit breakers (admin only)
 */
router.post('/reset-circuit-breakers', async (req, res) => {
  try {
    // This would need admin authentication in production
    // For now, we'll just document that this is an admin endpoint
    
    logger.info('Circuit breaker reset requested - this would reset all circuit breakers')
    
    // In a full implementation, we'd add a method to HeliusRPC to reset circuit breakers
    // HeliusRPC.resetCircuitBreakers()
    
    res.json({
      success: true,
      message: 'Circuit breakers reset (placeholder - feature not fully implemented)',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    logger.error('Failed to reset circuit breakers:', error)
    throw new AppError('Failed to reset circuit breakers', 500, 'CIRCUIT_BREAKER_RESET_FAILED')
  }
})

/**
 * GET /api/rpc/metrics
 * Get aggregated RPC metrics for monitoring
 */
router.get('/metrics', async (req, res) => {
  try {
    const healthSummary = HeliusRPC.getHealthSummary()
    const endpointStatus = HeliusRPC.getEndpointStatus()
    
    // Calculate aggregated metrics
    const totalRequests = endpointStatus.reduce((sum, e) => sum + e.totalRequests, 0)
    const totalSuccessful = endpointStatus.reduce((sum, e) => sum + e.successfulRequests, 0)
    const totalErrors = endpointStatus.reduce((sum, e) => sum + e.errorCount, 0)
    const avgResponseTime = endpointStatus.length > 0 ? 
      endpointStatus.reduce((sum, e) => sum + e.avgResponseTime, 0) / endpointStatus.length : 0

    const metrics = {
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      endpoints: {
        total: healthSummary.totalEndpoints,
        healthy: healthSummary.healthyEndpoints,
        unhealthy: healthSummary.totalEndpoints - healthSummary.healthyEndpoints
      },
      requests: {
        total: totalRequests,
        successful: totalSuccessful,
        failed: totalErrors,
        successRate: totalRequests > 0 ? (totalSuccessful / totalRequests) * 100 : 0,
        errorRate: totalRequests > 0 ? (totalErrors / totalRequests) * 100 : 0
      },
      performance: {
        avgResponseTimeMs: Math.round(avgResponseTime),
        requestsPerSecond: totalRequests / process.uptime()
      },
      health: {
        status: healthSummary.healthyEndpoints > 0 ? 'healthy' : 'degraded',
        healthScore: healthSummary.totalEndpoints > 0 ? 
          Math.round((healthSummary.healthyEndpoints / healthSummary.totalEndpoints) * 100) : 0
      }
    }

    res.json(metrics)

  } catch (error) {
    logger.error('Failed to get RPC metrics:', error)
    throw new AppError('Failed to get RPC metrics', 500, 'RPC_METRICS_FAILED')
  }
})

export default router