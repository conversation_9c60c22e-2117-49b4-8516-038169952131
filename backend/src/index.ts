import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import morgan from 'morgan'
import compression from 'compression'
import rateLimit from 'express-rate-limit'
import dotenv from 'dotenv'
import { createServer } from 'http'
import { WebSocketServer } from 'ws'

import { config } from '@/config/environment'
import { logger } from '@/utils/logger'
import { errorHandler } from '@/middleware/errorHandler'
import { authMiddleware } from '@/middleware/auth'
import { validateRequest } from '@/middleware/validation'

// Import routes
import authRoutes from '@/routes/auth'
import userRoutes from '@/routes/user'
import tradingRoutes from '@/routes/trading'
import portfolioRoutes from '@/routes/portfolio'
import alertRoutes from '@/routes/alerts'
import walletRoutes from '@/routes/wallet'
import healthRoutes from '@/routes/health'

// Import services
import { DatabaseService } from '@/services/database'
import { RedisService } from '@/services/redis'
import { WebSocketService } from '@/services/websocket'
import { TradingService } from '@/services/tradingService'
import { PriceService } from '@/services/priceService'
import { RiskService } from '@/services/riskService'
import { NotificationService } from '@/services/notificationService'
import { StrategyService } from '@/services/strategyService'
import { QueueManager } from '@/jobs/queueManager'

// Load environment variables
dotenv.config()

class App {
  public app: express.Application
  public server: any
  public wss: WebSocketServer
  private port: number

  constructor() {
    this.app = express()
    this.port = config.port
    
    this.initializeMiddlewares()
    this.initializeRoutes()
    this.initializeErrorHandling()
    this.initializeServices()
  }

  private initializeMiddlewares(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'", "wss:", "https:"],
        },
      },
    }))

    // CORS configuration
    this.app.use(cors({
      origin: config.cors.origin,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    }))

    // Rate limiting
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // limit each IP to 100 requests per windowMs
      message: 'Too many requests from this IP, please try again later.',
      standardHeaders: true,
      legacyHeaders: false,
    })
    this.app.use('/api/', limiter)

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }))
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }))

    // Compression middleware
    this.app.use(compression())

    // Logging middleware
    this.app.use(morgan('combined', {
      stream: { write: (message) => logger.info(message.trim()) }
    }))

    // Health check endpoint (before auth)
    this.app.get('/health', (req, res) => {
      res.status(200).json({ 
        status: 'healthy', 
        timestamp: new Date().toISOString(),
        version: process.env.npm_package_version || '1.0.0'
      })
    })
  }

  private initializeRoutes(): void {
    // API routes
    this.app.use('/api/auth', authRoutes)
    this.app.use('/api/user', authMiddleware, userRoutes)
    this.app.use('/api/trading', authMiddleware, tradingRoutes)
    this.app.use('/api/portfolio', authMiddleware, portfolioRoutes)
    this.app.use('/api/alerts', authMiddleware, alertRoutes)
    this.app.use('/api/wallet', authMiddleware, walletRoutes)
    this.app.use('/api/health', healthRoutes)

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Not Found',
        message: `Route ${req.originalUrl} not found`,
        timestamp: new Date().toISOString()
      })
    })
  }

  private initializeErrorHandling(): void {
    this.app.use(errorHandler)
  }

  private async initializeServices(): Promise<void> {
    try {
      // Initialize database
      await DatabaseService.initialize()
      logger.info('Database service initialized')

      // Initialize Redis
      await RedisService.initialize()
      logger.info('Redis service initialized')

      // Initialize queue manager
      await QueueManager.initialize()
      logger.info('Queue manager initialized')

      // Schedule recurring jobs
      await QueueManager.scheduleRecurringJobs()
      logger.info('Recurring jobs scheduled')

      // All services are now ready
      logger.info('All services initialized successfully')

    } catch (error) {
      logger.error('Failed to initialize services:', error)
      process.exit(1)
    }
  }

  private initializeWebSocket(): void {
    this.wss = new WebSocketServer({ server: this.server })
    
    // Initialize WebSocket service
    WebSocketService.initialize(this.wss)
    
    logger.info('WebSocket server initialized')
  }

  public async start(): Promise<void> {
    try {
      this.server = createServer(this.app)
      
      // Initialize WebSocket after HTTP server
      this.initializeWebSocket()

      this.server.listen(this.port, () => {
        logger.info(`🚀 MemeTrader Pro Backend running on port ${this.port}`)
        logger.info(`📊 Environment: ${config.nodeEnv}`)
        logger.info(`🔗 WebSocket server ready`)
      })

      // Graceful shutdown
      process.on('SIGTERM', this.gracefulShutdown.bind(this))
      process.on('SIGINT', this.gracefulShutdown.bind(this))

    } catch (error) {
      logger.error('Failed to start server:', error)
      process.exit(1)
    }
  }

  private async gracefulShutdown(): Promise<void> {
    logger.info('Received shutdown signal, starting graceful shutdown...')
    
    try {
      // Close WebSocket server
      this.wss.close()
      
      // Close HTTP server
      this.server.close(() => {
        logger.info('HTTP server closed')
      })

      // Shutdown services
      await StrategyService.shutdown()
      logger.info('Strategy service shutdown')

      await PriceService.shutdown()
      logger.info('Price service shutdown')

      await QueueManager.shutdown()
      logger.info('Queue manager shutdown')

      // Close database connections
      await DatabaseService.disconnect()
      
      // Close Redis connections
      await RedisService.disconnect()

      logger.info('Graceful shutdown completed')
      process.exit(0)
    } catch (error) {
      logger.error('Error during graceful shutdown:', error)
      process.exit(1)
    }
  }
}

// Start the application
const app = new App()
app.start().catch((error) => {
  logger.error('Failed to start application:', error)
  process.exit(1)
})

export default app
