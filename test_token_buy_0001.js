#!/usr/bin/env node

/**
 * Test Token Buy Script
 * Executes 0.001 SOL -> ATEe3MqHCzy4dKMm9DgCvd2cYBKxMTCPPb1ZnLnmbonk swap
 */

require('dotenv').config();
const http = require('http');
const jwt = require('jsonwebtoken');

// Swap parameters as requested
const SWAP_PARAMS = {
  tokenIn: 'So11111111111111111111111111111111111111112', // SOL
  tokenOut: 'ATEe3MqHCzy4dKMm9DgCvd2cYBKxMTCPPb1ZnLnmbonk', // Target token
  amount: 1000000, // 0.001 SOL in lamports
  slippage: 0.01, // 1% slippage (DEFAULT preset)
  priorityFeeLamports: 1000000, // 0.001 SOL priority fee (smaller for test)
  strategyId: null // No exit strategy for this test
};

const API_BASE_URL = 'http://localhost:3001/api'; // Updated to correct port

// Create a test JWT token manually
function createTestToken() {
  const payload = {
    id: 'test-user-id',
    email: '<EMAIL>',
    role: 'user',
    walletAddress: process.env.TRADING_WALLET_ADDRESS
  };
  
  return jwt.sign(payload, process.env.JWT_SECRET, { expiresIn: '1h' });
}

function makeHttpRequest(path, method = 'GET', data = null, authToken = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(API_BASE_URL + path);
    
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };
    
    if (authToken) {
      options.headers['Authorization'] = `Bearer ${authToken}`;
    }
    
    const req = http.request(options, (res) => {
      let body = '';
      
      res.on('data', (chunk) => {
        body += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: jsonBody
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function executeSwap() {
  console.log('🚀 MemeTrader Pro - Token Swap Test (0.001 SOL)');
  console.log('=' .repeat(60));
  console.log('⚠️  REAL MONEY TRANSACTION - MAINNET SOLANA');
  console.log('=' .repeat(60));
  
  console.log(`📊 Swap Configuration:`);
  console.log(`   From: SOL (${SWAP_PARAMS.tokenIn})`);
  console.log(`   To: ATEe3MqHCzy4dKMm9DgCvd2cYBKxMTCPPb1ZnLnmbonk`);
  console.log(`   Amount: 0.001 SOL (${SWAP_PARAMS.amount.toLocaleString()} lamports)`);
  console.log(`   Slippage: ${SWAP_PARAMS.slippage * 100}% (DEFAULT preset)`);
  console.log(`   Priority Fee: ${SWAP_PARAMS.priorityFeeLamports / **********} SOL`);
  console.log(`   Wallet: ${process.env.TRADING_WALLET_ADDRESS}`);
  console.log('');

  try {
    // Create test authentication token
    console.log('🔐 Creating authentication token...');
    const authToken = createTestToken();
    console.log('✅ Auth token created');
    console.log('');

    // Step 1: Check backend health
    console.log('🏥 Step 1: Checking backend health...');
    const healthResponse = await makeHttpRequest('/health');
    
    if (healthResponse.statusCode !== 200) {
      console.error('❌ Backend health check failed');
      console.error(healthResponse.body);
      return;
    }
    
    console.log('✅ Backend is healthy');
    console.log('');

    // Step 2: Get quote
    console.log('📈 Step 2: Getting Jupiter quote...');
    
    const quoteResponse = await makeHttpRequest('/trading/quote', 'POST', {
      tokenIn: SWAP_PARAMS.tokenIn,
      tokenOut: SWAP_PARAMS.tokenOut,
      amount: SWAP_PARAMS.amount,
      slippage: SWAP_PARAMS.slippage
    }, authToken);

    if (quoteResponse.statusCode !== 200) {
      console.error('❌ Quote request failed:');
      console.error(`Status: ${quoteResponse.statusCode}`);
      console.error('Response:', quoteResponse.body);
      return;
    }

    const quote = quoteResponse.body.data;
    console.log('✅ Quote received successfully:');
    console.log(`   Input Amount: ${quote.inAmount} lamports (${quote.inAmount / **********} SOL)`);
    console.log(`   Expected Output: ${quote.outAmount} tokens`);
    if (quote.priceImpactPct) {
      console.log(`   Price Impact: ${quote.priceImpactPct}%`);
    }
    if (quote.platformFee) {
      console.log(`   Platform Fee: ${quote.platformFee} lamports`);
    }
    console.log('');

    // Step 3: Execute the swap
    console.log('⚡ Step 3: Executing swap transaction...');
    console.log('⚠️  THIS WILL EXECUTE A REAL TRANSACTION ON MAINNET');
    console.log('');
    
    const executeResponse = await makeHttpRequest('/trading/execute', 'POST', {
      tokenIn: SWAP_PARAMS.tokenIn,
      tokenOut: SWAP_PARAMS.tokenOut,
      amount: SWAP_PARAMS.amount,
      slippage: SWAP_PARAMS.slippage,
      strategyId: SWAP_PARAMS.strategyId,
      priorityFeeLamports: SWAP_PARAMS.priorityFeeLamports
    }, authToken);

    if (executeResponse.statusCode === 200 && executeResponse.body.success) {
      console.log('🎉 SWAP EXECUTED SUCCESSFULLY! 🎉');
      console.log('=' .repeat(60));
      console.log(`📋 Transaction Details:`);
      console.log(`   Transaction Signature: ${executeResponse.body.data.signature}`);
      console.log(`   Status: ${executeResponse.body.data.status || 'Pending Confirmation'}`);
      
      if (executeResponse.body.data.outputAmount) {
        console.log(`   Tokens Received: ${executeResponse.body.data.outputAmount}`);
      }
      
      if (executeResponse.body.warnings && executeResponse.body.warnings.length > 0) {
        console.log(`⚠️  Warnings: ${executeResponse.body.warnings.join(', ')}`);
      }
      
      console.log('');
      console.log('🔗 View Transaction:');
      console.log(`   Solscan: https://solscan.io/tx/${executeResponse.body.data.signature}`);
      console.log(`   SolanaFM: https://solana.fm/tx/${executeResponse.body.data.signature}`);
      console.log(`   Solana Beach: https://solanabeach.io/transaction/${executeResponse.body.data.signature}`);
      console.log('');
      console.log('✅ 0.001 SOL -> ATEe3MqHCzy4dKMm9DgCvd2cYBKxMTCPPb1ZnLnmbonk swap completed!');
      
      return executeResponse.body.data.signature;
      
    } else {
      console.error('❌ SWAP EXECUTION FAILED');
      console.error(`   Status: ${executeResponse.statusCode}`);
      console.error(`   Response:`, JSON.stringify(executeResponse.body, null, 2));
      
      if (executeResponse.body.message) {
        console.error(`   Error: ${executeResponse.body.message}`);
      }
    }

  } catch (error) {
    console.error('💥 FATAL ERROR DURING SWAP EXECUTION');
    console.error(`   Type: ${error.name}`);
    console.error(`   Message: ${error.message}`);
    console.error(`   Stack: ${error.stack}`);
  }
}

// Check wallet balance first
async function checkBalanceAndExecute() {
  console.log('🔍 Pre-flight checks...');
  
  // Import the balance check
  const { execSync } = require('child_process');
  
  try {
    console.log('💰 Checking wallet balance...');
    const balanceResult = execSync('node check_wallet_balance.js', { 
      cwd: '/Users/<USER>/dev/github/agmentcode',
      encoding: 'utf8' 
    });
    
    console.log(balanceResult);
    
    if (balanceResult.includes('❌ Insufficient balance')) {
      console.log('');
      console.log('⛔ CANNOT EXECUTE SWAP - INSUFFICIENT BALANCE');
      console.log('');
      console.log('Options to proceed:');
      console.log('1. Fund wallet with at least 0.002 SOL (0.001 for swap + 0.001 for fees)');
      console.log('2. Execute smaller swap with available balance');
      console.log('3. Test the swap infrastructure with a simulation');
      console.log('');
      console.log('Send SOL to: 968aqNdZ5ZRLByseFo18YiSHywzqXj3S9z56Vwr62CGT');
      return;
    }
    
    if (balanceResult.includes('✅ Sufficient balance')) {
      console.log('');
      console.log('🚀 Balance sufficient - proceeding with swap...');
      console.log('');
      
      const signature = await executeSwap();
      if (signature) {
        console.log(`\n🎉 FINAL RESULT: Transaction executed with signature ${signature}`);
      }
    }
    
  } catch (error) {
    console.error('Error in pre-flight checks:', error.message);
  }
}

// Execute the complete flow
checkBalanceAndExecute()
  .then(() => {
    console.log('\nScript execution completed.');
  })
  .catch((error) => {
    console.error('Script failed:', error);
    process.exit(1);
  });