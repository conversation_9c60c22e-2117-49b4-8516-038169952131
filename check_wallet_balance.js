#!/usr/bin/env node

/**
 * Check Wallet Balance Script
 * Verifies SOL balance before executing swap
 */

require('dotenv').config();
const https = require('https');

const WALLET_ADDRESS = process.env.TRADING_WALLET_ADDRESS;
const HELIUS_RPC_URL = process.env.HELIUS_RPC_URL;

async function checkWalletBalance() {
  console.log('🔍 Checking wallet balance...');
  console.log(`📍 Wallet Address: ${WALLET_ADDRESS}`);
  console.log(`🔗 RPC Endpoint: ${HELIUS_RPC_URL}`);
  console.log('');

  try {
    const rpcPayload = {
      jsonrpc: '2.0',
      id: 1,
      method: 'getBalance',
      params: [WALLET_ADDRESS]
    };

    const url = new URL(HELIUS_RPC_URL);
    const options = {
      hostname: url.hostname,
      path: url.pathname + url.search,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const response = await new Promise((resolve, reject) => {
      const req = https.request(options, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          try {
            resolve(JSON.parse(data));
          } catch (error) {
            reject(error);
          }
        });
      });
      
      req.on('error', reject);
      req.write(JSON.stringify(rpcPayload));
      req.end();
    });

    if (response.error) {
      console.error('❌ RPC Error:', response.error);
      return;
    }

    const balanceLamports = response.result.value;
    const balanceSOL = balanceLamports / 1000000000; // Convert lamports to SOL

    console.log('💰 Wallet Balance Results:');
    console.log(`   Balance: ${balanceSOL.toFixed(6)} SOL`);
    console.log(`   Lamports: ${balanceLamports.toLocaleString()}`);
    console.log('');

    // Check if we have enough for the swap
    const requiredSOL = 0.25;
    const requiredWithFees = 0.26; // Extra for transaction fees

    if (balanceSOL >= requiredWithFees) {
      console.log('✅ Sufficient balance for 0.25 SOL swap + fees');
      console.log(`   Available for swap: ${(balanceSOL - 0.01).toFixed(6)} SOL (keeping 0.01 SOL for fees)`);
    } else if (balanceSOL >= requiredSOL) {
      console.log('⚠️  Sufficient balance for swap but low on fees');
      console.log('   Recommend keeping more SOL for transaction fees');
    } else {
      console.log('❌ Insufficient balance for 0.25 SOL swap');
      console.log(`   Required: ${requiredSOL} SOL`);
      console.log(`   Available: ${balanceSOL.toFixed(6)} SOL`);
      console.log(`   Shortfall: ${(requiredSOL - balanceSOL).toFixed(6)} SOL`);
    }

    return { balanceSOL, balanceLamports, sufficient: balanceSOL >= requiredSOL };

  } catch (error) {
    console.error('💥 Error checking wallet balance:', error.message);
    throw error;
  }
}

// Run the balance check
checkWalletBalance()
  .then(result => {
    if (result && result.sufficient) {
      console.log('\n🚀 Ready to proceed with swap execution!');
    } else {
      console.log('\n⛔ Cannot proceed - insufficient balance');
    }
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });