#!/usr/bin/env node

import { Connection, PublicKey, Keypair } from '@solana/web3.js'
import bs58 from 'bs58'

async function testWalletBalance() {
  try {
    console.log('🔍 Testing wallet balance fetching...')
    
    // Environment variables
    const encryptedPrivateKey = '4WvFbqEgvamA9PjtpdssRWfgQ9Vxzn8eaYPuTWFRCTmfnTco7GJnajymxZM6caXrE2dEwB5Ua9Qg1HTPqSAy1Gmq'
    const rpcUrl = 'https://mainnet.helius-rpc.com/?api-key=ce149de7-a6e9-4ad8-9d80-6f24e5056550'
    
    // Load wallet
    const wallet = Keypair.fromSecretKey(bs58.decode(encryptedPrivateKey))
    console.log('🔑 Wallet loaded:', wallet.publicKey.toString())
    
    // Connect to Solana
    const connection = new Connection(rpcUrl, 'confirmed')
    console.log('🔗 Connected to Solana RPC')
    
    // Get SOL balance
    const solBalanceLamports = await connection.getBalance(wallet.publicKey)
    const solBalance = solBalanceLamports / 1e9
    console.log('💰 SOL Balance:', solBalance)
    
    // Get token accounts
    const tokenAccounts = await connection.getParsedTokenAccountsByOwner(
      wallet.publicKey,
      { programId: new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') }
    )
    
    console.log(`📊 Found ${tokenAccounts.value.length} token accounts:`)
    
    for (const accountInfo of tokenAccounts.value) {
      const parsedInfo = accountInfo.account.data.parsed.info
      const mintAddress = parsedInfo.mint
      const balance = parsedInfo.tokenAmount.uiAmount
      const decimals = parsedInfo.tokenAmount.decimals
      
      if (balance > 0) {
        console.log(`  - Token: ${mintAddress}`)
        console.log(`    Balance: ${balance}`)
        console.log(`    Decimals: ${decimals}`)
        
        // Check if this is the user's specific token
        if (mintAddress === 'ATEe3MqHCzy4dKMm9DgCvd2cYBKxMTCPPb1ZnLnmbonk') {
          console.log('    🎯 This is your target token!')
        }
        console.log('')
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error)
  }
}

testWalletBalance()