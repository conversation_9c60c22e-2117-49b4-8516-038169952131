# Docker Compose Setup for MemeTrader Pro

## Overview

This project uses Docker Compose to orchestrate a multi-service architecture with PostgreSQL, Redis, Backend API, and Frontend Next.js application. The setup includes development-optimized configurations with hot reload capabilities.

## Architecture

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Frontend  │    │   Backend   │    │ PostgreSQL  │    │    Redis    │
│   Next.js   │────│   Node.js   │────│  Database   │    │    Cache    │
│   Port 3000 │    │   Port 5000 │    │   Port 5432 │    │   Port 6379 │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
```

## Services

### 1. PostgreSQL Database (`postgres`)
- **Image:** `postgres:15-alpine`
- **Port:** 5432
- **Persistent Volume:** `postgres_data`
- **Health Checks:** Enabled
- **Init Scripts:** `backend/prisma/init.sql`, `backend/prisma/seed.sql` (dev)

### 2. Redis Cache (`redis`)
- **Image:** `redis:7-alpine`
- **Port:** 6379
- **Persistent Volume:** `redis_data`
- **Password Protected:** Yes (configurable via `.env`)
- **Health Checks:** Enabled

### 3. Backend API (`backend`)
- **Build:** Custom Dockerfile (`backend/Dockerfile`)
- **Port:** 5000
- **Dependencies:** PostgreSQL, Redis
- **Hot Reload:** Enabled in development
- **Health Endpoint:** `/api/health`

### 4. Frontend Application (`frontend`)
- **Build:** Custom Dockerfile (`frontend/Dockerfile`)
- **Port:** 3000
- **Dependencies:** Backend API
- **Hot Reload:** Enabled in development

## Quick Start

### Prerequisites
- Docker and Docker Compose installed
- Node.js 18+ (for local development)
- Git

### 1. Clone and Setup
```bash
git clone <repository-url>
cd memetrader-pro
cp .env.example .env  # Edit with your configuration
```

### 2. Configure Environment
Edit `.env` file with your settings:
```bash
# Database credentials
DATABASE_PASSWORD=your-secure-password

# JWT secrets (generate secure random strings)
JWT_SECRET=your-jwt-secret-minimum-32-characters
JWT_REFRESH_SECRET=your-jwt-refresh-secret-minimum-32-characters

# Solana/API configurations
SOLANA_RPC_URL=your-solana-rpc-url
HELIUS_API_KEY=your-helius-api-key
```

### 3. Start Development Environment
```bash
# Start all services in development mode
npm run docker:dev

# Or start with build (first time or after changes)
npm run docker:dev:build

# Enable hot reload watching
npm run docker:dev:watch
```

### 4. Access Applications
- **Frontend:** http://localhost:3000
- **Backend API:** http://localhost:5000
- **Database:** localhost:5432
- **Redis:** localhost:6379

## Available Scripts

### Development Commands
```bash
npm run docker:dev              # Start development environment
npm run docker:dev:build        # Start with fresh build
npm run docker:dev:watch        # Enable file watching with hot reload
```

### Production Commands
```bash
npm run docker:prod             # Start production environment
npm run docker:build           # Build all images
npm run docker:rebuild          # Rebuild without cache
```

### Management Commands
```bash
npm run docker:up               # Start services (detached)
npm run docker:down             # Stop services
npm run docker:logs             # Follow logs
npm run docker:clean            # Remove volumes and images
npm run docker:reset            # Complete reset and rebuild
```

## File Structure

```
├── compose.yaml                 # Main Docker Compose configuration
├── docker-compose.override.yml  # Development overrides
├── .env                        # Environment variables
├── backend/
│   ├── Dockerfile              # Backend container definition
│   ├── .dockerignore           # Backend build exclusions
│   └── prisma/
│       ├── init.sql            # Database initialization
│       └── seed.sql            # Development seed data
├── frontend/
│   ├── Dockerfile              # Frontend container definition
│   └── .dockerignore           # Frontend build exclusions
└── DOCKER.md                   # This documentation
```

## Development Features

### Hot Reload
Both frontend and backend support hot reload in development mode:
- **Backend:** Uses `tsx watch` for TypeScript hot reload
- **Frontend:** Uses Next.js development server with Fast Refresh
- **File Watching:** Docker Compose Watch automatically syncs changes

### Volume Mounts
Development mode mounts source code as volumes:
```yaml
volumes:
  - ./backend/src:/app/src:ro    # Backend source (read-only)
  - ./frontend/src:/app/src:ro   # Frontend source (read-only)
```

### Health Checks
All services include health checks for proper startup ordering:
- **PostgreSQL:** `pg_isready` check
- **Redis:** `redis-cli ping` check
- **Backend:** HTTP health endpoint check
- **Frontend:** HTTP availability check

## Database Management

### Initial Setup
The database is automatically initialized with:
1. Required PostgreSQL extensions
2. System configuration defaults
3. Trading presets
4. Development seed data (in dev mode)

### Migrations
Run Prisma migrations:
```bash
# Inside backend container
docker compose exec backend npm run db:migrate

# Or from host (if backend is running)
npm run db:migrate
```

### Database Access
Connect to PostgreSQL:
```bash
# Using Docker
docker compose exec postgres psql -U postgres -d memetrader

# Using external client
psql -h localhost -U postgres -d memetrader
```

## Environment Configuration

### Required Variables
```bash
# Database
DATABASE_PASSWORD=secure-password

# JWT
JWT_SECRET=minimum-32-character-secret
JWT_REFRESH_SECRET=minimum-32-character-refresh-secret

# Blockchain
SOLANA_RPC_URL=your-rpc-endpoint
HELIUS_API_KEY=your-helius-key
```

### Optional Variables
```bash
# Service Ports
FRONTEND_PORT=3000
BACKEND_PORT=5000
DATABASE_PORT=5432
REDIS_PORT=6379

# Redis
REDIS_PASSWORD=redis-password

# Trading
JUPITER_API_URL=https://quote-api.jup.ag/v6
```

## Networking

### Custom Network
Services communicate through a custom bridge network:
- **Network Name:** `memetrader-network`
- **Subnet:** `**********/16`
- **Service Discovery:** Automatic via service names

### Service Communication
```bash
# Backend connects to database
DATABASE_URL=************************************/db

# Backend connects to Redis
REDIS_URL=redis://:password@redis:6379

# Frontend connects to backend
NEXT_PUBLIC_API_URL=http://localhost:5000/api
```

## Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Check what's using the port
   lsof -i :3000  # or :5000, :5432, :6379
   
   # Change ports in .env file
   FRONTEND_PORT=3001
   ```

2. **Database Connection Issues**
   ```bash
   # Check database logs
   docker compose logs postgres
   
   # Verify health status
   docker compose ps
   ```

3. **Build Failures**
   ```bash
   # Clean rebuild
   npm run docker:clean
   npm run docker:dev:build
   ```

4. **Permission Issues**
   ```bash
   # Fix file permissions
   sudo chown -R $USER:$USER .
   ```

### Logs and Debugging

```bash
# View all logs
docker compose logs -f

# View specific service logs
docker compose logs -f backend
docker compose logs -f frontend
docker compose logs -f postgres
docker compose logs -f redis

# Execute commands in containers
docker compose exec backend bash
docker compose exec frontend bash
```

### Performance Optimization

1. **Use .dockerignore files** (already configured)
2. **Multi-stage builds** (implemented in Dockerfiles)
3. **Volume caching** for node_modules
4. **Health checks** for proper startup ordering

## Production Deployment

### Production Mode
```bash
# Use production compose file only
docker compose -f compose.yaml up -d

# Or use the npm script
npm run docker:prod
```

### Production Considerations
1. **Environment Variables:** Use secure values
2. **SSL/TLS:** Configure reverse proxy (nginx/traefik)
3. **Secrets Management:** Use Docker secrets or external vault
4. **Monitoring:** Add logging and monitoring solutions
5. **Backups:** Set up database backup strategy

## Security Notes

1. **Never commit sensitive data** to version control
2. **Use strong passwords** for database and Redis
3. **Generate secure JWT secrets** (32+ characters)
4. **Keep API keys secure** and rotate regularly
5. **Use environment variables** for all sensitive configuration

## Support

For issues related to Docker setup:
1. Check the logs: `docker compose logs -f`
2. Verify environment configuration
3. Ensure all required ports are available
4. Check Docker and Docker Compose versions

Minimum requirements:
- Docker: 20.10+
- Docker Compose: 2.0+