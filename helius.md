
i am using free plan Plan	RPC Rate Limit. 10 requests/s	DAS & Enhanced APIs. 2 requests/s


Build Your First Solana App with Helius

Copy page

Learn the fundamentals of building on Solana by creating your first application using Helius APIs. From setup to deployment in minutes.

Ready to build on Solana? This guide will walk you through creating your first application using Helius APIs. You’ll learn how to fetch NFT data, understand the response structure, and see how easy it is to build powerful applications with our infrastructure.
1
Create Your Free Helius Account
Start by creating your free account at the Helius Dashboard. Your free tier includes 100,000 DAS API calls per month; perfect for getting started and building prototypes.
2
Get Your API Key
Navigate to the API Keys section and copy your key. This key gives you access to all Helius APIs, including RPC nodes, DAS API, and enhanced transaction data.
3
Make Your First API Call
Let’s start with a practical example: fetching NFTs from a wallet. We’ll use the getAssetsByOwner method to query assets owned by 86xCn…o2MMY (<PERSON><PERSON><PERSON>’s wallet, co-founder of Solana).
Your API Key
Enter your API key
Owner Address
86xCnPeV69n6t3DnyGvkKobf9FdN2H9oiVDdaMpo2MMY
Get Random NFT
What’s happening here? The DAS API allows you to query compressed and standard NFTs with a single call. Notice how the response includes metadata, image URLs, and ownership information - all the data you need to build rich user experiences.
4
Understanding the Response
Great! You’ve successfully fetched NFT data. The response includes:
Asset ID: Unique identifier for each NFT
Metadata: Name, symbol, description, and attributes
Content: Image URLs and file information
Ownership: Current owner and authority information
​
Build It with Code
Now let’s implement this in a real application. We’ll create a simple NFT portfolio viewer that you can expand upon.
We’ll use Node.js for this example. Make sure you have it installed from nodejs.org.
1
Set up your project

Copy

Ask AI
mkdir solana-nft-viewer
cd solana-nft-viewer
npm init -y
npm install node-fetch
2
Create the NFT portfolio viewer
nft-portfolio.js

Copy

Ask AI
const fetch = require('node-fetch');

class NFTPortfolioViewer {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.baseUrl = 'https://mainnet.helius-rpc.com';
  }

  async fetchNFTsByOwner(ownerAddress, limit = 10) {
    try {
      const response = await fetch(`${this.baseUrl}/?api-key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: '1',
          method: 'getAssetsByOwner',
          params: {
            ownerAddress,
            page: 1,
            limit,
            displayOptions: {
              showFungible: false,
              showNativeBalance: false,
            },
          },
        }),
      });

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error.message);
      }

      return data.result;
    } catch (error) {
      console.error('Error fetching NFTs:', error.message);
      throw error;
    }
  }

  displayNFTPortfolio(nfts) {
    console.log('\n🖼️  NFT Portfolio Summary');
    console.log('========================');
    console.log(`Total NFTs: ${nfts.total}`);
    console.log(`Showing: ${nfts.items.length} items\n`);

    nfts.items.forEach((nft, index) => {
      console.log(`${index + 1}. ${nft.content?.metadata?.name || 'Unnamed NFT'}`);
      console.log(`   Collection: ${nft.grouping?.[0]?.group_value || 'Individual'}`);
      console.log(`   Compressed: ${nft.compression?.compressed ? 'Yes' : 'No'}`);
      console.log(`   Image: ${nft.content?.files?.[0]?.uri || 'No image'}`);
      console.log(`   ID: ${nft.id}\n`);
    });
  }

  async getRandomNFT(ownerAddress) {
    const portfolio = await this.fetchNFTsByOwner(ownerAddress, 50);

    if (portfolio.items.length === 0) {
      console.log('No NFTs found for this address.');
      return null;
    }

    const randomIndex = Math.floor(Math.random() * portfolio.items.length);
    return portfolio.items[randomIndex];
  }
}

// Usage example
async function main() {
  const viewer = new NFTPortfolioViewer('YOUR_API_KEY');

  // Anatoly's wallet address
  const walletAddress = '86xCnPeV69n6t3DnyGvkKobf9FdN2H9oiVDdaMpo2MMY';

  console.log('🚀 Fetching NFT portfolio...');

  try {
    // Get full portfolio overview
    const portfolio = await viewer.fetchNFTsByOwner(walletAddress);
    viewer.displayNFTPortfolio(portfolio);

    // Get a random NFT for featured display
    console.log('🎲 Featured Random NFT:');
    console.log('=====================');
    const randomNFT = await viewer.getRandomNFT(walletAddress);

    if (randomNFT) {
      console.log(`Name: ${randomNFT.content?.metadata?.name}`);
      console.log(`Description: ${randomNFT.content?.metadata?.description || 'No description'}`);
      console.log(`Image: ${randomNFT.content?.files?.[0]?.uri}`);
    }

  } catch (error) {
    console.error('Failed to fetch NFT data:', error.message);
  }
}

main();
3
Add your API key
Replace YOUR_API_KEY with your actual API key from the Helius dashboard.
4
Run your NFT portfolio viewer

Copy

Ask AI
node nft-portfolio.js
5
Success! 🎉
You’ve built your first Solana application! The output shows:
Total NFT count in the wallet
Individual NFT details including compression status
Collection information
A featured random NFT
What you’ve learned:
How to structure API calls to Helius
Working with the DAS API response format
Handling compressed vs standard NFTs
Building reusable code for NFT operations



========================
CODE SNIPPETS
========================
TITLE: Go: Initialize Yellowstone gRPC Client Project and Install Dependencies
DESCRIPTION: This snippet details the process of setting up a new Go project, initializing its module, creating the necessary directory structure, and installing the Go-specific dependencies for the Yellowstone gRPC client, along with the resulting `go.mod` content.

SOURCE: https://www.helius.dev/docs/grpc/quickstart

LANGUAGE: bash
CODE:
```
mkdir go-yellowstone-client
cd go-yellowstone-client
go mod init go-yellowstone-client
mkdir -p cmd/client
touch cmd/client/main.go
go get github.com/mr-tron/base58@v1.2.0
go get github.com/rpcpool/yellowstone-grpc/examples/golang@latest
go get google.golang.org/grpc@v1.67.1
```

LANGUAGE: go.mod
CODE:
```
module go-yellowstone-client

go 1.21

require (
    github.com/mr-tron/base58 v1.2.0
    github.com/rpcpool/yellowstone-grpc/examples/golang v0.0.0-20250206164228-b9f96ae944bb
    google.golang.org/grpc v1.67.1
)

require (
    golang.org/x/net v0.28.0 // indirect
    golang.org/x/sys v0.24.0 // indirect
    golang.org/x/text v0.17.0 // indirect
    google.golang.org/genproto/googleapis/rpc v0.0.0-20240814211410-ddb44dafa142 // indirect
    google.golang.org/protobuf v1.35.1 // indirect
)
```

----------------------------------------

TITLE: Helius Laserstream: Basic Subscription Setup
DESCRIPTION: This TypeScript example demonstrates the fundamental setup for subscribing to real-time data streams using the `helius-laserstream` library. It shows how to define a `SubscribeRequest` with an empty configuration for various data types, set up the `LaserstreamConfig` with an API key and endpoint, and initiate the subscription with callbacks for handling incoming data and errors. This snippet provides a template for a general-purpose Laserstream connection.

SOURCE: https://www.helius.dev/docs/laserstream/grpc

LANGUAGE: TypeScript
CODE:
```
import { subscribe, CommitmentLevel, LaserstreamConfig, SubscribeRequest } from 'helius-laserstream'

async function main() {
    const subscriptionRequest: SubscribeRequest = {
        entry: {},
        accounts: {},
        accountsDataSlice: [],
        slots: {},
        blocks: {},
        blocksMeta: {
            blockmetadata: {}
        },
        transactions: {},
        transactionsStatus: {},
        commitment: CommitmentLevel.CONFIRMED,
    };

    const config: LaserstreamConfig = {
        apiKey: 'YOUR_API_KEY', // Replace with your key
        endpoint: 'https://laserstream-mainnet-ewr.helius-rpc.com', // Choose your closest region
    }

    await subscribe(config, subscriptionRequest, async (data) => {
        console.log(data);
    }, async (error) => {
        console.error(error);
    });
}

main().catch(console.error);
```

----------------------------------------

TITLE: Install Helius LaserStream and TypeScript Dependencies
DESCRIPTION: This snippet installs the 'helius-laserstream' library, 'typescript', and 'ts-node' as development dependencies, then initializes a TypeScript configuration file.

SOURCE: https://www.helius.dev/docs/laserstream/grpc

LANGUAGE: bash
CODE:
```
npm install helius-laserstream
npm install --save-dev typescript ts-node
npx tsc --init
```

----------------------------------------

TITLE: TypeScript/JavaScript: Initialize Yellowstone gRPC Client Project
DESCRIPTION: This snippet provides the commands to create a new Node.js project, install necessary Helius Yellowstone gRPC client dependencies, and set up TypeScript configuration for development.

SOURCE: https://www.helius.dev/docs/grpc/quickstart

LANGUAGE: bash
CODE:
```
mkdir ts-yellowstone-client
cd ts-yellowstone-client
npm init -y
npm install @triton-one/yellowstone-grpc bs58
npm install typescript ts-node @types/node --save-dev
npx tsc --init
```

----------------------------------------

TITLE: Run Helius LaserStream Subscription Script
DESCRIPTION: This command executes the TypeScript subscription script using 'ts-node', starting the LaserStream data stream and logging results to the console.

SOURCE: https://www.helius.dev/docs/laserstream/grpc

LANGUAGE: bash
CODE:
```
npx ts-node index.ts
```

----------------------------------------

TITLE: Subscribe to Slot Updates with Helius LaserStream SDK (TypeScript)
DESCRIPTION: This example demonstrates how to subscribe to real-time slot updates using the Helius LaserStream SDK. It configures a subscription request to filter slot updates by commitment level and logs incoming data, providing a basic setup for monitoring blockchain progress.

SOURCE: https://www.helius.dev/docs/laserstream/grpc

LANGUAGE: typescript
CODE:
```
import { subscribe, CommitmentLevel, LaserstreamConfig, SubscribeRequest } from 'helius-laserstream'

async function main() {
    const subscriptionRequest: SubscribeRequest = {
        transactions: {},
        commitment: CommitmentLevel.CONFIRMED,
        accounts: {},
        slots: {
            slot: { filterByCommitment: true },
        },
        transactionsStatus: {},
        blocks: {},
        blocksMeta: {},
        entry: {},
        accountsDataSlice: [],
    };

    const config: LaserstreamConfig = {
        apiKey: 'YOUR_API_KEY', // Replace with your key
        endpoint: 'https://laserstream-mainnet-ewr.helius-rpc.com', // Choose your closest region
    }

    await subscribe(config, subscriptionRequest, async (data) => {
        console.log(data);
    }, async (error) => {
        console.error(error);
    });
}

main().catch(console.error);
```

----------------------------------------

TITLE: Get Rent Exemption for a Zero-Byte Account
DESCRIPTION: This example shows how to query the base rent exemption for an account with no data, representing the absolute minimum lamports required for any account to be rent-exempt.

SOURCE: https://www.helius.dev/docs/rpc/guides/getminimumbalanceforrentexemption

LANGUAGE: cURL
CODE:
```
# Replace <api-key> with your Helius API key
curl https://mainnet.helius-rpc.com/?api-key=<api-key> -X POST -H "Content-Type: application/json" -d \
  '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "getMinimumBalanceForRentExemption",
    "params": [
      0
    ]
  }'
```

----------------------------------------

TITLE: Rust Quick Start: Parse Transactions with Helius SDK
DESCRIPTION: A straightforward example demonstrating how to use the Helius Rust SDK to parse a given transaction using the Enhanced Transactions API. It covers SDK initialization, constructing a parse request, and handling the response.

SOURCE: https://www.helius.dev/docs/sdks

LANGUAGE: Rust
CODE:
```
use helius::error::Result;
use helius::types::*;
use helius::Helius;

#[tokio::main]
async fn main() -> Result<()> {
    let api_key: &str = "your_api_key";
    let cluster: Cluster = Cluster::MainnetBeta;

    let helius: Helius = Helius::new(api_key, cluster).unwrap();

    let request: ParseTransactionsRequest = ParseTransactionsRequest {
        transactions: vec![
            "2sShYqqcWAcJiGc3oK74iFsYKgLCNiY2DsivMbaJGQT8pRzR8z5iBcdmTMXRobH8cZNZgeV9Ur9VjvLsykfFE2Li".to_string(),
        ],
    };

    let response: Result<Vec<EnhancedTransaction>, HeliusError> = helius.parse_transactions(request).await;
    println!("Assets: {:?}", response);

    Ok(())
}
```

----------------------------------------

TITLE: Rust: Initialize Yellowstone gRPC Client Project and Add Dependencies
DESCRIPTION: This snippet outlines the steps to create a new Rust project and configure its `Cargo.toml` file with the required dependencies for interacting with the Yellowstone gRPC interface, including `yellowstone-grpc-client` and `tokio`.

SOURCE: https://www.helius.dev/docs/grpc/quickstart

LANGUAGE: bash
CODE:
```
cargo new rust-yellowstone-client
cd rust-yellowstone-client
```

LANGUAGE: toml
CODE:
```
[dependencies]
yellowstone-grpc-client = "1.13.0"
yellowstone-grpc-proto = "1.13.0"
tokio = { version = "1.0", features = ["full"] }
anyhow = "1.0"
futures = "0.3"
tonic = "0.10"
tonic-health = "0.10"
hex = "0.4"
solana-sdk = "1.17"
log = "0.4"
```

----------------------------------------

TITLE: Fetch Limited Blocks from Start Slot (cURL)
DESCRIPTION: This example fetches a list of up to 5 confirmed block slots starting from slot `280000000` using cURL.

SOURCE: https://www.helius.dev/docs/rpc/guides/getblockswithlimit

LANGUAGE: cURL
CODE:
```
curl https://mainnet.helius-rpc.com/?api-key=<api-key> -X POST -H "Content-Type: application/json" -d \
  '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "getBlocksWithLimit",
    "params": [
      280000000,
      5
    ]
  }'
```

----------------------------------------

TITLE: Example Response: Get Epoch Schedule
DESCRIPTION: An example JSON response returned by the 'getEpochSchedule' RPC method, detailing the epoch schedule parameters such as slots per epoch, leader schedule slot offset, warmup status, first normal epoch, and first normal slot.

SOURCE: https://www.helius.dev/docs/api-reference/rpc/http/getepochschedule

LANGUAGE: JSON
CODE:
```
{
  "jsonrpc": "2.0",
  "id": "1",
  "result": {
    "slotsPerEpoch": 8192,
    "leaderScheduleSlotOffset": 8192,
    "warmup": true,
    "firstNormalEpoch": 8,
    "firstNormalSlot": 8160
  }
}
```

----------------------------------------

TITLE: cURL Example: Get All Webhooks
DESCRIPTION: Demonstrates how to make a GET request using cURL to retrieve a list of all configured webhooks from the Helius API.

SOURCE: https://www.helius.dev/docs/api-reference/webhooks/get-all-webhooks

LANGUAGE: cURL
CODE:
```
curl --request GET \
  --url https://api.helius.xyz/v0/webhooks
```

----------------------------------------

TITLE: Initialize New Project Directory with npm
DESCRIPTION: This snippet creates a new directory for the project, navigates into it, and initializes a new npm project with default settings.

SOURCE: https://www.helius.dev/docs/laserstream/grpc

LANGUAGE: bash
CODE:
```
mkdir laserstream-grpc-demo
cd laserstream-grpc-demo
npm init -y
```

----------------------------------------

TITLE: Get Token Supply using cURL
DESCRIPTION: Example cURL commands to call the `getTokenSupply` RPC method, demonstrating how to query the total supply of a token mint with and without a specified commitment level.

SOURCE: https://www.helius.dev/docs/rpc/guides/gettokensupply

LANGUAGE: cURL
CODE:
```
# Replace <TOKEN_MINT_PUBKEY> with the actual mint address
curl -X POST -H "Content-Type: application/json" -d \
  '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "getTokenSupply",
    "params": [
      "<TOKEN_MINT_PUBKEY>"
    ]
  }' \
  <YOUR_RPC_URL>

# Example with commitment level
curl -X POST -H "Content-Type: application/json" -d \
  '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "getTokenSupply",
    "params": [
      "<TOKEN_MINT_PUBKEY>",
      { "commitment": "confirmed" }
    ]
  }' \
  <YOUR_RPC_URL>
```

----------------------------------------

TITLE: Rust SDK Example: Transferring SOL
DESCRIPTION: This example demonstrates how to use the Helius Rust SDK (version 0.1.5+) to transfer 0.01 SOL. It shows how to initialize the Helius client, create a new keypair, define a transfer instruction, and send a smart transaction. Users need to set the HELIUS_API_KEY environment variable and fund the sender account.

SOURCE: https://www.helius.dev/docs/sending-transactions/send-with-sdk

LANGUAGE: Rust
CODE:
```
use helius::{config::Config, types::*, Helius};
use solana_sdk::{
    pubkey::Pubkey,
    signature::{Keypair, Signer},
    system_instruction,
};
use std::env;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let api_key = env::var("HELIUS_API_KEY").expect("HELIUS_API_KEY must be set");
    let helius = Helius::new(&api_key, Cluster::MainnetBeta)?;

    // Create a new keypair to send from
    let from_keypair = Keypair::new();
    let from_pubkey = from_keypair.pubkey();

    // (You'll need to fund this account first)

    // The address to send SOL to
    let to_pubkey = Pubkey::new_unique();

    // Create a simple instruction (transfer 0.01 SOL)
    let lamports = 10_000_000; // 0.01 SOL
    let instruction = system_instruction::transfer(&from_pubkey, &to_pubkey, lamports);

    // Build the config for the smart transaction
    let config = SmartTransactionConfig {
        instructions: vec![instruction],
        signers: vec![&from_keypair],
        ..Default::default()
    };

    // Send the transaction
    match helius.send_smart_transaction(&config).await {
        Ok(signature) => {
            println!("✅ Success! Transaction signature: {signature}");
            println!("https://explorer.solana.com/tx/{signature}?cluster=devnet");
        }
        Err(e) => {
            eprintln!("❌ Error sending transaction: {:?}", e);
        }
    }

    Ok(())
}
```

----------------------------------------

TITLE: Subscribe to Block Updates with Helius LaserStream SDK (TypeScript)
DESCRIPTION: This example shows how to subscribe to real-time block updates using the Helius LaserStream SDK. It configures a basic block subscription, allowing applications to receive notifications whenever new blocks are added to the blockchain.

SOURCE: https://www.helius.dev/docs/laserstream/grpc

LANGUAGE: typescript
CODE:
```
import { subscribe, CommitmentLevel, LaserstreamConfig, SubscribeRequest } from 'helius-laserstream'

async function main() {
    const subscriptionRequest: SubscribeRequest = {
        entry: {},
        accounts: {},
        accountsDataSlice: [],
        slots: {},
        blocks: {
            blocks: {
                accountInclude: []
            }
        },
        blocksMeta: {},
        transactions: {},
        transactionsStatus: {},
        commitment: CommitmentLevel.CONFIRMED,
    };

    const config: LaserstreamConfig = {
        apiKey: 'YOUR_API_KEY', // Replace with your key
        endpoint: 'https://laserstream-mainnet-ewr.helius-rpc.com', // Choose your closest region
    }

    await subscribe(config, subscriptionRequest, async (data) => {
        console.log(data);
    }, async (error) => {
        console.error(error);
    });
}
```

----------------------------------------

TITLE: Get Current Epoch Information (No Parameters)
DESCRIPTION: This example demonstrates how to fetch information about the current Solana epoch using the `getEpochInfo` RPC method without any additional parameters, relying on the default commitment level.

SOURCE: https://www.helius.dev/docs/rpc/guides/getepochinfo

LANGUAGE: cURL
CODE:
```
curl https://mainnet.helius-rpc.com/?api-key=<api-key> -X POST -H "Content-Type: application/json" -d \
  '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "getEpochInfo"
  }'
```

----------------------------------------

TITLE: Subscribe to Solana Program Updates with Helius
DESCRIPTION: This JavaScript function illustrates how to subscribe to real-time updates for any account owned by a specific Solana program using the Helius WebSocket connection. It sends a 'programSubscribe' request with 'jsonParsed' encoding and 'confirmed' commitment. An example demonstrates subscribing to the Token Program.

SOURCE: https://www.helius.dev/docs/data-streaming/quickstart

LANGUAGE: JavaScript
CODE:
```
// Subscribe to program updates
const subscribeToProgram = (programId) => {
  const subscriptionRequest = {
    jsonrpc: '2.0',
    id: 1,
    method: 'programSubscribe',
    params: [
      programId,
      {
        encoding: 'jsonParsed',
        commitment: 'confirmed'
      }
    ]
  };

  ws.send(JSON.stringify(subscriptionRequest));
};

// Example: Subscribe to the Token Program
subscribeToProgram('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA');
```

----------------------------------------

TITLE: Retrieve Blocks from Start Slot to Latest Confirmed Slot using getBlocks (cURL)
DESCRIPTION: This example shows how to fetch confirmed block slots starting from a given slot up to the latest confirmed block using the `getBlocks` RPC method via cURL, respecting the 500,000 slot range limit.

SOURCE: https://www.helius.dev/docs/rpc/guides/getblocks

LANGUAGE: cURL
CODE:
```
curl https://mainnet.helius-rpc.com/?api-key=<api-key> -X POST -H "Content-Type: application/json" -d \
  '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "getBlocks",
    "params": [
      260000000
    ]
  }'
```

----------------------------------------

TITLE: Helius Laserstream: Subscribe to Entry Data
DESCRIPTION: This TypeScript example focuses on subscribing specifically to 'entry' data using the `helius-laserstream` library. It illustrates how to modify the `SubscribeRequest` to include `entry: { entrySubscribe: {} }`, which configures the stream to receive all entry-related updates. The snippet also covers setting up the `LaserstreamConfig` and defining callbacks for processing received data and managing potential errors, providing a targeted approach to Laserstream subscriptions.

SOURCE: https://www.helius.dev/docs/laserstream/grpc

LANGUAGE: TypeScript
CODE:
```
import { subscribe, CommitmentLevel, LaserstreamConfig, SubscribeRequest } from 'helius-laserstream'

async function main() {
    const subscriptionRequest: SubscribeRequest = {
        entry: {
            entrySubscribe: {}  // Subscribe to all entries
        },
        accounts: {},
        accountsDataSlice: [],
        slots: {},
        blocks: {},
        blocksMeta: {},
        transactions: {},
        transactionsStatus: {},
        commitment: CommitmentLevel.CONFIRMED,
    };

    const config: LaserstreamConfig = {
        apiKey: 'YOUR_API_KEY', // Replace with your key
        endpoint: 'https://laserstream-mainnet-ewr.helius-rpc.com', // Choose your closest region
    }

    await subscribe(config, subscriptionRequest, async (data) => {
        console.log(data);
    }, async (error) => {
        console.error(error);
    });
}

main().catch(console.error);
```

----------------------------------------

TITLE: cURL Example to Get Webhook
DESCRIPTION: Example cURL command to retrieve webhook details by ID from the Helius API.

SOURCE: https://www.helius.dev/docs/api-reference/webhooks/get-webhook

LANGUAGE: cURL
CODE:
```
curl --request GET \\
  --url https://api.helius.xyz/v0/webhooks/{webhookID}
```

----------------------------------------

TITLE: Example Webhook Response Body
DESCRIPTION: An example JSON structure returned when successfully retrieving webhook details, showing various webhook properties and their expected types.

SOURCE: https://www.helius.dev/docs/api-reference/webhooks/get-webhook

LANGUAGE: JSON
CODE:
```
{
  "webhookID": "<string>",
  "wallet": "<string>",
  "webhookURL": "<string>",
  "transactionTypes": [
    "ACCEPT_ESCROW_ARTIST"
  ],
  "accountAddresses": [
    "<string>"
  ],
  "webhookType": "enhanced",
  "authHeader": "<string>"
}
```

----------------------------------------

TITLE: Example Response for Get Largest Accounts RPC
DESCRIPTION: An example JSON response showing the structure of the data returned by the `getLargestAccounts` RPC method. It includes the context (slot) and a list of accounts, each with their Lamport balance and public address.

SOURCE: https://www.helius.dev/docs/api-reference/rpc/http/getlargestaccounts

LANGUAGE: JSON
CODE:
```
{
  "jsonrpc": "2.0",
  "id": "1",
  "result": {
    "context": {
      "slot": 54
    },
    "value": [
      {
        "lamports": 999974,
        "address": "99P8ZgtJYe1buSK8JXkvpLh8xPsCFuLYhz9hQFNw93WJ"
      }
    ]
  }
}
```

----------------------------------------

TITLE: Setting Up a Node.js WebSocket Client
DESCRIPTION: This snippet provides the necessary shell commands to initialize a new Node.js project and install the `ws` library, which is essential for creating a WebSocket client to interact with Helius LaserStream.

SOURCE: https://www.helius.dev/docs/laserstream/websocket

LANGUAGE: Shell
CODE:
```
mkdir laserstream-enhanced-ws-demo
cd laserstream-enhanced-ws-demo
npm init -y
npm install ws
```

----------------------------------------

TITLE: getIndexerSlot Request Examples
DESCRIPTION: Examples demonstrating how to call the `getIndexerSlot` RPC method using various programming languages. Only the cURL example is provided in the source text.

SOURCE: https://www.helius.dev/docs/api-reference/zk-compression/getindexerslot

LANGUAGE: cURL
CODE:
```
curl --request POST \
  --url https://mainnet.helius-rpc.com/ \
  --header 'Content-Type: application/json' \
  --data '{
  "jsonrpc": "2.0",
  "id": "1",
  "method": "getIndexerSlot"
}'
```

LANGUAGE: Python
CODE:
```
Code not provided in source.
```

LANGUAGE: JavaScript
CODE:
```
Code not provided in source.
```

LANGUAGE: PHP
CODE:
```
Code not provided in source.
```

LANGUAGE: Go
CODE:
```
Code not provided in source.
```

LANGUAGE: Java
CODE:
```
Code not provided in source.
```

----------------------------------------

TITLE: Execute Helius NFT Fetcher Script (Node.js)
DESCRIPTION: This command executes the `fetchRandomNFT.js` script using the Node.js runtime. It initiates the API call to Helius, processes the response, and displays the fetched NFT details in the terminal.

SOURCE: https://www.helius.dev/docs/quickstart

LANGUAGE: bash
CODE:
```
node fetchRandomNFT.js
```

----------------------------------------

TITLE: Example Response: getTokenLargestAccounts RPC Call
DESCRIPTION: An example JSON response demonstrating the structure and typical values returned by the getTokenLargestAccounts RPC method.

SOURCE: https://www.helius.dev/docs/rpc/guides/gettokenlargestaccounts

LANGUAGE: JSON
CODE:
```
{
  "jsonrpc": "2.0",
  "result": {
    "context": { "slot": ********* },
    "value": [
      {
        "address": "TokenAccountPubkey1...",
        "amount": "*********0000",
        "decimals": 6,
        "uiAmount": 1000000.0,
        "uiAmountString": "1000000.0"
      },
      {
        "address": "TokenAccountPubkey2...",
        "amount": "************",
        "decimals": 6,
        "uiAmount": 500000.0,
        "uiAmountString": "500000.0"
      }
    ]
  },
  "id": 1
}
```

----------------------------------------

TITLE: Get Leader Schedule for Current Epoch
DESCRIPTION: This example fetches the complete leader schedule for the current epoch using the getLeaderSchedule RPC method.

SOURCE: https://www.helius.dev/docs/rpc/guides/getleaderschedule

LANGUAGE: cURL
CODE:
```
# Replace <api-key> with your Helius API key
curl https://mainnet.helius-rpc.com/?api-key=<api-key> -X POST -H "Content-Type: application/json" -d \
  '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "getLeaderSchedule"
  }'
```

----------------------------------------

TITLE: Create JavaScript File for Helius NFT Fetcher
DESCRIPTION: This command creates an empty JavaScript file named `fetchRandomNFT.js`. This file will contain the Node.js code for interacting with the Helius API to fetch NFT data.

SOURCE: https://www.helius.dev/docs/quickstart

LANGUAGE: bash
CODE:
```
touch fetchRandomNFT.js
```

----------------------------------------

TITLE: Get Epoch Schedule for the Cluster using cURL
DESCRIPTION: This example demonstrates how to fetch the epoch schedule from the Solana cluster using the `getEpochSchedule` RPC method via a cURL command.

SOURCE: https://www.helius.dev/docs/rpc/guides/getepochschedule

LANGUAGE: cURL
CODE:
```
curl https://mainnet.helius-rpc.com/?api-key=<api-key> -X POST -H "Content-Type: application/json" -d \
  '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "getEpochSchedule"
  }'
```

----------------------------------------

TITLE: Helius Webhook Event Payload Example
DESCRIPTION: This JSON structure illustrates the typical payload received by your webhook endpoint from Helius. It contains details about the blockchain event, including account data, description, event specifics, fees, fee payer, transaction signature, slot, timestamp, and event type.

SOURCE: https://www.helius.dev/docs/event-listening/quickstart

LANGUAGE: JSON
CODE:
```
{
  "accountData": [],
  "description": "",
  "events": {},
  "fee": 5000,
  "feePayer": "YOUR_ACCOUNT_ADDRESS",
  "signature": "TRANSACTION_SIGNATURE",
  "slot": 1234567,
  "timestamp": *************,
  "type": "TRANSACTION_TYPE"
}
```

----------------------------------------

TITLE: Connect to Helius WebSocket Endpoint using Node.js
DESCRIPTION: This JavaScript snippet demonstrates how to establish a real-time connection to the Helius WebSocket endpoint. It uses the `ws` library and requires your Helius API key as a query parameter for authentication.

SOURCE: https://www.helius.dev/docs/event-listening/quickstart

LANGUAGE: JavaScript
CODE:
```
const WebSocket = require('ws');

// Connect to Helius WebSocket endpoint
const ws = new WebSocket('wss://api.helius.xyz/v0/webhook-events?api-key=YOUR_API_KEY');
```

----------------------------------------

TITLE: Full Example: Solana Priority Fee Estimation and Application (JavaScript)
DESCRIPTION: This comprehensive example illustrates the end-to-end process of fetching and applying Solana priority fees using the Helius API. It initializes a connection, defines account keys, fetches estimates for different priority levels, and includes helper functions for both single-level and all-level estimations.

SOURCE: https://www.helius.dev/docs/priority-fee/estimating-fees-using-account-keys

LANGUAGE: JavaScript
CODE:
```
const {
  Connection,
  PublicKey,
  Transaction,
  ComputeBudgetProgram
} = require("@solana/web3.js");

// Initialize connection
const connection = new Connection("https://mainnet.helius-rpc.com/?api-key=YOUR_API_KEY");

async function estimatePriorityFee() {
  // Define the accounts involved in your transaction
  const accountKeys = [
    "2CiBfRKcERi2GgYn83UaGo1wFaYHHrXGGfnDaa2hxdEA",
    "FinesLuXpYnT9ENY55WXNdLJ5xzssrYMzwAcUXrDQBk9",
    "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
  ];

  // Get priority fee estimate for different priority levels
  const lowPriority = await getPriorityFeeEstimate(connection, accountKeys, "Low");
  const mediumPriority = await getPriorityFeeEstimate(connection, accountKeys, "Medium");
  const highPriority = await getPriorityFeeEstimate(connection, accountKeys, "High");

  console.log(`Low priority fee: ${lowPriority} micro-lamports`);
  console.log(`Medium priority fee: ${mediumPriority} micro-lamports`);
  console.log(`High priority fee: ${highPriority} micro-lamports`);

  // Get all priority levels at once
  const allLevels = await getAllPriorityLevels(connection, accountKeys);
  console.log("All priority levels:", allLevels);

  return {
    low: lowPriority,
    medium: mediumPriority,
    high: highPriority,
    allLevels
  };
}

// Helper function to get priority fee estimate
async function getPriorityFeeEstimate(connection, accountKeys, priorityLevel) {
  const response = await fetch(connection.rpcEndpoint, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      jsonrpc: "2.0",
      id: "1",
      method: "getPriorityFeeEstimate",
      params: [
        {
          accountKeys: accountKeys,
          options: {
            priorityLevel: priorityLevel,
            recommended: true
          }
        }
      ]
    })
  });

  const result = await response.json();

  if (result.error) {
    throw new Error(`Fee estimation failed: ${JSON.stringify(result.error)}`);
  }

  return result.result.priorityFeeEstimate;
}

// Helper function to get all priority levels
async function getAllPriorityLevels(connection, accountKeys) {
  const response = await fetch(connection.rpcEndpoint, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      jsonrpc: "2.0",
      id: "1",
      method: "getPriorityFeeEstimate",
      params: [
        {
          accountKeys: accountKeys,
          options: {
            includeAllPriorityFeeLevels: true
          }
        }
      ]
    })
  });

  const result = await response.json();

  if (result.error) {
    throw new Error(`Fee estimation failed: ${JSON.stringify(result.error)}`);
  }

  return result.result.priorityFeeLevels;
}

// Run the estimation
estimatePriorityFee();
```

----------------------------------------

TITLE: Initialize Helius WebSocket Connection
DESCRIPTION: This code snippet demonstrates how to establish a basic WebSocket connection to the Helius mainnet RPC. It includes handlers for connection open, received messages, errors, and connection closure. Remember to replace 'YOUR_API_KEY' with your actual Helius API key.

SOURCE: https://www.helius.dev/docs/data-streaming/quickstart

LANGUAGE: JavaScript
CODE:
```
// Initialize WebSocket connection
const ws = new WebSocket('wss://mainnet.helius-rpc.com?api-key=YOUR_API_KEY');

// Handle connection opened
ws.onopen = () => {
  console.log('WebSocket connection established');
  // You can send subscription requests once the connection is established
};

// Handle received messages
ws.onmessage = (event) => {
  const response = JSON.parse(event.data);
  console.log('Received data:', response);
};

// Handle errors
ws.onerror = (error) => {
  console.error('WebSocket error:', error);
};

// Handle connection close
ws.onclose = () => {
  console.log('WebSocket connection closed');
};
```

----------------------------------------

TITLE: Subscribe to Account Updates for Specific Addresses with Helius LaserStream SDK (TypeScript)
DESCRIPTION: This example illustrates how to subscribe to real-time updates for specific blockchain accounts using the Helius LaserStream SDK. It configures a subscription to monitor changes for the USDC mint account, demonstrating how to track activity on particular addresses.

SOURCE: https://www.helius.dev/docs/laserstream/grpc

LANGUAGE: typescript
CODE:
```
import { subscribe, CommitmentLevel, LaserstreamConfig, SubscribeRequest } from 'helius-laserstream'

async function main() {
    const subscriptionRequest: SubscribeRequest = {
        accounts: {
            accountSubscribe: {
                account: ["EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"], // USDC mint account
                owner: [],
                filters: []
            }
        },
        accountsDataSlice: [],
        commitment: CommitmentLevel.CONFIRMED,
        slots: {},
        transactions: {},
        transactionsStatus: {},
        blocks: {},
        blocksMeta: {},
        entry: {}
    };

    const config: LaserstreamConfig = {
        apiKey: 'YOUR_API_KEY', // Replace with your key
        endpoint: 'https://laserstream-mainnet-ewr.helius-rpc.com', // Choose your closest region
    }

    await subscribe(config, subscriptionRequest, async (data) => {
        console.log(data);
    }, async (error) => {
        console.error(error);
    });
}

main().catch(console.error);
```

----------------------------------------

TITLE: Example Request: Get SPL Token Supply with cURL
DESCRIPTION: Demonstrates how to make a POST request to the Helius RPC endpoint using cURL. This example retrieves the total supply of a specific SPL Token by providing its address in the request body.

SOURCE: https://www.helius.dev/docs/api-reference/rpc/http/gettokensupply

LANGUAGE: cURL
CODE:
```
curl --request POST \
  --url https://mainnet.helius-rpc.com/ \
  --header 'Content-Type: application/json' \
  --data '{
  "jsonrpc": "2.0",
  "id": "1",
  "method": "getTokenSupply",
  "params": [
    "7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU"
  ]
}'
```

----------------------------------------

TITLE: Initialize Subscribe Request Object
DESCRIPTION: Initializes a `SubscribeRequest` object with default empty configurations for various data types like accounts, transactions, slots, blocks, and entries. It sets the commitment level to `CONFIRMED`.

SOURCE: https://www.helius.dev/docs/laserstream/grpc

LANGUAGE: TypeScript
CODE:
```
const subscriptionRequest: SubscribeRequest = {
  commitment: CommitmentLevel.CONFIRMED,
  accountsDataSlice: [],
  transactions: {},
  accounts: {},
  slots: {},
  blocks: {},
  blocksMeta: {},
  entry: {},
}
```

----------------------------------------

TITLE: JSON Response Example: Get Compression Signatures
DESCRIPTION: Provides a sample JSON response structure for a successful `getCompressionSignaturesForTokenOwner` API call, including context, cursor, and a list of signature items with block time, signature, and slot.

SOURCE: https://www.helius.dev/docs/api-reference/zk-compression/getcompressionsignaturesfortokenowner

LANGUAGE: JSON
CODE:
```
{
  "context": {
    "slot": 100
  },
  "value": {
    "cursor": "<string>",
    "items": [
      {
        "blockTime": **********,
        "signature": "5J8H5sTvEhnGcB4R8K1n7mfoiWUD9RzPVGES7e3WxC7c",
        "slot": 100
      }
    ]
  }
}
```

----------------------------------------

TITLE: Rust: Initialize GrpcStreamManager and Subscribe to Token Program Transactions
DESCRIPTION: This example demonstrates how to initialize the `GrpcStreamManager`, create a subscription request for token program transactions, and start the subscription. It includes error handling for the connection process and shows how to specify filters for transactions, such as including specific accounts and commitment levels.

SOURCE: https://www.helius.dev/docs/grpc/transaction-monitoring

LANGUAGE: Rust
CODE:
```
#[tokio::main]
async fn main() -> Result<()> {
    // Initialize gRPC stream manager
    let manager = GrpcStreamManager::new(
        "your-grpc-url:2053",
        "your-x-token",
    ).await?;

    let mut manager_lock = manager.lock().await;

    // Create subscription request for token program transactions
    let request = SubscribeRequest {
        transactions: HashMap::from_iter(vec![(
            "transactions".to_string(),
            SubscribeRequestFilterTransactions {
                vote: Some(false),
                failed: Some(false),
                signature: None,
                account_include: vec!["TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA".to_string()],
                account_exclude: vec![],
                account_required: vec![],
            },
        )]),
        commitment: Some(CommitmentLevel::Confirmed as i32),
        ..Default::default()
    };

    println!("Starting subscription for Token Program transactions");

    // Start the subscription
    let result = manager_lock.connect(request).await;
    if let Err(e) = &result {
        println!("Subscription error: {:?}", e);
    }
    result?;

    Ok(())
}
```

----------------------------------------

TITLE: Get Slot Leaders for a Specific Range using cURL
DESCRIPTION: This cURL example demonstrates how to fetch the leaders for 5 slots, starting from a specified slot (e.g., *********), by sending a POST request to the Helius RPC endpoint with the `getSlotLeaders` method and parameters. Remember to replace `<api-key>` and `STARTING_SLOT`.

SOURCE: https://www.helius.dev/docs/rpc/guides/getslotleaders

LANGUAGE: cURL
CODE:
```
# Replace <api-key> with your Helius API key
# Replace STARTING_SLOT with a recent or future slot number
curl https://mainnet.helius-rpc.com/?api-key=<api-key> -X POST -H "Content-Type: application/json" -d \
  '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "getSlotLeaders",
    "params": [
      *********,
      5
    ]
  }'
```

----------------------------------------

TITLE: Example JSON Response for Get Signatures
DESCRIPTION: A sample JSON response demonstrating the successful output of the `getSignaturesForAddress` RPC method, including a transaction signature, slot, and confirmation status.

SOURCE: https://www.helius.dev/docs/api-reference/rpc/http/getsignaturesforaddress

LANGUAGE: JSON
CODE:
```
{
  "jsonrpc": "2.0",
  "id": 1,
  "result": [
    {
      "signature": "5h6xBEauJ3PK6SWCZ1PGjBvj8vDdWG3KpwATGy1ARAXFSDwt8GFXM7W5Ncn16wmqokgpiKRLuS83KUxyZyv2sUYv",
      "slot": 114,
      "err": null,
      "memo": null,
      "blockTime": null,
      "confirmationStatus": "finalized"
    }
  ]
}
```

----------------------------------------

TITLE: Fetch Blocks with Commitment Level (cURL)
DESCRIPTION: This example fetches up to 3 blocks starting from slot `290000000` using the `confirmed` commitment level via cURL.

SOURCE: https://www.helius.dev/docs/rpc/guides/getblockswithlimit

LANGUAGE: cURL
CODE:
```
curl https://mainnet.helius-rpc.com/?api-key=<api-key> -X POST -H "Content-Type: application/json" -d \
  '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "getBlocksWithLimit",
    "params": [
      290000000,
      3,
      { "commitment": "confirmed" }
    ]
  }'
```

----------------------------------------

TITLE: Quickstart: Stake in Three Simple Steps
DESCRIPTION: This snippet demonstrates how to create, sign, and broadcast a stake transaction using the Helius SDK and Solana Web3.js. It covers building the transaction, partial signing with the payer, and sending the raw transaction to the network.

SOURCE: https://www.helius.dev/docs/staking/how-to-stake-with-helius-programmatically

LANGUAGE: JavaScript
CODE:
```
import { Transaction } from '@solana/web3.js';
import bs58 from 'bs58';

/* 1. Build the unsigned transaction */
const { serializedTx, stakeAccountPubkey } =
  await helius.rpc.createStakeTransaction(payer.publicKey, 1.5); // amount in SOL, rent added automatically

/* 2. Sign with the fee payer */
const tx = Transaction.from(bs58.decode(serializedTx));
tx.partialSign(payer);          // the new stake account is already signed by the SDK

/* 3. Broadcast */
const sig = await helius.connection.sendRawTransaction(tx.serialize());
console.log(`Staked 1.5 SOL. Track it at https://orb.helius.dev/tx/${sig}`);
```

----------------------------------------

TITLE: Get First Available Block Slot using cURL
DESCRIPTION: This example fetches the slot of the oldest block available on the queried node using a cURL command.

SOURCE: https://www.helius.dev/docs/rpc/guides/getfirstavailableblock

LANGUAGE: cURL
CODE:
```
curl https://mainnet.helius-rpc.com/?api-key=<api-key> -X POST -H "Content-Type: application/json" -d \
  '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "getFirstAvailableBlock"
  }'
```

----------------------------------------

TITLE: Quick Start: Search Assets with Helius API in TypeScript
DESCRIPTION: This TypeScript snippet demonstrates how to make a basic RPC call to the Helius `searchAssets` endpoint. It includes a function to encapsulate the API request and an example call to fetch the first 50 compressed NFTs owned by a specific wallet address.

SOURCE: https://www.helius.dev/docs/das/search

LANGUAGE: TypeScript
CODE:
```
// Replace YOUR_API_KEY with your Helius API key
const url = `https://mainnet.helius-rpc.com/?api-key=YOUR_API_KEY`;

async function searchAssets(params) {
  const body = {
    jsonrpc: "2.0",
    id: "search-assets-example",
    method: "searchAssets",
    params,
  };
  const res = await fetch(url, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(body),
  });
  if (!res.ok) {
    throw new Error(`${res.status} ${res.statusText}`);
  }
  const { result } = await res.json();
  return result;
}

// Example: fetch first 50 compressed NFTs in a wallet
searchAssets({
  ownerAddress: "86xCnPeV69n6t3DnyGvkKobf9FdN2H9oiVDdaMpo2MMY",
  tokenType: "compressedNft",
  limit: 50,
}).then(console.log);
```

----------------------------------------

TITLE: Get Inflation Rewards for Multiple Addresses for a Specific Epoch with cURL
DESCRIPTION: This example fetches inflation rewards for multiple addresses for a specific epoch using cURL, demonstrating how to include the epoch and commitment level in the request.

SOURCE: https://www.helius.dev/docs/rpc/guides/getinflationreward

LANGUAGE: cURL
CODE:
```
# Replace <api-key> with your Helius API key
# Replace PUBKEY_1 and PUBKEY_2 with actual public keys
curl https://mainnet.helius-rpc.com/?api-key=<api-key> -X POST -H "Content-Type: application/json" -d \
  '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "getInflationReward",
    "params": [
      ["PUBKEY_1", "PUBKEY_2"],
      { "epoch": 450, "commitment": "confirmed" }
    ]
  }'
```

----------------------------------------

TITLE: Get Block Production for Current Epoch (All Validators) using cURL
DESCRIPTION: This example fetches block production data for all validators in the current epoch using the getBlockProduction RPC method via a cURL command.

SOURCE: https://www.helius.dev/docs/rpc/guides/getblockproduction

LANGUAGE: cURL
CODE:
```
curl https://mainnet.helius-rpc.com/?api-key=<api-key> -X POST -H "Content-Type: application/json" -d \
  '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "getBlockProduction"
  }'
```

----------------------------------------

TITLE: Connect to Helius RPC Node with JavaScript/TypeScript (Web3.js)
DESCRIPTION: This snippet demonstrates how to establish a connection to a Helius Solana RPC node using the `@solana/web3.js` library in JavaScript or TypeScript. It initializes a connection with your Helius RPC URL and tests it by fetching the Solana version.

SOURCE: https://www.helius.dev/docs/rpc/quickstart

LANGUAGE: JavaScript
CODE:
```
import { Connection } from '@solana/web3.js';

// Your Helius RPC URL
const rpcUrl = 'https://mainnet.helius-rpc.com/?api-key=YOUR_API_KEY';

// Create a connection
const connection = new Connection(rpcUrl);

// Test the connection
const getVersion = async () => {
  try {
    const version = await connection.getVersion();
    console.log('Connection successful!');
    console.log('Solana version:', version);
  } catch (error) {
    console.error('Connection failed:', error);
  }
};

getVersion();
```

----------------------------------------

TITLE: Example JSON Response for getVersion RPC
DESCRIPTION: Illustrates the successful JSON response structure for the getVersion RPC method, providing details such as the Solana core version and feature set.

SOURCE: https://www.helius.dev/docs/api-reference/rpc/http/getversion

LANGUAGE: JSON
CODE:
```
{
  "jsonrpc": "2.0",
  "id": "1",
  "result": {
    "solana-core": "1.16.7",
    "feature-set": 2891131721
  }
}
```

----------------------------------------

TITLE: Fetch Solana Assets by Owner with Helius Node.js SDK
DESCRIPTION: A quick start example demonstrating how to initialize the Helius Node.js SDK with an API key and use its RPC client to fetch a paginated list of digital assets owned by a specific Solana address. The response items are then logged to the console.

SOURCE: https://www.helius.dev/docs/sdks

LANGUAGE: JavaScript
CODE:
```
import { Helius } from "helius-sdk";

const helius = new Helius("YOUR_API_KEY");
const response = await helius.rpc.getAssetsByOwner({
  ownerAddress: "86xCnPeV69n6t3DnyGvkKobf9FdN2H9oiVDdaMpo2MMY",
  page: 1,
});

console.log(response.items);
```

----------------------------------------

TITLE: Example: Helius.dev Subscription Configuration JSON
DESCRIPTION: Illustrates a comprehensive JSON structure for configuring various Helius.dev subscriptions, including slots, accounts with filters, transactions, blocks, and commitment levels. This example demonstrates how to specify account filters using `memcmp`, `datasize`, `token_account_state`, and `lamports`.

SOURCE: https://www.helius.dev/docs/api-reference/laserstream/grpc/subscribe

LANGUAGE: JSON
CODE:
```
{
  "slots": {
      "slots": {}
  },
  "accounts": {
      "user-defined-label": {
          "account": [
              "DjUF9ASpyMbVpGJmTvzfSbCgUWj6JowwLh8dGAJzSPmu",
              "5U3bH5b6XtG99aVCE9ycvDgBKQx3fVT8WwTNbMToFuEr"
          ],
          "owner": [
              "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"
          ],
          "filter": {
              "memcmp": {
                  "offset": 0,
                  "bytes": "**********"
              },
              "datasize": 165,
              "token_account_state": true,
              "lamports": {
                  "gt": *********
              }
          },
          "nonempty_txn_signature": true
      }
  },
  "transactions": {},
  "blocks": {},
  "blocks_meta": {},
  "accounts_data_slice": [],
  "commitment": 1
}
```

----------------------------------------

TITLE: Get Block Production for a Specific Slot Range and Validator
DESCRIPTION: This example fetches block production data for a specific validator within a defined slot range using the `getBlockProduction` RPC method.

SOURCE: https://www.helius.dev/docs/rpc/guides/getblockproduction

LANGUAGE: cURL
CODE:
```
# Replace YOUR_VALIDATOR_IDENTITY_PUBKEY, START_SLOT, and END_SLOT.
curl https://mainnet.helius-rpc.com/?api-key=<api-key> -X POST -H "Content-Type: application/json" -d \
  '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "getBlockProduction",
    "params": [
      {
        "identity": "YOUR_VALIDATOR_IDENTITY_PUBKEY",
        "range": {
          "firstSlot": START_SLOT, # e.g., *********
          "lastSlot": END_SLOT    # e.g., *********
        }
      }
    ]
  }'
```

----------------------------------------

TITLE: Get Block Production for a Specific Validator in the Current Epoch
DESCRIPTION: This example fetches block production for a specific validator identity in the current epoch using the `getBlockProduction` RPC method.

SOURCE: https://www.helius.dev/docs/rpc/guides/getblockproduction

LANGUAGE: cURL
CODE:
```
# Replace YOUR_VALIDATOR_IDENTITY_PUBKEY with the actual validator's base58 public key.
curl https://mainnet.helius-rpc.com/?api-key=<api-key> -X POST -H "Content-Type: application/json" -d \
  '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "getBlockProduction",
    "params": [
      {
        "identity": "YOUR_VALIDATOR_IDENTITY_PUBKEY"
      }
    ]
  }'
```

----------------------------------------

TITLE: Connect to Helius RPC Node with Python (Solana.py)
DESCRIPTION: This snippet shows how to connect to a Helius Solana RPC node using the `solana.rpc.api.Client` in Python. It sets up a client with your Helius RPC URL and verifies the connection by retrieving the Solana version.

SOURCE: https://www.helius.dev/docs/rpc/quickstart

LANGUAGE: Python
CODE:
```
from solana.rpc.api import Client

# Your Helius RPC URL
rpc_url = 'https://mainnet.helius-rpc.com/?api-key=YOUR_API_KEY'

# Create a client
client = Client(rpc_url)

# Test the connection
try:
    version = client.get_version()
    print('Connection successful!')
    print(f'Solana version: {version}')
except Exception as e:
    print(f'Connection failed: {e}')
```

----------------------------------------

TITLE: Rust gRPC Stream Manager and Subscription Example
DESCRIPTION: This Rust code defines a `GrpcStreamManager` responsible for establishing and maintaining a gRPC stream connection. It includes error handling for connection loss, an asynchronous `reconnect` method with exponential backoff, and a `run` method to process incoming messages. The `main` function demonstrates how to initialize the manager, create a subscription request for a specific account (e.g., USDC mint), and start the stream, handling potential subscription errors.

SOURCE: https://www.helius.dev/docs/grpc/account-monitoring

LANGUAGE: Rust
CODE:
```
                            println!("Other update received: {:?}", msg);
                        }
                    }
                }
                Err(err) => {
                    error!("Error: {:?}", err);
                    self.is_connected = false;
                    Box::pin(self.reconnect(request.clone())).await?;
                    break;
                }
            }
        }

        Ok(())
    }

    /// Attempts to reconnect when the connection is lost
    ///
    /// # Arguments
    /// * `request` - The original subscription request to reestablish the connection
    async fn reconnect(&mut self, request: SubscribeRequest) -> Result<()> {
        if self.reconnect_attempts >= self.max_reconnect_attempts {
            println!("Max reconnection attempts reached");
            return Ok(());
        }

        self.reconnect_attempts += 1;
        println!("Reconnecting... Attempt {}", self.reconnect_attempts);

        let backoff = self.reconnect_interval * std::cmp::min(self.reconnect_attempts, 5);
        tokio::time::sleep(backoff).await;

        Box::pin(self.connect(request)).await
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize gRPC stream manager
    let manager = GrpcStreamManager::new(
        "your-grpc-url:2053",
        "your-x-token",
    ).await?;

    let mut manager_lock = manager.lock().await;

    // Create subscription request for USDC mint account
    let request = SubscribeRequest {
        accounts: HashMap::from_iter(vec![(
            "client".to_string(),
            SubscribeRequestFilterAccounts {
                account: vec!["EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v".to_string()],
                owner: vec![],
                filters: vec![],
            },
        )]),
        commitment: Some(CommitmentLevel::Confirmed as i32),
        ..Default::default()
    };

    // Start the subscription
    let result = manager_lock.connect(request).await;
    if let Err(e) = &result {
        println!("Subscription error: {:?}", e);
    }
    result?;

    Ok(())
}
```
