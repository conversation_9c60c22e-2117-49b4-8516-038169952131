# 🔐 Wallet Replacement Security Plan

## ⚠️ **CRITICAL SECURITY NOTICE**

Your current wallet private key has been **publicly exposed** and must be replaced immediately for production use.

**Current Compromised Wallet:**
- Address: `968aqNdZ5ZRLByseFo18YiSHywzqXj3S9z56Vwr62CGT`
- Balance: 0.096 SOL
- Status: ⚠️ **COMPROMISED** - Do not add more funds

---

## 🚨 **IMMEDIATE ACTION REQUIRED**

### Step 1: Secure Fund Transfer (URGENT)
```bash
# If you have significant funds in the compromised wallet, transfer them NOW
# Use the Jupiter swap interface or any wallet app to move funds to a secure wallet
```

### Step 2: Generate New Secure Wallet
```bash
# Install Solana CLI if not already installed
sh -c "$(curl -sSfL https://release.solana.com/v1.18.18/install)"

# Generate new keypair for production
solana-keygen new --outfile ~/.solana/mainnet-wallet.json

# Get the new public key
solana-keygen pubkey ~/.solana/mainnet-wallet.json

# Generate new keypair for development/testing
solana-keygen new --outfile ~/.solana/testnet-wallet.json
```

### Step 3: Update Environment Configuration
```bash
# Get base58 encoded private key for the new wallet
WALLET_PRIVATE_KEY=$(cat ~/.solana/mainnet-wallet.json | jq -r '.[0:32]' | base58)

# Get public key
WALLET_PUBLIC_KEY=$(solana-keygen pubkey ~/.solana/mainnet-wallet.json)
```

### Step 4: Update .env File
```env
# Replace these values in your .env file:
WALLET_PRIVATE_KEY=YOUR_NEW_SECURE_PRIVATE_KEY_HERE
ENCRYPTED_PRIVATE_KEY=YOUR_NEW_SECURE_PRIVATE_KEY_HERE  
TRADING_WALLET_ADDRESS=YOUR_NEW_PUBLIC_KEY_HERE
SOLANA_FEE_ACCOUNT=YOUR_NEW_PUBLIC_KEY_HERE
```

---

## 🔒 **PRODUCTION SECURITY CHECKLIST**

### Environment Security
- [ ] Generate completely new wallet keypair
- [ ] Never share private keys in any form
- [ ] Use environment variables, never hardcode keys
- [ ] Enable file system encryption
- [ ] Set proper file permissions (chmod 600)

### Wallet Security Best Practices
- [ ] **Hardware Wallet**: Consider using Ledger/Trezor for large amounts
- [ ] **Multi-sig**: Implement multi-signature for team wallets
- [ ] **Separation**: Use different wallets for dev/staging/production
- [ ] **Backup**: Securely backup wallet files (encrypted)
- [ ] **Monitoring**: Set up alerts for unusual activity

### Testing Strategy
```bash
# Start with testnet/devnet for all development
export SOLANA_RPC_URL="https://api.devnet.solana.com"

# Use minimal amounts for mainnet testing
# Start with 0.01 SOL maximum for initial tests
```

---

## 🧪 **TESTING PROCEDURE**

### Phase 1: Devnet Testing
```bash
# 1. Switch to devnet
export SOLANA_RPC_URL="https://api.devnet.solana.com"

# 2. Get devnet SOL from faucet
solana airdrop 2 --url devnet

# 3. Test all swap functionality on devnet first
npm run test
```

### Phase 2: Mainnet Testing (Small Amounts)
```bash
# 1. Switch to mainnet with new secure wallet
export SOLANA_RPC_URL="https://mainnet.helius-rpc.com/?api-key=ce149de7-a6e9-4ad8-9d80-6f24e5056550"

# 2. Test with minimal amounts (0.001 - 0.01 SOL)
# 3. Gradually increase amounts as confidence builds
```

---

## 🔧 **MIGRATION STEPS**

### 1. Backup Current Setup
```bash
# Backup current configuration
cp .env .env.backup
cp -r backend/prisma/migrations backend/prisma/migrations.backup
```

### 2. Generate New Credentials
```bash
# Generate new wallet
solana-keygen new --outfile ~/.solana/production-wallet.json --force

# Extract private key (keep this secret!)
NEW_PRIVATE_KEY=$(solana-keygen encode ~/.solana/production-wallet.json)
NEW_PUBLIC_KEY=$(solana-keygen pubkey ~/.solana/production-wallet.json)

echo "New Private Key: $NEW_PRIVATE_KEY"
echo "New Public Key: $NEW_PUBLIC_KEY"
```

### 3. Update Configuration
```bash
# Update .env file with new credentials
sed -i '' "s/WALLET_PRIVATE_KEY=.*/WALLET_PRIVATE_KEY=$NEW_PRIVATE_KEY/" .env
sed -i '' "s/TRADING_WALLET_ADDRESS=.*/TRADING_WALLET_ADDRESS=$NEW_PUBLIC_KEY/" .env
sed -i '' "s/SOLANA_FEE_ACCOUNT=.*/SOLANA_FEE_ACCOUNT=$NEW_PUBLIC_KEY/" .env
```

### 4. Test New Setup
```bash
# Run the verification script
node test_swap_setup.js

# Verify all systems are working
curl http://localhost:3001/health
```

---

## 🚨 **EMERGENCY PROCEDURES**

### If Wallet is Compromised
1. **IMMEDIATELY** stop all trading operations
2. **TRANSFER** all funds to a new secure wallet
3. **REVOKE** all API keys and regenerate
4. **AUDIT** all transaction history
5. **NOTIFY** team members and stakeholders

### Recovery Checklist
- [ ] New wallet generated and funded
- [ ] All environment variables updated
- [ ] Database updated with new wallet address
- [ ] All services restarted with new configuration
- [ ] Trading functionality tested and verified
- [ ] Security monitoring activated

---

## 📊 **MONITORING & ALERTS**

### Set Up Wallet Monitoring
```javascript
// Example monitoring setup
const WALLET_ADDRESS = process.env.TRADING_WALLET_ADDRESS
const ALERT_THRESHOLD = 0.1 // Alert if balance drops below 0.1 SOL

// Monitor balance every minute
setInterval(async () => {
  const balance = await connection.getBalance(new PublicKey(WALLET_ADDRESS))
  if (balance < ALERT_THRESHOLD * LAMPORTS_PER_SOL) {
    // Send alert
    console.log(`⚠️ LOW BALANCE ALERT: ${balance / LAMPORTS_PER_SOL} SOL`)
  }
}, 60000)
```

### Security Monitoring
- Monitor for unusual transaction patterns
- Set up alerts for large transactions
- Track API usage and rate limiting
- Log all trading activities

---

## ✅ **VERIFICATION CHECKLIST**

After wallet replacement, verify:

- [ ] New wallet generated with secure entropy
- [ ] Private key properly stored and secured
- [ ] Environment variables updated
- [ ] Database references updated
- [ ] All services restart cleanly
- [ ] Trading functionality works with small amounts
- [ ] Monitoring and alerts configured
- [ ] Backup procedures documented
- [ ] Team members notified of changes

---

## 🔗 **NEXT STEPS**

1. **Generate new wallet immediately**
2. **Test on devnet first**
3. **Migrate with minimal mainnet amounts**
4. **Implement monitoring**
5. **Document procedures**
6. **Train team on security practices**

**Remember: Security is not optional in DeFi. Take time to do this properly.**