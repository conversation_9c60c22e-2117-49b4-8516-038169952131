
> @memetrader-pro/frontend@1.0.0 dev
> next dev

  ▲ Next.js 14.2.30
  - Local:        http://localhost:3000

 ✓ Starting...
 ✓ Ready in 1422ms
(node:21354) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 ○ Compiling / ...
 ✓ Compiled / in 3.4s (919 modules)
 GET / 200 in 3619ms
 ✓ Compiled in 404ms (464 modules)
 GET / 200 in 46ms
 ✓ Compiled /_not-found in 296ms (899 modules)
 GET /api/wallet/balance?minValue=0.5 404 in 370ms
 GET /api/wallet/balance?minValue=0.5 404 in 17ms
 ✓ Compiled in 426ms (910 modules)
 GET / 200 in 143ms
 ✓ Compiled /_not-found in 116ms (899 modules)
 GET /api/wallet/balance?minValue=0.5 404 in 153ms
 GET /api/wallet/balance?minValue=0.5 404 in 12ms
 ⚠ Fast Refresh had to perform a full reload. Read more: https://nextjs.org/docs/messages/fast-refresh-reload
 ✓ Compiled in 229ms (913 modules)
 GET / 200 in 201ms
 GET /api/wallet/balance?minValue=0.5 404 in 29ms
 GET /api/wallet/balance?minValue=0.5 404 in 20ms
 ✓ Compiled in 304ms (913 modules)
 ⚠ Fast Refresh had to perform a full reload. Read more: https://nextjs.org/docs/messages/fast-refresh-reload
 GET / 200 in 91ms
 GET /api/wallet/balance?minValue=0.5 404 in 17ms
 GET /api/wallet/balance?minValue=0.5 404 in 15ms
 ✓ Compiled in 803ms (913 modules)
 GET /api/wallet/balance?minValue=0.5 404 in 21ms
 GET / 200 in 138ms
 GET /api/wallet/balance?minValue=0.5 404 in 65ms
 GET /api/wallet/balance?minValue=0.5 404 in 22ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ✓ Compiled in 921ms (910 modules)
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 GET / 200 in 847ms
 GET / 200 in 134ms
 ✓ Compiled /_not-found in 444ms (899 modules)
 GET /api/wallet/balance?minValue=0.5 404 in 17ms
 GET /api/wallet/balance?minValue=0.5 404 in 13ms
 GET /api/wallet/balance?minValue=0.5 404 in 18ms
 GET /api/wallet/balance?minValue=0.5 404 in 14ms
 GET / 200 in 138ms
 GET /api/wallet/balance?minValue=0.5 404 in 28ms
 GET /api/wallet/balance?minValue=0.5 404 in 26ms
 ✓ Compiled in 491ms (910 modules)
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 GET / 200 in 129ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 GET / 200 in 72ms
 GET / 200 in 51ms
 ✓ Compiled /_not-found in 120ms (899 modules)
 GET /api/wallet/balance?minValue=0.5 404 in 153ms
 GET /api/wallet/balance?minValue=0.5 404 in 19ms
 GET /api/wallet/balance?minValue=0.5 404 in 14ms
 GET /api/wallet/balance?minValue=0.5 404 in 16ms
 GET /api/wallet/balance?minValue=0.5 404 in 15ms
 GET /api/wallet/balance?minValue=0.5 404 in 14ms
 ⨯ ./src/components/trading/TokenSelector.tsx
Error: 
  [31mx[0m Expected ',', got 'function'
     ,-[[36;1;4m/Users/<USER>/dev/github/agmentcode/frontend/src/components/trading/TokenSelector.tsx[0m:392:1]
 [2m392[0m |   )
 [2m393[0m | }
 [2m394[0m | 
 [2m395[0m | function AddressInput({ 
     : [31;1m^^^^^^^^[0m
 [2m396[0m |   onValidate, 
 [2m397[0m |   onAddToPopular, 
 [2m398[0m |   isInPopular,
     `----

Caused by:
    Syntax Error

Import trace for requested module:
./src/components/trading/TokenSelector.tsx
./src/components/trading/SwapInterface.tsx
./src/app/page.tsx
 ✓ Compiled in 642ms (913 modules)
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 GET / 200 in 116ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 GET / 200 in 37ms
 GET / 200 in 24ms
 GET /api/wallet/balance?minValue=0.5 404 in 22ms
 GET /api/wallet/balance?minValue=0.5 404 in 15ms
 GET /api/wallet/balance?minValue=0.5 404 in 14ms
 GET /api/wallet/balance?minValue=0.5 404 in 15ms
 GET /api/wallet/balance?minValue=0.5 404 in 21ms
 GET /api/wallet/balance?minValue=0.5 404 in 13ms
 ✓ Compiled in 357ms (913 modules)
 ✓ Compiled in 346ms (913 modules)
 GET / 200 in 328ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 GET / 200 in 202ms
 GET /api/wallet/balance?minValue=0.5 404 in 117ms
 GET / 200 in 264ms
 GET /api/wallet/balance?minValue=0.5 404 in 19ms
 GET /api/wallet/balance?minValue=0.5 404 in 16ms
 GET /api/wallet/balance?minValue=0.5 404 in 16ms
 GET /api/wallet/balance?minValue=0.5 404 in 14ms
 GET /api/wallet/balance?minValue=0.5 404 in 17ms
 GET /api/wallet/balance?minValue=0.5 404 in 12ms
 ✓ Compiled in 301ms (913 modules)
 ✓ Compiled in 463ms (913 modules)
 ✓ Compiled in 329ms (913 modules)
 GET / 200 in 168ms
 GET /api/wallet/balance?minValue=0.5 404 in 46ms
 GET /api/wallet/balance?minValue=0.5 404 in 10ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ✓ Compiled in 1223ms (899 modules)
 GET /api/wallet/balance?minValue=0.5 404 in 694ms
 GET / 200 in 701ms
 GET /api/wallet/balance?minValue=0.5 404 in 49ms
 GET /api/wallet/balance?minValue=0.5 404 in 16ms
 GET / 200 in 61ms
 GET /api/wallet/balance?minValue=0.5 404 in 24ms
 GET /api/wallet/balance?minValue=0.5 404 in 13ms
 GET /api/wallet/balance?minValue=0.5 404 in 16ms
 GET /api/wallet/balance?minValue=0.5 404 in 13ms
 GET / 200 in 49ms
 GET /api/wallet/balance?minValue=0.5 404 in 27ms
 GET /api/wallet/balance?minValue=0.5 404 in 17ms
 ✓ Compiled in 324ms (464 modules)
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 GET / 200 in 56ms
 GET / 200 in 31ms
 GET / 200 in 51ms
 ✓ Compiled /_not-found in 125ms (899 modules)
 GET /api/wallet/balance?minValue=0.5 404 in 209ms
 GET /api/wallet/balance?minValue=0.5 404 in 16ms
 GET /api/wallet/balance?minValue=0.5 404 in 17ms
 GET /api/wallet/balance?minValue=0.5 404 in 16ms
 GET /api/wallet/balance?minValue=0.5 404 in 19ms
 GET /api/wallet/balance?minValue=0.5 404 in 14ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ✓ Compiled in 730ms (893 modules)
 ⨯ src/components/trading/TokenSelector.tsx (902:8) @ Popover
 ⨯ ReferenceError: Popover is not defined
    at TokenSelector (./src/components/trading/TokenSelector.tsx:1294:89)
digest: "1807905100"
[0m [90m 900 |[39m   [36mreturn[39m ([0m
[0m [90m 901 |[39m     [33m<[39m[33mdiv[39m className[33m=[39m{cn([32m"w-full"[39m[33m,[39m className)}[33m>[39m[0m
[0m[31m[1m>[22m[39m[90m 902 |[39m       [33m<[39m[33mPopover[39m key[33m=[39m[32m"token-selector-popover"[39m open[33m=[39m{open} onOpenChange[33m=[39m{setOpen}[33m>[39m[0m
[0m [90m     |[39m        [31m[1m^[22m[39m[0m
[0m [90m 903 |[39m         [33m<[39m[33mPopoverTrigger[39m asChild[33m>[39m[0m
[0m [90m 904 |[39m           [33m<[39m[33mButton[39m[0m
[0m [90m 905 |[39m             ref[33m=[39m{buttonRef}[0m
 GET / 500 in 489ms
 ⨯ src/components/trading/TokenSelector.tsx (902:8) @ Popover
 ⨯ ReferenceError: Popover is not defined
    at TokenSelector (./src/components/trading/TokenSelector.tsx:1294:89)
digest: "1807905100"
[0m [90m 900 |[39m   [36mreturn[39m ([0m
[0m [90m 901 |[39m     [33m<[39m[33mdiv[39m className[33m=[39m{cn([32m"w-full"[39m[33m,[39m className)}[33m>[39m[0m
[0m[31m[1m>[22m[39m[90m 902 |[39m       [33m<[39m[33mPopover[39m key[33m=[39m[32m"token-selector-popover"[39m open[33m=[39m{open} onOpenChange[33m=[39m{setOpen}[33m>[39m[0m
[0m [90m     |[39m        [31m[1m^[22m[39m[0m
[0m [90m 903 |[39m         [33m<[39m[33mPopoverTrigger[39m asChild[33m>[39m[0m
[0m [90m 904 |[39m           [33m<[39m[33mButton[39m[0m
[0m [90m 905 |[39m             ref[33m=[39m{buttonRef}[0m
 GET / 500 in 50ms
 ⨯ src/components/trading/TokenSelector.tsx (902:8) @ Popover
 ⨯ ReferenceError: Popover is not defined
    at TokenSelector (./src/components/trading/TokenSelector.tsx:1294:89)
digest: "1807905100"
[0m [90m 900 |[39m   [36mreturn[39m ([0m
[0m [90m 901 |[39m     [33m<[39m[33mdiv[39m className[33m=[39m{cn([32m"w-full"[39m[33m,[39m className)}[33m>[39m[0m
[0m[31m[1m>[22m[39m[90m 902 |[39m       [33m<[39m[33mPopover[39m key[33m=[39m[32m"token-selector-popover"[39m open[33m=[39m{open} onOpenChange[33m=[39m{setOpen}[33m>[39m[0m
[0m [90m     |[39m        [31m[1m^[22m[39m[0m
[0m [90m 903 |[39m         [33m<[39m[33mPopoverTrigger[39m asChild[33m>[39m[0m
[0m [90m 904 |[39m           [33m<[39m[33mButton[39m[0m
[0m [90m 905 |[39m             ref[33m=[39m{buttonRef}[0m
 GET / 500 in 121ms
 ⨯ ./src/components/trading/TokenSelector.tsx
Error: 
  [31mx[0m Unexpected token `div`. Expected jsx identifier
     ,-[[36;1;4m/Users/<USER>/dev/github/agmentcode/frontend/src/components/trading/TokenSelector.tsx[0m:898:1]
 [2m898[0m |   }, [isUserVerified, removeUserVerification, addUserVerification])
 [2m899[0m | 
 [2m900[0m |   return (
 [2m901[0m |     <div className={cn("w-full", className)}>
     : [31;1m     ^^^[0m
 [2m902[0m |       <StableDropdown
 [2m903[0m |         open={open}
 [2m904[0m |         onOpenChange={setOpen}
     `----

Caused by:
    Syntax Error

Import trace for requested module:
./src/components/trading/TokenSelector.tsx
./src/components/trading/SwapInterface.tsx
./src/app/page.tsx
 ⨯ ./src/components/trading/TokenSelector.tsx
Error: 
  [31mx[0m Unexpected token `div`. Expected jsx identifier
     ,-[[36;1;4m/Users/<USER>/dev/github/agmentcode/frontend/src/components/trading/TokenSelector.tsx[0m:898:1]
 [2m898[0m |   }, [isUserVerified, removeUserVerification, addUserVerification])
 [2m899[0m | 
 [2m900[0m |   return (
 [2m901[0m |     <div className={cn("w-full", className)}>
     : [31;1m     ^^^[0m
 [2m902[0m |       <StableDropdown
 [2m903[0m |         open={open}
 [2m904[0m |         onOpenChange={setOpen}
     `----

Caused by:
    Syntax Error

Import trace for requested module:
./src/components/trading/TokenSelector.tsx
./src/components/trading/SwapInterface.tsx
./src/app/page.tsx
 ⨯ ./src/components/trading/TokenSelector.tsx
Error: 
  [31mx[0m Unexpected token `div`. Expected jsx identifier
     ,-[[36;1;4m/Users/<USER>/dev/github/agmentcode/frontend/src/components/trading/TokenSelector.tsx[0m:898:1]
 [2m898[0m |   }, [isUserVerified, removeUserVerification, addUserVerification])
 [2m899[0m | 
 [2m900[0m |   return (
 [2m901[0m |     <div className={cn("w-full", className)}>
     : [31;1m     ^^^[0m
 [2m902[0m |       <StableDropdown
 [2m903[0m |         open={open}
 [2m904[0m |         onOpenChange={setOpen}
     `----

Caused by:
    Syntax Error

Import trace for requested module:
./src/components/trading/TokenSelector.tsx
./src/components/trading/SwapInterface.tsx
./src/app/page.tsx
 ✓ Compiled in 706ms (895 modules)
 GET / 200 in 59ms
 GET / 200 in 34ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 GET / 200 in 48ms
 GET / 200 in 56ms
 GET /api/wallet/balance?minValue=0.5 404 in 27ms
 GET /api/wallet/balance?minValue=0.5 404 in 31ms
 GET /api/wallet/balance?minValue=0.5 404 in 30ms
 GET /api/wallet/balance?minValue=0.5 404 in 26ms
 GET /api/wallet/balance?minValue=0.5 404 in 17ms
 GET /api/wallet/balance?minValue=0.5 404 in 18ms
 ⚠ Found a change in next.config.js. Restarting the server to apply the changes...
  ▲ Next.js 14.2.30
  - Local:        http://localhost:3000

 ✓ Starting...
 ✓ Ready in 1846ms
 ○ Compiling /_not-found ...
(node:28980) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 ✓ Compiled /_not-found in 4.6s (472 modules)
 GET /api/wallet/balance?minValue=0.5 404 in 4874ms
 ✓ Compiled in 243ms (449 modules)
 GET /api/wallet/balance?minValue=0.5 404 in 232ms
 GET /api/wallet/balance?minValue=0.5 404 in 16ms
 ✓ Compiled in 234ms (241 modules)
 ✓ Compiled in 239ms (241 modules)
 ✓ Compiled in 329ms (241 modules)
 ✓ Compiled in 208ms (241 modules)
 GET /api/wallet/balance?minValue=0.5 404 in 49ms
 GET /api/wallet/balance?minValue=0.5 404 in 13ms
 GET /api/wallet/balance?minValue=0.5 404 in 19ms
 ✓ Compiled in 281ms (241 modules)
 ✓ Compiled in 258ms (241 modules)
 ✓ Compiled in 290ms (241 modules)
 ✓ Compiled in 263ms (239 modules)
 ✓ Compiled in 203ms (239 modules)
 ✓ Compiled in 251ms (239 modules)
 ✓ Compiled in 260ms (239 modules)
 ✓ Compiled /_not-found in 264ms (449 modules)
 GET /api/wallet/balance?minValue=0.5 404 in 368ms
 GET /api/wallet/balance?minValue=0.5 404 in 14ms
 ✓ Compiled in 198ms (241 modules)
 GET /api/wallet/balance?minValue=0.5 404 in 17ms
 ○ Compiling / ...
 ✓ Compiled / in 3.1s (883 modules)
 ⨯ src/components/trading/TokenSelector.tsx (872:7) @ customPopularTokens
 ⨯ ReferenceError: customPopularTokens is not defined
    at TokenSelector (./src/components/trading/TokenSelector.tsx:1224:9)
digest: "1200284863"
[0m [90m 870 |[39m       [33m...[39muniqueCustomTokens[33m.[39mmap(token [33m=>[39m ({ [33m...[39mtoken[33m,[39m isCustom[33m:[39m [36mtrue[39m }))[0m
[0m [90m 871 |[39m     ][0m
[0m[31m[1m>[22m[39m[90m 872 |[39m   }[33m,[39m [customPopularTokens])[0m
[0m [90m     |[39m       [31m[1m^[22m[39m[0m
[0m [90m 873 |[39m[0m
[0m [90m 874 |[39m   [90m// Memoize trigger content with stable dependencies[39m[0m
[0m [90m 875 |[39m   [36mconst[39m triggerContent [33m=[39m useMemo(() [33m=>[39m {[0m
 GET / 500 in 3565ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ✓ Compiled in 776ms (897 modules)
 ⨯ src/components/trading/TokenSelector.tsx (885:22) @ customTokenNames
 ⨯ ReferenceError: customTokenNames is not defined
    at TokenSelector (./src/components/trading/TokenSelector.tsx:1264:9)
digest: "195624923"
[0m [90m 883 |[39m       [33m<[39m[33m/[39m[33mdiv[39m[33m>[39m[0m
[0m [90m 884 |[39m     )[0m
[0m[31m[1m>[22m[39m[90m 885 |[39m   }[33m,[39m [selectedToken[33m,[39m customTokenNames[33m,[39m userVerifiedTokens[33m,[39m placeholder])[0m
[0m [90m     |[39m                      [31m[1m^[22m[39m[0m
[0m [90m 886 |[39m[0m
[0m [90m 887 |[39m   [36mconst[39m handleTokenSelect [33m=[39m useCallback((token[33m:[39m [33mToken[39m) [33m=>[39m {[0m
[0m [90m 888 |[39m     addRecentToken(token) [90m// Add to recent tokens[39m[0m
 ⨯ src/components/trading/TokenSelector.tsx (885:22) @ customTokenNames
 ⨯ ReferenceError: customTokenNames is not defined
    at TokenSelector (./src/components/trading/TokenSelector.tsx:1264:9)
digest: "195624923"
[0m [90m 883 |[39m       [33m<[39m[33m/[39m[33mdiv[39m[33m>[39m[0m
[0m [90m 884 |[39m     )[0m
[0m[31m[1m>[22m[39m[90m 885 |[39m   }[33m,[39m [selectedToken[33m,[39m customTokenNames[33m,[39m userVerifiedTokens[33m,[39m placeholder])[0m
[0m [90m     |[39m                      [31m[1m^[22m[39m[0m
[0m [90m 886 |[39m[0m
[0m [90m 887 |[39m   [36mconst[39m handleTokenSelect [33m=[39m useCallback((token[33m:[39m [33mToken[39m) [33m=>[39m {[0m
[0m [90m 888 |[39m     addRecentToken(token) [90m// Add to recent tokens[39m[0m
 GET / 500 in 307ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ✓ Compiled in 671ms (894 modules)
 GET / 200 in 311ms
 ✓ Compiled /_not-found in 132ms (883 modules)
 GET /api/wallet/balance?minValue=0.5 404 in 177ms
 GET /api/wallet/balance?minValue=0.5 404 in 64ms
 GET / 200 in 165ms
 GET /api/wallet/balance?minValue=0.5 404 in 38ms
 GET /api/wallet/balance?minValue=0.5 404 in 399ms
 GET /api/wallet/balance?minValue=0.5 404 in 24ms
 GET /api/wallet/balance?minValue=0.5 404 in 42ms
 GET /api/wallet/balance?minValue=0.5 404 in 48ms
 GET /api/wallet/balance?minValue=0.5 404 in 36ms
 GET /api/wallet/balance?minValue=0.5 404 in 32ms
 GET /api/wallet/balance?minValue=0.5 404 in 51ms
 GET /api/wallet/balance?minValue=0.5 404 in 32ms
 GET /api/wallet/balance?minValue=0.5 404 in 35ms
 GET /api/wallet/balance?minValue=0.5 404 in 50ms
 GET /api/wallet/balance?minValue=0.5 404 in 18ms
 GET /api/wallet/balance?minValue=0.5 404 in 96ms
 GET /api/wallet/balance?minValue=0.5 404 in 18ms
 GET /api/wallet/balance?minValue=0.5 404 in 28ms
 ✓ Compiled in 1080ms (883 modules)
 ○ Compiling /api/wallet/balance ...
 ✓ Compiled /api/wallet/balance in 1493ms (580 modules)
 GET /api/wallet/balance?minValue=0.5 500 in 1765ms
 GET /api/wallet/balance?minValue=0.5 500 in 6ms
 GET /api/wallet/balance?minValue=0.5 500 in 38ms
 GET /api/wallet/balance?minValue=0.5 500 in 24ms
 GET /api/wallet/balance?minValue=0.5 500 in 17ms
 GET /api/wallet/balance?minValue=0.5 500 in 25ms
 GET /api/wallet/balance?minValue=0.5 500 in 20ms
   Reload env: .env.local
 ✓ Compiled in 886ms (1021 modules)
 GET /api/wallet/balance?minValue=0.5 200 in 1296ms
 GET /api/wallet/balance 200 in 426ms
 GET /api/wallet/balance?minValue=0.5 200 in 661ms
 GET /api/wallet/balance?minValue=0.5 200 in 449ms
 GET /api/wallet/balance?minValue=0.5 200 in 512ms
Wallet balance error: Error: failed to get balance of account 968aqNdZ5ZRLByseFo18YiSHywzqXj3S9z56Vwr62CGT: TypeError: fetch failed
    at eval (webpack-internal:///(rsc)/../node_modules/@solana/web3.js/lib/index.esm.js:6231:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Connection.getBalance (webpack-internal:///(rsc)/../node_modules/@solana/web3.js/lib/index.esm.js:6230:12)
    at async GET (webpack-internal:///(rsc)/./src/app/api/wallet/balance/route.ts:30:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:57228
    at async eT.execute (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:46851)
    at async eT.handle (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:58760)
    at async doRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1366:42)
    at async cacheEntry.responseCache.get.routeKind (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1588:28)
    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1496:28)
    at async DevServer.renderPageComponent (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1924:24)
    at async DevServer.renderToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1962:32)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:272:17)
    at async DevServer.handleRequestImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:818:17)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:359:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13)
 GET /api/wallet/balance?minValue=0.5 500 in 99ms
Wallet balance error: Error: failed to get balance of account 968aqNdZ5ZRLByseFo18YiSHywzqXj3S9z56Vwr62CGT: TypeError: fetch failed
    at eval (webpack-internal:///(rsc)/../node_modules/@solana/web3.js/lib/index.esm.js:6231:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Connection.getBalance (webpack-internal:///(rsc)/../node_modules/@solana/web3.js/lib/index.esm.js:6230:12)
    at async GET (webpack-internal:///(rsc)/./src/app/api/wallet/balance/route.ts:30:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:57228
    at async eT.execute (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:46851)
    at async eT.handle (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:58760)
    at async doRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1366:42)
    at async cacheEntry.responseCache.get.routeKind (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1588:28)
    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1496:28)
    at async DevServer.renderPageComponent (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1924:24)
    at async DevServer.renderToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1962:32)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:272:17)
    at async DevServer.handleRequestImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:818:17)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:359:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13)
 GET /api/wallet/balance?minValue=0.5 500 in 57ms
Wallet balance error: Error: failed to get balance of account 968aqNdZ5ZRLByseFo18YiSHywzqXj3S9z56Vwr62CGT: TypeError: fetch failed
    at eval (webpack-internal:///(rsc)/../node_modules/@solana/web3.js/lib/index.esm.js:6231:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Connection.getBalance (webpack-internal:///(rsc)/../node_modules/@solana/web3.js/lib/index.esm.js:6230:12)
    at async GET (webpack-internal:///(rsc)/./src/app/api/wallet/balance/route.ts:30:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:57228
    at async eT.execute (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:46851)
    at async eT.handle (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:58760)
    at async doRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1366:42)
    at async cacheEntry.responseCache.get.routeKind (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1588:28)
    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1496:28)
    at async DevServer.renderPageComponent (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1924:24)
    at async DevServer.renderToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1962:32)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:272:17)
    at async DevServer.handleRequestImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:818:17)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:359:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13)
 GET /api/wallet/balance?minValue=0.5 500 in 60ms
Wallet balance error: Error: failed to get balance of account 968aqNdZ5ZRLByseFo18YiSHywzqXj3S9z56Vwr62CGT: TypeError: fetch failed
    at eval (webpack-internal:///(rsc)/../node_modules/@solana/web3.js/lib/index.esm.js:6231:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Connection.getBalance (webpack-internal:///(rsc)/../node_modules/@solana/web3.js/lib/index.esm.js:6230:12)
    at async GET (webpack-internal:///(rsc)/./src/app/api/wallet/balance/route.ts:30:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:57228
    at async eT.execute (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:46851)
    at async eT.handle (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:58760)
    at async doRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1366:42)
    at async cacheEntry.responseCache.get.routeKind (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1588:28)
    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1496:28)
    at async DevServer.renderPageComponent (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1924:24)
    at async DevServer.renderToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1962:32)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:272:17)
    at async DevServer.handleRequestImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:818:17)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:359:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13)
 GET /api/wallet/balance?minValue=0.5 500 in 86ms
Wallet balance error: Error: failed to get balance of account 968aqNdZ5ZRLByseFo18YiSHywzqXj3S9z56Vwr62CGT: TypeError: fetch failed
    at eval (webpack-internal:///(rsc)/../node_modules/@solana/web3.js/lib/index.esm.js:6231:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Connection.getBalance (webpack-internal:///(rsc)/../node_modules/@solana/web3.js/lib/index.esm.js:6230:12)
    at async GET (webpack-internal:///(rsc)/./src/app/api/wallet/balance/route.ts:30:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:57228
    at async eT.execute (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:46851)
    at async eT.handle (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:58760)
    at async doRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1366:42)
    at async cacheEntry.responseCache.get.routeKind (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1588:28)
    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1496:28)
    at async DevServer.renderPageComponent (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1924:24)
    at async DevServer.renderToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1962:32)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:272:17)
    at async DevServer.handleRequestImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:818:17)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:359:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13)
 GET /api/wallet/balance?minValue=0.5 500 in 65ms
Wallet balance error: Error: failed to get balance of account 968aqNdZ5ZRLByseFo18YiSHywzqXj3S9z56Vwr62CGT: TypeError: fetch failed
    at eval (webpack-internal:///(rsc)/../node_modules/@solana/web3.js/lib/index.esm.js:6231:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Connection.getBalance (webpack-internal:///(rsc)/../node_modules/@solana/web3.js/lib/index.esm.js:6230:12)
    at async GET (webpack-internal:///(rsc)/./src/app/api/wallet/balance/route.ts:30:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:57228
    at async eT.execute (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:46851)
    at async eT.handle (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:58760)
    at async doRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1366:42)
    at async cacheEntry.responseCache.get.routeKind (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1588:28)
    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1496:28)
    at async DevServer.renderPageComponent (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1924:24)
    at async DevServer.renderToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1962:32)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:272:17)
    at async DevServer.handleRequestImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:818:17)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:359:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13)
 GET /api/wallet/balance?minValue=0.5 500 in 80ms
Wallet balance error: Error: failed to get balance of account 968aqNdZ5ZRLByseFo18YiSHywzqXj3S9z56Vwr62CGT: TypeError: fetch failed
    at eval (webpack-internal:///(rsc)/../node_modules/@solana/web3.js/lib/index.esm.js:6231:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Connection.getBalance (webpack-internal:///(rsc)/../node_modules/@solana/web3.js/lib/index.esm.js:6230:12)
    at async GET (webpack-internal:///(rsc)/./src/app/api/wallet/balance/route.ts:30:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:57228
    at async eT.execute (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:46851)
    at async eT.handle (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:58760)
    at async doRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1366:42)
    at async cacheEntry.responseCache.get.routeKind (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1588:28)
    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1496:28)
    at async DevServer.renderPageComponent (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1924:24)
    at async DevServer.renderToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1962:32)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:272:17)
    at async DevServer.handleRequestImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:818:17)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:359:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13)
 GET /api/wallet/balance?minValue=0.5 500 in 81ms
Wallet balance error: Error: failed to get balance of account 968aqNdZ5ZRLByseFo18YiSHywzqXj3S9z56Vwr62CGT: TypeError: fetch failed
    at eval (webpack-internal:///(rsc)/../node_modules/@solana/web3.js/lib/index.esm.js:6231:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Connection.getBalance (webpack-internal:///(rsc)/../node_modules/@solana/web3.js/lib/index.esm.js:6230:12)
    at async GET (webpack-internal:///(rsc)/./src/app/api/wallet/balance/route.ts:30:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:57228
    at async eT.execute (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:46851)
    at async eT.handle (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:58760)
    at async doRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1366:42)
    at async cacheEntry.responseCache.get.routeKind (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1588:28)
    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1496:28)
    at async DevServer.renderPageComponent (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1924:24)
    at async DevServer.renderToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1962:32)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:272:17)
    at async DevServer.handleRequestImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:818:17)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:359:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13)
 GET /api/wallet/balance?minValue=0.5 500 in 113ms
Wallet balance error: Error: failed to get balance of account 968aqNdZ5ZRLByseFo18YiSHywzqXj3S9z56Vwr62CGT: TypeError: fetch failed
    at eval (webpack-internal:///(rsc)/../node_modules/@solana/web3.js/lib/index.esm.js:6231:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Connection.getBalance (webpack-internal:///(rsc)/../node_modules/@solana/web3.js/lib/index.esm.js:6230:12)
    at async GET (webpack-internal:///(rsc)/./src/app/api/wallet/balance/route.ts:30:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:57228
    at async eT.execute (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:46851)
    at async eT.handle (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:58760)
    at async doRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1366:42)
    at async cacheEntry.responseCache.get.routeKind (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1588:28)
    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1496:28)
    at async DevServer.renderPageComponent (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1924:24)
    at async DevServer.renderToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1962:32)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:272:17)
    at async DevServer.handleRequestImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:818:17)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:359:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13)
 GET /api/wallet/balance?minValue=0.5 500 in 152ms
Wallet balance error: Error: failed to get balance of account 968aqNdZ5ZRLByseFo18YiSHywzqXj3S9z56Vwr62CGT: TypeError: fetch failed
    at eval (webpack-internal:///(rsc)/../node_modules/@solana/web3.js/lib/index.esm.js:6231:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Connection.getBalance (webpack-internal:///(rsc)/../node_modules/@solana/web3.js/lib/index.esm.js:6230:12)
    at async GET (webpack-internal:///(rsc)/./src/app/api/wallet/balance/route.ts:30:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:57228
    at async eT.execute (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:46851)
    at async eT.handle (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:58760)
    at async doRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1366:42)
    at async cacheEntry.responseCache.get.routeKind (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1588:28)
    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1496:28)
    at async DevServer.renderPageComponent (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1924:24)
    at async DevServer.renderToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1962:32)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:272:17)
    at async DevServer.handleRequestImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:818:17)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:359:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13)
 GET /api/wallet/balance?minValue=0.5 500 in 43ms
Wallet balance error: Error: failed to get balance of account 968aqNdZ5ZRLByseFo18YiSHywzqXj3S9z56Vwr62CGT: TypeError: fetch failed
    at eval (webpack-internal:///(rsc)/../node_modules/@solana/web3.js/lib/index.esm.js:6231:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Connection.getBalance (webpack-internal:///(rsc)/../node_modules/@solana/web3.js/lib/index.esm.js:6230:12)
    at async GET (webpack-internal:///(rsc)/./src/app/api/wallet/balance/route.ts:30:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:57228
    at async eT.execute (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:46851)
    at async eT.handle (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:58760)
    at async doRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1366:42)
    at async cacheEntry.responseCache.get.routeKind (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1588:28)
    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1496:28)
    at async DevServer.renderPageComponent (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1924:24)
    at async DevServer.renderToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1962:32)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:272:17)
    at async DevServer.handleRequestImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:818:17)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:359:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13)
 GET /api/wallet/balance?minValue=0.5 500 in 27ms
Wallet balance error: Error: failed to get balance of account 968aqNdZ5ZRLByseFo18YiSHywzqXj3S9z56Vwr62CGT: TypeError: fetch failed
    at eval (webpack-internal:///(rsc)/../node_modules/@solana/web3.js/lib/index.esm.js:6231:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Connection.getBalance (webpack-internal:///(rsc)/../node_modules/@solana/web3.js/lib/index.esm.js:6230:12)
    at async GET (webpack-internal:///(rsc)/./src/app/api/wallet/balance/route.ts:30:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:57228
    at async eT.execute (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:46851)
    at async eT.handle (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:58760)
    at async doRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1366:42)
    at async cacheEntry.responseCache.get.routeKind (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1588:28)
    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1496:28)
    at async DevServer.renderPageComponent (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1924:24)
    at async DevServer.renderToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1962:32)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:272:17)
    at async DevServer.handleRequestImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:818:17)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:359:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13)
 GET /api/wallet/balance?minValue=0.5 500 in 35ms
Wallet balance error: Error: failed to get balance of account 968aqNdZ5ZRLByseFo18YiSHywzqXj3S9z56Vwr62CGT: TypeError: fetch failed
    at eval (webpack-internal:///(rsc)/../node_modules/@solana/web3.js/lib/index.esm.js:6231:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Connection.getBalance (webpack-internal:///(rsc)/../node_modules/@solana/web3.js/lib/index.esm.js:6230:12)
    at async GET (webpack-internal:///(rsc)/./src/app/api/wallet/balance/route.ts:30:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:57228
    at async eT.execute (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:46851)
    at async eT.handle (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:58760)
    at async doRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1366:42)
    at async cacheEntry.responseCache.get.routeKind (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1588:28)
    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1496:28)
    at async DevServer.renderPageComponent (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1924:24)
    at async DevServer.renderToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1962:32)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:272:17)
    at async DevServer.handleRequestImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:818:17)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:359:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13)
 GET /api/wallet/balance?minValue=0.5 500 in 68ms
Wallet balance error: Error: failed to get balance of account 968aqNdZ5ZRLByseFo18YiSHywzqXj3S9z56Vwr62CGT: TypeError: fetch failed
    at eval (webpack-internal:///(rsc)/../node_modules/@solana/web3.js/lib/index.esm.js:6231:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Connection.getBalance (webpack-internal:///(rsc)/../node_modules/@solana/web3.js/lib/index.esm.js:6230:12)
    at async GET (webpack-internal:///(rsc)/./src/app/api/wallet/balance/route.ts:30:25)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:57228
    at async eT.execute (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:46851)
    at async eT.handle (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:58760)
    at async doRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1366:42)
    at async cacheEntry.responseCache.get.routeKind (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1588:28)
    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1496:28)
    at async DevServer.renderPageComponent (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1924:24)
    at async DevServer.renderToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1962:32)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:272:17)
    at async DevServer.handleRequestImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:818:17)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:359:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13)
 GET /api/wallet/balance?minValue=0.5 500 in 81ms
 GET /api/wallet/balance?minValue=0.5 200 in 558ms
 GET /api/wallet/balance?minValue=0.5 200 in 3871ms
 ✓ Compiled in 3.8s (1035 modules)
 ⨯ SyntaxError: Unexpected end of JSON input
    at JSON.parse (<anonymous>)
    at loadManifest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/load-manifest.js:36:25)
    at DevServer.getNextFontManifest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:750:47)
    at DevServer.findPageComponents (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:571:43)
    at async DevServer.renderPageComponent (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1910:24)
    at async DevServer.renderToResponseImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:1962:32)
    at async DevServer.pipeImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:922:25)
    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/next-server.js:272:17)
    at async DevServer.handleRequestImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/base-server.js:818:17)
    at async /Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:339:20
    at async Span.traceAsyncFn (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/trace/trace.js:154:20)
    at async DevServer.handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/dev/next-dev-server.js:336:24)
    at async invokeRender (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:179:21)
    at async handleRequest (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:359:24)
    at async requestHandlerImpl (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/router-server.js:383:13)
    at async Server.requestListener (/Users/<USER>/dev/github/agmentcode/node_modules/next/dist/server/lib/start-server.js:141:13) {
  page: '/api/wallet/balance'
}
 ○ Compiling /_error ...
 ✓ Compiled /_error in 9.1s (1247 modules)
 GET /api/wallet/balance?minValue=0.5 500 in 7137ms
 GET /api/wallet/balance?minValue=0.5 200 in 4936ms
   Reload env: .env.local
 ✓ Compiled in 1384ms (1247 modules)
 ✓ Compiled in 1236ms (1261 modules)
 GET /api/wallet/balance 200 in 1122ms
 GET / 200 in 629ms
 GET /api/wallet/balance?minValue=0.5 200 in 913ms
 ○ Compiling /_not-found ...
 ✓ Compiled /_not-found in 9s (1248 modules)
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 9761ms
 GET / 200 in 940ms
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 136ms
 GET /api/wallet/balance?minValue=0.5 200 in 1213ms
 GET /api/wallet/balance?minValue=0.5 200 in 1223ms
 GET /api/wallet/balance?minValue=0.5 200 in 552ms
 GET /api/wallet/balance?minValue=0.5 200 in 347ms
 GET /api/wallet/balance?minValue=0.5 200 in 357ms
 GET /api/wallet/balance?minValue=0.5 200 in 489ms
 GET / 200 in 315ms
 GET /api/wallet/balance?minValue=0.5 200 in 581ms
 GET /api/wallet/balance?minValue=0.5 200 in 719ms
 GET /api/wallet/balance?minValue=0.5 200 in 522ms
 GET /api/wallet/balance?minValue=0.5 200 in 464ms
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 92ms
 GET /api/wallet/balance?minValue=0.5 200 in 667ms
 GET /api/wallet/balance?minValue=0.5 200 in 644ms
 GET /api/wallet/balance?minValue=0.5 200 in 579ms
 GET /api/wallet/balance?minValue=0.5 200 in 489ms
 GET /api/wallet/balance?minValue=0.5 200 in 515ms
 GET /api/wallet/balance?minValue=0.5 200 in 461ms
 GET /api/wallet/balance?minValue=0.5 200 in 562ms
 GET /api/wallet/balance?minValue=0.5 200 in 442ms
 GET /api/wallet/balance?minValue=0.5 200 in 2641ms
 ✓ Compiled in 7.8s (1259 modules)
 GET /api/wallet/balance?minValue=0.5 200 in 3279ms
 GET /api/wallet/balance?minValue=0.5 200 in 500ms
 ✓ Compiled in 3s (1259 modules)
 ✓ Compiled in 1ms (618 modules)
 ✓ Compiled in 0ms (618 modules)
 ✓ Compiled in 0ms (618 modules)
 GET /api/wallet/balance?minValue=0.5 200 in 1192ms
 ✓ Compiled in 2.5s (1259 modules)
 ✓ Compiled in 1ms (618 modules)
 ✓ Compiled in 1ms (618 modules)
 ✓ Compiled in 3ms (618 modules)
 GET /api/wallet/balance?minValue=0.5 200 in 1944ms
 GET /api/wallet/balance?minValue=0.5 200 in 617ms
 GET /api/wallet/balance?minValue=0.5 200 in 789ms
 GET /api/wallet/balance?minValue=0.5 200 in 409ms
 GET /api/wallet/balance?minValue=0.5 200 in 671ms
 GET /api/wallet/balance?minValue=0.5 200 in 492ms
 GET /api/wallet/balance?minValue=0.5 200 in 602ms
 GET /api/wallet/balance?minValue=0.5 200 in 857ms
 GET /api/wallet/balance?minValue=0.5 200 in 515ms
 GET /api/wallet/balance?minValue=0.5 200 in 661ms
 GET /api/wallet/balance?minValue=0.5 200 in 667ms
 ✓ Compiled in 7.7s (1259 modules)
 ✓ Compiled in 1ms (618 modules)
 ✓ Compiled in 7ms (618 modules)
 ✓ Compiled in 0ms (618 modules)
 GET /api/wallet/balance?minValue=0.5 200 in 6942ms
 GET /api/wallet/balance?minValue=0.5 200 in 812ms
 GET / 200 in 949ms
 ○ Compiling /_not-found ...
 ✓ Compiled /_not-found in 4.7s (1248 modules)
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 4945ms
 GET /api/wallet/balance?minValue=0.5 200 in 1016ms
 GET /api/wallet/balance?minValue=0.5 200 in 447ms
 POST /api/trading/quote/live 404 in 393ms
 GET /api/wallet/balance?minValue=0.5 200 in 500ms
 ✓ Compiled in 2.8s (1262 modules)
 ✓ Compiled in 5ms (619 modules)
 ✓ Compiled in 0ms (619 modules)
 ✓ Compiled in 0ms (619 modules)
 GET /api/wallet/balance?minValue=0.5 200 in 4476ms
 GET /api/wallet/balance?minValue=0.5 200 in 1484ms
 GET /api/wallet/balance?minValue=0.5 200 in 763ms
 GET /api/wallet/balance?minValue=0.5 200 in 983ms
 GET /api/wallet/balance?minValue=0.5 200 in 571ms
 GET /api/wallet/balance?minValue=0.5 200 in 515ms
 GET /api/wallet/balance?minValue=0.5 200 in 585ms
 GET /api/wallet/balance?minValue=0.5 200 in 547ms
 GET /api/wallet/balance?minValue=0.5 200 in 472ms
 GET /api/wallet/balance?minValue=0.5 200 in 832ms
 GET /api/wallet/balance?minValue=0.5 200 in 392ms
 GET /api/wallet/balance?minValue=0.5 200 in 819ms
 GET /api/wallet/balance?minValue=0.5 200 in 373ms
 GET /api/wallet/balance?minValue=0.5 200 in 686ms
 GET /api/wallet/balance?minValue=0.5 200 in 473ms
 GET /api/wallet/balance?minValue=0.5 200 in 442ms
 GET /api/wallet/balance?minValue=0.5 200 in 643ms
 GET /api/wallet/balance?minValue=0.5 200 in 616ms
 GET /api/wallet/balance 200 in 309ms
 GET /api/wallet/balance?minValue=0.5 200 in 619ms
 GET /api/wallet/balance?minValue=0.5 200 in 147ms
 GET /api/wallet/balance?minValue=0.5 200 in 344ms
 GET /api/wallet/balance?minValue=0.5 200 in 229ms
 GET /api/wallet/balance?minValue=0.5 200 in 1035ms
 GET /api/wallet/balance?minValue=0.5 200 in 461ms
 GET /api/wallet/balance?minValue=0.5 200 in 362ms
 GET /api/wallet/balance?minValue=0.5 200 in 474ms
 GET /api/wallet/balance?minValue=0.5 200 in 577ms
 GET /api/wallet/balance?minValue=0.5 200 in 503ms
 GET / 200 in 1382ms
 GET /.well-known/appspecific/com.chrome.devtools.json 404 in 866ms
 GET /api/wallet/balance?minValue=0.5 200 in 1058ms
 POST /api/trading/quote/live 404 in 286ms
 POST /api/trading/execute 404 in 237ms
 GET /api/wallet/balance?minValue=0.5 200 in 876ms
 GET /api/wallet/balance?minValue=0.5 200 in 569ms
 GET /api/wallet/balance?minValue=0.5 200 in 987ms
 ✓ Compiled in 13.8s (1248 modules)
 ✓ Compiled in 6.6s (1247 modules)
 GET /api/wallet/balance?minValue=0.5 200 in 815ms
   Reload env: .env.local
 ✓ Compiled in 26.9s (1245 modules)
 GET /api/wallet/balance?minValue=0.5 200 in 6519ms
 ✓ Compiled in 8.5s (1245 modules)
 GET /api/wallet/balance?minValue=0.5 200 in 4729ms
 ○ Compiling /api/trading/quote/live ...
 ✓ Compiled /api/trading/quote/live in 1893ms (620 modules)
 POST /api/trading/quote/live 200 in 2732ms
 ○ Compiling /api/trading/strategies ...
 ✓ Compiled /api/trading/strategies in 761ms (622 modules)
 GET /api/trading/strategies 200 in 880ms
 GET /api/wallet/balance?minValue=0.5 200 in 1186ms
 GET /api/wallet/balance?minValue=0.5 200 in 8450ms
[?25h
