#!/usr/bin/env node

/**
 * Simple test script to verify Jupiter swap functionality
 * Tests the core swap implementation with minimal setup
 */

const { Connection, PublicKey, Keypair } = require('@solana/web3.js')
const bs58 = require('bs58')
require('dotenv').config()

async function testSwapSetup() {
  console.log('🧪 Testing Solana Token Swap Setup...\n')

  // 1. Test Environment Variables
  console.log('1️⃣ Checking Environment Variables:')
  console.log(`   SOLANA_RPC_URL: ${process.env.SOLANA_RPC_URL ? '✅ Set' : '❌ Missing'}`)
  console.log(`   HELIUS_API_KEY: ${process.env.HELIUS_API_KEY ? '✅ Set' : '❌ Missing'}`)
  console.log(`   WALLET_PRIVATE_KEY: ${process.env.WALLET_PRIVATE_KEY ? '✅ Set' : '❌ Missing'}`)
  console.log(`   TRADING_WALLET_ADDRESS: ${process.env.TRADING_WALLET_ADDRESS || '❌ Missing'}`)

  if (!process.env.SOLANA_RPC_URL || !process.env.WALLET_PRIVATE_KEY) {
    console.log('❌ Missing required environment variables')
    process.exit(1)
  }

  // 2. Test Solana Connection
  console.log('\n2️⃣ Testing Solana Connection:')
  try {
    const connection = new Connection(process.env.SOLANA_RPC_URL, 'confirmed')
    const latestBlockhash = await connection.getLatestBlockhash()
    console.log(`   ✅ Connected to Solana (Blockhash: ${latestBlockhash.blockhash.slice(0, 8)}...)`)
  } catch (error) {
    console.log(`   ❌ Failed to connect to Solana: ${error.message}`)
    return
  }

  // 3. Test Wallet
  console.log('\n3️⃣ Testing Wallet:')
  try {
    const wallet = Keypair.fromSecretKey(bs58.decode(process.env.WALLET_PRIVATE_KEY))
    const connection = new Connection(process.env.SOLANA_RPC_URL, 'confirmed')
    const balance = await connection.getBalance(wallet.publicKey)
    console.log(`   ✅ Wallet loaded: ${wallet.publicKey.toString()}`)
    console.log(`   💰 SOL Balance: ${balance / 1e9} SOL`)
  } catch (error) {
    console.log(`   ❌ Failed to load wallet: ${error.message}`)
    return
  }

  // 4. Test Jupiter API
  console.log('\n4️⃣ Testing Jupiter API:')
  try {
    const fetch = (await import('node-fetch')).default
    const response = await fetch('https://quote-api.jup.ag/v6/quote?inputMint=So11111111111111111111111111111111111111112&outputMint=EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v&amount=1000000&slippageBps=50')
    
    if (response.ok) {
      const quote = await response.json()
      console.log(`   ✅ Jupiter API responding`)
      console.log(`   💱 Test Quote: ${quote.inAmount} → ${quote.outAmount}`)
    } else {
      console.log(`   ❌ Jupiter API error: ${response.status}`)
    }
  } catch (error) {
    console.log(`   ❌ Failed to reach Jupiter API: ${error.message}`)
  }

  // 5. Test Database Connection (if available)
  console.log('\n5️⃣ Testing Database Connection:')
  if (process.env.DATABASE_URL) {
    try {
      const { Client } = require('pg')
      const client = new Client({
        connectionString: process.env.DATABASE_URL
      })
      await client.connect()
      const result = await client.query('SELECT NOW()')
      await client.end()
      console.log(`   ✅ Database connected: ${result.rows[0].now}`)
    } catch (error) {
      console.log(`   ❌ Database connection failed: ${error.message}`)
    }
  } else {
    console.log('   ⚠️  DATABASE_URL not set')
  }

  console.log('\n🎉 Setup test completed!')
  console.log('\n📋 Summary:')
  console.log('   • Solana RPC: Connected')
  console.log('   • Wallet: Loaded and funded')
  console.log('   • Jupiter API: Accessible')
  console.log('   • Ready for swaps!')
}

testSwapSetup().catch(console.error)