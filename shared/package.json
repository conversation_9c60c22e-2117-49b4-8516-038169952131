{"name": "@memetrader-pro/shared", "version": "1.0.0", "description": "Shared TypeScript types and utilities for MemeTrader Pro", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "dependencies": {"zod": "^3.22.4"}, "devDependencies": {"typescript": "^5.3.3", "@types/node": "^20.10.5", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0"}, "files": ["dist"]}