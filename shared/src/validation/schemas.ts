import { z } from 'zod';
import { PresetType, RiskLevel, StrategyType, TransactionType, AlertType, AlertPriority, MEVProtectionLevel } from '../types/enums';

// Enum schemas
export const PresetTypeSchema = z.nativeEnum(PresetType);
export const RiskLevelSchema = z.nativeEnum(RiskLevel);
export const StrategyTypeSchema = z.nativeEnum(StrategyType);
export const TransactionTypeSchema = z.nativeEnum(TransactionType);
export const AlertTypeSchema = z.nativeEnum(AlertType);
export const AlertPrioritySchema = z.nativeEnum(AlertPriority);
export const MEVProtectionLevelSchema = z.nativeEnum(MEVProtectionLevel);

// Trading schemas
export const TradeParamsSchema = z.object({
  tokenIn: z.string().min(32).max(44),
  tokenOut: z.string().min(32).max(44),
  amount: z.number().positive(),
  slippage: z.number().min(0.1).max(50),
  preset: PresetTypeSchema,
  strategyId: z.string().optional()
});

export const QuoteParamsSchema = z.object({
  tokenIn: z.string().min(32).max(44),
  tokenOut: z.string().min(32).max(44),
  amount: z.number().positive(),
  slippage: z.number().min(0.1).max(50).optional()
});

export const SimulationParamsSchema = z.object({
  tokenIn: z.string().min(32).max(44),
  tokenOut: z.string().min(32).max(44),
  amount: z.number().positive(),
  preset: PresetTypeSchema
});

// Strategy schemas
export const StopLossConfigSchema = z.object({
  percentage: z.number().min(1).max(100),
  trailing: z.boolean(),
  emergencySlippage: z.number().min(1).max(50)
});

export const ProfitTargetSchema = z.object({
  percentage: z.number().min(1),
  sellPercentage: z.number().min(1).max(100),
  priority: z.number().min(1).max(10)
});

export const MoonBagConfigSchema = z.object({
  percentage: z.number().min(1).max(50),
  exitTarget: z.number().min(100),
  enabled: z.boolean()
});

export const ExitStrategySchema = z.object({
  type: StrategyTypeSchema,
  stopLoss: StopLossConfigSchema,
  profitTargets: z.array(ProfitTargetSchema).min(1).max(10),
  moonBag: MoonBagConfigSchema.optional(),
  locked: z.boolean(),
  customName: z.string().max(100).optional()
});

export const CustomStrategySchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  config: z.object({
    stopLoss: StopLossConfigSchema,
    profitTargets: z.array(ProfitTargetSchema).min(1).max(10),
    moonBag: MoonBagConfigSchema.optional(),
    riskParameters: z.object({
      maxPositionSize: z.number().min(0.1).max(100),
      maxDailyLoss: z.number().min(1).max(50),
      maxDrawdown: z.number().min(5).max(80),
      correlationLimit: z.number().min(0.1).max(1)
    })
  }),
  prdCompliant: z.boolean()
});

// Alert schemas
export const AlertConfigSchema = z.object({
  enabled: z.boolean(),
  channels: z.array(z.object({
    type: z.enum(['sound', 'email', 'desktop', 'webhook']),
    enabled: z.boolean(),
    config: z.record(z.any())
  })),
  quietHours: z.object({
    enabled: z.boolean(),
    start: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
    end: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
    highPriorityOverride: z.boolean()
  }),
  filters: z.object({
    minPriority: AlertPrioritySchema,
    categories: z.array(AlertTypeSchema),
    tokens: z.array(z.string())
  })
});

// Portfolio schemas
export const RebalanceParamsSchema = z.object({
  targetAllocations: z.record(z.number().min(0).max(100)),
  maxSlippage: z.number().min(0.1).max(50),
  priorityFee: z.number().min(0).max(1)
});

// Pagination schema
export const PaginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).optional()
});

// Query schemas
export const PositionQuerySchema = z.object({
  status: z.string().optional(),
  limit: z.number().min(1).max(100).optional(),
  offset: z.number().min(0).optional()
});

export const TransactionQuerySchema = z.object({
  limit: z.number().min(1).max(100).optional(),
  offset: z.number().min(0).optional(),
  type: TransactionTypeSchema.optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional()
});

export const AlertQuerySchema = z.object({
  limit: z.number().min(1).max(100).optional(),
  offset: z.number().min(0).optional(),
  read: z.boolean().optional(),
  priority: AlertPrioritySchema.optional(),
  type: AlertTypeSchema.optional()
});

export const MarketSearchSchema = z.object({
  q: z.string().min(1).max(100),
  limit: z.number().min(1).max(50).optional()
});

// Wallet address validation
export const SolanaAddressSchema = z.string().regex(/^[1-9A-HJ-NP-Za-km-z]{32,44}$/, 'Invalid Solana address');

// Transaction hash validation
export const TransactionHashSchema = z.string().regex(/^[1-9A-HJ-NP-Za-km-z]{64,88}$/, 'Invalid transaction hash');

// Trading preset schema
export const TradingPresetSchema = z.object({
  id: z.string(),
  name: PresetTypeSchema,
  priorityFee: z.number().min(0),
  slippageLimit: z.number().min(0.1).max(50),
  mevProtectionLevel: MEVProtectionLevelSchema,
  brideAmount: z.number().min(0).optional(),
  locked: z.boolean(),
  buySettings: z.object({
    maxSlippage: z.number().min(0.1).max(50),
    priorityFee: z.number().min(0),
    mevProtection: z.boolean(),
    simulateFirst: z.boolean(),
    maxRetries: z.number().min(1).max(10)
  }),
  sellSettings: z.object({
    maxSlippage: z.number().min(0.1).max(50),
    priorityFee: z.number().min(0),
    mevProtection: z.boolean(),
    simulateFirst: z.boolean(),
    maxRetries: z.number().min(1).max(10)
  })
});
