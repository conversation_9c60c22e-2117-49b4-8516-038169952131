import { z } from 'zod'
import { MEVProtectionLevelSchema, RiskLevelSchema, AlertPrioritySchema, AlertTypeSchema } from './schemas'
import { AlertConfigSchema, TradingPresetSchema } from './schemas'

// Quick amount configuration schema
export const QuickAmountConfigSchema = z.object({
  label: z.string().min(1).max(10),
  type: z.enum(['percentage', 'sol']),
  value: z.number().min(0.01).max(1000), // 0.01 to 1000 (SOL or percentage)
  isDefault: z.boolean().optional()
})

// Trading settings schema
export const TradingSettingsSchema = z.object({
  defaultSlippage: z.number().min(0.1).max(50),
  priorityFee: z.number().min(0).max(100000), // lamports
  mevProtectionLevel: MEVProtectionLevelSchema,
  tradingPresets: z.array(TradingPresetSchema),
  autoApproveBelow: z.number().min(0).max(100),
  simulateFirst: z.boolean(),
  defaultPositionSize: z.number().min(1).max(100),
  quickAmounts: z.array(QuickAmountConfigSchema).min(1).max(12)
})

// Notification channel schema
export const NotificationChannelSchema = z.object({
  type: z.enum(['desktop', 'sound', 'email', 'webhook']),
  enabled: z.boolean(),
  config: z.record(z.any())
})

// Quiet hours configuration schema
export const QuietHoursConfigSchema = z.object({
  enabled: z.boolean(),
  start: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/), // HH:MM format
  end: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
  timezone: z.string(),
  highPriorityOverride: z.boolean(),
  weekendsOnly: z.boolean().optional()
})

// Alert category configuration schema
export const AlertCategoryConfigSchema = z.object({
  type: AlertTypeSchema,
  enabled: z.boolean(),
  minPriority: AlertPrioritySchema,
  customSound: z.string().optional()
})

// Webhook settings schema
export const WebhookSettingsSchema = z.object({
  url: z.string().url(),
  secret: z.string().optional(),
  retryAttempts: z.number().min(0).max(5),
  timeout: z.number().min(1000).max(60000) // 1s to 60s
})

// Alert settings schema - fully defined to avoid inheritance conflicts
export const AlertSettingsSchema = z.object({
  enabled: z.boolean(),
  channels: z.array(z.object({
    type: z.enum(['sound', 'email', 'desktop', 'webhook']),
    enabled: z.boolean(),
    config: z.record(z.any())
  })),
  quietHours: z.object({
    enabled: z.boolean(),
    start: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
    end: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
    highPriorityOverride: z.boolean()
  }),
  filters: z.object({
    minPriority: AlertPrioritySchema,
    categories: z.array(AlertTypeSchema),
    tokens: z.array(z.string()).default([])
  }),
  notificationChannels: z.array(NotificationChannelSchema),
  alertCategories: z.array(AlertCategoryConfigSchema),
  webhookSettings: WebhookSettingsSchema.optional()
})

// Take profit configuration schema
export const TakeProfitConfigSchema = z.object({
  targets: z.array(z.number().min(1).max(10000)), // percentage gains
  exitPercentages: z.array(z.number().min(1).max(100)),
  moonBagPercentage: z.number().min(0).max(50)
}).refine(
  (data) => data.targets.length === data.exitPercentages.length,
  { message: "Targets and exit percentages must have the same length" }
).refine(
  (data) => data.exitPercentages.reduce((sum, pct) => sum + pct, 0) + data.moonBagPercentage <= 100,
  { message: "Total exit percentages plus moon bag cannot exceed 100%" }
)

// Diversification rule schema
export const DiversificationRuleSchema = z.object({
  type: z.enum(['sector', 'marketcap', 'correlation']),
  maxAllocation: z.number().min(1).max(100),
  enabled: z.boolean()
})

// Portfolio settings schema
export const PortfolioSettingsSchema = z.object({
  riskTolerance: RiskLevelSchema,
  defaultPositionSize: z.number().min(1).max(100),
  maxExposure: z.number().min(10).max(100),
  correlationLimit: z.number().min(0).max(1),
  rebalanceThreshold: z.number().min(1).max(50),
  stopLossDefault: z.number().min(1).max(50),
  takeProfitDefaults: TakeProfitConfigSchema,
  diversificationRules: z.array(DiversificationRuleSchema).max(10)
})

// Security settings schema
export const SecuritySettingsSchema = z.object({
  autoApproveTransactions: z.boolean(),
  sessionTimeout: z.number().min(5).max(1440), // 5 min to 24 hours
  walletConnectionTimeout: z.number().min(10).max(300), // 10s to 5 min
  emergencyStopEnabled: z.boolean(),
  requireConfirmationAbove: z.number().min(0).max(1000),
  whitelistedContracts: z.array(z.string()).max(100),
  blacklistedContracts: z.array(z.string()).max(100),
  maxDailyTransactions: z.number().min(1).max(1000),
  maxDailyVolume: z.number().min(1).max(100000), // in SOL
  twoFactorEnabled: z.boolean()
})

// Dashboard widget schema
export const DashboardWidgetSchema = z.object({
  id: z.string(),
  type: z.string(),
  position: z.object({ x: z.number(), y: z.number() }),
  size: z.object({ width: z.number().min(1), height: z.number().min(1) }),
  config: z.record(z.any()).optional()
})

// Dashboard layout schema
export const DashboardLayoutSchema = z.object({
  type: z.enum(['default', 'compact', 'advanced', 'custom']),
  widgets: z.array(DashboardWidgetSchema),
  columns: z.number().min(1).max(12)
})

// Chart settings schema
export const ChartSettingsSchema = z.object({
  defaultInterval: z.enum(['1m', '5m', '15m', '1h', '4h', '1d']),
  showVolume: z.boolean(),
  indicators: z.array(z.string()).max(10),
  colorScheme: z.enum(['default', 'tradingview', 'custom'])
})

// UI settings schema
export const UISettingsSchema = z.object({
  theme: z.enum(['light', 'dark', 'auto']),
  timezone: z.string(),
  language: z.string().length(2), // ISO 639-1 code
  dateFormat: z.string(),
  numberFormat: z.enum(['comma', 'period', 'space']),
  compactMode: z.boolean(),
  dashboardLayout: DashboardLayoutSchema,
  animations: z.boolean(),
  chartSettings: ChartSettingsSchema,
  keyboardShortcuts: z.boolean()
})

// Performance settings schema
export const PerformanceSettingsSchema = z.object({
  websocketHeartbeat: z.number().min(5000).max(60000), // 5s to 60s
  priceUpdateFrequency: z.number().min(100).max(10000), // 100ms to 10s
  cacheEnabled: z.boolean(),
  cacheDuration: z.number().min(60).max(3600), // 1 min to 1 hour
  rateLimitBuffer: z.number().min(0).max(100),
  maxConcurrentRequests: z.number().min(1).max(50),
  dataRetentionDays: z.number().min(1).max(365),
  autoRefreshEnabled: z.boolean(),
  autoRefreshInterval: z.number().min(10).max(3600), // 10s to 1 hour
  lowBandwidthMode: z.boolean()
})

// Main app settings schema
export const AppSettingsSchema = z.object({
  trading: TradingSettingsSchema,
  alerts: AlertSettingsSchema,
  portfolio: PortfolioSettingsSchema,
  security: SecuritySettingsSchema,
  ui: UISettingsSchema,
  performance: PerformanceSettingsSchema,
  version: z.string(),
  lastUpdated: z.string()
})

// Settings preset schema
export const SettingsPresetSchema = z.object({
  id: z.string(),
  name: z.string().min(1).max(50),
  description: z.string().max(200),
  category: z.enum(['conservative', 'balanced', 'aggressive', 'custom']),
  settings: AppSettingsSchema.partial(),
  isDefault: z.boolean().optional()
})

// Settings export schema
export const SettingsExportSchema = z.object({
  version: z.string(),
  exportDate: z.string(),
  settings: AppSettingsSchema,
  checksum: z.string().optional()
})

// Default values from environment
export const DEFAULT_TRADING_SETTINGS = {
  defaultSlippage: 3, // 3% default from MAX_SLIPPAGE_BPS: 300
  priorityFee: 1000, // DEFAULT_PRIORITY_FEE from environment.ts
  websocketHeartbeat: 30000, // WS_HEARTBEAT_INTERVAL from environment.ts
}

// Validation helper functions
export function validateSettings(settings: unknown): { valid: boolean; errors: string[] } {
  const result = AppSettingsSchema.safeParse(settings)
  if (result.success) {
    return { valid: true, errors: [] }
  } else {
    return {
      valid: false,
      errors: result.error.errors.map(e => `${e.path.join('.')}: ${e.message}`)
    }
  }
}

export function validateSettingsPartial(settings: unknown): { valid: boolean; errors: string[] } {
  const result = AppSettingsSchema.partial().safeParse(settings)
  if (result.success) {
    return { valid: true, errors: [] }
  } else {
    return {
      valid: false,
      errors: result.error.errors.map(e => `${e.path.join('.')}: ${e.message}`)
    }
  }
}