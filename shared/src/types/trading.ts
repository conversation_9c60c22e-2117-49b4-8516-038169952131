import { PresetType, RiskLevel, PositionStatus, StrategyType, TransactionType, MEVProtectionLevel, StrategyExecutionState } from './enums';

// Trading Configuration Types
export interface TradeSettings {
  maxSlippage: number;
  priorityFee: number;
  mevProtection: boolean;
  simulateFirst: boolean;
  maxRetries: number;
}

export interface TradingPreset {
  id: string;
  name: PresetType;
  priorityFee: number; // SOL amount
  slippageLimit: number; // Percentage
  mevProtectionLevel: MEVProtectionLevel;
  brideAmount?: number; // Optional SOL amount
  locked: boolean;
  buySettings: TradeSettings;
  sellSettings: TradeSettings;
}

// Position Types
export interface Position {
  id: string;
  userId: string;
  tokenAddress: string;
  tokenSymbol: string;
  entryPrice: number;
  currentPrice: number;
  quantity: number;
  entryTimestamp: Date;
  strategyId?: string;
  presetUsed: PresetType;
  riskLevel: RiskLevel;
  status: PositionStatus;

  // Calculated fields
  pnl: number;
  pnlPercentage: number;
  age: number; // milliseconds
  marketData?: TokenMarketData;
}

export interface TokenMarketData {
  price: number;
  change24h: number;
  volume24h: number;
  marketCap: number;
  liquidity?: number;
  holders?: number;
}

// Strategy Configuration Types
export interface StopLossConfig {
  percentage: number;
  trailing: boolean;
  emergencySlippage: number;
}

export interface ProfitTarget {
  percentage: number;
  sellPercentage: number;
  priority: number;
}

export interface MoonBagConfig {
  percentage: number; // % of position to keep
  exitTarget: number; // % gain to exit moon bag
  enabled: boolean;
}

export interface RiskParameters {
  maxPositionSize: number;
  maxDailyLoss: number;
  maxDrawdown: number;
  correlationLimit: number;
}

// Exit Strategy Types
export interface ExitStrategy {
  id: string;
  userId: string;
  positionId?: string;
  type: StrategyType;
  stopLoss: StopLossConfig;
  profitTargets: ProfitTarget[];
  moonBag?: MoonBagConfig;
  locked: boolean;
  customName?: string;

  // Execution state
  executionState: StrategyExecutionState;
  lastUpdate: Date;
}

export interface CustomStrategy {
  id: string;
  userId: string;
  name: string;
  description?: string;
  config: {
    stopLoss: StopLossConfig;
    profitTargets: ProfitTarget[];
    moonBag?: MoonBagConfig;
    riskParameters: RiskParameters;
  };
  prdCompliant: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Transaction Types
export interface TransactionFees {
  jupiterFee: number;
  priorityFee: number;
  networkFee: number;
  total: number;
}

export interface Transaction {
  id: string;
  userId: string;
  positionId?: string;
  hash: string;
  type: TransactionType;
  tokenIn: string;
  tokenOut: string;
  amountIn: number;
  amountOut: number;
  price: number;
  fees: TransactionFees;
  strategyId?: string;
  presetUsed: PresetType;
  mevProtected: boolean;
  timestamp: Date;
}

// Trading Execution Types
export interface TradeParams {
  tokenIn: string;
  tokenOut: string;
  amount: number;
  slippage: number;
  preset: PresetType;
  strategyId?: string;
}

export interface Quote {
  inputMint: string;
  outputMint: string;
  inAmount: string;
  outAmount: string;
  priceImpactPct: number;
  routePlan: any[];
  fees: TransactionFees;
}

export interface TradeResult {
  success: boolean;
  transactionHash?: string;
  error?: string;
  quote: Quote;
  executionTime: number;
}

export interface SimulationParams {
  tokenIn: string;
  tokenOut: string;
  amount: number;
  preset: PresetType;
}

export interface SimulationResult {
  success: boolean;
  estimatedOutput: number;
  priceImpact: number;
  fees: TransactionFees;
  route: any[];
  warnings: string[];
}

export interface ExecutionResult {
  success: boolean;
  transactionHash?: string;
  amountExecuted: number;
  priceExecuted: number;
  fees: TransactionFees;
  error?: string;
}

// Transaction Intelligence Center Types
export interface PerformanceMetrics {
  totalTrades: number;
  successfulTrades: number;
  winRate: number;
  totalPnlSol: number;
  totalPnlUsd: number;
  totalVolume: number;
  profitFactor: number;
  sharpeRatio: number;
  maxDrawdown: number;
  averageHoldTime: number; // in hours
  averageDailyReturn: number;
  standardDeviation: number;
}

export interface StrategyAnalytics {
  strategyType: PresetType;
  totalTrades: number;
  winRate: number;
  averagePnl: number;
  totalPnl: number;
  bestTrade: number;
  worstTrade: number;
  averageHoldTime: number;
  totalVolume: number;
}

export interface TransactionFilters {
  dateRange: {
    start: Date | null;
    end: Date | null;
  };
  strategies: PresetType[];
  transactionTypes: TransactionType[];
  tokenSearch: string;
  pnlRange: {
    min: number | null;
    max: number | null;
  };
  profitableOnly: boolean;
  lossesOnly: boolean;
}

export interface ExportData {
  date: string;
  tokenInSymbol: string;
  tokenOutSymbol: string;
  amountIn: number;
  amountOut: number;
  price: number;
  totalFeesSol: number;
  strategyUsed: PresetType;
  pnlSol: number;
  pnlUsd: number;
  transactionHash: string;
}

export interface ChartDataPoint {
  date: string;
  value: number;
  label?: string;
}

export interface DailyPnlData extends ChartDataPoint {
  cumulativePnl: number;
  trades: number;
}

export interface StrategyPerformanceData {
  strategy: PresetType;
  pnl: number;
  trades: number;
  winRate: number;
}

export interface WinLossDistribution {
  name: string;
  value: number;
  percentage: number;
  color: string;
}
