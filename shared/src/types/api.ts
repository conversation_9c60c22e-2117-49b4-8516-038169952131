import { 
  TradeParams, 
  TradeResult, 
  Quote, 
  SimulationParams, 
  SimulationResult,
  TradingPreset,
  Position,
  Transaction,
  ExitStrategy,
  CustomStrategy
} from './trading';
import { PortfolioMetrics, RebalanceParams, RebalanceResult } from './portfolio';
import { Alert, AlertConfig } from './alerts';

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: Date;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Trading API Types
export interface TradingAPI {
  'POST /api/trades/execute': {
    body: TradeParams;
    response: ApiResponse<TradeResult>;
  };
  'GET /api/trades/quote': {
    query: {
      tokenIn: string;
      tokenOut: string;
      amount: number;
      slippage?: number;
    };
    response: ApiResponse<Quote>;
  };
  'POST /api/trades/simulate': {
    body: SimulationParams;
    response: ApiResponse<SimulationResult>;
  };
  'GET /api/presets': {
    response: ApiResponse<TradingPreset[]>;
  };
  'PUT /api/presets/:id': {
    params: { id: string };
    body: Partial<TradingPreset>;
    response: ApiResponse<TradingPreset>;
  };
}

// Portfolio API Types
export interface PortfolioAPI {
  'GET /api/portfolio/metrics': {
    response: ApiResponse<PortfolioMetrics>;
  };
  'GET /api/portfolio/positions': {
    query?: {
      status?: string;
      limit?: number;
      offset?: number;
    };
    response: PaginatedResponse<Position>;
  };
  'GET /api/portfolio/positions/:id': {
    params: { id: string };
    response: ApiResponse<Position>;
  };
  'POST /api/portfolio/rebalance': {
    body: RebalanceParams;
    response: ApiResponse<RebalanceResult>;
  };
}

// Strategy API Types
export interface StrategyAPI {
  'GET /api/strategies': {
    response: ApiResponse<ExitStrategy[]>;
  };
  'POST /api/strategies': {
    body: Omit<ExitStrategy, 'id' | 'userId' | 'executionState' | 'lastUpdate'>;
    response: ApiResponse<ExitStrategy>;
  };
  'PUT /api/strategies/:id': {
    params: { id: string };
    body: Partial<ExitStrategy>;
    response: ApiResponse<ExitStrategy>;
  };
  'DELETE /api/strategies/:id': {
    params: { id: string };
    response: ApiResponse<void>;
  };
  'GET /api/strategies/custom': {
    response: ApiResponse<CustomStrategy[]>;
  };
  'POST /api/strategies/custom': {
    body: Omit<CustomStrategy, 'id' | 'userId' | 'createdAt' | 'updatedAt'>;
    response: ApiResponse<CustomStrategy>;
  };
}

// Transaction API Types
export interface TransactionAPI {
  'GET /api/transactions': {
    query?: {
      limit?: number;
      offset?: number;
      type?: string;
      startDate?: string;
      endDate?: string;
    };
    response: PaginatedResponse<Transaction>;
  };
  'GET /api/transactions/:id': {
    params: { id: string };
    response: ApiResponse<Transaction>;
  };
}

// Alert API Types
export interface AlertAPI {
  'GET /api/alerts': {
    query?: {
      limit?: number;
      offset?: number;
      read?: boolean;
      priority?: string;
      type?: string;
    };
    response: PaginatedResponse<Alert>;
  };
  'PUT /api/alerts/:id/read': {
    params: { id: string };
    response: ApiResponse<Alert>;
  };
  'PUT /api/alerts/read-all': {
    response: ApiResponse<void>;
  };
  'DELETE /api/alerts/:id': {
    params: { id: string };
    response: ApiResponse<void>;
  };
  'GET /api/alerts/config': {
    response: ApiResponse<AlertConfig>;
  };
  'PUT /api/alerts/config': {
    body: Partial<AlertConfig>;
    response: ApiResponse<AlertConfig>;
  };
}

// Market Data API Types
export interface MarketAPI {
  'GET /api/market/price/:tokenAddress': {
    params: { tokenAddress: string };
    response: ApiResponse<{
      price: number;
      change24h: number;
      volume24h: number;
      marketCap: number;
    }>;
  };
  'GET /api/market/search': {
    query: { q: string; limit?: number };
    response: ApiResponse<Array<{
      address: string;
      symbol: string;
      name: string;
      logoURI?: string;
    }>>;
  };
}

// Health Check API Types
export interface HealthAPI {
  'GET /api/health': {
    response: ApiResponse<{
      status: 'healthy' | 'degraded' | 'unhealthy';
      services: {
        database: boolean;
        redis: boolean;
        jupiter: boolean;
        helius: boolean;
      };
      uptime: number;
    }>;
  };
}
