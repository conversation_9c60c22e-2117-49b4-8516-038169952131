import { AlertType, AlertPriority } from './enums';
import { ExecutionResult } from './trading';

// Alert Types
export interface Alert {
  id: string;
  userId: string;
  type: AlertType;
  priority: AlertPriority;
  title: string;
  message: string;
  metadata: Record<string, any>;
  read: boolean;
  actionable: boolean;
  timestamp: Date;
}

export interface AlertConfig {
  enabled: boolean;
  channels: AlertChannel[];
  quietHours: {
    enabled: boolean;
    start: string; // "23:00"
    end: string;   // "07:00"
    highPriorityOverride: boolean;
  };
  filters: {
    minPriority: AlertPriority;
    categories: AlertType[];
    tokens: string[];
  };
}

export interface AlertChannel {
  type: 'sound' | 'email' | 'desktop' | 'webhook';
  enabled: boolean;
  config: Record<string, any>;
}

// WebSocket Message Types
export interface SocketMessage {
  type: 'PRICE_UPDATE' | 'POSITION_UPDATE' | 'STRATEGY_TRIGGER' | 'ALERT' | 'TRANSACTION_UPDATE';
  data: any;
  timestamp: Date;
}

export interface PriceUpdateMessage extends SocketMessage {
  type: 'PRICE_UPDATE';
  data: {
    tokenAddress: string;
    price: number;
    change24h: number;
    volume24h: number;
    marketCap: number;
  };
}

export interface PositionUpdateMessage extends SocketMessage {
  type: 'POSITION_UPDATE';
  data: {
    positionId: string;
    currentPrice: number;
    pnl: number;
    pnlPercentage: number;
  };
}

export interface StrategyTriggerMessage extends SocketMessage {
  type: 'STRATEGY_TRIGGER';
  data: {
    positionId: string;
    strategyId: string;
    triggerType: 'PROFIT_TARGET' | 'STOP_LOSS' | 'TRAILING_STOP';
    executionResult: ExecutionResult;
  };
}

export interface AlertMessage extends SocketMessage {
  type: 'ALERT';
  data: Alert;
}

export interface TransactionUpdateMessage extends SocketMessage {
  type: 'TRANSACTION_UPDATE';
  data: {
    transactionHash: string;
    status: 'pending' | 'confirmed' | 'failed';
    confirmations?: number;
  };
}

// Alert State Types
export interface AlertState {
  alerts: Alert[];
  unreadCount: number;
  config: AlertConfig;
  
  // Actions
  addAlert: (alert: Omit<Alert, 'id' | 'timestamp'>) => void;
  markAsRead: (alertId: string) => void;
  markAllAsRead: () => void;
  deleteAlert: (alertId: string) => void;
  updateConfig: (config: Partial<AlertConfig>) => void;
  filterAlerts: (filters: Partial<AlertConfig['filters']>) => Alert[];
}
