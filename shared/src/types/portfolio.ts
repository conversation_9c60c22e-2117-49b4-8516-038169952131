import { Position, RiskLevel } from './trading';

// Portfolio Analytics Types
export interface PortfolioMetrics {
  totalValue: number;
  totalPnL: number;
  totalPnLPercentage: number;
  dayPnL: number;
  dayPnLPercentage: number;
  activePositions: number;
  winRate: number;
  avgHoldTime: number;
  maxDrawdown: number;
  sharpeRatio: number;
}

export interface ExposureMeter {
  totalExposure: number;
  riskScore: number;
  concentrationRisk: number;
  correlationRisk: number;
  liquidityRisk: number;
  recommendations: string[];
}

export interface RiskScore {
  overall: number;
  breakdown: {
    position: number;
    concentration: number;
    correlation: number;
    liquidity: number;
    volatility: number;
  };
  level: RiskLevel;
  warnings: string[];
}

export interface PositionSizer {
  portfolioValue: number;
  riskPerTrade: number;
  maxPositionSize: number;
  recommendedSize: number;
  riskLevel: RiskLevel;
}

// Portfolio State Types
export interface PortfolioState {
  positions: Position[];
  metrics: PortfolioMetrics;
  exposureMeter: ExposureMeter;
  riskScore: RiskScore;
  positionSizer: PositionSizer;
  
  // Actions
  updatePosition: (position: Position) => void;
  calculateRisk: () => void;
  updateExposure: () => void;
  updateMetrics: () => void;
}

// Rebalancing Types
export interface RebalanceParams {
  targetAllocations: Record<string, number>;
  maxSlippage: number;
  priorityFee: number;
}

export interface RebalanceResult {
  success: boolean;
  trades: Array<{
    tokenAddress: string;
    action: 'buy' | 'sell';
    amount: number;
    transactionHash?: string;
  }>;
  totalCost: number;
  error?: string;
}
