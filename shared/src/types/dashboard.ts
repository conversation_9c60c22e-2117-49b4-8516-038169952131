// Dashboard-specific types for MemeTrader Pro

export interface DashboardMetrics {
  portfolioValue: number
  dailyChange: number
  dailyChangePercentage: number
  availableCapital: number
  capitalUsage: number
  activePositions: number
}

export interface PerformanceData {
  totalPnL24h: number
  yesterdayChange: number
  winRate: number
  winRateChange: number
  avgHoldTime: number
  holdTimeChange: number
  totalTrades: number
  successfulTrades: number
}

export interface DailyPnL {
  date: string
  pnl: number
}

export interface GoalData {
  targetAmount: number
  currentAmount: number
  dailyPnL: DailyPnL[]
  averageDaily7d: number
  averageDaily30d: number
  estimatedDaysConservative: number
  estimatedDaysOptimistic: number
  dailyAmountNeeded: number
  progressPercentage: number
}

export interface RiskData {
  riskScore: 'Low' | 'Medium' | 'High'
  riskChange: number
  tradingLimit: number
  limitUsage: number
  availableCapital: number
  connectionStatus: string
  lastUpdate: string
}

export interface ExposureData {
  currentExposure: number
  maximumLimit: number
  capitalUsage: number
  activePositions: number
  availableCapital: number
  positionsUnderLimit: number
}

// Dashboard state types
export interface DashboardState {
  metrics: DashboardMetrics
  performance: PerformanceData
  goalTracking: GoalData
  riskAssessment: RiskData
  exposureData: ExposureData
  
  // Loading states
  isLoading: boolean
  lastUpdated: Date
  
  // Actions
  updateMetrics: (metrics: Partial<DashboardMetrics>) => void
  updatePerformance: (performance: Partial<PerformanceData>) => void
  updateGoalData: (goalData: Partial<GoalData>) => void
  updateRiskData: (riskData: Partial<RiskData>) => void
  updateExposureData: (exposureData: Partial<ExposureData>) => void
  refreshAll: () => Promise<void>
}

// API response types
export interface DashboardApiResponse {
  success: boolean
  data?: {
    metrics: DashboardMetrics
    performance: PerformanceData
    goalTracking: GoalData
    riskAssessment: RiskData
    exposureData: ExposureData
  }
  error?: string
  timestamp: number
}

// WebSocket message types for real-time updates
export interface DashboardSocketMessage {
  type: 'DASHBOARD_UPDATE'
  data: {
    component: 'metrics' | 'performance' | 'goal' | 'risk' | 'exposure'
    payload: any
  }
  timestamp: Date
}

export interface GoalProgressUpdate {
  currentAmount: number
  todaysPnL: number
  progressPercentage: number
  estimatedDays: number
}

// Time periods for analytics
export type TimeframePeriod = '7d' | '30d' | '90d' | '1y'

// Goal tracking enums
export enum GoalStatus {
  ON_TRACK = 'on_track',
  BEHIND = 'behind',
  AHEAD = 'ahead',
  REACHED = 'reached'
}

// Risk assessment levels
export enum RiskLevel {
  LOW = 'Low',
  MEDIUM = 'Medium',
  HIGH = 'High',
  CRITICAL = 'Critical'
}

// Connection status types
export enum ConnectionStatus {
  CONNECTED = 'Connected',
  CONNECTING = 'Connecting',
  DISCONNECTED = 'Disconnected',
  MAINNET_READY = 'Mainnet Ready',
  TESTNET = 'Testnet'
}