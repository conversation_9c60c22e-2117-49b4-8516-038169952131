import { AlertConfig } from './alerts'
import { RiskLevel, MEVProtectionLevel, AlertPriority, AlertType } from './enums'
import { TradingPreset } from './trading'

// Main application settings interface
export interface AppSettings {
  trading: TradingSettings
  alerts: AlertSettings
  portfolio: PortfolioSettings
  security: SecuritySettings
  ui: UISettings
  performance: PerformanceSettings
  version: string
  lastUpdated: string
}

// Trading settings interface
export interface TradingSettings {
  defaultSlippage: number // Percentage (0.1 - 50)
  priorityFee: number // In lamports
  mevProtectionLevel: MEVProtectionLevel
  tradingPresets: TradingPreset[]
  autoApproveBelow: number // SOL amount threshold
  simulateFirst: boolean
  defaultPositionSize: number // Percentage of portfolio
  quickAmounts: QuickAmountConfig[]
}

// Quick amount button configuration
export interface QuickAmountConfig {
  label: string
  type: 'percentage' | 'sol'
  value: number // Percentage of available balance OR fixed SOL amount
  isDefault?: boolean
}

// Alert settings extending existing AlertConfig
export interface AlertSettings extends AlertConfig {
  notificationChannels: NotificationChannel[]
  alertCategories: AlertCategoryConfig[]
  webhookSettings?: WebhookSettings
}

// Notification channel configuration
export interface NotificationChannel {
  type: 'desktop' | 'sound' | 'email' | 'webhook'
  enabled: boolean
  config: Record<string, any>
}

// Quiet hours configuration
export interface QuietHoursConfig {
  enabled: boolean
  start: string // HH:MM format
  end: string // HH:MM format
  timezone: string
  highPriorityOverride: boolean
  weekendsOnly?: boolean
}

// Alert category configuration
export interface AlertCategoryConfig {
  type: AlertType
  enabled: boolean
  minPriority: AlertPriority
  customSound?: string
}

// Webhook configuration
export interface WebhookSettings {
  url: string
  secret?: string
  retryAttempts: number
  timeout: number // milliseconds
}

// Portfolio settings interface
export interface PortfolioSettings {
  riskTolerance: RiskLevel
  defaultPositionSize: number // Percentage (1-100)
  maxExposure: number // Maximum portfolio exposure percentage
  correlationLimit: number // 0-1 correlation coefficient
  rebalanceThreshold: number // Percentage deviation
  stopLossDefault: number // Default stop loss percentage
  takeProfitDefaults: TakeProfitConfig
  diversificationRules: DiversificationRule[]
}

// Take profit configuration
export interface TakeProfitConfig {
  targets: number[] // Percentage gains
  exitPercentages: number[] // Percentage to exit at each target
  moonBagPercentage: number // Percentage to hold long-term
}

// Diversification rule
export interface DiversificationRule {
  type: 'sector' | 'marketcap' | 'correlation'
  maxAllocation: number // Maximum percentage per category
  enabled: boolean
}

// Security settings interface
export interface SecuritySettings {
  autoApproveTransactions: boolean
  sessionTimeout: number // Minutes
  walletConnectionTimeout: number // Seconds
  emergencyStopEnabled: boolean
  requireConfirmationAbove: number // SOL amount
  whitelistedContracts: string[]
  blacklistedContracts: string[]
  maxDailyTransactions: number
  maxDailyVolume: number // In SOL
  twoFactorEnabled: boolean
}

// UI/UX settings interface
export interface UISettings {
  theme: 'light' | 'dark' | 'auto'
  timezone: string
  language: string
  dateFormat: string
  numberFormat: 'comma' | 'period' | 'space'
  compactMode: boolean
  dashboardLayout: DashboardLayout
  animations: boolean
  chartSettings: ChartSettings
  keyboardShortcuts: boolean
}

// Dashboard layout configuration
export interface DashboardLayout {
  type: 'default' | 'compact' | 'advanced' | 'custom'
  widgets: DashboardWidget[]
  columns: number
}

// Dashboard widget configuration
export interface DashboardWidget {
  id: string
  type: string
  position: { x: number; y: number }
  size: { width: number; height: number }
  config?: Record<string, any>
}

// Chart settings
export interface ChartSettings {
  defaultInterval: '1m' | '5m' | '15m' | '1h' | '4h' | '1d'
  showVolume: boolean
  indicators: string[]
  colorScheme: 'default' | 'tradingview' | 'custom'
}

// Performance settings interface
export interface PerformanceSettings {
  websocketHeartbeat: number // Milliseconds
  priceUpdateFrequency: number // Milliseconds
  cacheEnabled: boolean
  cacheDuration: number // Seconds
  rateLimitBuffer: number // Percentage (0-100)
  maxConcurrentRequests: number
  dataRetentionDays: number
  autoRefreshEnabled: boolean
  autoRefreshInterval: number // Seconds
  lowBandwidthMode: boolean
}

// Settings preset configuration
export interface SettingsPreset {
  id: string
  name: string
  description: string
  category: 'conservative' | 'balanced' | 'aggressive' | 'custom'
  settings: Partial<AppSettings>
  isDefault?: boolean
}

// Settings import/export format
export interface SettingsExport {
  version: string
  exportDate: string
  settings: AppSettings
  checksum?: string
}

// Settings validation result
export interface SettingsValidation {
  valid: boolean
  errors: SettingsValidationError[]
  warnings: string[]
}

// Settings validation error
export interface SettingsValidationError {
  field: string
  message: string
  value?: any
}

// Settings lock state
export interface SettingsLockState {
  lockedSettings: Record<string, boolean>
}